---
description: 
globs: *.vue
alwaysApply: false
---
# Vue 组件开发规范

## 基本原则

### 核心理念
- **JSDoc驱动的组件契约**: 每个组件都应通过完整的JSDoc定义其外部接口
- **定义与实现分离**: 组件的公共接口通过JSDoc明确定义，内部实现保持封装
- **低耦合高内聚**: 组件间仅通过明确的Props、Events、Slots进行通信
- **可测试性优先**: 组件设计应便于编写和维护测试用例

### Vue 2.x Options API 规范
```javascript
// ✅ 推荐：使用Options API结构
export default {
  name: 'ComponentName',
  
  // 组件配置按照固定顺序
  mixins: [],
  extends: {},
  
  // 依赖注入
  inject: {},
  provide() {},
  
  // 组件属性
  props: {},
  
  // 响应式数据
  data() {},
  
  // 计算属性
  computed: {},
  
  // 侦听器
  watch: {},
  
  // 生命周期钩子
  created() {},
  mounted() {},
  beforeDestroy() {},
  
  // 方法定义
  methods: {}
};
```

## JSDoc 组件注释规范

**注意：以下的代码示例均对应`.vue`文件的`<script></script>`片段内部的代码注释示例！**

### 组件级别注释

```javascript
/**
 * 用户信息卡片组件
 * @description 展示用户基本信息的卡片组件，支持编辑和删除操作
 * @component UserCard
 * @example
 * <UserCard
 *   :user="userInfo"
 *   :editable="true"
 *   @edit="handleEdit"
 *   @delete="handleDelete"
 * />
 * @since 1.0.0
 */
export default {
  name: 'UserCard',
  
  /**
   * 组件属性定义
   * @property {Object} user - 用户信息对象
   * @property {string} user.id - 用户ID
   * @property {string} user.name - 用户姓名
   * @property {string} user.email - 用户邮箱
   * @property {string} user.avatar - 用户头像URL
   * @property {boolean} [editable=false] - 是否可编辑
   * @property {boolean} [showAvatar=true] - 是否显示头像
   * @property {('small'|'medium'|'large')} [size='medium'] - 卡片尺寸
   */
  props: {
    /**
     * 用户信息对象
     * @type {Object}
     * @required
     */
    user: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.id === 'string' && typeof value.name === 'string';
      }
    },
    
    /**
     * 是否可编辑
     * @type {boolean}
     * @default false
     */
    editable: {
      type: Boolean,
      default: false
    },
    
    /**
     * 是否显示头像
     * @type {boolean}
     * @default true
     */
    showAvatar: {
      type: Boolean,
      default: true
    },
    
    /**
     * 卡片尺寸
     * @type {string}
     * @default 'medium'
     * @enum {'small', 'medium', 'large'}
     */
    size: {
      type: String,
      default: 'medium',
      validator(value) {
        return ['small', 'medium', 'large'].includes(value);
      }
    }
  }
};
```

### 事件定义注释

```javascript
export default {
  name: 'UserCard',
  
  methods: {
    /**
     * 处理编辑按钮点击
     * @method handleEditClick
     * @private
     * @fires edit
     */
    handleEditClick() {
      /**
       * 编辑事件
       * @event edit
       * @type {Object}
       * @property {string} userId - 用户ID
       * @property {Object} user - 完整用户信息
       */
      this.$emit('edit', {
        userId: this.user.id,
        user: { ...this.user }
      });
    },

    /**
     * 处理删除确认
     * @method handleDeleteConfirm
     * @private
     * @fires delete
     */
    handleDeleteConfirm() {
      /**
       * 删除事件
       * @event delete
       * @type {string} 用户ID
       */
      this.$emit('delete', this.user.id);
    },

    /**
     * 更新用户状态
     * @method updateUserStatus
     * @public
     * @param {('active'|'inactive'|'suspended')} status - 新状态
     * @fires statusChange
     * @example
     * this.$refs.userCard.updateUserStatus('active');
     */
    updateUserStatus(status) {
      const validStatuses = ['active', 'inactive', 'suspended'];
      if (!validStatuses.includes(status)) {
        console.warn(`无效的状态值: ${status}`);
        return;
      }

      /**
       * 状态变更事件
       * @event statusChange
       * @type {Object}
       * @property {string} userId - 用户ID
       * @property {string} oldStatus - 原状态
       * @property {string} newStatus - 新状态
       */
      this.$emit('statusChange', {
        userId: this.user.id,
        oldStatus: this.user.status,
        newStatus: status
      });
    }
  }
};
```

### 插槽定义注释

```javascript
/**
 * 卡片组件
 * @component Card
 * @slot default - 卡片主要内容区域
 * @slot header - 卡片头部内容，通常放置标题和操作按钮
 * @slot footer - 卡片底部内容，通常放置操作按钮
 * @slot actions - 操作按钮区域，会自动添加适当的间距
 * @example
 * <Card>
 *   <template #header>
 *     <h3>标题</h3>
 *   </template>
 *   
 *   <template #default>
 *     <p>主要内容</p>
 *   </template>
 *   
 *   <template #footer>
 *     <button>确定</button>
 *   </template>
 * </Card>
 */
export default {
  name: 'Card',
  
  /**
   * 渲染函数中的插槽定义
   * @method renderSlots
   * @private
   * @returns {VNode[]} 虚拟节点数组
   */
  methods: {
    renderSlots() {
      const slots = {};
      
      /**
       * 默认插槽
       * @slot default
       * @description 卡片的主要内容区域
       */
      if (this.$slots.default) {
        slots.default = this.$slots.default;
      }
      
      /**
       * 头部插槽
       * @slot header
       * @description 卡片头部内容区域
       * @param {Object} slotProps - 插槽属性
       * @param {string} slotProps.title - 自动传递的标题
       */
      if (this.$slots.header) {
        slots.header = this.$slots.header;
      }
      
      return slots;
    }
  }
};
```

### 状态数据注释

```javascript
export default {
  name: 'UserProfileEditor',

  /**
   * 组件的响应式数据
   */
  data() {
    return {
      /**
       * 用户个人资料数据
       * @type {Object}
       * @property {string} id - 用户唯一标识符
       * @property {string} username - 用户名
       * @property {string} email - 用户邮箱
       * @property {string} [bio=''] - 用户简介 (可选)
       * @property {string[]} [roles=[]] - 用户角色列表 (可选，默认为空数组)
       * @default {id: '', username: '', email: '', bio: '', roles: []}
       */
      userProfile: {
        id: '',
        username: '',
        email: '',
        bio: '',
        roles: []
      },
      /**
       * 数据加载状态
       * @type {boolean}
       * @default false
       */
      isLoading: false,
      /**
       * 错误信息列表
       * @type {Array<string>}
       * @default []
       */
      errors: [],
      /**
       * 表单是否已提交
       * @type {boolean}
       * @default false
       */
      isSubmitted: false
    };
  }
};
```



### 计算属性和侦听器注释

```javascript
export default {
  name: 'UserCard',
  
  /**
   * 计算属性定义
   */
  computed: {
    /**
     * 用户显示名称
     * @computed displayName
     * @description 根据用户信息生成显示名称，优先显示昵称，其次显示用户名
     * @returns {string} 格式化的显示名称
     * @example
     * // 当用户有昵称时
     * // 返回: "张三 (zhangsan)"
     * // 当用户无昵称时  
     * // 返回: "zhangsan"
     */
    displayName() {
      if (!this.user) return '';
      
      const { nickname, name } = this.user;
      return nickname ? `${nickname} (${name})` : name;
    },

    /**
     * 用户状态样式类
     * @computed statusClass
     * @description 根据用户状态返回对应的CSS类名
     * @returns {string} CSS类名
     */
    statusClass() {
      const statusMap = {
        active: 'status-active',
        inactive: 'status-inactive',
        suspended: 'status-suspended'
      };
      return statusMap[this.user?.status] || 'status-unknown';
    }
  },

  /**
   * 侦听器定义
   */
  watch: {
    /**
     * 侦听用户信息变化
     * @watch user
     * @description 当用户信息发生变化时，验证数据完整性并更新本地状态
     * @param {Object} newUser - 新的用户信息
     * @param {Object} oldUser - 旧的用户信息
     */
    user: {
      handler(newUser, oldUser) {
        if (!newUser) {
          console.warn('UserCard: 用户信息不能为空');
          return;
        }
        
        // 验证必要字段
        const requiredFields = ['id', 'name', 'email'];
        const missingFields = requiredFields.filter(field => !newUser[field]);
        
        if (missingFields.length > 0) {
          console.error(`UserCard: 缺少必要字段: ${missingFields.join(', ')}`);
        }
        
        // 触发内部状态更新
        this.updateInternalState(newUser);
      },
      immediate: true,
      deep: true
    }
  }
};
```

### 依赖注入注释

```javascript
/**
 * 子组件示例
 * @component ChildComponent
 * @description 演示如何使用依赖注入获取父组件提供的数据和方法
 */
export default {
  name: 'ChildComponent',
  
  /**
   * 注入的依赖
   * @inject theme - 主题配置对象
   * @inject setTheme - 设置主题的方法
   * @inject userService - 用户服务实例
   */
  inject: {
    /**
     * 应用主题配置
     * @type {Object}
     * @property {string} primaryColor - 主要颜色
     * @property {string} mode - 主题模式 ('light'|'dark')
     */
    theme: {
      type: Object,
      default: () => ({
        primaryColor: '#1890ff',
        mode: 'light'
      })
    },
    
    /**
     * 设置主题的方法
     * @type {Function}
     * @param {Object} newTheme - 新的主题配置
     */
    setTheme: {
      type: Function,
      default: () => {}
    },
    
    /**
     * 用户服务实例
     * @type {Object}
     * @property {Function} getCurrentUser - 获取当前用户信息
     * @property {Function} updateUser - 更新用户信息
     */
    userService: {
      type: Object,
      default: () => ({
        getCurrentUser: () => Promise.resolve(null),
        updateUser: () => Promise.resolve()
      })
    }
  },

  /**
   * 提供给子组件的数据和方法
   * @provide
   * @returns {Object} 提供的数据对象
   */
  provide() {
    return {
      /**
       * 组件实例引用
       * @type {Object}
       */
      parentComponent: this,
      
      /**
       * 更新父组件状态的方法
       * @type {Function}
       * @param {Object} newState - 新状态
       */
      updateParentState: this.updateState.bind(this)
    };
  }
};
```

## 组件设计最佳实践

### 单一职责原则

```javascript
// ✅ 推荐：职责单一的组件
/**
 * 用户头像组件
 * @component UserAvatar
 * @description 仅负责显示用户头像，支持不同尺寸和样式
 */
export default {
  name: 'UserAvatar',
  
  props: {
    user: {
      type: Object,
      required: true
    },
    size: {
      type: String,
      default: 'medium'
    }
  }
};

// ❌ 避免：职责过多的组件
/**
 * ❌ 不推荐：一个组件处理用户信息、编辑、权限管理等多种职责
 */
export default {
  name: 'UserEverything', // 避免这样的设计
  // ... 过多的功能集成
};
```

### Props 验证和默认值

```javascript
export default {
  name: 'DataTable',
  
  props: {
    /**
     * 表格数据
     * @type {Array<Object>}
     * @required
     */
    data: {
      type: Array,
      required: true,
      validator(value) {
        // 验证数组中的每个元素都有id字段
        return value.every(item => item && typeof item.id !== 'undefined');
      }
    },
    
    /**
     * 表格列配置
     * @type {Array<Object>}
     * @default []
     */
    columns: {
      type: Array,
      default: () => [],
      validator(value) {
        // 验证列配置的必要字段
        return value.every(col => col.key && col.title);
      }
    },
    
    /**
     * 分页配置
     * @type {Object|boolean}
     * @default false
     */
    pagination: {
      type: [Object, Boolean],
      default: false,
      validator(value) {
        if (typeof value === 'boolean') return true;
        if (typeof value === 'object') {
          return value.current && value.total;
        }
        return false;
      }
    }
  }
};
```

### 事件命名和数据传递

```javascript
export default {
  name: 'SearchInput',
  
  methods: {
    /**
     * 处理搜索输入
     * @method handleSearch
     * @private
     * @fires search
     * @fires input
     */
    handleSearch() {
      const searchData = {
        keyword: this.keyword,
        timestamp: Date.now(),
        filters: { ...this.activeFilters }
      };
      
      /**
       * 搜索事件 - 符合Vue事件命名约定
       * @event search
       * @type {Object}
       * @property {string} keyword - 搜索关键词
       * @property {number} timestamp - 搜索时间戳
       * @property {Object} filters - 活动的过滤器
       */
      this.$emit('search', searchData);
    },

    /**
     * 处理输入值变化 - 支持v-model
     * @method handleInput
     * @private
     * @fires input
     */
    handleInput(value) {
      /**
       * 输入事件 - v-model支持
       * @event input
       * @type {string} 输入值
       */
      this.$emit('input', value);
    }
  }
};
```

### 生命周期钩子注释

```javascript
export default {
  name: 'DataChart',
  
  /**
   * 组件创建时初始化基础配置
   * @lifecycle created
   * @description 在这个阶段初始化数据和配置，但还不能访问DOM
   */
  created() {
    this.initializeChart();
    this.setupEventListeners();
  },

  /**
   * 组件挂载后渲染图表
   * @lifecycle mounted
   * @description DOM已经可用，可以进行图表渲染和第三方库初始化
   */
  mounted() {
    this.$nextTick(() => {
      this.renderChart();
      this.bindResizeListener();
    });
  },

  /**
   * 组件销毁前清理资源
   * @lifecycle beforeDestroy
   * @description 清理定时器、事件监听器和第三方库实例，防止内存泄漏
   */
  beforeDestroy() {
    this.cleanupChart();
    this.removeEventListeners();
    
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
};
```

## 组件通信模式

### Props Down, Events Up

```javascript
// 父组件
/**
 * 用户管理页面
 * @component UserManagement
 */
export default {
  name: 'UserManagement',
  
  methods: {
    /**
     * 处理用户编辑事件
     * @method handleUserEdit
     * @param {Object} editData - 编辑数据
     * @param {string} editData.userId - 用户ID
     * @param {Object} editData.user - 用户信息
     */
    handleUserEdit({ userId, user }) {
      // 处理编辑逻辑
      this.openEditDialog(user);
    },

    /**
     * 处理用户删除事件
     * @method handleUserDelete
     * @param {string} userId - 要删除的用户ID
     */
    handleUserDelete(userId) {
      // 处理删除逻辑
      this.confirmDeleteUser(userId);
    }
  }
};
```

### 兄弟组件通信

```javascript
// 使用事件总线或Vuex进行兄弟组件通信
/**
 * 事件总线工具
 * @module EventBus
 * @description 用于兄弟组件间的事件通信
 */
import Vue from 'vue';
export const EventBus = new Vue();

// 发送事件的组件
export default {
  name: 'ComponentA',
  
  methods: {
    /**
     * 通知其他组件数据更新
     * @method notifyDataUpdate
     * @param {Object} newData - 更新的数据
     */
    notifyDataUpdate(newData) {
      /**
       * 数据更新事件
       * @event data-updated
       * @type {Object}
       */
      EventBus.$emit('data-updated', newData);
    }
  }
};

// 接收事件的组件
export default {
  name: 'ComponentB',
  
  /**
   * 监听数据更新事件
   * @lifecycle created
   */
  created() {
    /**
     * 处理数据更新事件
     * @method handleDataUpdate
     * @param {Object} newData - 更新的数据
     */
    EventBus.$on('data-updated', this.handleDataUpdate);
  },

  /**
   * 清理事件监听器
   * @lifecycle beforeDestroy
   */
  beforeDestroy() {
    EventBus.$off('data-updated', this.handleDataUpdate);
  }
};
```

## 性能优化实践

### 合理使用计算属性和方法

```javascript
export default {
  name: 'ProductList',
  
  computed: {
    /**
     * 过滤后的产品列表
     * @computed filteredProducts
     * @description 根据搜索条件和过滤器筛选产品，结果会被缓存
     * @returns {Array<Object>} 过滤后的产品数组
     */
    filteredProducts() {
      return this.products.filter(product => {
        const matchesSearch = !this.searchKeyword || 
          product.name.toLowerCase().includes(this.searchKeyword.toLowerCase());
        const matchesCategory = !this.selectedCategory || 
          product.category === this.selectedCategory;
        
        return matchesSearch && matchesCategory;
      });
    }
  },

  methods: {
    /**
     * 格式化价格显示
     * @method formatPrice
     * @param {number} price - 原始价格
     * @returns {string} 格式化后的价格字符串
     * @example
     * formatPrice(1234.56) // "￥1,234.56"
     */
    formatPrice(price) {
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      }).format(price);
    }
  }
};
```

### 组件懒加载注释

```javascript
/**
 * 路由配置 - 组件懒加载
 * @description 使用动态导入实现组件懒加载，提高应用启动性能
 */
const routes = [
  {
    path: '/user',
    name: 'User',
    /**
     * 懒加载用户管理组件
     * @returns {Promise<Component>} 用户组件的Promise
     */
    component: () => import(/* webpackChunkName: "user" */ '@/pages/User'),
    meta: {
      title: '用户管理',
      requiresAuth: true
    }
  }
];
```

## 错误边界和异常处理

```javascript
/**
 * 错误边界混入
 * @mixin ErrorBoundary
 * @description 为组件提供统一的错误处理能力
 */
export const ErrorBoundaryMixin = {
  /**
   * 错误捕获处理
   * @method errorCaptured
   * @param {Error} err - 捕获的错误
   * @param {Object} instance - 出错的组件实例
   * @param {string} info - 错误信息
   * @returns {boolean} 是否阻止错误向上传播
   */
  errorCaptured(err, instance, info) {
    console.error('组件错误:', {
      error: err,
      component: instance?.$options.name,
      info
    });
    
    // 上报错误到监控系统
    this.reportError(err, instance, info);
    
    // 阻止错误向上传播
    return false;
  },

  methods: {
    /**
     * 上报错误到监控系统
     * @method reportError
     * @private
     * @param {Error} error - 错误对象
     * @param {Object} instance - 组件实例
     * @param {string} info - 错误信息
     */
    reportError(error, instance, info) {
      // 错误上报逻辑
      if (process.env.NODE_ENV === 'production') {
        // 发送到监控系统
      }
    }
  }
};
``` 