---
description: 
globs: jest.config.js,tests/unit/**/*
alwaysApply: false
---
# Jest 单元测试规范

## 测试理念

### 核心原则
- **测试驱动开发(TDD)**: 先写测试，再写实现
- **行为驱动开发(BDD)**: 专注于业务行为的测试描述
- **高覆盖率**: 单元测试代码覆盖率目标85%以上
- **快速反馈**: 测试运行时间控制在合理范围内

### 测试分层
```
单元测试 (Jest)
├── 纯函数测试 (utils/, models/)
├── Vue组件测试 (@vue/test-utils)
├── 服务层测试 (services/)
└── 状态管理测试 (store/)
```

## 测试文件组织

### 目录结构
```
tests/
├── unit/                    # 单元测试目录
│   ├── components/         # 组件测试
│   │   └── UserCard.unit.spec.js
│   ├── utils/             # 工具函数测试
│   │   └── formatUtils.unit.spec.js
│   ├── services/          # 服务层测试
│   │   └── userService.unit.spec.js
│   ├── models/            # 数据模型测试
│   │   └── User.unit.spec.js
│   └── store/             # 状态管理测试
│       └── userModule.unit.spec.js
├── __mocks__/             # Mock文件
│   ├── axios.js
│   └── localStorage.js
└── setupTests.js          # 测试环境配置
```

### 文件命名规范
- 测试文件以 `.unit.spec.js` 结尾
- 文件路径与源文件保持一致的相对路径结构
- 一对一测试：一个源文件对应一个测试文件
- 多场景测试：使用后缀区分 `UserCard-edit.unit.spec.js`

## 测试编写规范

### 基本测试结构

```javascript
/**
 * 用户工具函数测试
 * @description 测试用户相关的工具函数
 */
import { formatUserName, validateEmail, calculateUserAge } from '@/utils/userUtils';

describe('userUtils', () => {
  // 每个测试套件的设置
  beforeEach(() => {
    // 在每个测试前执行的设置
    jest.clearAllMocks();
  });

  afterEach(() => {
    // 在每个测试后执行的清理
    jest.restoreAllMocks();
  });

  describe('formatUserName', () => {
    it('应该正确格式化完整姓名', () => {
      // Given - 准备测试数据
      const user = {
        firstName: '张',
        lastName: '三',
        nickname: 'zhangsan'
      };

      // When - 执行被测试的函数
      const result = formatUserName(user);

      // Then - 验证结果
      expect(result).toBe('张三 (zhangsan)');
    });

    it('应该处理只有姓氏的情况', () => {
      const user = {
        firstName: '张',
        lastName: '',
        nickname: ''
      };

      const result = formatUserName(user);

      expect(result).toBe('张');
    });

    it('应该处理空用户对象', () => {
      expect(() => formatUserName(null)).toThrow('用户信息不能为空');
      expect(() => formatUserName(undefined)).toThrow('用户信息不能为空');
    });
  });
});
```

### Vue 组件测试

```javascript
/**
 * UserCard 组件测试
 * @description 测试用户卡片组件的各种功能和状态
 */
import { shallowMount, mount } from '@vue/test-utils';
import UserCard from '@/components/UserCard';
import { createLocalVue } from '@vue/test-utils';

// 创建本地Vue实例用于测试
const localVue = createLocalVue();

describe('UserCard.vue', () => {
  let wrapper;
  const mockUser = {
    id: '123',
    name: '张三',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg',
    status: 'active'
  };

  // 默认props
  const defaultProps = {
    user: mockUser,
    editable: false,
    showAvatar: true,
    size: 'medium'
  };

  beforeEach(() => {
    wrapper = null;
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  /**
   * 创建组件实例的辅助函数
   * @param {Object} props - 组件属性
   * @param {Object} options - 挂载选项
   * @returns {Wrapper} Vue测试包装器
   */
  const createWrapper = (props = {}, options = {}) => {
    return shallowMount(UserCard, {
      localVue,
      propsData: {
        ...defaultProps,
        ...props
      },
      ...options
    });
  };

  describe('渲染测试', () => {
    it('应该正确渲染用户信息', () => {
      wrapper = createWrapper();

      expect(wrapper.find('.user-name').text()).toBe('张三');
      expect(wrapper.find('.user-email').text()).toBe('<EMAIL>');
      expect(wrapper.find('.user-avatar').attributes('src')).toBe(mockUser.avatar);
    });

    it('应该根据props控制头像显示', () => {
      wrapper = createWrapper({ showAvatar: false });

      expect(wrapper.find('.user-avatar').exists()).toBe(false);
    });

    it('应该根据size属性应用正确的CSS类', () => {
      wrapper = createWrapper({ size: 'large' });

      expect(wrapper.classes()).toContain('user-card--large');
    });

    it('应该在编辑模式下显示编辑按钮', () => {
      wrapper = createWrapper({ editable: true });

      expect(wrapper.find('.edit-button').exists()).toBe(true);
    });
  });

  describe('事件测试', () => {
    it('应该在点击编辑按钮时触发edit事件', async () => {
      wrapper = createWrapper({ editable: true });

      await wrapper.find('.edit-button').trigger('click');

      expect(wrapper.emitted('edit')).toBeTruthy();
      expect(wrapper.emitted('edit')[0][0]).toEqual({
        userId: '123',
        user: mockUser
      });
    });

    it('应该在点击删除按钮时触发delete事件', async () => {
      wrapper = createWrapper({ editable: true });

      await wrapper.find('.delete-button').trigger('click');

      expect(wrapper.emitted('delete')).toBeTruthy();
      expect(wrapper.emitted('delete')[0][0]).toBe('123');
    });
  });

  describe('计算属性测试', () => {
    it('应该正确计算显示名称', () => {
      const userWithNickname = {
        ...mockUser,
        nickname: 'zhangsan'
      };
      wrapper = createWrapper({ user: userWithNickname });

      expect(wrapper.vm.displayName).toBe('zhangsan (张三)');
    });

    it('应该正确计算状态样式类', () => {
      wrapper = createWrapper();

      expect(wrapper.vm.statusClass).toBe('status-active');
    });
  });

  describe('公共方法测试', () => {
    it('应该能够通过公共方法更新用户状态', async () => {
      wrapper = createWrapper();

      wrapper.vm.updateUserStatus('inactive');

      await wrapper.vm.$nextTick();

      expect(wrapper.emitted('statusChange')).toBeTruthy();
      expect(wrapper.emitted('statusChange')[0][0]).toEqual({
        userId: '123',
        oldStatus: 'active',
        newStatus: 'inactive'
      });
    });

    it('应该在传入无效状态时输出警告', () => {
      wrapper = createWrapper();
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      wrapper.vm.updateUserStatus('invalid');

      expect(consoleSpy).toHaveBeenCalledWith('无效的状态值: invalid');
      
      consoleSpy.mockRestore();
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空用户对象', () => {
      expect(() => {
        wrapper = createWrapper({ user: null });
      }).not.toThrow();
    });

    it('应该处理缺少必要字段的用户对象', () => {
      const incompleteUser = { id: '123' };
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      wrapper = createWrapper({ user: incompleteUser });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('缺少必要字段')
      );
      
      consoleSpy.mockRestore();
    });
  });
});
```

### 服务层测试

```javascript
/**
 * UserService 测试
 * @description 测试用户服务的API调用和数据处理
 */
import UserService from '@/services/userService';
import axios from 'axios';
import { User } from '@/models/User';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

describe('UserService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    it('应该返回用户列表', async () => {
      // Mock API响应
      const mockResponse = {
        data: {
          users: [
            { id: '1', name: '张三', email: '<EMAIL>' },
            { id: '2', name: '李四', email: '<EMAIL>' }
          ]
        }
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await UserService.getUsers();

      expect(mockedAxios.get).toHaveBeenCalledWith('/api/users');
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(User);
      expect(result[0].name).toBe('张三');
    });

    it('应该处理API错误', async () => {
      const errorMessage = '网络错误';
      mockedAxios.get.mockRejectedValue(new Error(errorMessage));

      await expect(UserService.getUsers()).rejects.toThrow(errorMessage);
    });

    it('应该支持过滤参数', async () => {
      const filters = { role: 'admin', active: true };
      mockedAxios.get.mockResolvedValue({ data: { users: [] } });

      await UserService.getUsers(filters);

      expect(mockedAxios.get).toHaveBeenCalledWith('/api/users', {
        params: filters
      });
    });
  });

  describe('createUser', () => {
    it('应该创建新用户', async () => {
      const userData = {
        name: '王五',
        email: '<EMAIL>'
      };
      const mockResponse = {
        data: { 
          id: '3', 
          ...userData,
          createdAt: '2023-01-01T00:00:00Z'
        }
      };
      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await UserService.createUser(userData);

      expect(mockedAxios.post).toHaveBeenCalledWith('/api/users', userData);
      expect(result).toBeInstanceOf(User);
      expect(result.name).toBe('王五');
    });
  });
});
```

### 数据模型测试

```javascript
/**
 * User 模型测试
 * @description 测试用户数据模型类
 */
import { User } from '@/models/User';

describe('User 模型', () => {
  const userData = {
    id: '123',
    name: '张三',
    email: '<EMAIL>',
    role: 'user',
    createdAt: '2023-01-01T00:00:00Z'
  };

  describe('构造函数', () => {
    it('应该正确初始化用户对象', () => {
      const user = new User(userData);

      expect(user.id).toBe('123');
      expect(user.name).toBe('张三');
      expect(user.email).toBe('<EMAIL>');
      expect(user.role).toBe('user');
    });

    it('应该设置默认值', () => {
      const minimalData = { id: '123', name: '张三' };
      const user = new User(minimalData);

      expect(user.role).toBe('user'); // 默认角色
      expect(user.isActive).toBe(true); // 默认激活状态
    });

    it('应该处理空数据', () => {
      expect(() => new User(null)).toThrow('用户数据不能为空');
      expect(() => new User({})).toThrow('用户ID和姓名是必需的');
    });
  });

  describe('实例方法', () => {
    let user;

    beforeEach(() => {
      user = new User(userData);
    });

    it('应该正确验证邮箱格式', () => {
      expect(user.isValidEmail()).toBe(true);

      user.email = 'invalid-email';
      expect(user.isValidEmail()).toBe(false);
    });

    it('应该正确计算用户年龄', () => {
      user.birthDate = '1990-01-01';
      const age = user.getAge();

      expect(age).toBeGreaterThan(30);
      expect(typeof age).toBe('number');
    });

    it('应该正确格式化显示名称', () => {
      user.nickname = 'zhangsan';
      expect(user.getDisplayName()).toBe('zhangsan (张三)');

      user.nickname = null;
      expect(user.getDisplayName()).toBe('张三');
    });
  });

  describe('静态方法', () => {
    it('应该从API响应创建用户实例', () => {
      const apiResponse = {
        user_id: '123',
        full_name: '张三',
        email_address: '<EMAIL>'
      };

      const user = User.fromApiResponse(apiResponse);

      expect(user).toBeInstanceOf(User);
      expect(user.id).toBe('123');
      expect(user.name).toBe('张三');
      expect(user.email).toBe('<EMAIL>');
    });

    it('应该验证用户数据格式', () => {
      const validData = { id: '123', name: '张三', email: '<EMAIL>' };
      const invalidData = { name: '张三' }; // 缺少ID

      expect(User.isValidUserData(validData)).toBe(true);
      expect(User.isValidUserData(invalidData)).toBe(false);
    });
  });
});
```

## Mock 和 Stub 规范

### HTTP 请求 Mock

```javascript
// tests/__mocks__/axios.js
/**
 * Axios Mock
 * @description 模拟HTTP请求，提供可控的测试环境
 */
export default {
  get: jest.fn(() => Promise.resolve({ data: {} })),
  post: jest.fn(() => Promise.resolve({ data: {} })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  create: jest.fn(() => ({
    get: jest.fn(() => Promise.resolve({ data: {} })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} }))
  }))
};
```

### 浏览器 API Mock

```javascript
// tests/__mocks__/localStorage.js
/**
 * LocalStorage Mock
 * @description 模拟浏览器本地存储
 */
const localStorageMock = {
  getItem: jest.fn((key) => {
    return localStorageMock.store[key] || null;
  }),
  setItem: jest.fn((key, value) => {
    localStorageMock.store[key] = String(value);
  }),
  removeItem: jest.fn((key) => {
    delete localStorageMock.store[key];
  }),
  clear: jest.fn(() => {
    localStorageMock.store = {};
  }),
  store: {}
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});
```

### Vue 组件 Mock

```javascript
// 在测试文件中Mock子组件
jest.mock('@/components/UserAvatar', () => ({
  name: 'UserAvatar',
  template: '<div class="mocked-avatar">{{ user.name }}</div>',
  props: ['user', 'size']
}));
```

## 测试工具函数

### 测试辅助函数

```javascript
// tests/utils/testHelpers.js
/**
 * 测试辅助函数
 * @description 提供通用的测试工具函数
 */

/**
 * 创建模拟用户数据
 * @param {Object} overrides - 覆盖的属性
 * @returns {Object} 模拟用户对象
 */
export const createMockUser = (overrides = {}) => ({
  id: '123',
  name: '测试用户',
  email: '<EMAIL>',
  role: 'user',
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  ...overrides
});

/**
 * 创建模拟Vue组件包装器
 * @param {Object} component - Vue组件
 * @param {Object} options - 挂载选项
 * @returns {Wrapper} Vue测试包装器
 */
export const createMockWrapper = (component, options = {}) => {
  const localVue = createLocalVue();
  
  return shallowMount(component, {
    localVue,
    ...options
  });
};

/**
 * 等待异步操作完成
 * @param {number} ms - 等待时间（毫秒）
 * @returns {Promise<void>}
 */
export const delay = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 触发DOM事件
 * @param {Element} element - DOM元素
 * @param {string} eventType - 事件类型
 * @param {Object} eventData - 事件数据
 */
export const triggerEvent = (element, eventType, eventData = {}) => {
  const event = new Event(eventType, { bubbles: true });
  Object.assign(event, eventData);
  element.dispatchEvent(event);
};
```

## 测试覆盖率要求

### 覆盖率目标
- **语句覆盖率**: 85%以上
- **分支覆盖率**: 80%以上  
- **函数覆盖率**: 90%以上
- **行覆盖率**: 85%以上

### 覆盖率配置
```javascript
// jest.config.js 中的覆盖率配置
module.exports = {
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!src/router/index.js',
    '!**/node_modules/**'
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 90,
      lines: 85,
      statements: 85
    }
  }
};
```

## 最佳实践

### 测试命名约定
```javascript
describe('功能模块名称', () => {
  describe('具体功能/方法名', () => {
    it('应该在特定条件下产生预期结果', () => {
      // 测试实现
    });
    
    it('应该在异常情况下正确处理错误', () => {
      // 异常测试
    });
  });
});
```

### 测试数据管理
- 使用工厂函数创建测试数据
- 为不同测试场景准备不同的数据集
- 测试数据应该最小化且有意义
- 避免在测试间共享可变状态

### 异步测试
```javascript
// 使用 async/await
it('应该正确处理异步操作', async () => {
  const result = await someAsyncFunction();
  expect(result).toBe(expectedValue);
});

// 使用 Promise
it('应该正确处理Promise', () => {
  return somePromiseFunction().then(result => {
    expect(result).toBe(expectedValue);
  });
});

// 测试异步错误
it('应该正确处理异步错误', async () => {
  await expect(someAsyncFunction()).rejects.toThrow('错误信息');
});
```

### 性能测试注意事项
- 避免在测试中使用真实的延时
- 使用 `jest.useFakeTimers()` 模拟时间
- 及时清理定时器和事件监听器
- 控制测试执行时间，避免超时 