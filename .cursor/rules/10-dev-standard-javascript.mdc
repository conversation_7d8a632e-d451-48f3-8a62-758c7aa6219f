---
description: 
globs: *.js,*.vue
alwaysApply: false
---
# JavaScript 开发规范

## 基本原则

### 核心理念
- **JSDoc为核心**: 所有函数、类、常量必须有完整的JSDoc注释
- **类型安全**: 通过JSDoc类型注解实现编译时类型检查
- **可读性优先**: 代码应当自文档化，易于理解和维护
- **现代语法**: 使用ES6+语法提高代码质量和开发效率

### 语法选择
```javascript
// ✅ 推荐：使用现代语法
const getUserInfo = async (userId) => {
  const { data } = await api.fetchUser(userId);
  return data;
};

// ❌ 避免：过时的语法
var getUserInfo = function(userId) {
  return api.fetchUser(userId).then(function(response) {
    return response.data;
  });
};
```

## JSDoc 规范

### 必须注释的代码元素

所有以下代码元素都**必须**包含JSDoc注释：

1. **函数和方法**
2. **类和构造函数**
3. **常量和配置对象**
4. **模块导出的变量**
5. **复杂的数据结构**

### 函数注释规范

```javascript
/**
 * 计算两个数的和
 * @description 这是一个用于演示的简单加法函数
 * @param {number} a - 第一个加数
 * @param {number} b - 第二个加数
 * @returns {number} 两数之和
 * @throws {TypeError} 当参数不是数字时抛出类型错误
 * @example
 * // 基本用法
 * const result = add(5, 3);
 * console.log(result); // 输出: 8
 * 
 * @example
 * // 错误处理
 * try {
 *   add('5', 3); // 抛出 TypeError
 * } catch (error) {
 *   console.error(error.message);
 * }
 * @since 1.0.0
 * @public
 */
const add = (a, b) => {
  if (typeof a !== 'number' || typeof b !== 'number') {
    throw new TypeError('参数必须是数字类型');
  }
  return a + b;
};
```

### 异步函数注释

```javascript
/**
 * 获取用户信息
 * @async
 * @description 从服务器获取指定用户的详细信息
 * @param {string} userId - 用户ID
 * @param {Object} [options={}] - 请求选项
 * @param {boolean} [options.includeProfile=false] - 是否包含用户资料
 * @param {number} [options.timeout=5000] - 请求超时时间（毫秒）
 * @returns {Promise<Object>} 用户信息对象
 * @returns {string} returns.id - 用户ID
 * @returns {string} returns.name - 用户姓名
 * @returns {string} returns.email - 用户邮箱
 * @throws {NetworkError} 网络请求失败时抛出
 * @throws {ValidationError} 用户ID格式不正确时抛出
 * @example
 * // 基本用法
 * const user = await getUserInfo('123');
 * 
 * @example
 * // 带选项的用法
 * const user = await getUserInfo('123', {
 *   includeProfile: true,
 *   timeout: 3000
 * });
 */
const getUserInfo = async (userId, options = {}) => {
  const { includeProfile = false, timeout = 5000 } = options;
  
  try {
    const response = await fetch(`/api/users/${userId}`, {
      signal: AbortSignal.timeout(timeout)
    });
    
    if (!response.ok) {
      throw new NetworkError(`请求失败: ${response.status}`);
    }
    
    const userData = await response.json();
    return userData;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new NetworkError('请求超时');
    }
    throw error;
  }
};
```

### 类注释规范

```javascript
/**
 * 用户管理器类
 * @class UserManager
 * @description 负责用户数据的增删改查操作
 * @example
 * const userManager = new UserManager({
 *   apiUrl: 'https://api.example.com',
 *   timeout: 5000
 * });
 */
class UserManager {
  /**
   * 创建用户管理器实例
   * @constructor
   * @param {Object} config - 配置对象
   * @param {string} config.apiUrl - API服务器地址
   * @param {number} [config.timeout=3000] - 请求超时时间
   * @param {string} [config.apiKey] - API密钥
   */
  constructor({ apiUrl, timeout = 3000, apiKey }) {
    /** @private */
    this._apiUrl = apiUrl;
    /** @private */
    this._timeout = timeout;
    /** @private */
    this._apiKey = apiKey;
  }

  /**
   * 获取用户列表
   * @method
   * @async
   * @param {Object} [filters={}] - 过滤条件
   * @param {string} [filters.role] - 用户角色
   * @param {boolean} [filters.active] - 是否激活
   * @returns {Promise<Array<Object>>} 用户列表
   * @public
   */
  async getUsers(filters = {}) {
    // 实现代码...
  }

  /**
   * 内部方法：验证用户数据
   * @method
   * @private
   * @param {Object} userData - 用户数据
   * @returns {boolean} 验证结果
   */
  _validateUserData(userData) {
    // 实现代码...
  }
}
```

### 常量和配置注释

```javascript
/**
 * 应用配置常量
 * @namespace AppConfig
 */
const AppConfig = {
  /**
   * API配置
   * @type {Object}
   * @property {string} baseUrl - API基础URL
   * @property {number} timeout - 请求超时时间（毫秒）
   * @property {string} version - API版本
   */
  api: {
    baseUrl: 'https://api.example.com',
    timeout: 5000,
    version: 'v1'
  },

  /**
   * 用户角色枚举
   * @enum {string}
   */
  roles: {
    ADMIN: 'admin',
    USER: 'user',
    GUEST: 'guest'
  }
};

/**
 * 默认分页配置
 * @constant {Object}
 * @default
 */
const DEFAULT_PAGINATION = {
  /** @type {number} 页码，从1开始 */
  page: 1,
  /** @type {number} 每页记录数 */
  pageSize: 20,
  /** @type {number} 总记录数 */
  total: 0
};
```

### 复杂数据结构注释

#### @typedef 类型定义（简单数据结构）

```javascript
/**
 * 用户信息类型定义
 * @typedef {Object} UserInfo
 * @property {string} id - 用户唯一标识
 * @property {string} name - 用户姓名
 * @property {string} email - 用户邮箱
 * @property {('admin'|'user'|'guest')} role - 用户角色
 * @property {UserProfile} [profile] - 用户资料（可选）
 * @property {string} createdAt - 创建时间（ISO 8601格式）
 * @property {string} updatedAt - 更新时间（ISO 8601格式）
 */

/**
 * 用户资料类型定义
 * @typedef {Object} UserProfile
 * @property {string} avatar - 头像URL
 * @property {string} bio - 个人简介
 * @property {Object} preferences - 用户偏好设置
 * @property {boolean} preferences.emailNotifications - 是否接收邮件通知
 * @property {('light'|'dark')} preferences.theme - 界面主题
 */

/**
 * 处理用户数据
 * @param {UserInfo} user - 用户信息
 * @returns {Object} 处理后的用户数据
 */
const processUser = (user) => {
  // 实现代码...
};
```

#### 推荐：使用模型类（复杂数据结构）

**对于包含业务逻辑的复杂数据结构，强烈推荐使用模型类而非 @typedef**：

```javascript
/**
 * 用户数据模型类
 * @class User
 * @description 用户数据模型，包含用户信息和相关业务方法
 * @example
 * const user = new User({
 *   id: '123',
 *   name: '张三',
 *   email: '<EMAIL>'
 * });
 * 
 * if (user.isValidEmail()) {
 *   console.log(user.getDisplayName());
 * }
 */
class User {
  /**
   * 创建用户实例
   * @constructor
   * @param {Object} data - 用户数据
   * @param {string} data.id - 用户ID
   * @param {string} data.name - 用户姓名
   * @param {string} data.email - 用户邮箱
   * @param {string} [data.role='user'] - 用户角色
   */
  constructor(data) {
    if (!data || !data.id || !data.name) {
      throw new Error('用户ID和姓名是必需的');
    }
    
    this.id = data.id;
    this.name = data.name;
    this.email = data.email;
    this.role = data.role || 'user';
    this.createdAt = data.createdAt || new Date().toISOString();
  }

  /**
   * 验证邮箱格式
   * @method isValidEmail
   * @public
   * @returns {boolean} 邮箱是否有效
   */
  isValidEmail() {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(this.email);
  }

  /**
   * 获取用户显示名称
   * @method getDisplayName
   * @public
   * @returns {string} 格式化的显示名称
   */
  getDisplayName() {
    return this.nickname ? `${this.nickname} (${this.name})` : this.name;
  }

  /**
   * 检查用户是否为管理员
   * @method isAdmin
   * @public
   * @returns {boolean} 是否为管理员
   */
  isAdmin() {
    return this.role === 'admin';
  }

  /**
   * 将用户数据转换为API格式
   * @method toApiFormat
   * @public
   * @returns {Object} API格式的用户数据
   */
  toApiFormat() {
    return {
      user_id: this.id,
      full_name: this.name,
      email_address: this.email,
      user_role: this.role,
      created_at: this.createdAt
    };
  }

  /**
   * 从API响应创建用户实例
   * @static
   * @method fromApiResponse
   * @param {Object} apiData - API响应数据
   * @returns {User} 用户实例
   */
  static fromApiResponse(apiData) {
    return new User({
      id: apiData.user_id,
      name: apiData.full_name,
      email: apiData.email_address,
      role: apiData.user_role,
      createdAt: apiData.created_at
    });
  }
}
```

**模型类的优势**：
1. **逻辑复用**: 业务方法可以在多处复用
2. **单元测试**: 可以针对每个方法编写测试用例
3. **类型安全**: 提供更强的类型约束
4. **封装性**: 数据和操作封装在一起
5. **扩展性**: 便于添加新方法和属性

**使用指导原则**：
- **简单数据传输**: 使用 `@typedef`
- **包含业务逻辑**: 使用模型类 `class`
- **数据验证需求**: 使用模型类 `class`
- **需要单元测试**: 使用模型类 `class`

## 现代JavaScript语法规范

### 变量声明

```javascript
// ✅ 推荐：优先使用const
const API_URL = 'https://api.example.com';
const users = [];

// ✅ 需要重新赋值时使用let
let currentUser = null;
let isLoading = false;

// ❌ 避免：不使用var
var oldStyleVar = 'avoid this';
```

### 函数定义

```javascript
// ✅ 推荐：箭头函数用于简单函数
const calculateTotal = (items) => {
  return items.reduce((sum, item) => sum + item.price, 0);
};

// ✅ 推荐：async/await处理异步
const fetchUserData = async (userId) => {
  try {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('获取用户数据失败:', error);
    throw error;
  }
};

// ✅ 推荐：对象方法简写
const userService = {
  async getUser(id) {
    return await this.fetchUserData(id);
  },
  
  validateUser({ name, email }) {
    return name && email;
  }
};
```

### 解构赋值

```javascript
// ✅ 推荐：对象解构
const { name, email, role } = user;
const { data, error } = await apiCall();

// ✅ 推荐：数组解构
const [first, second, ...rest] = items;

// ✅ 推荐：函数参数解构
const createUser = ({ name, email, role = 'user' }) => {
  return {
    id: generateId(),
    name,
    email,
    role,
    createdAt: new Date().toISOString()
  };
};
```

### 模板字符串

```javascript
// ✅ 推荐：模板字符串
const welcomeMessage = `欢迎 ${user.name}！您的角色是 ${user.role}`;

// ✅ 推荐：多行字符串
const htmlTemplate = `
  <div class="user-card">
    <h3>${user.name}</h3>
    <p>${user.email}</p>
  </div>
`;
```

### 数组和对象操作

```javascript
// ✅ 推荐：展开运算符
const newUser = { ...existingUser, updatedAt: new Date() };
const allItems = [...items1, ...items2];

// ✅ 推荐：数组方法链
const activeUsers = users
  .filter(user => user.active)
  .map(user => ({ ...user, displayName: user.name.toUpperCase() }))
  .sort((a, b) => a.createdAt.localeCompare(b.createdAt));
```

## 错误处理规范

### 统一错误处理

```javascript
/**
 * 自定义错误类
 * @class ApplicationError
 * @extends Error
 */
class ApplicationError extends Error {
  /**
   * @param {string} message - 错误消息
   * @param {string} code - 错误代码
   * @param {number} [statusCode=500] - HTTP状态码
   */
  constructor(message, code, statusCode = 500) {
    super(message);
    this.name = 'ApplicationError';
    this.code = code;
    this.statusCode = statusCode;
  }
}

/**
 * API调用包装器
 * @param {Function} apiCall - API调用函数
 * @returns {Promise<*>} API响应数据
 * @throws {ApplicationError} 统一格式的错误
 */
const withErrorHandling = async (apiCall) => {
  try {
    return await apiCall();
  } catch (error) {
    if (error instanceof ApplicationError) {
      throw error;
    }
    
    // 转换为统一错误格式
    throw new ApplicationError(
      error.message || '未知错误',
      'UNKNOWN_ERROR',
      error.status || 500
    );
  }
};
```

## 代码质量要求

### 命名规范

```javascript
// ✅ 推荐：描述性命名
const getUsersByRole = (role) => { /* ... */ };
const isEmailValid = (email) => { /* ... */ };
const MAX_RETRY_COUNT = 3;

// ❌ 避免：模糊命名
const getData = () => { /* ... */ };
const check = (x) => { /* ... */ };
const MAX = 3;
```

### 函数设计原则

1. **单一职责**: 每个函数只做一件事
2. **纯函数优先**: 避免副作用，便于测试
3. **参数限制**: 参数数量不超过3个，多的使用对象传参
4. **返回值一致**: 同一函数的返回值类型应该一致

```javascript
// ✅ 推荐：纯函数
const formatCurrency = (amount, currency = 'CNY') => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency
  }).format(amount);
};

// ✅ 推荐：对象参数
const createApiClient = ({ baseUrl, timeout, apiKey }) => {
  // 实现代码...
};
```

### 注释最佳实践

1. **JSDoc必须完整**: 包含描述、参数、返回值、异常
2. **代码即文档**: 好的命名减少注释需求
3. **更新及时**: 代码变更时同步更新注释
4. **示例丰富**: 复杂函数提供使用示例

## 性能和优化

### 避免常见性能陷阱

```javascript
// ✅ 推荐：缓存DOM查询
const element = document.querySelector('.my-element');
const handler = (event) => {
  // 使用缓存的element
};

// ✅ 推荐：避免不必要的计算
const expensiveValue = useMemo(() => {
  return complexCalculation(data);
}, [data]);

// ✅ 推荐：合理使用防抖和节流
const debouncedSearch = debounce(performSearch, 300);
```

### 内存管理

```javascript
// ✅ 推荐：及时清理事件监听器
const setupEventListener = () => {
  const handler = (event) => {
    // 处理事件
  };
  
  element.addEventListener('click', handler);
  
  // 返回清理函数
  return () => {
    element.removeEventListener('click', handler);
  };
};
``` 