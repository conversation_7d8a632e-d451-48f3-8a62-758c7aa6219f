---
description: 
globs: 
alwaysApply: true
---
# 项目架构规范

## 技术栈

### 核心框架
- **Vue 2.x**: 渐进式JavaScript框架，使用Options API
- **Ant Design Vue 1.7.8**: 企业级UI组件库
- **Vue Router**: 官方路由管理器
- **Vuex**: 状态管理模式和库
- **Axios 1.7.9**: 后端服务请求框架

### 构建工具与兼容性
- **Vue CLI**: 基于webpack的构建工具链
- **Babel**: JavaScript编译器，支持ES6+语法转ES5
- **Core.js 2**: JavaScript标准库的polyfill
- **目标兼容性**: ES5运行时，ES6+开发语法

### 测试框架
- **Jest**: JavaScript单元测试框架
- **Playwright**: 现代化端到端测试框架
- **@vue/test-utils**: Vue组件测试工具

### 代码质量
- **ESLint**: JavaScript代码检查工具
- **JSDoc**: 文档生成和类型注解标准
- **vue-docgen**: Vue组件API文档生成框架

## 目录结构规范

```
项目根目录/
├── src/                    # 源代码目录
│   ├── main.js            # 应用入口文件
│   ├── App.vue            # 根组件
│   ├── components/        # 通用组件目录
│   ├── pages/            # 页面组件目录
│   ├── router/           # 路由配置
│   │   └── index.js
│   ├── store/            # 状态管理
│   ├── utils/            # 工具函数
│   ├── services/         # API服务层
│   └── models/           # 数据模型
├── tests/                 # 测试文件目录
│   ├── unit/             # 单元测试
│   │   └── **/*.unit.spec.js
│   └── e2e/              # 端到端测试
│       ├── **/*Test.vue    
│       └── **/*.e2e.spec.js
├── docs/                 # 文档目录
│   ├── design/          # 设计文档，包含`<component or page name>-FDS.md` 的功能设计文档、`<component or page name>-TDS.md` 的技术详细文档。
│   └── references/      # API 参考文档（目录结构与src的子目录一致）
├── public/              # 静态资源
└── 配置文件
    ├── vue.config.js    # Vue CLI配置
    ├── babel.config.js  # Babel配置
    ├── jsconfig.json    # JavaScript项目配置
    ├── jest.config.js   # Jest测试配置
    └── playwright.config.js # Playwright配置
```

### 组件目录结构

对于复杂组件，采用独立目录结构：

```
components/ComponentName/
├── index.vue           # 组件入口文件
├── components/         # 子组件目录
├── utils/             # 组件内部工具函数
└── models/            # 组件相关数据模型
```

## 开发流程理念

### 以JSDoc为核心的开发模式

本项目采用**JSDoc为核心**的开发理念，实现**定义与实现分离**：

1. **组件契约优先**: 通过JSDoc明确定义组件的Props、Events、Methods、Slots
2. **文档驱动开发**: JSDoc注释即是文档，确保代码自文档化
3. **类型安全**: 通过JSDoc类型注解提供编译时类型检查
4. **AI友好**: 清晰的接口定义便于AI理解和辅助开发

### BDD/TDD驱动的开发流程

```mermaid
graph LR
    A[需求分析] --> B[组件设计]
    B --> C[JSDoc定义]
    C --> D[测试用例编写]
    D --> E[组件实现]
    E --> F[测试验证]
    F --> G[文档完善]
    G --> H[代码审查]
```

#### 开发步骤

1. **需求分析与设计**
   - 分析功能需求和用户场景
   - 设计组件API和交互流程

2. **JSDoc契约定义**
   - 定义组件Props、Events、Methods、Slots
   - 明确数据类型和业务约束
   - 编写详细的使用说明和示例

3. **测试先行**
   - 基于JSDoc定义编写单元测试
   - 设计端到端测试场景
   - 确保测试覆盖率达标

4. **组件实现**
   - 严格按照JSDoc定义实现功能
   - 遵循ES6+语法编写可读性强的代码
   - 确保通过所有测试用例

5. **集成与验证**
   - 运行完整测试套件
   - 验证组件在实际场景中的表现
   - 进行代码质量检查

## 编码约定

### 语法规范
- 使用现代ES6+语法编写代码（const/let、箭头函数、解构赋值、async/await等）
- 目标运行环境为ES5，通过Babel自动转译
- 优先使用const声明，需要重新赋值时使用let
- 避免使用var声明变量

### 模块化规范
- 使用ES6模块语法（import/export）
- 支持路径别名：`@/` 指向 `src/` 目录
- 组件导入时省略文件扩展名

### 异步处理
- 优先使用async/await语法处理异步操作
- Promise链式调用作为备选方案
- 统一的错误处理机制

## 组件分类标准

### 通用组件 (components/)
- 可在多个页面复用的UI组件
- 不包含业务逻辑和路由相关代码
- 具有清晰的Props和Events定义

### 页面组件 (pages/)
- 特定业务场景的页面级组件
- 可包含路由逻辑和业务状态管理
- 通常不被其他页面复用

### 组件开发原则
1. **单一职责**: 每个组件只负责一个明确的功能
2. **松耦合**: 组件间通过Props和Events通信
3. **高内聚**: 相关功能集中在同一组件内
4. **可测试**: 便于编写和维护测试用例 

## 作为 AI Agent 的行为规范
- 严格遵循用户的要求，一字不差。
- 首先逐步思考——用伪代码详细描述你的构建计划。
- 确认后，再编写代码！
- 始终编写正确、符合最佳实践、遵循 DRY 原则（不要重复自己）、无错误、功能完整且可运行的代码，同时应遵循下面列出的代码实现指南中的规则。
- 注重代码的简洁性和可读性，而不是性能。
- **完整实现所有请求的功能**。
- 不要留下任何待办事项、占位符或缺失部分。
- 确保代码完整！彻底验证最终结果。
- 包含所有必需的导入，并确保关键组件的命名正确。
- 简洁明了，尽量减少其他描述。
- **如果你认为可能没有正确答案，请如实告知**。
- **如果你不知道答案，请如实说明，而不是猜测**。
- 对于可复用的工具函数或者方法，请添加注释，解释其用途和功能。**最重要是生成对应的单元测试用例，并运行单元测试命令保证所有通过所有单元测试，最后还要保证用例的代码覆盖率**。