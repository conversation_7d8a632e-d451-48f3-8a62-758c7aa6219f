---
description: 
globs: playwright.config.js,tests/e2e/**/*
alwaysApply: false
---
# Playwright 端到端测试规范

## 测试理念

### 核心原则
- **用户中心**: 从真实用户的角度测试完整的业务流程
- **端到端覆盖**: 测试从前端到后端的完整数据流
- **跨浏览器兼容**: 确保在不同浏览器环境下的一致性
- **稳定可靠**: 编写抗变化的、稳定的测试用例

### 测试范围
```
端到端测试 (Playwright)
├── 用户认证流程
├── 核心业务流程
├── 页面导航和路由
├── 表单提交和验证
├── 数据交互和API集成
└── 错误处理和边界情况
```

## 测试文件组织

### 目录结构
```
tests/
├── e2e/                     # 端到端测试目录
│   ├── pages/              # 页面测试
|   |   ├── UserManagementTest.Vue
│   │   └── UserManagement.e2e.spec.js
│   ├── components/         # 组件集成测试
|   |   ├── UserCardTest.Vue
│   │   └── UserCard.e2e.spec.js
│   ├── flows/              # 业务流程测试
│   │   ├── auth/           # 认证流程
│   │   ├── user-management/  # 用户管理流程
│   │   └── data-processing/  # 数据处理流程
│   ├── fixtures/           # 测试数据
│   │   ├── users.json
│   │   └── testData.js
│   ├── page-objects/       # 页面对象模式
│   │   ├── BasePage.js
│   │   ├── LoginPage.js
│   │   └── UserManagementPage.js
│   └── helpers/            # 测试辅助函数
│       ├── setup.js
│       ├── cleanup.js
│       └── mockData.js
├── router/                 # 测试路由配置
│   └── test-routes.js
└── playwright.config.js    # Playwright配置
```

### 文件命名规范
- 测试文件以 `.e2e.spec.js` 结尾
- 页面对象文件以 `Page.js` 结尾
- 测试页面组件以 `Test.vue` 结尾
- 业务流程测试按功能模块组织目录

## 页面对象模式 (Page Object Model)

### 基础页面对象

```javascript
/**
 * 基础页面对象类
 * @description 提供所有页面对象的通用功能和方法
 */
class BasePage {
  /**
   * 构造函数
   * @param {import('@playwright/test').Page} page - Playwright页面对象
   */
  constructor(page) {
    this.page = page;
    this.baseUrl = process.env.BASE_URL || 'http://localhost:8080';
  }

  /**
   * 导航到指定路径
   * @param {string} path - 相对路径
   * @returns {Promise<void>}
   */
  async goto(path = '') {
    const url = `${this.baseUrl}${path}`;
    await this.page.goto(url);
    await this.waitForPageLoad();
  }

  /**
   * 等待页面加载完成
   * @returns {Promise<void>}
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * 等待元素可见
   * @param {string} selector - 元素选择器
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise<void>}
   */
  async waitForElement(selector, timeout = 10000) {
    await this.page.waitForSelector(selector, { 
      state: 'visible', 
      timeout 
    });
  }

  /**
   * 填写表单字段
   * @param {string} selector - 输入框选择器
   * @param {string} value - 填写的值
   * @returns {Promise<void>}
   */
  async fillField(selector, value) {
    await this.page.fill(selector, value);
  }

  /**
   * 点击元素
   * @param {string} selector - 元素选择器
   * @returns {Promise<void>}
   */
  async clickElement(selector) {
    await this.page.click(selector);
  }

  /**
   * 获取元素文本
   * @param {string} selector - 元素选择器
   * @returns {Promise<string>} 元素文本内容
   */
  async getElementText(selector) {
    return await this.page.textContent(selector);
  }

  /**
   * 截图保存
   * @param {string} name - 截图文件名
   * @returns {Promise<void>}
   */
  async takeScreenshot(name) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}.png`,
      fullPage: true 
    });
  }

  /**
   * 检查元素是否存在
   * @param {string} selector - 元素选择器
   * @returns {Promise<boolean>} 是否存在
   */
  async isElementVisible(selector) {
    try {
      await this.page.waitForSelector(selector, { 
        state: 'visible', 
        timeout: 3000 
      });
      return true;
    } catch {
      return false;
    }
  }
}

export default BasePage;
```

### 具体页面对象

```javascript
/**
 * 用户管理页面对象
 * @description 封装用户管理页面的所有交互操作
 */
import BasePage from './BasePage.js';

class UserManagementPage extends BasePage {
  constructor(page) {
    super(page);
    
    // 页面元素选择器
    this.selectors = {
      // 头部区域
      pageTitle: '[data-testid="page-title"]',
      addUserButton: '[data-testid="add-user-btn"]',
      searchInput: '[data-testid="search-input"]',
      filterSelect: '[data-testid="filter-select"]',
      
      // 用户列表
      userTable: '[data-testid="user-table"]',
      userRow: '[data-testid="user-row"]',
      userName: '[data-testid="user-name"]',
      userEmail: '[data-testid="user-email"]',
      userStatus: '[data-testid="user-status"]',
      
      // 操作按钮
      editButton: '[data-testid="edit-btn"]',
      deleteButton: '[data-testid="delete-btn"]',
      viewButton: '[data-testid="view-btn"]',
      
      // 对话框
      editDialog: '[data-testid="edit-dialog"]',
      deleteConfirmDialog: '[data-testid="delete-confirm-dialog"]',
      confirmButton: '[data-testid="confirm-btn"]',
      cancelButton: '[data-testid="cancel-btn"]',
      
      // 表单字段
      nameInput: '[data-testid="name-input"]',
      emailInput: '[data-testid="email-input"]',
      roleSelect: '[data-testid="role-select"]',
      saveButton: '[data-testid="save-btn"]',
      
      // 分页
      pagination: '[data-testid="pagination"]',
      nextPageButton: '[data-testid="next-page"]',
      prevPageButton: '[data-testid="prev-page"]',
      
      // 状态提示
      loadingSpinner: '[data-testid="loading"]',
      successMessage: '[data-testid="success-message"]',
      errorMessage: '[data-testid="error-message"]'
    };
  }

  /**
   * 导航到用户管理页面
   * @returns {Promise<void>}
   */
  async navigateTo() {
    await this.goto('/users');
    await this.waitForElement(this.selectors.pageTitle);
  }

  /**
   * 搜索用户
   * @param {string} keyword - 搜索关键词
   * @returns {Promise<void>}
   */
  async searchUsers(keyword) {
    await this.fillField(this.selectors.searchInput, keyword);
    await this.page.keyboard.press('Enter');
    await this.waitForTableUpdate();
  }

  /**
   * 等待表格数据更新
   * @returns {Promise<void>}
   */
  async waitForTableUpdate() {
    // 等待加载状态消失
    try {
      await this.page.waitForSelector(this.selectors.loadingSpinner, {
        state: 'hidden',
        timeout: 10000
      });
    } catch {
      // 如果没有加载状态，继续执行
    }
    
    // 等待表格内容更新
    await this.page.waitForFunction(() => {
      const table = document.querySelector('[data-testid="user-table"]');
      return table && table.children.length > 0;
    });
  }

  /**
   * 添加新用户
   * @param {Object} userData - 用户数据
   * @param {string} userData.name - 用户姓名
   * @param {string} userData.email - 用户邮箱
   * @param {string} userData.role - 用户角色
   * @returns {Promise<void>}
   */
  async addUser(userData) {
    await this.clickElement(this.selectors.addUserButton);
    await this.waitForElement(this.selectors.editDialog);
    
    await this.fillField(this.selectors.nameInput, userData.name);
    await this.fillField(this.selectors.emailInput, userData.email);
    await this.page.selectOption(this.selectors.roleSelect, userData.role);
    
    await this.clickElement(this.selectors.saveButton);
    await this.waitForSuccessMessage();
  }

  /**
   * 编辑用户信息
   * @param {string} userName - 用户名称
   * @param {Object} newData - 新的用户数据
   * @returns {Promise<void>}
   */
  async editUser(userName, newData) {
    const userRow = await this.findUserRow(userName);
    await userRow.locator(this.selectors.editButton).click();
    
    await this.waitForElement(this.selectors.editDialog);
    
    if (newData.name) {
      await this.page.fill(this.selectors.nameInput, '');
      await this.fillField(this.selectors.nameInput, newData.name);
    }
    
    if (newData.email) {
      await this.page.fill(this.selectors.emailInput, '');
      await this.fillField(this.selectors.emailInput, newData.email);
    }
    
    if (newData.role) {
      await this.page.selectOption(this.selectors.roleSelect, newData.role);
    }
    
    await this.clickElement(this.selectors.saveButton);
    await this.waitForSuccessMessage();
  }

  /**
   * 删除用户
   * @param {string} userName - 用户名称
   * @returns {Promise<void>}
   */
  async deleteUser(userName) {
    const userRow = await this.findUserRow(userName);
    await userRow.locator(this.selectors.deleteButton).click();
    
    await this.waitForElement(this.selectors.deleteConfirmDialog);
    await this.clickElement(this.selectors.confirmButton);
    await this.waitForSuccessMessage();
  }

  /**
   * 查找用户行
   * @param {string} userName - 用户名称
   * @returns {Promise<import('@playwright/test').Locator>} 用户行元素
   */
  async findUserRow(userName) {
    const rows = this.page.locator(this.selectors.userRow);
    const count = await rows.count();
    
    for (let i = 0; i < count; i++) {
      const row = rows.nth(i);
      const nameElement = row.locator(this.selectors.userName);
      const text = await nameElement.textContent();
      
      if (text?.includes(userName)) {
        return row;
      }
    }
    
    throw new Error(`找不到用户: ${userName}`);
  }

  /**
   * 获取用户列表
   * @returns {Promise<Array<Object>>} 用户数据数组
   */
  async getUserList() {
    await this.waitForElement(this.selectors.userTable);
    
    const rows = this.page.locator(this.selectors.userRow);
    const count = await rows.count();
    const users = [];
    
    for (let i = 0; i < count; i++) {
      const row = rows.nth(i);
      const name = await row.locator(this.selectors.userName).textContent();
      const email = await row.locator(this.selectors.userEmail).textContent();
      const status = await row.locator(this.selectors.userStatus).textContent();
      
      users.push({
        name: name?.trim(),
        email: email?.trim(),
        status: status?.trim()
      });
    }
    
    return users;
  }

  /**
   * 等待成功消息显示
   * @returns {Promise<void>}
   */
  async waitForSuccessMessage() {
    await this.waitForElement(this.selectors.successMessage);
    
    // 等待消息消失
    await this.page.waitForSelector(this.selectors.successMessage, {
      state: 'hidden',
      timeout: 5000
    });
  }

  /**
   * 检查是否有错误消息
   * @returns {Promise<string|null>} 错误消息内容，如果没有错误则返回null
   */
  async getErrorMessage() {
    try {
      await this.page.waitForSelector(this.selectors.errorMessage, {
        state: 'visible',
        timeout: 3000
      });
      return await this.getElementText(this.selectors.errorMessage);
    } catch {
      return null;
    }
  }

  /**
   * 切换到下一页
   * @returns {Promise<void>}
   */
  async goToNextPage() {
    await this.clickElement(this.selectors.nextPageButton);
    await this.waitForTableUpdate();
  }

  /**
   * 应用过滤器
   * @param {string} filter - 过滤条件
   * @returns {Promise<void>}
   */
  async applyFilter(filter) {
    await this.page.selectOption(this.selectors.filterSelect, filter);
    await this.waitForTableUpdate();
  }
}

export default UserManagementPage;
```

## 测试用例编写

### 页面功能测试

```javascript
/**
 * 用户管理页面端到端测试
 * @description 测试用户管理页面的完整功能
 */
import { test, expect } from '@playwright/test';
import UserManagementPage from '../page-objects/UserManagementPage.js';
import { createTestUser, cleanupTestData } from '../helpers/testData.js';

test.describe('用户管理页面', () => {
  let userManagementPage;
  let testUsers = [];

  test.beforeEach(async ({ page }) => {
    userManagementPage = new UserManagementPage(page);
    
    // 创建测试数据
    testUsers = await createTestUser([
      { name: '张三', email: '<EMAIL>', role: 'user' },
      { name: '李四', email: '<EMAIL>', role: 'admin' }
    ]);
    
    await userManagementPage.navigateTo();
  });

  test.afterEach(async () => {
    // 清理测试数据
    await cleanupTestData(testUsers);
  });

  test('应该正确显示页面标题和基本元素', async () => {
    await expect(userManagementPage.page.locator('[data-testid="page-title"]'))
      .toHaveText('用户管理');
    
    await expect(userManagementPage.page.locator('[data-testid="add-user-btn"]'))
      .toBeVisible();
    
    await expect(userManagementPage.page.locator('[data-testid="search-input"]'))
      .toBeVisible();
    
    await expect(userManagementPage.page.locator('[data-testid="user-table"]'))
      .toBeVisible();
  });

  test('应该能够搜索用户', async () => {
    await userManagementPage.searchUsers('张三');
    
    const users = await userManagementPage.getUserList();
    expect(users).toHaveLength(1);
    expect(users[0].name).toContain('张三');
  });

  test('应该能够添加新用户', async () => {
    const newUser = {
      name: '王五',
      email: '<EMAIL>',
      role: 'user'
    };

    await userManagementPage.addUser(newUser);
    
    // 验证用户已添加
    await userManagementPage.searchUsers('王五');
    const users = await userManagementPage.getUserList();
    
    expect(users).toHaveLength(1);
    expect(users[0].name).toBe('王五');
    expect(users[0].email).toBe('<EMAIL>');
  });

  test('应该能够编辑用户信息', async () => {
    const updatedData = {
      name: '张三（已更新）',
      email: '<EMAIL>'
    };

    await userManagementPage.editUser('张三', updatedData);
    
    // 验证用户信息已更新
    await userManagementPage.searchUsers('张三（已更新）');
    const users = await userManagementPage.getUserList();
    
    expect(users).toHaveLength(1);
    expect(users[0].name).toBe('张三（已更新）');
    expect(users[0].email).toBe('<EMAIL>');
  });

  test('应该能够删除用户', async () => {
    await userManagementPage.deleteUser('张三');
    
    // 验证用户已被删除
    await userManagementPage.searchUsers('张三');
    const users = await userManagementPage.getUserList();
    
    expect(users).toHaveLength(0);
  });

  test('应该正确处理验证错误', async () => {
    const invalidUser = {
      name: '',
      email: 'invalid-email',
      role: 'user'
    };

    await userManagementPage.addUser(invalidUser);
    
    const errorMessage = await userManagementPage.getErrorMessage();
    expect(errorMessage).toBeTruthy();
    expect(errorMessage).toContain('请填写用户姓名');
  });

  test('应该支持分页功能', async () => {
    // 如果有多页数据，测试分页
    const isNextPageVisible = await userManagementPage.isElementVisible(
      '[data-testid="next-page"]'
    );
    
    if (isNextPageVisible) {
      const firstPageUsers = await userManagementPage.getUserList();
      
      await userManagementPage.goToNextPage();
      
      const secondPageUsers = await userManagementPage.getUserList();
      expect(secondPageUsers).not.toEqual(firstPageUsers);
    }
  });
});
```

### 业务流程测试

```javascript
/**
 * 用户管理完整流程测试
 * @description 测试从登录到用户管理的完整业务流程
 */
import { test, expect } from '@playwright/test';
import LoginPage from '../page-objects/LoginPage.js';
import UserManagementPage from '../page-objects/UserManagementPage.js';
import { createTestAdmin } from '../helpers/testData.js';

test.describe('用户管理业务流程', () => {
  let loginPage;
  let userManagementPage;
  let testAdmin;

  test.beforeAll(async () => {
    // 创建测试管理员账户
    testAdmin = await createTestAdmin({
      username: 'admin-test',
      password: 'test123456',
      email: '<EMAIL>'
    });
  });

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    userManagementPage = new UserManagementPage(page);
  });

  test('完整的用户管理流程', async () => {
    // 1. 登录系统
    await loginPage.navigateTo();
    await loginPage.login(testAdmin.username, testAdmin.password);
    
    // 验证登录成功
    await expect(loginPage.page).toHaveURL(/\/dashboard/);
    
    // 2. 导航到用户管理页面
    await userManagementPage.navigateTo();
    
    // 3. 创建新用户
    const newUser = {
      name: '测试用户001',
      email: '<EMAIL>',
      role: 'user'
    };
    
    await userManagementPage.addUser(newUser);
    
    // 4. 验证用户创建成功
    await userManagementPage.searchUsers('测试用户001');
    let users = await userManagementPage.getUserList();
    expect(users).toHaveLength(1);
    expect(users[0].name).toBe('测试用户001');
    
    // 5. 编辑用户信息
    await userManagementPage.editUser('测试用户001', {
      name: '测试用户001（已编辑）',
      role: 'admin'
    });
    
    // 6. 验证编辑成功
    await userManagementPage.searchUsers('测试用户001（已编辑）');
    users = await userManagementPage.getUserList();
    expect(users[0].name).toBe('测试用户001（已编辑）');
    
    // 7. 删除用户
    await userManagementPage.deleteUser('测试用户001（已编辑）');
    
    // 8. 验证删除成功
    await userManagementPage.searchUsers('测试用户001');
    users = await userManagementPage.getUserList();
    expect(users).toHaveLength(0);
  });

  test('批量操作流程', async () => {
    // 登录
    await loginPage.navigateTo();
    await loginPage.login(testAdmin.username, testAdmin.password);
    await userManagementPage.navigateTo();
    
    // 批量创建用户
    const users = [
      { name: '批量用户1', email: '<EMAIL>', role: 'user' },
      { name: '批量用户2', email: '<EMAIL>', role: 'user' },
      { name: '批量用户3', email: '<EMAIL>', role: 'admin' }
    ];
    
    for (const user of users) {
      await userManagementPage.addUser(user);
    }
    
    // 验证所有用户都已创建
    for (const user of users) {
      await userManagementPage.searchUsers(user.name);
      const foundUsers = await userManagementPage.getUserList();
      expect(foundUsers).toHaveLength(1);
      expect(foundUsers[0].name).toBe(user.name);
    }
    
    // 清理测试数据
    for (const user of users) {
      await userManagementPage.deleteUser(user.name);
    }
  });
});
```

## 测试数据管理

### 测试数据工厂

```javascript
// tests/e2e/helpers/testData.js
/**
 * 测试数据管理工具
 * @description 提供测试数据的创建、管理和清理功能
 */

/**
 * 创建测试用户
 * @param {Array<Object>|Object} userData - 用户数据或用户数据数组
 * @returns {Promise<Array<Object>>} 创建的用户数组
 */
export const createTestUser = async (userData) => {
  const users = Array.isArray(userData) ? userData : [userData];
  const createdUsers = [];
  
  for (const user of users) {
    const testUser = {
      id: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: user.name,
      email: user.email,
      role: user.role || 'user',
      isTestData: true,
      createdAt: new Date().toISOString()
    };
    
    // 调用API创建用户（这里需要根据实际API调整）
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });
    
    if (response.ok) {
      const createdUser = await response.json();
      createdUsers.push(createdUser);
    }
  }
  
  return createdUsers;
};

/**
 * 创建测试管理员
 * @param {Object} adminData - 管理员数据
 * @returns {Promise<Object>} 创建的管理员对象
 */
export const createTestAdmin = async (adminData) => {
  const admin = {
    id: `admin-test-${Date.now()}`,
    username: adminData.username,
    password: adminData.password,
    email: adminData.email,
    role: 'admin',
    isTestData: true,
    createdAt: new Date().toISOString()
  };
  
  // 调用API创建管理员
  const response = await fetch('/api/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(admin)
  });
  
  if (response.ok) {
    return await response.json();
  }
  
  throw new Error('创建测试管理员失败');
};

/**
 * 清理测试数据
 * @param {Array<Object>} testUsers - 要清理的测试用户数组
 * @returns {Promise<void>}
 */
export const cleanupTestData = async (testUsers) => {
  for (const user of testUsers) {
    try {
      await fetch(`/api/users/${user.id}`, {
        method: 'DELETE'
      });
    } catch (error) {
      console.warn(`清理测试用户失败: ${user.id}`, error);
    }
  }
};

/**
 * 生成随机测试数据
 * @param {string} type - 数据类型 ('user', 'email', 'name')
 * @returns {string} 生成的测试数据
 */
export const generateTestData = (type) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 5);
  
  switch (type) {
    case 'user':
      return {
        name: `测试用户-${random}`,
        email: `test-${random}@example.com`,
        role: 'user'
      };
    case 'email':
      return `test-${random}-${timestamp}@example.com`;
    case 'name':
      return `测试姓名-${random}`;
    default:
      return `test-${random}`;
  }
};
```

## API Mock 和测试环境

### API Mock 配置

```javascript
// tests/e2e/helpers/apiMock.js
/**
 * API Mock 工具
 * @description 为E2E测试提供可控的API响应
 */

/**
 * 设置用户API的Mock响应
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 * @param {Object} options - Mock选项
 * @returns {Promise<void>}
 */
export const mockUserApi = async (page, options = {}) => {
  const {
    users = [],
    delay = 0,
    shouldFail = false
  } = options;

  // Mock获取用户列表API
  await page.route('**/api/users**', async (route) => {
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    if (shouldFail) {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal Server Error',
          message: '服务器内部错误'
        })
      });
      return;
    }

    const url = new URL(route.request().url());
    const searchParams = url.searchParams;
    const keyword = searchParams.get('search');
    const role = searchParams.get('role');
    
    let filteredUsers = [...users];
    
    // 应用搜索过滤
    if (keyword) {
      filteredUsers = filteredUsers.filter(user =>
        user.name.includes(keyword) || user.email.includes(keyword)
      );
    }
    
    // 应用角色过滤
    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }

    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        users: filteredUsers,
        total: filteredUsers.length,
        page: 1,
        pageSize: 20
      })
    });
  });

  // Mock创建用户API
  await page.route('**/api/users', async (route) => {
    if (route.request().method() === 'POST') {
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      if (shouldFail) {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Validation Error',
            message: '用户数据验证失败'
          })
        });
        return;
      }

      const userData = await route.request().postDataJSON();
      const newUser = {
        id: `user-${Date.now()}`,
        ...userData,
        createdAt: new Date().toISOString()
      };

      await route.fulfill({
        status: 201,
        contentType: 'application/json',
        body: JSON.stringify(newUser)
      });
    }
  });
};

/**
 * 重置所有API Mock
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 * @returns {Promise<void>}
 */
export const resetApiMocks = async (page) => {
  await page.unrouteAll();
};
```

## 测试配置和最佳实践

### Playwright 配置

```javascript
// playwright.config.js
/**
 * Playwright 端到端测试配置
 */
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  // 测试目录
  testDir: './tests/e2e',
  
  // 全局超时设置
  timeout: 30000,
  expect: {
    timeout: 10000
  },
  
  // 失败重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并发工作进程数
  workers: process.env.CI ? 1 : undefined,
  
  // 测试报告
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }]
  ],
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: process.env.BASE_URL || 'http://localhost:8080',
    
    // 浏览器设置
    headless: process.env.CI ? true : false,
    viewport: { width: 1280, height: 720 },
    
    // 截图和视频
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 网络设置
    ignoreHTTPSErrors: true,
    
    // 操作延迟
    actionTimeout: 10000,
    navigationTimeout: 30000
  },

  // 测试项目配置（不同浏览器）
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] }
    }
  ],

  // 本地开发服务器
  webServer: process.env.CI ? undefined : {
    command: 'npm run serve',
    port: 8080,
    reuseExistingServer: !process.env.CI
  }
});
```

### 测试最佳实践

1. **使用有意义的测试标识符**
```javascript
// ✅ 推荐：使用 data-testid
<button data-testid="submit-btn">提交</button>

// ❌ 避免：依赖CSS类或文本
<button class="btn btn-primary">提交</button>
```

2. **合理使用等待**
```javascript
// ✅ 推荐：等待特定状态
await page.waitForSelector('[data-testid="user-table"]', { 
  state: 'visible' 
});

// ❌ 避免：固定延迟
await page.waitForTimeout(3000);
```

3. **独立的测试用例**
```javascript
// ✅ 推荐：每个测试都独立设置数据
test.beforeEach(async () => {
  await createTestData();
});

test.afterEach(async () => {
  await cleanupTestData();
});
```

4. **有效的错误处理**
```javascript
// ✅ 推荐：捕获和记录错误
test('用户创建流程', async ({ page }) => {
  try {
    await userPage.addUser(userData);
  } catch (error) {
    await page.screenshot({ path: 'error-screenshot.png' });
    throw error;
  }
});
```

5. **性能考虑**
```javascript
// ✅ 推荐：并行执行独立测试
test.describe.parallel('独立的功能测试', () => {
  test('测试1', async ({ page }) => {
    // 独立测试逻辑
  });
  
  test('测试2', async ({ page }) => {
    // 独立测试逻辑
  });
});
``` 