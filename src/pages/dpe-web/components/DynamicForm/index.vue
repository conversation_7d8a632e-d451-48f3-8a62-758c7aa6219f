<template>
  <div class="dynamic-form">
    <a-form 
      ref="form"
      :model="formData"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :colon="false"
    >
      <a-form-item
        v-for="field in configFields"
        :key="field.key"
        :label="getFieldLabel(field)"
        :required="isFieldRequired(field)"
        :rules="getFieldRules(field)"
        :prop="field.key"
      >
        <component
          :is="getFieldComponent(field)"
          v-bind="getFieldProps(field)"
          :value="formData[field.key]"
          :disabled="readonly"
          @change="handleFieldChange(field.key, $event)"
          @input="handleFieldInput(field.key, $event)"
        />
        <div v-if="field.schema.description" class="field-description">
          {{ field.schema.description }}
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script>
import DynamicArrayInput from './components/ArrayInput.vue';
import DynamicObjectInput from './components/ObjectInput.vue';
import StringInput from './components/StringInput.vue'; // 导入新创建的 StringInput 组件

/**
 * 动态表单组件
 * 根据ConfigField列表基于json-schema渲染动态表单
 * 支持字符串、数字、布尔值、对象、数组等各种数据类型的表单控件
 */
export default {
  name: 'DynamicForm',
  components: {
    DynamicArrayInput,
    DynamicObjectInput,
    StringInput // 注册 StringInput 组件
  },
  props: {
    // ConfigField数组，定义表单结构
    configFields: {
      type: Array,
      required: true,
      default: function() {
        return [];
      }
    },
    // 表单初始值
    value: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 是否只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 表单布局配置
    layout: {
      type: Object,
      default: function() {
        return {
          labelCol: { span: 6 },
          wrapperCol: { span: 18 }
        };
      }
    }
  },

  data: function() {
    return {
      formData: {}
    };
  },

  watch: {
    value: {
      handler: function(newValue) {
        this.formData = { ...newValue };
      },
      immediate: true,
      deep: true
    },
    
    configFields: {
      handler: function() {
        this.initializeFormData();
      },
      immediate: true
    }
  },

  methods: {
    /**
     * 初始化表单数据
     */
    initializeFormData: function() {
      var self = this;
      var initialData = { ...this.value };
      
      this.configFields.forEach(function(field) {
        if (!(field.key in initialData)) {
          initialData[field.key] = self.getDefaultValue(field);
        }
      });
      
      this.formData = initialData;
      this.$emit('input', this.formData);
    },

    /**
     * 获取字段默认值
     * @param {Object} field - 配置字段
     * @returns {any} 默认值
     */
    getDefaultValue: function(field) {
      var schema = field.schema;
      
      if (schema.default !== undefined) {
        return schema.default;
      }
      
      switch (schema.type) {
        case 'string':
          return '';
        case 'number':
        case 'integer':
          return schema.minimum || 0;
        case 'boolean':
          return false;
        case 'array':
          return [];
        case 'object':
          return {};
        default:
          return null;
      }
    },

    /**
     * 获取字段标签
     * @param {Object} field - 配置字段
     * @returns {string} 字段标签
     */
    getFieldLabel: function(field) {
      return field.schema.title || field.key;
    },

    /**
     * 判断字段是否必填
     * @param {Object} field - 配置字段
     * @returns {boolean} 是否必填
     */
    isFieldRequired: function(field) {
      return field.schema.required === true;
    },

    /**
     * 获取字段验证规则
     * @param {Object} field - 配置字段
     * @returns {Array} 验证规则数组
     */
    getFieldRules: function(field) {
      var rules = [];
      var schema = field.schema;

      // 必填验证
      if (this.isFieldRequired(field)) {
        rules.push({
          required: true,
          message: '请输入' + this.getFieldLabel(field)
        });
      }

      // 字符串类型验证
      if (schema.type === 'string') {
        if (schema.minLength) {
          rules.push({
            min: schema.minLength,
            message: '最少输入' + schema.minLength + '个字符'
          });
        }
        if (schema.maxLength) {
          rules.push({
            max: schema.maxLength,
            message: '最多输入' + schema.maxLength + '个字符'
          });
        }
        if (schema.pattern) {
          rules.push({
            pattern: new RegExp(schema.pattern),
            message: '格式不正确'
          });
        }
      }

      // 数值类型验证
      if (schema.type === 'number' || schema.type === 'integer') {
        if (schema.minimum !== undefined) {
          rules.push({
            type: 'number',
            min: schema.minimum,
            message: '不能小于' + schema.minimum
          });
        }
        if (schema.maximum !== undefined) {
          rules.push({
            type: 'number',
            max: schema.maximum,
            message: '不能大于' + schema.maximum
          });
        }
      }

      return rules;
    },

    /**
     * 获取字段组件类型
     * @param {Object} field - 配置字段
     * @returns {string} 组件类型
     */
    getFieldComponent: function(field) {
      var schema = field.schema;
      
      switch (schema.type) {
        case 'string':
          // 所有的字符串类型都交由 StringInput 内部处理
          return 'StringInput';
          
        case 'number':
        case 'integer':
          return 'a-input-number';
          
        case 'boolean':
          return 'a-switch';
          
        case 'array':
          return 'DynamicArrayInput';
          
        case 'object':
          return 'DynamicObjectInput';
          
        default:
          return 'StringInput'; // 将默认返回类型改为 StringInput
      }
    },

    /**
     * 获取字段组件属性
     * @param {Object} field - 配置字段
     * @returns {Object} 组件属性
     */
    getFieldProps: function(field) {
      var schema = field.schema;
      var props = {
        placeholder: schema.placeholder || '请输入' + this.getFieldLabel(field)
      };

      switch (schema.type) {
        case 'string':
          // 将 format 和 options 传递给 StringInput
          props.format = schema.format;
          if (schema.enum) {
            props.options = schema.enum.map(function(item) {
              return { label: item, value: item };
            });
          }
          if (schema.format === 'textarea') {
            props.rows = schema.rows || 4;
          }
          break;
          
        case 'number':
        case 'integer':
          if (schema.minimum !== undefined) {
            props.min = schema.minimum;
          }
          if (schema.maximum !== undefined) {
            props.max = schema.maximum;
          }
          if (schema.type === 'integer') {
            props.precision = 0;
          }
          break;
          
        case 'boolean':
          props.checkedChildren = '是';
          props.unCheckedChildren = '否';
          break;
          
        case 'array':
          props.itemSchema = schema.items || { type: 'string' };
          props.minItems = schema.minItems;
          props.maxItems = schema.maxItems;
          break;
          
        case 'object':
          props.objectSchema = schema;
          break;
      }

      return props;
    },

    /**
     * 处理字段值变化
     * @param {string} key - 字段key
     * @param {any} value - 新值
     */
    handleFieldChange: function(key, value) {
      this.updateValue(key, value);
    },

    /**
     * 处理字段输入
     * @param {string} key - 字段key  
     * @param {any} value - 新值
     */
    handleFieldInput: function(key, value) {
      // 对于input事件，直接处理事件对象
      if (value && value.target) {
        this.updateValue(key, value.target.value);
      } else {
        this.updateValue(key, value);
      }
    },

    /**
     * 更新表单数据并触发change事件
     * @param {string} key - 字段key
     * @param {any} value - 新值
     */
    updateValue: function(key, value) {
      this.$set(this.formData, key, value);
      this.$emit('input', { ...this.formData });
      this.$emit('change', { 
        key: key, 
        value: value, 
        formData: this.formData 
      });
    },

    /**
     * 验证表单数据
     * @returns {Promise<boolean>} 验证结果
     */
    validateForm: function() {
      var self = this;
      return new Promise(function(resolve) {
        // 检查是否有form引用和validate方法
        if (!self.$refs.form) {
          console.warn('DynamicForm: form ref not found');
          resolve(true);
          return;
        }
        
        // 检查不同版本的验证方法
        if (typeof self.$refs.form.validate === 'function') {
          self.$refs.form.validate(function(valid) {
            resolve(valid);
          });
        } else if (typeof self.$refs.form.validateFields === 'function') {
          self.$refs.form.validateFields(function(err) {
            resolve(!err);
          });
        } else {
          // 如果没有验证方法，直接返回true
          console.warn('DynamicForm: no validation method found, assuming valid');
          resolve(true);
        }
      });
    },

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData: function() {
      return { ...this.formData };
    },

    /**
     * 重置表单
     */
    resetForm: function() {
      this.$refs.form.resetFields();
      this.initializeFormData();
    },

    /**
     * 清空表单验证状态
     */
    clearValidate: function() {
      try {
        if (this.$refs.form && typeof this.$refs.form.clearValidate === 'function') {
          this.$refs.form.clearValidate();
        } else if (this.$refs.form && typeof this.$refs.form.resetFields === 'function') {
          this.$refs.form.resetFields();
        }
      } catch (error) {
        console.warn('DynamicForm: Could not clear form validation:', error);
      }
    }
  }
};
</script>

<style scoped>
.dynamic-form {
  padding: 16px;
}

.field-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.dynamic-form .ant-form-item {
  margin-bottom: 16px;
}

.dynamic-form .ant-form-item-label {
  font-weight: 500;
}
</style> 