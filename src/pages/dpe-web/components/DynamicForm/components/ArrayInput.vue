<template>
  <div class="dynamic-array-input">
    <div v-if="items.length === 0" class="empty-placeholder">
      <a-empty :image="false" description="暂无数据">
        <a-button type="dashed" @click="addItem" :disabled="disabled">
          <a-icon type="plus" />
          添加项目
        </a-button>
      </a-empty>
    </div>
    
    <div v-else>
      <div 
        v-for="(item, index) in items" 
        :key="index"
        class="array-item"
      >
        <div class="item-content">
          <component
            :is="getItemComponent()"
            v-bind="getItemProps()"
            :value="item"
            :disabled="disabled"
            @change="handleItemChange(index, $event)"
            @input="handleItemInput(index, $event)"
          />
        </div>
        <div class="item-actions">
          <a-button 
            size="small" 
            type="danger" 
            icon="delete"
            :disabled="disabled"
            @click="removeItem(index)"
            title="删除"
          />
        </div>
      </div>
      
      <div class="add-button-wrapper">
        <a-button 
          type="dashed" 
          block 
          :disabled="disabled"
          @click="addItem"
        >
          <a-icon type="plus" />
          添加项目
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 动态数组输入组件
 * 支持添加、删除数组项，每个数组项根据schema类型渲染对应的输入控件
 */
export default {
  name: 'DynamicArrayInput',
  props: {
    // 数组值
    value: {
      type: Array,
      default: function() {
        return [];
      }
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 数组项的schema定义
    itemSchema: {
      type: Object,
      default: function() {
        return { type: 'string' };
      }
    },
    // 最小项数
    minItems: {
      type: Number,
      default: 0
    },
    // 最大项数
    maxItems: {
      type: Number,
      default: null
    }
  },

  data: function() {
    return {
      items: []
    };
  },

  watch: {
    value: {
      handler: function(newValue) {
        this.items = Array.isArray(newValue) ? [...newValue] : [];
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    /**
     * 获取数组项组件类型
     * @returns {string} 组件类型
     */
    getItemComponent: function() {
      var schema = this.itemSchema;
      
      switch (schema.type) {
        case 'string':
          if (schema.enum) {
            return 'a-select';
          } else if (schema.format === 'textarea') {
            return 'a-textarea';
          }
          return 'a-input';
          
        case 'number':
        case 'integer':
          return 'a-input-number';
          
        case 'boolean':
          return 'a-switch';
          
        default:
          return 'a-input';
      }
    },

    /**
     * 获取数组项组件属性
     * @returns {Object} 组件属性
     */
    getItemProps: function() {
      var schema = this.itemSchema;
      var props = {
        placeholder: schema.placeholder || '请输入值'
      };

      switch (schema.type) {
        case 'string':
          if (schema.enum) {
            props.options = schema.enum.map(function(item) {
              return { label: item, value: item };
            });
          }
          if (schema.format === 'textarea') {
            props.rows = schema.rows || 3;
          }
          break;
          
        case 'number':
        case 'integer':
          if (schema.minimum !== undefined) {
            props.min = schema.minimum;
          }
          if (schema.maximum !== undefined) {
            props.max = schema.maximum;
          }
          if (schema.type === 'integer') {
            props.precision = 0;
          }
          break;
          
        case 'boolean':
          props.checkedChildren = '是';
          props.unCheckedChildren = '否';
          break;
      }

      return props;
    },

    /**
     * 获取默认项值
     * @returns {any} 默认值
     */
    getDefaultItemValue: function() {
      var schema = this.itemSchema;
      
      if (schema.default !== undefined) {
        return schema.default;
      }
      
      switch (schema.type) {
        case 'string':
          return '';
        case 'number':
        case 'integer':
          return schema.minimum || 0;
        case 'boolean':
          return false;
        case 'array':
          return [];
        case 'object':
          return {};
        default:
          return null;
      }
    },

    /**
     * 添加数组项
     */
    addItem: function() {
      if (this.maxItems && this.items.length >= this.maxItems) {
        this.$message.warning('已达到最大项数限制');
        return;
      }
      
      var newItem = this.getDefaultItemValue();
      this.items.push(newItem);
      this.emitChange();
    },

    /**
     * 删除数组项
     * @param {number} index - 项索引
     */
    removeItem: function(index) {
      if (this.items.length <= this.minItems) {
        this.$message.warning('不能删除更多项目');
        return;
      }
      
      this.items.splice(index, 1);
      this.emitChange();
    },

    /**
     * 处理数组项值变化
     * @param {number} index - 项索引
     * @param {any} value - 新值
     */
    handleItemChange: function(index, value) {
      this.$set(this.items, index, value);
      this.emitChange();
    },

    /**
     * 处理数组项输入
     * @param {number} index - 项索引
     * @param {any} value - 新值
     */
    handleItemInput: function(index, value) {
      // 对于input事件，处理事件对象
      if (value && value.target) {
        this.$set(this.items, index, value.target.value);
      } else {
        this.$set(this.items, index, value);
      }
      this.emitChange();
    },

    /**
     * 触发变化事件
     */
    emitChange: function() {
      var newValue = [...this.items];
      this.$emit('input', newValue);
      this.$emit('change', newValue);
    }
  }
};
</script>

<style scoped>
.dynamic-array-input {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.array-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.array-item:last-of-type {
  margin-bottom: 12px;
}

.item-content {
  flex: 1;
  margin-right: 8px;
}

.item-actions {
  flex-shrink: 0;
}

.add-button-wrapper {
  margin-top: 8px;
}

.empty-placeholder {
  text-align: center;
  padding: 20px;
}

.empty-placeholder .ant-empty {
  margin: 0;
}
</style> 