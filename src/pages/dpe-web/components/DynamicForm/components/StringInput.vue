<template>
  <component
    :is="componentType"
    v-bind="componentProps"
    @input="handleEvent"
    @change="handleEvent"
  />
</template>

<script>
/**
 * StringInput 组件
 * @description 封装 Ant Design Vue 的 `a-input` 系列组件（包括 `a-input-password`, `a-textarea`, `a-select`），
 *              并处理 `value` prop 的类型适配，特别是将布尔值转换为字符串，以避免 Vue prop 类型警告。
 * @component StringInput
 * @example
 * // 普通文本输入
 * <StringInput v-model="myStringValue" />
 * 
 * // 密码输入
 * <StringInput v-model="myPassword" format="password" />
 * 
 * // 多行文本输入
 * <StringInput v-model="myTextArea" format="textarea" :rows="5" />
 * 
 * // 下拉选择
 * <StringInput v-model="mySelection" :options="[{ label: 'Option A', value: 'A' }]" />
 * @since 1.0.0
 */
export default {
  name: 'StringInput',
  /**
   * 组件属性定义
   * @property {string | number | boolean} value - 输入框的值，支持字符串、数字和布尔值。
   *                                              布尔值会被转换为字符串。
   * @property {string} [format=''] - 字符串的格式，可选值为 'password', 'textarea'。
   * @property {Array<Object>} [options=[]] - 当需要渲染为 `a-select` 时的选项列表。
   *                                          每个选项应包含 `label` 和 `value` 属性。
   */
  props: {
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    format: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    /**
     * 内部值计算属性
     * @computed internalValue
     * @description 将传入的 `value` 转换为适合内部组件的字符串格式。
     *              如果原始值是布尔值，则将其转换为字符串。
     * @returns {string}
     */
    internalValue() {
      if (typeof this.value === 'boolean') {
        return String(this.value);
      } else if (this.value === null || this.value === undefined) {
        return '';
      }
      return this.value;
    },

    /**
     * 动态渲染的组件类型
     * @computed componentType
     * @returns {string} Ant Design Vue 组件名称
     */
    componentType() {
      if (this.options && this.options.length > 0) {
        return 'a-select';
      }
      switch (this.format) {
        case 'textarea':
          return 'a-textarea';
        case 'password':
          return 'a-input-password';
        default:
          return 'a-input';
      }
    },

    /**
     * 传递给动态组件的属性
     * @computed componentProps
     * @returns {Object} 属性对象
     */
    componentProps() {
      const props = {
        ...this.$attrs,
        value: this.internalValue
      };

      if (this.format === 'textarea') {
        props.rows = props.rows || 4; // 默认行数
      }
      if (this.options && this.options.length > 0) {
        props.options = this.options; // 传递选项给 a-select
      }
      return props;
    }
  },
  methods: {
    /**
     * 通用事件处理函数，适配不同 Ant Design Vue 组件的事件模型
     * @method handleEvent
     * @private
     * @param {Event|string|number} valueOrEvent - 输入事件对象或直接的值
     * @fires input
     */
    handleEvent(valueOrEvent) {
      let emittedValue;
      // 判断是原生 input 事件对象还是直接的值（如 a-select 的 change 事件）
      if (valueOrEvent && typeof valueOrEvent === 'object' && 'target' in valueOrEvent) {
        emittedValue = valueOrEvent.target.value;
      } else {
        emittedValue = valueOrEvent;
      }
      /**
       * 输入事件，用于 `v-model` 绑定。始终发出字符串类型的值。
       * @event input
       * @type {string}
       */
      this.$emit('input', emittedValue);
    }
  }
};
</script> 