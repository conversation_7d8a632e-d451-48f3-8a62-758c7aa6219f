<template>
  <div class="dynamic-object-input">
    <div class="object-input-header">
      <a-radio-group 
        v-model="editMode" 
        size="small"
        :disabled="disabled"
        @change="handleModeChange"
      >
        <a-radio-button value="form">结构化编辑</a-radio-button>
        <a-radio-button value="json">JSON编辑</a-radio-button>
      </a-radio-group>
    </div>

    <div class="object-input-content">
      <!-- 结构化编辑模式 -->
      <div v-if="editMode === 'form'" class="form-mode">
        <div v-if="hasProperties" class="properties-form">
          <a-form 
            ref="propertiesForm"
            :model="formData"
            layout="vertical"
            :colon="false"
          >
            <a-form-item
              v-for="(propSchema, propKey) in properties"
              :key="propKey"
              :label="getPropertyLabel(propKey, propSchema)"
              :required="isPropertyRequired(propKey)"
              :prop="propKey"
            >
              <component
                :is="getPropertyComponent(propSchema)"
                v-bind="getPropertyProps(propSchema)"
                :value="formData[propKey]"
                :disabled="disabled"
                @change="handlePropertyChange(propKey, $event)"
                @input="handlePropertyInput(propKey, $event)"
              />
              <div v-if="propSchema.description" class="property-description">
                {{ propSchema.description }}
              </div>
            </a-form-item>
          </a-form>
        </div>
        <div v-else class="no-properties">
          <a-empty 
            :image="false" 
            description="对象无预定义属性，请使用JSON编辑模式"
          />
        </div>
      </div>

      <!-- JSON编辑模式 -->
      <div v-else class="json-mode">
        <a-textarea
          ref="jsonEditor"
          v-model="jsonValue"
          :disabled="disabled"
          :rows="jsonRows"
          placeholder="请输入JSON格式的对象"
          @blur="handleJsonBlur"
          @change="handleJsonChange"
        />
        <div v-if="jsonError" class="json-error">
          <a-icon type="exclamation-circle" />
          {{ jsonError }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 动态对象输入组件
 * 支持结构化编辑和JSON编辑两种模式
 */
export default {
  name: 'DynamicObjectInput',
  props: {
    // 对象值
    value: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 对象schema定义
    objectSchema: {
      type: Object,
      default: function() {
        return {};
      }
    },
    // JSON编辑器行数
    jsonRows: {
      type: Number,
      default: 6
    }
  },

  data: function() {
    return {
      editMode: 'form', // form | json
      formData: {},
      jsonValue: '',
      jsonError: null
    };
  },

  computed: {
    /**
     * 对象的属性定义
     * @returns {Object} 属性定义
     */
    properties: function() {
      return this.objectSchema.properties || {};
    },

    /**
     * 是否有预定义属性
     * @returns {boolean} 是否有属性
     */
    hasProperties: function() {
      return Object.keys(this.properties).length > 0;
    },

    /**
     * 必填属性列表
     * @returns {Array} 必填属性列表
     */
    requiredProperties: function() {
      return this.objectSchema.required || [];
    }
  },

  watch: {
    value: {
      handler: function(newValue) {
        this.syncFromValue(newValue);
      },
      immediate: true,
      deep: true
    },

    hasProperties: {
      handler: function(hasProps) {
        // 如果没有预定义属性，自动切换到JSON模式
        if (!hasProps && this.editMode === 'form') {
          this.editMode = 'json';
        }
      },
      immediate: true
    }
  },

  methods: {
    /**
     * 从value同步到内部状态
     * @param {Object} value - 对象值
     */
    syncFromValue: function(value) {
      var objValue = value || {};
      
      // 更新表单数据
      this.formData = { ...objValue };
      
      // 更新JSON值
      try {
        this.jsonValue = JSON.stringify(objValue, null, 2);
        this.jsonError = null;
      } catch (error) {
        this.jsonValue = '{}';
        this.jsonError = 'JSON格式错误';
      }
    },

    /**
     * 获取属性标签
     * @param {string} propKey - 属性键
     * @param {Object} propSchema - 属性schema
     * @returns {string} 属性标签
     */
    getPropertyLabel: function(propKey, propSchema) {
      return propSchema.title || propKey;
    },

    /**
     * 判断属性是否必填
     * @param {string} propKey - 属性键
     * @returns {boolean} 是否必填
     */
    isPropertyRequired: function(propKey) {
      return this.requiredProperties.indexOf(propKey) !== -1;
    },

    /**
     * 获取属性组件类型
     * @param {Object} propSchema - 属性schema
     * @returns {string} 组件类型
     */
    getPropertyComponent: function(propSchema) {
      switch (propSchema.type) {
        case 'string':
          if (propSchema.enum) {
            return 'a-select';
          } else if (propSchema.format === 'textarea') {
            return 'a-textarea';
          }
          return 'a-input';
          
        case 'number':
        case 'integer':
          return 'a-input-number';
          
        case 'boolean':
          return 'a-switch';
          
        default:
          return 'a-input';
      }
    },

    /**
     * 获取属性组件属性
     * @param {Object} propSchema - 属性schema
     * @returns {Object} 组件属性
     */
    getPropertyProps: function(propSchema) {
      var props = {
        placeholder: propSchema.placeholder || '请输入值'
      };

      switch (propSchema.type) {
        case 'string':
          if (propSchema.enum) {
            props.options = propSchema.enum.map(function(item) {
              return { label: item, value: item };
            });
          }
          if (propSchema.format === 'textarea') {
            props.rows = propSchema.rows || 3;
          }
          break;
          
        case 'number':
        case 'integer':
          if (propSchema.minimum !== undefined) {
            props.min = propSchema.minimum;
          }
          if (propSchema.maximum !== undefined) {
            props.max = propSchema.maximum;
          }
          if (propSchema.type === 'integer') {
            props.precision = 0;
          }
          break;
          
        case 'boolean':
          props.checkedChildren = '是';
          props.unCheckedChildren = '否';
          break;
      }

      return props;
    },

    /**
     * 处理编辑模式变化
     */
    handleModeChange: function() {
      if (this.editMode === 'json') {
        // 切换到JSON模式时，同步当前表单数据到JSON
        this.syncFormToJson();
      } else {
        // 切换到表单模式时，同步JSON到表单
        this.syncJsonToForm();
      }
    },

    /**
     * 同步表单数据到JSON
     */
    syncFormToJson: function() {
      try {
        this.jsonValue = JSON.stringify(this.formData, null, 2);
        this.jsonError = null;
      } catch (error) {
        this.jsonError = 'JSON序列化失败';
      }
    },

    /**
     * 同步JSON到表单数据
     */
    syncJsonToForm: function() {
      try {
        var parsedValue = JSON.parse(this.jsonValue || '{}');
        this.formData = { ...parsedValue };
        this.jsonError = null;
        this.emitChange();
      } catch (error) {
        this.jsonError = 'JSON格式错误: ' + error.message;
      }
    },

    /**
     * 处理属性值变化
     * @param {string} propKey - 属性键
     * @param {any} value - 新值
     */
    handlePropertyChange: function(propKey, value) {
      this.$set(this.formData, propKey, value);
      this.emitChange();
    },

    /**
     * 处理属性输入
     * @param {string} propKey - 属性键
     * @param {any} value - 新值
     */
    handlePropertyInput: function(propKey, value) {
      if (value && value.target) {
        this.$set(this.formData, propKey, value.target.value);
      } else {
        this.$set(this.formData, propKey, value);
      }
      this.emitChange();
    },

    /**
     * 处理JSON失焦
     */
    handleJsonBlur: function() {
      this.syncJsonToForm();
    },

    /**
     * 处理JSON变化
     */
    handleJsonChange: function() {
      // 实时检查JSON格式
      try {
        JSON.parse(this.jsonValue || '{}');
        this.jsonError = null;
      } catch (error) {
        this.jsonError = 'JSON格式错误';
      }
    },

    /**
     * 触发变化事件
     */
    emitChange: function() {
      var newValue;
      
      if (this.editMode === 'json') {
        try {
          newValue = JSON.parse(this.jsonValue || '{}');
        } catch (error) {
          // JSON格式错误时不触发变化
          return;
        }
      } else {
        newValue = { ...this.formData };
      }
      
      this.$emit('input', newValue);
      this.$emit('change', newValue);
    }
  }
};
</script>

<style scoped>
.dynamic-object-input {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.object-input-header {
  padding: 8px 12px;
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.object-input-content {
  padding: 12px;
}

.properties-form .ant-form-item {
  margin-bottom: 16px;
}

.property-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.no-properties {
  text-align: center;
  padding: 20px;
}

.json-mode {
  position: relative;
}

.json-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.4;
}

.json-error .anticon {
  margin-right: 4px;
}
</style> 