<template>
  <div class="operator-panel" data-testid="operator-panel">
    <!-- 搜索和分类区域 -->
    <div class="panel-header">
      <!-- 搜索框 -->
      <a-input
        v-model="internalSearchKeyword"
        placeholder="搜索算子..."
        class="search-input"
        data-testid="search-input"
        @input="handleSearchChange"
      >
        <a-icon slot="prefix" type="search" />
      </a-input>
      
      <!-- 分类选择 -->
      <a-select
        v-model="internalSelectedCategory"
        class="category-select"
        data-testid="category-select"
        @change="handleCategoryChange"
      >
        <a-select-option value="all" data-testid="category-all">全部</a-select-option>
        <a-select-option
          v-for="category in availableCategories"
          :key="category.key"
          :value="category.key"
          :data-testid="`category-${category.key}`"
        >
          {{ category.name }}
        </a-select-option>
      </a-select>
    </div>

    <!-- 数据集按钮 -->
    <div class="dataset-section">
      <a-button
        type="primary"
        block
        class="add-dataset-btn"
        data-testid="add-dataset-btn"
        @click="handleAddDataset"
      >
        <a-icon type="plus" />
        添加数据集
      </a-button>
    </div>

    <!-- 算子列表 -->
    <div class="operators-list" data-testid="operators-list">
      <div
        v-for="operator in filteredOperators"
        :key="operator.type"
        class="operator-item"
        data-testid="operator-item"
        :data-testid-specific="`operator-item-${operator.type}`"
        draggable="true"
        @click="handleOperatorClick(operator)"
        @dragstart="handleDragStart($event, operator)"
        @dragend="handleDragEnd"
      >
        <div class="operator-icon">
          <a-icon :type="operator.metadata?.annotations?.icon || 'tool'" />
        </div>
        <div class="operator-content">
          <div class="operator-name" :data-testid="`operator-name-${operator.type}`">
            {{ operator.metadata.displayName || operator.type }}
          </div>
          <div class="operator-description" :data-testid="`operator-description-${operator.type}`">
            {{ operator.metadata.description }}
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredOperators.length === 0" class="empty-state" data-testid="empty-state">
        <a-icon type="inbox" style="font-size: 48px; color: #d9d9d9;" />
        <p>未找到匹配的算子</p>
      </div>
    </div>
  </div>
</template>

<script>
import OperatorInfo from '@/pages/dpe-web/models/OperatorInfo';

/**
 * 算子面板组件
 * @description 用于展示算子库的Vue组件，支持搜索、分类筛选、点击选择和拖拽操作
 * @component OperatorPanel
 * @example
 * <OperatorPanel
 *   :operators="availableOperators"
 *   :selected-category="currentCategory"
 *   :search-keyword="searchString"
 *   @search="handlePanelSearch"
 *   @category-change="handlePanelCategoryChange"
 *   @operator-select="handleOperatorSelect"
 *   @operator-drag-start="handleOperatorDragStart"
 *   @operator-drag-end="handleOperatorDragEnd"
 *   @add-dataset="handleAddNewDataset"
 * />
 * @since 1.0.0
 */
export default {
  name: 'OperatorPanel',

  /**
   * 组件属性定义
   * @property {Array<OperatorInfo>} operators - 算子库中所有可用算子的列表
   * @property {string} [selectedCategory='all'] - 当前选中的算子分类
   * @property {string} [searchKeyword=''] - 当前的搜索关键词
   */
  props: {
    /**
     * 算子库中所有可用算子的列表
     * @type {Array<OperatorInfo>}
     * @required
     */
    operators: {
      type: Array,
      required: true,
      default: () => [],
      validator(value) {
        return value.every(item => item instanceof OperatorInfo);
      }
    },

    /**
     * 当前选中的算子分类
     * @type {string}
     * @default 'all'
     */
    selectedCategory: {
      type: String,
      default: 'all'
    },

    /**
     * 当前的搜索关键词
     * @type {string}
     * @default ''
     */
    searchKeyword: {
      type: String,
      default: ''
    }
  },

  /**
   * 组件响应式数据
   */
  data() {
    return {
      /**
       * 内部搜索关键词状态
       * @type {string}
       * @default ''
       */
      internalSearchKeyword: '',

      /**
       * 内部选中分类状态
       * @type {string}
       * @default 'all'
       */
      internalSelectedCategory: 'all'
    };
  },

  /**
   * 计算属性定义
   */
  computed: {
    /**
     * 可用的分类列表
     * @computed availableCategories
     * @description 从算子列表中提取所有不重复的分类
     * @returns {Array<Object>} 分类列表，包含key和name属性
     */
    availableCategories() {
      const categories = new Set();
      this.operators.forEach(operator => {
        const category = operator.metadata.labels?.category;
        if (category) {
          categories.add(category);
        }
      });
      
      return Array.from(categories).map(category => ({
        key: category,
        name: this.getCategoryDisplayName(category)
      }));
    },

    /**
     * 过滤后的算子列表
     * @computed filteredOperators
     * @description 根据搜索关键词和选中分类过滤算子列表
     * @returns {Array<OperatorInfo>} 过滤后的算子数组
     */
    filteredOperators() {
      let filtered = [...this.operators];

      // 按分类过滤
      if (this.internalSelectedCategory !== 'all') {
        filtered = filtered.filter(operator => 
          operator.metadata.labels?.category === this.internalSelectedCategory
        );
      }

      // 按搜索关键词过滤
      if (this.internalSearchKeyword.trim()) {
        const keyword = this.internalSearchKeyword.toLowerCase().trim();
        filtered = filtered.filter(operator =>
          operator.metadata.displayName.toLowerCase().includes(keyword) ||
          operator.metadata.description.toLowerCase().includes(keyword) ||
          operator.type.toLowerCase().includes(keyword)
        );
      }

      return filtered;
    }
  },

  /**
   * 侦听器定义
   */
  watch: {
    /**
     * 侦听外部传入的搜索关键词变化
     * @watch searchKeyword
     * @param {string} newVal - 新的搜索关键词
     */
    searchKeyword: {
      handler(newVal) {
        this.internalSearchKeyword = newVal || '';
      },
      immediate: true
    },

    /**
     * 侦听外部传入的选中分类变化
     * @watch selectedCategory
     * @param {string} newVal - 新的选中分类
     */
    selectedCategory: {
      handler(newVal) {
        this.internalSelectedCategory = newVal || 'all';
      },
      immediate: true
    }
  },

  mounted() {
    console.log('OperatorPanel mounted',this.operators);
  },

  /**
   * 方法定义
   */
  methods: {
    /**
     * 处理搜索输入变化
     * @method handleSearchChange
     * @private
     * @fires search
     */
    handleSearchChange() {
      /**
       * 搜索关键词变化事件
       * @event search
       * @type {string} 最新的搜索关键词
       */
      this.$emit('search', this.internalSearchKeyword);
    },

    /**
     * 处理分类选择变化
     * @method handleCategoryChange
     * @private
     * @param {string} category - 选中的分类key
     * @fires category-change
     */
    handleCategoryChange(category) {
      /**
       * 分类选择变化事件
       * @event category-change
       * @type {string} 被选中的分类key
       */
      this.$emit('category-change', category);
    },

    /**
     * 处理算子项点击事件
     * @method handleOperatorClick
     * @private
     * @param {OperatorInfo} operator - 被点击的算子信息对象
     * @fires operator-select
     */
    handleOperatorClick(operator) {
      /**
       * 算子项点击选择事件
       * @event operator-select
       * @type {OperatorInfo} 被点击的算子信息对象
       */
      this.$emit('operator-select', operator);
    },

    /**
     * 处理算子拖拽开始事件
     * @method handleDragStart
     * @private
     * @param {DragEvent} event - 拖拽事件对象
     * @param {OperatorInfo} operator - 被拖拽的算子信息对象
     * @fires operator-drag-start
     */
    handleDragStart(event, operator) {
      // 设置拖拽数据
      event.dataTransfer.setData('application/json', JSON.stringify({
        type: 'operator',
        data: operator
      }));
      event.dataTransfer.effectAllowed = 'copy';

      /**
       * 算子拖拽开始事件
       * @event operator-drag-start
       * @type {Object}
       * @property {DragEvent} event - 拖拽事件对象
       * @property {OperatorInfo} operator - 被拖拽的算子信息对象
       */
      this.$emit('operator-drag-start', { event, operator });
    },

    /**
     * 处理算子拖拽结束事件
     * @method handleDragEnd
     * @private
     * @param {DragEvent} event - 拖拽结束事件对象
     * @fires operator-drag-end
     */
    handleDragEnd(event) {
      /**
       * 算子拖拽结束事件
       * @event operator-drag-end
       * @type {DragEvent} 拖拽结束事件对象
       */
      this.$emit('operator-drag-end', event);
    },

    /**
     * 处理添加数据集按钮点击事件
     * @method handleAddDataset
     * @private
     * @fires add-dataset
     */
    handleAddDataset() {
      /**
       * 点击添加数据集按钮事件
       * @event add-dataset
       */
      this.$emit('add-dataset');
    },

    /**
     * 获取分类显示名称
     * @method getCategoryDisplayName
     * @private
     * @param {string} category - 分类key
     * @returns {string} 分类的显示名称
     */
    getCategoryDisplayName(category) {
      const categoryMap = {
        'data-source': '数据源',
        'data-process': '数据处理',
        'machine-learning': '机器学习',
        'data-output': '数据输出'
      };
      return categoryMap[category] || category;
    }
  }
};
</script>

<style scoped>
.operator-panel {
  width: 300px;
  height: 100%;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.search-input {
  margin-bottom: 12px;
}

.category-select {
  width: 100%;
}

.dataset-section {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.add-dataset-btn {
  width: 100%;
}

.operators-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.operator-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.operator-item:hover {
  border-color: #40a9ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.operator-item:active {
  transform: translateY(0);
}

.operator-icon {
  margin-right: 12px;
  color: #1890ff;
  font-size: 16px;
}

.operator-content {
  flex: 1;
  min-width: 0;
}

.operator-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.operator-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.empty-state p {
  margin-top: 16px;
  margin-bottom: 0;
}
</style>
