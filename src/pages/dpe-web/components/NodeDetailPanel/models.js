/**
 * NodeDetailPanel 组件数据模型定义
 * @description 定义了 NodeDetailPanel 组件及其子组件使用的数据结构和回调接口
 */

import ObjectMetadata from '../../models/ObjectMetadata';

/**
 * 节点表单数据定义
 * @typedef {Object} NodeFormData
 * @property {Object.<string, any>} opConfigs - 算子配置的具体键值对，键为配置项名称，值为配置数据
 * @property {Object.<string, string[]>} opInputs - 算子输入字段与上游节点ID的映射，键为输入字段名称，值为一个包含上游节点ID的字符串数组
 * @property {ObjectMetadata} metadata - 节点的元数据，对应 GraphNode 的 metadata 属性，包含显示名称、描述等信息
 * 
 * @example
 * const nodeFormData = {
 *   metadata: {
 *     displayName: '示例节点',
 *     description: '这是一个示例节点的元数据',
 *     labels: {},
 *     annotations: {}
 *   },
 *   opConfigs: {
 *     param1: 'value1',
 *     param2: 123,
 *   },
 *   opInputs: {
 *     input_field_1: ['upstream_node_id_1', 'upstream_node_id_2'],
 *     input_field_2: [],
 *   },
 * };
 */

/**
 * 表单验证结果
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - 验证是否通过
 * @property {string} message - 验证消息，通常在验证失败时包含错误信息
 */

/**
 * NodeDetailPanel 的回调函数接口定义
 * @description 该类作为一个抽象接口，定义了 NodeDetailPanel 与父组件交互的异步方法
 * @class NodeDetailPanelCallbacks
 */
class NodeDetailPanelCallbacks {
  /**
   * 异步验证回调函数，用于在保存配置前对表单数据进行校验
   * @param {NodeFormData} formData - 当前节点的表单数据
   * @returns {Promise<ValidationResult>} 一个 Promise，resolve 一个包含 valid (布尔值) 和 message (字符串) 的对象
   * @example
   * const result = await callbacks.validate(formData);
   * if (!result.valid) {
   *   console.error('验证失败:', result.message);
   * }
   */
  async validate(formData) {
    throw new Error('Method \'validate\' must be implemented.');
  }

  /**
   * 异步保存回调函数，当用户点击保存按钮时触发，用于将配置数据持久化到后端
   * @param {NodeFormData} formData - 当前节点的表单数据
   * @returns {Promise<boolean>} 一个 Promise，resolve 一个布尔值表示保存是否成功
   * @example
   * const success = await callbacks.save(formData);
   * if (success) {
   *   console.log('保存成功');
   * }
   */
  async save(formData) {
    throw new Error('Method \'save\' must be implemented.');
  }

  /**
   * 异步运行任务回调函数，当用户点击运行任务按钮时触发，用于向后端提交运行当前节点的任务
   * @param {NodeFormData} formData - 当前节点的表单数据
   * @returns {Promise<import('../../models/TaskInfo').TaskInfo>} 一个 Promise，resolve 一个 TaskInfo 实例
   * @example
   * const taskInfo = await callbacks.runTask(formData);
   * console.log('任务已启动:', taskInfo.id);
   */
  async runTask(formData) {
    throw new Error('Method \'runTask\' must be implemented.');
  }

  /**
   * 异步取消任务回调函数，当用户点击取消任务按钮时触发，用于向后端发送取消当前任务的请求
   * @param {string} taskId - 需要取消的任务ID
   * @returns {Promise<boolean>} 一个 Promise，resolve 一个布尔值表示取消是否成功
   * @example
   * const success = await callbacks.cancelTask('task-123');
   * if (success) {
   *   console.log('任务已取消');
   * }
   */
  async cancelTask(taskId) {
    throw new Error('Method \'cancelTask\' must be implemented.');
  }
}

/**
 * 创建默认的 NodeFormData 对象
 * @param {Object} [overrides={}] - 覆盖默认值的对象
 * @returns {NodeFormData} 初始化的 NodeFormData 对象
 * @example
 * const defaultFormData = createDefaultNodeFormData();
 * const customFormData = createDefaultNodeFormData({
 *   metadata: { displayName: '自定义节点' }
 * });
 */
export const createDefaultNodeFormData = (overrides = {}) => {
  const defaultData = {
    metadata: new ObjectMetadata({
      displayName: '',
      description: '',
      labels: {},
      annotations: {}
    }),
    opConfigs: {},
    opInputs: {}
  };

  return {
    ...defaultData,
    ...overrides,
    metadata: {
      ...defaultData.metadata,
      ...(overrides.metadata || {})
    }
  };
};

/**
 * 验证 NodeFormData 数据结构的基本有效性
 * @param {any} data - 待验证的数据
 * @returns {ValidationResult} 验证结果
 * @example
 * const result = validateNodeFormDataStructure(formData);
 * if (!result.valid) {
 *   console.error('数据结构无效:', result.message);
 * }
 */
export const validateNodeFormDataStructure = (data) => {
  if (!data || typeof data !== 'object' || Array.isArray(data)) {
    return { valid: false, message: 'NodeFormData 必须是一个对象' };
  }

  if (!data.metadata || typeof data.metadata !== 'object') {
    return { valid: false, message: 'metadata 字段是必需的且必须是对象' };
  }

  if (!data.opConfigs || typeof data.opConfigs !== 'object' || Array.isArray(data.opConfigs)) {
    return { valid: false, message: 'opConfigs 字段是必需的且必须是对象' };
  }

  if (!data.opInputs || typeof data.opInputs !== 'object') {
    return { valid: false, message: 'opInputs 字段是必需的且必须是对象' };
  }

  // 验证 opInputs 的值都是数组
  for (const [key, value] of Object.entries(data.opInputs)) {
    if (!Array.isArray(value)) {
      return { 
        valid: false, 
        message: `opInputs.${key} 必须是数组，当前类型: ${typeof value}` 
      };
    }
    
    // 验证数组中的每个元素都是字符串
    for (const [index, item] of value.entries()) {
      if (typeof item !== 'string') {
        return { 
          valid: false, 
          message: `opInputs.${key}[${index}] 必须是字符串，当前类型: ${typeof item}` 
        };
      }
    }
  }

  return { valid: true, message: '数据结构验证通过' };
};

export { NodeDetailPanelCallbacks }; 