<template>
  <div class="node-data-panel" data-testid="node-data-panel">
    <!-- 数据预览主内容区域 -->
    <div class="data-panel-content">
      <!-- 加载状态 - 手动加载中或任务运行中 -->
      <div v-if="loading || isTaskRunning" class="loading-state" data-testid="loading-state">
        <a-spin size="large">
          <div class="loading-content">
            <a-icon type="loading" style="font-size: 24px; margin-bottom: 16px;" />
            <p v-if="loading">正在加载数据...</p>
            <p v-else-if="isTaskRunning">{{ taskInfo.message || '任务处理中...' }}</p>
          </div>
        </a-spin>
      </div>

      <!-- 错误状态 - 手动加载错误或任务执行错误 -->
      <div v-else-if="error || isTaskError" class="error-state" data-testid="error-state">
        <a-result
          status="error"
          title="数据加载失败"
          :sub-title="error || (taskInfo && taskInfo.message) || '任务执行失败'"
        >
          <template #extra>
            <a-button type="primary" @click="loadData">
              重新加载
            </a-button>
          </template>
        </a-result>
      </div>

      <!-- 空状态 - 无任务信息 -->
      <div v-else-if="isEmpty" class="empty-state" data-testid="empty-state">
        <a-empty>
          <template #description>
            <div>
              <p>暂无数据预览</p>
              <p style="color: #999; font-size: 14px;">任务尚未完成或无结果资源</p>
            </div>
          </template>
        </a-empty>
      </div>

      <!-- 动态数据内容组件 -->
      <div v-else class="data-content" data-testid="data-content">
        <!-- 结构化数据预览 -->
        <NodeStructuredDataContent
          v-if="dataType === 'structured'"
          :resource-id="resourceId"
          @error="handleDataError"
          data-testid="structured-data-content"
        />

        <!-- 未知数据类型 -->
        <div v-else class="unsupported-data-type" data-testid="unsupported-data-type">
          <a-result
            status="warning"
            title="暂不支持此数据类型"
            :sub-title="`数据类型: ${dataType || '未知'}`"
          >
            <template #extra>
              <a-button @click="loadData">
                重新检测
              </a-button>
            </template>
          </a-result>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NodeStructuredDataContent from './contents/NodeStructuredDataContent.vue';

/**
 * NodeDataPanel 组件
 * @description 数据预览面板容器组件，根据任务结果类型动态加载相应的数据预览子组件
 * @component NodeDataPanel
 * @example
 * <NodeDataPanel
 *   :session-id="sessionId"
 *   :node-id="nodeId"
 *   :task-info="taskInfo"
 * />
 * @since 1.0.0
 */
export default {
  name: 'NodeDataPanel',

  components: {
    NodeStructuredDataContent
  },

  props: {
    /**
     * 会话唯一标识符
     * @type {String}
     * @required
     */
    sessionId: {
      type: String,
      required: true,
      validator: (value) => {
        return value && value.trim().length > 0;
      }
    },

    /**
     * 节点唯一标识符
     * @type {String}
     * @required
     */
    nodeId: {
      type: String,
      required: true,
      validator: (value) => {
        return value && value.trim().length > 0;
      }
    },

    /**
     * 任务信息对象，包含任务状态和结果资源信息
     * @type {Object|null}
     * @property {String} id - 任务ID
     * @property {String} status - 任务状态
     * @property {String} [resultResourceId] - 结果资源ID
     * @property {String} [resultResourceType] - 结果资源类型
     */
    taskInfo: {
      type: Object,
      default: null,
      validator: (value) => {
        // taskInfo 可以为 null，但如果提供则必须是对象
        return value === null || (typeof value === 'object' && value.id);
      }
    }
  },

  data() {
    return {
      // 组件状态
      loading: false,
      error: null,
      
      // 数据类型和资源信息
      dataType: null,
      resourceId: null
    };
  },

  computed: {
    /**
     * 是否为空状态
     * @computed isEmpty
     * @returns {Boolean} 是否显示空状态
     */
    isEmpty() {
      return !this.loading && !this.error && !this.taskInfo;
    },

    /**
     * 是否有有效的任务结果
     * @computed hasValidResult
     * @returns {Boolean} 任务是否有有效结果
     */
    hasValidResult() {
      return this.taskInfo && 
             this.taskInfo.status === 'SUCCESS' && 
             this.taskInfo.resultResourceId;
    },

    /**
     * 是否为任务运行状态
     * @computed isTaskRunning
     * @returns {Boolean} 任务是否正在运行
     */
    isTaskRunning() {
      return this.taskInfo && 
             (this.taskInfo.status === 'RUNNING' || this.taskInfo.status === 'PENDING');
    },

    /**
     * 是否为任务错误状态
     * @computed isTaskError
     * @returns {Boolean} 任务是否出错
     */
    isTaskError() {
      return this.taskInfo && 
             (this.taskInfo.status === 'ERROR' || this.taskInfo.status === 'FAILED');
    }
  },

  watch: {
    /**
     * 监听任务信息变化
     * @watch taskInfo
     * @param {Object} newTaskInfo - 新的任务信息
     * @param {Object} oldTaskInfo - 旧的任务信息
     */
    taskInfo: {
      handler(newTaskInfo, oldTaskInfo) {
        // 当任务信息变化时，重新加载数据
        // docs: 使用 deep watch 和 JSON.stringify 确保能检测到对象内部的变化
        if (JSON.stringify(newTaskInfo) !== JSON.stringify(oldTaskInfo)) {
          this.loadData();
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    /**
     * 加载数据
     * @method loadData
     * @public
     * @description 根据任务信息加载相应的数据预览组件
     * @returns {Promise<void>}
     * @example
     * this.$refs.nodeDataPanel.loadData();
     */
    async loadData() {
      try {
        this.loading = true;
        this.error = null;
        this.dataType = null;
        this.resourceId = null;

        // 检查任务信息是否存在
        if (!this.taskInfo) {
          this.loading = false;
          return;
        }

        // 如果任务正在运行，不需要加载数据，直接显示运行状态
        if (this.isTaskRunning) {
          this.loading = false;
          return;
        }

        // 如果任务出错，不需要加载数据，直接显示错误状态
        if (this.isTaskError) {
          this.loading = false;
          return;
        }

        // 检查任务是否有有效结果
        if (!this.hasValidResult) {
          this.loading = false;
          return;
        }

        // 获取资源信息
        this.resourceId = this.taskInfo.resultResourceId;
        
        // 根据任务信息推断数据类型
        this.dataType = this.detectDataType(this.taskInfo);

        this.loading = false;
      } catch (error) {
        console.error('NodeDataPanel: 加载数据失败', error);
        this.error = error.message || '加载数据时发生未知错误';
        this.loading = false;
      }
    },

    /**
     * 检测数据类型
     * @method detectDataType
     * @private
     * @param {Object} taskInfo - 任务信息
     * @returns {String} 数据类型
     */
    detectDataType(taskInfo) {
      // 如果任务信息中明确指定了资源类型
      if (taskInfo.resultResourceType) {
        const resourceType = taskInfo.resultResourceType.toLowerCase();
        if (resourceType.includes('structured') || resourceType.includes('table')) {
          return 'structured';
        }
        if (resourceType.includes('text')) {
          return 'text';
        }
        if (resourceType.includes('image')) {
          return 'image';
        }
        if (resourceType.includes('graph')) {
          return 'graph';
        }
        
        // 如果是未知类型，直接返回
        if (resourceType.includes('unknown') || !resourceType.match(/^(structured|table|text|image|graph)/)) {
          return taskInfo.resultResourceType;
        }
      }

      // 默认假设为结构化数据（当前主要支持的类型）
      return 'structured';
    },

    /**
     * 处理数据加载错误
     * @method handleDataError
     * @private
     * @param {String} error - 错误信息
     */
    handleDataError(error) {
      console.error('NodeDataPanel: 数据组件加载错误', error);
      this.error = error || '数据组件加载失败';
    }
  }
};
</script>

<style lang="less" scoped>
.node-data-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .data-panel-content {
    flex: 1;
    min-height: 0;
    position: relative;
  }

  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;

    .loading-content {
      text-align: center;
      color: #666;

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }

  .error-state {
    padding: 40px 20px;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    padding: 40px 20px;
  }

  .data-content {
    height: 100%;
    overflow: hidden;
  }

  .unsupported-data-type {
    padding: 40px 20px;
  }
}

// 深色主题支持
.ant-layout.dark {
  .node-data-panel {
    .loading-content {
      color: rgba(255, 255, 255, 0.65);
    }
  }
}
</style> 