<template>
  <div class="node-structured-data-content" data-testid="structured-data-content">
    <!-- 数据表格主内容区域 -->
    <div class="data-table-container">
      <!-- 工具栏区域 -->
      <div class="toolbar" data-testid="view-switcher">
        <div class="toolbar-left">
          <a-button 
            :icon="showSchema ? 'table' : 'info-circle'" 
            @click="toggleSchemaView"
            :title="showSchema ? '查看数据' : '查看数据结构'"
            :data-testid="showSchema ? 'table-view-btn' : 'structure-view-btn'"
          >
            {{ showSchema ? '数据视图' : '结构视图' }}
          </a-button>
          <span class="data-count" v-if="!showSchema && dataInfo.total">
            共 {{ dataInfo.total }} 条记录
          </span>
        </div>
        
        <div class="toolbar-right">
          <a-button 
            icon="reload" 
            @click="refreshData"
            :loading="loading"
            :title="'刷新数据'"
          >
            刷新
          </a-button>
        </div>
      </div>

      <!-- 数据结构视图 -->
      <div v-if="showSchema" class="schema-view" data-testid="structure-view">
        <a-spin :spinning="schemaLoading">
          <a-table
            :columns="schemaColumns"
            :data-source="schemaData"
            :pagination="false"
            size="small"
            :scroll="{ y: 400 }"
            :row-key="(record) => record.name"
          >
            <template #type="text">
              <a-tag :color="getTypeColor(text)">{{ text }}</a-tag>
            </template>
            <template #nullable="nullable">
              <a-icon 
                :type="nullable ? 'check-circle' : 'close-circle'" 
                :style="{ color: nullable ? '#52c41a' : '#ff4d4f' }"
              />
            </template>
          </a-table>
        </a-spin>
        
        <div v-if="schemaError" class="error-message" data-testid="error-state">
          <a-alert
            type="error"
            :message="'加载数据结构失败'"
            :description="schemaError"
            show-icon
            closable
          />
        </div>
      </div>

      <!-- 数据表格视图 -->
      <div v-else class="data-view">
        <a-spin :spinning="loading">
          <a-table
            :columns="dataColumns"
            :data-source="tableData"
            :pagination="false"
            :scroll="{ x: 'max-content', y: 500 }"
            :row-key="(record, index) => index"
            size="small"
            :locale="{ emptyText: loading ? '加载中...' : '暂无数据' }"
            data-testid="data-table"
          >
            <!-- 动态渲染表格单元格内容 -->
            <template 
              v-for="col in dynamicColumns" 
              #[col.dataIndex]="text, record"
            >
              <span :title="String(text)" class="cell-content">
                {{ formatCellValue(text) }}
              </span>
            </template>
          </a-table>
        </a-spin>

        <!-- 分页组件 -->
        <div class="pagination-container" v-if="dataInfo.total > 0">
          <a-pagination
            v-model="dataInfo.page"
            :page-size="dataInfo.pageSize"
            :total="dataInfo.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
            :page-size-options="['10', '20', '50', '100']"
            size="small"
            @change="handlePageChange"
            @showSizeChange="handlePageSizeChange"
            data-testid="pagination"
          />
        </div>

        <!-- 错误状态 -->
        <div v-if="dataError" class="error-message" data-testid="error-state">
          <a-alert
            type="error"
            :message="'加载数据失败'"
            :description="dataError"
            show-icon
            closable
            @close="clearError"
          />
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && !dataError && tableData.length === 0" class="empty-state" data-testid="empty-state">
          <a-empty 
            description="暂无数据"
          >
            <a-button type="primary" @click="refreshData">重新加载</a-button>
          </a-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import resourceService from '@/pages/dpe-web/services/ResourceService';

/**
 * NodeStructuredDataContent 组件
 * @description 结构化数据预览组件，专门用于展示表格形式的结构化数据，支持分页、搜索和数据结构查看功能
 * @example
 * <NodeStructuredDataContent
 *   resource-id="structured-data-resource-123"
 *   @error="handleError"
 * />
 */
export default {
  name: 'NodeStructuredDataContent',
  
  props: {
    /**
     * 结构化数据资源的唯一标识符
     * @type {String}
     * @required
     */
    resourceId: {
      type: String,
      required: true,
      validator: (value) => {
        return value && value.trim().length > 0;
      }
    }
  },

  data() {
    return {
      // 资源客户端
      resourceClient: null,
      
      // 数据加载状态
      loading: false,
      schemaLoading: false,
      
      // 错误状态
      dataError: null,
      schemaError: null,
      
      // 视图状态
      showSchema: false,
      
      // 表格数据
      tableData: [],
      schemaData: [],
      
      // 数据信息
      dataInfo: {
        total: 0,
        page: 1,
        pageSize: 20
      },
      
      // 表格列定义
      dataColumns: [],
      dynamicColumns: [],
      
      // 数据结构表格列定义
      schemaColumns: [
        {
          title: '字段名',
          dataIndex: 'name',
          key: 'name',
          width: 200,
          ellipsis: true
        },
        {
          title: '数据类型',
          dataIndex: 'type',
          key: 'type',
          width: 120,
          scopedSlots: { customRender: 'type' }
        },
        {
          title: '可为空',
          dataIndex: 'nullable',
          key: 'nullable',
          width: 80,
          align: 'center',
          scopedSlots: { customRender: 'nullable' }
        },
        {
          title: '描述',
          dataIndex: 'description',
          key: 'description',
          ellipsis: true
        }
      ]
    };
  },

  computed: {
    /**
     * 分页配置
     * @returns {Object} 分页配置对象
     */
    paginationConfig() {
      return {
        current: this.dataInfo.page,
        pageSize: this.dataInfo.pageSize,
        total: this.dataInfo.total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => 
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        pageSizeOptions: ['10', '20', '50', '100'],
        size: 'small',
        class: 'pagination-testid'
      };
    }
  },

  watch: {
    /**
     * 监听资源ID变化，重新加载数据
     * @param {String} newResourceId - 新的资源ID
     */
    resourceId: {
      async handler(newResourceId) {
        if (newResourceId) {
          await this.initResourceClient();
          await this.initData();
        }
      },
      immediate: true
    }
  },

  methods: {
    /**
     * 初始化资源客户端
     * @returns {Promise<void>}
     */
    async initResourceClient() {
      try {
        this.resourceClient = await resourceService.openStructuredDataResourceClient(this.resourceId);
      } catch (error) {
        this.dataError = `初始化资源客户端失败: ${error.message}`;
        this.$emit('error', error);
      }
    },

    /**
     * 初始化数据加载
     * @returns {Promise<void>}
     */
    async initData() {
      if (!this.resourceClient) {
        return;
      }
      
      this.clearError();
      await Promise.all([
        this.loadPreviewData(),
        this.loadSchemaData()
      ]);
    },

    /**
     * 预览数据内容（公共方法）
     * @public
     * @param {Number} [page=1] - 页码，从1开始
     * @param {Number} [pageSize=20] - 每页条数
     * @returns {Promise<{total: Number, schema: Array<Object>, data: Array<Object>}>} 数据预览结果
     */
    async previewData(page = 1, pageSize = 20) {
      try {
        this.loading = true;
        this.dataError = null;

        if (!this.resourceClient) {
          throw new Error('资源客户端未初始化');
        }

        // 调用ResourceService的previewData方法
        // 注意：ResourceService使用0基页码，而组件使用1基页码
        const response = await this.resourceClient.previewData(page - 1, pageSize);

        this.dataInfo = {
          page,
          pageSize,
          total: response.total
        };

        this.tableData = response.data || [];
        
        this.updateDataColumns(response.schema || []);

        return {
          total: response.total,
          schema: response.schema,
          data: response.data
        };
      } catch (error) {
        this.dataError = error.message || '加载数据失败';
        this.$emit('error', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取结构化数据的数据结构（公共方法）
     * @public
     * @returns {Promise<Array<Object>>} 数据结构定义
     */
    async getSchema() {
      try {
        this.schemaLoading = true;
        this.schemaError = null;

        if (!this.resourceClient) {
          throw new Error('资源客户端未初始化');
        }

        // 调用ResourceService的getSchema方法
        const schema = await this.resourceClient.getSchema();

        this.schemaData = schema || [];
        return schema;
      } catch (error) {
        this.schemaError = error.message || '加载数据结构失败';
        this.$emit('error', error);
        throw error;
      } finally {
        this.schemaLoading = false;
      }
    },

    /**
     * 加载预览数据
     * @private
     * @returns {Promise<void>}
     */
    async loadPreviewData() {
      await this.previewData(this.dataInfo.page, this.dataInfo.pageSize);
    },

    /**
     * 加载数据结构
     * @private
     * @returns {Promise<void>}
     */
    async loadSchemaData() {
      await this.getSchema();
    },

    /**
     * 更新数据表格列定义
     * @private
     * @param {Array<SchemaField>} schema - 数据结构定义
     */
    updateDataColumns(schema) {
      const columns = schema.map(field => ({
        title: field.name || field.getName(),
        dataIndex: field.name || field.getName(),
        key: field.name || field.getName(),
        width: this.getColumnWidth(field.type || field.getType()),
        ellipsis: true,
        sorter: this.isNumericType(field.type || field.getType()),
        scopedSlots: { customRender: field.name || field.getName() }
      }));

      this.dataColumns = columns;
      this.dynamicColumns = columns.filter(col => col.scopedSlots);
    },

    /**
     * 根据数据类型获取列宽度
     * @private
     * @param {String} type - 数据类型
     * @returns {Number} 列宽度
     */
    getColumnWidth(type) {
      const typeWidthMap = {
        'string': 200,
        'number': 120,
        'integer': 120,
        'boolean': 80,
        'date': 150,
        'datetime': 180
      };
      return typeWidthMap[type] || 150;
    },

    /**
     * 判断是否为数值类型
     * @private
     * @param {String} type - 数据类型
     * @returns {Boolean} 是否为数值类型
     */
    isNumericType(type) {
      return ['number', 'integer'].includes(type);
    },

    /**
     * 格式化表格单元格值
     * @private
     * @param {*} value - 单元格值
     * @returns {String} 格式化后的值
     */
    formatCellValue(value) {
      if (value === null || value === undefined) {
        return '-';
      }
      
      if (typeof value === 'string' && value.length > 100) {
        return value.substring(0, 97) + '...';
      }
      
      return String(value);
    },

    /**
     * 获取数据类型对应的颜色
     * @private
     * @param {String} type - 数据类型
     * @returns {String} 颜色值
     */
    getTypeColor(type) {
      const colorMap = {
        'string': 'blue',
        'number': 'green',
        'integer': 'green',
        'boolean': 'orange',
        'date': 'purple',
        'datetime': 'purple',
        'object': 'red'
      };
      return colorMap[type] || 'default';
    },

    /**
     * 处理表格变化事件
     * @private
     * @param {Object} pagination - 分页信息
     * @param {Object} filters - 过滤信息
     * @param {Object} sorter - 排序信息
     */
    async handleTableChange(pagination, filters, sorter) {
      const { current, pageSize } = pagination;
      this.dataInfo.page = current;
      this.dataInfo.pageSize = pageSize;
      
      await this.loadPreviewData();
    },

    /**
     * 处理页码变化事件
     * @private
     * @param {Number} page - 页码
     * @param {Number} pageSize - 每页条数
     */
    async handlePageChange(page, pageSize) {
      this.dataInfo.page = page;
      this.dataInfo.pageSize = pageSize;
      await this.loadPreviewData();
    },

    /**
     * 处理每页条数变化事件
     * @private
     * @param {Number} current - 当前页码
     * @param {Number} size - 每页条数
     */
    async handlePageSizeChange(current, size) {
      this.dataInfo.page = 1; // 重置到第一页
      this.dataInfo.pageSize = size;
      await this.loadPreviewData();
    },

    /**
     * 切换数据结构视图
     * @private
     */
    toggleSchemaView() {
      this.showSchema = !this.showSchema;
    },

    /**
     * 刷新数据
     * @private
     * @returns {Promise<void>}
     */
    async refreshData() {
      await this.initData();
    },

    /**
     * 清除错误信息
     * @private
     */
    clearError() {
      this.dataError = null;
      this.schemaError = null;
    },


  }
};
</script>

<style scoped>
.node-structured-data-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.data-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-count {
  color: #666;
  font-size: 14px;
}

.schema-view,
.data-view {
  flex: 1;
  min-height: 0;
}

.error-message {
  margin-top: 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.pagination-container {
  padding: 16px 0;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
}

.cell-content {
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 深度选择器 - 自定义表格样式 */
.node-structured-data-content >>> .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.node-structured-data-content >>> .ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff;
}

.node-structured-data-content >>> .ant-empty-image {
  height: 60px;
}

.node-structured-data-content >>> .ant-spin-container {
  min-height: 200px;
}

.node-structured-data-content >>> .pagination-testid {
  /* 为分页添加 testid */
}

.node-structured-data-content >>> .pagination-testid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  /* 此样式用于添加分页的 data-testid 识别 */
}
</style> 