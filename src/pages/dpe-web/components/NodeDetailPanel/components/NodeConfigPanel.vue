<template>
  <div class="node-config-panel">
    <!-- 节点元数据编辑区域 -->
    <div class="metadata-section">
      <h4 class="section-title">节点信息</h4>
      <a-form 
        ref="metadataForm"
        :model="internalFormData.metadata"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :colon="false"
      >
        <a-form-item label="算子类型">
          <a-input 
            :value="operatorInfo.metadata.displayName || operatorInfo.type"
            disabled
            style="background-color: #f5f5f5;"
          />
          <div style="margin-top: 4px; color: #666; font-size: 12px;">
            类型: {{ operatorInfo.type }}
          </div>
        </a-form-item>
        
        <a-form-item 
          label="显示名称" 
          prop="displayName"
          :rules="[{ required: true, message: '请输入节点显示名称' }]"
        >
          <a-input 
            v-model="internalFormData.metadata.displayName"
            :disabled="!editable"
            placeholder="请输入节点显示名称"
            @input="handleMetadataChange"
          />
        </a-form-item>
        
        <a-form-item 
          label="描述信息" 
          prop="description"
        >
          <a-textarea 
            v-model="internalFormData.metadata.description"
            :disabled="!editable"
            placeholder="请输入节点描述信息"
            :rows="3"
            @input="handleMetadataChange"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 算子配置编辑区域 -->
    <div class="config-section">
      <h4 class="section-title">算子配置</h4>
      <DynamicFormSimplify
        ref="configForm"
        :config-fields="configFields"
        :value="internalFormData.opConfigs"
        :readonly="!editable"
        @input="handleConfigChange"
      />
    </div>

    <!-- 输入连接配置区域 -->
    <div class="inputs-section" v-if="hasInputFields">
      <h4 class="section-title">输入连接</h4>
      <a-form 
        ref="inputsForm"
        :model="internalFormData.opInputs"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :colon="false"
      >
        <a-form-item 
          v-for="inputField in inputFields"
          :key="inputField"
          :label="inputField"
          :prop="inputField"
        >
          <a-select
            v-model="internalFormData.opInputs[inputField]"
            mode="multiple"
            :disabled="!editable"
            :placeholder="`请选择 ${inputField} 的上游节点`"
            allow-clear
            @change="handleInputChange"
          >
            <a-select-option 
              v-for="node in availableUpstreamNodes"
              :key="node.id"
              :value="node.id"
            >
              {{ node.metadata.displayName || node.id }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script>
import DynamicForm from '../../DynamicForm';
import DynamicFormSimplify from '../../DynamicFormSimplify';
import { validateNodeFormDataStructure } from '../models.js';

/**
 * 节点配置编辑面板组件
 * @description 基于 DynamicForm 组件构建的节点配置编辑组件，支持 v-model 双向绑定、表单验证和保存功能
 * @component NodeConfigPanel
 * @example
 * <NodeConfigPanel
 *   :session-id="sessionId"
 *   :node-id="nodeId"
 *   :operator-info="operatorInfo"
 *   :editable="true"
 *   v-model="nodeFormData"
 *   :callbacks="callbacks"
 *   @input="handleFormDataChange"
 * />
 */
export default {
  name: 'NodeConfigPanel',
  
  components: {
    DynamicForm,
    DynamicFormSimplify,
  },
  
  /**
   * 组件属性定义
   * @property {string} sessionId - 当前会话的唯一标识符
   * @property {string} nodeId - 当前节点的唯一标识符
   * @property {Object} operatorInfo - 算子信息对象，包含算子类型、元数据和配置元数据等
   * @property {boolean} [editable=true] - 控制节点配置是否可编辑
   * @property {Object} value - 使用 v-model 绑定的节点表单数据
   * @property {Object} callbacks - 父组件传入的回调函数集合，包含表单验证、保存等异步方法
   */
  props: {
    /**
     * 当前会话的唯一标识符
     * @type {string}
     * @required
     */
    sessionId: {
      type: String,
      required: true
    },
    
    /**
     * 当前节点的唯一标识符
     * @type {string}
     * @required
     */
    nodeId: {
      type: String,
      required: true
    },
    
    /**
     * 算子信息对象
     * @type {Object}
     * @required
     */
    operatorInfo: {
      type: Object,
      required: true,
      validator(value) {
        return value && 
               value.opConfigMetadata && 
               Array.isArray(value.opConfigMetadata) &&
               value.inputFields &&
               Array.isArray(value.inputFields);
      }
    },
    
    /**
     * 是否可编辑
     * @type {boolean}
     * @default true
     */
    editable: {
      type: Boolean,
      default: true
    },
    
    /**
     * 节点表单数据 (v-model)
     * @type {Object}
     * @required
     */
    value: {
      type: Object,
      required: true,
      validator(value) {
        const result = validateNodeFormDataStructure(value);
        if (!result.valid) {
          console.warn('NodeConfigPanel: 无效的 nodeFormData 结构:', result.message);
        }
        return result.valid;
      }
    },
    
    /**
     * 回调函数集合
     * @type {Object}
     * @required
     */
    callbacks: {
      type: Object,
      required: true,
      validator(value) {
        return value &&
               typeof value.validate === 'function' &&
               typeof value.save === 'function';
      }
    },
    
    /**
     * 当前会话中所有可以作为上游节点的 GraphNode 列表
     * @type {Array<GraphNode>}
     * @default []
     */
    availableUpstreamNodes: {
      type: Array,
      default: () => [],
      validator(value) {
        // 验证每个元素都是一个对象，并且包含id和metadata.displayName
        return value.every(node =>
          node !== null &&
          typeof node.id === 'string' &&
          typeof node.metadata === 'object' &&
          node.metadata !== null &&
          typeof node.metadata.displayName === 'string'
        );
      }
    }
  },
  
  data() {
    return {
      /** @private {Object} 内部表单数据 */
      internalFormData: {
        metadata: {
          displayName: '',
          description: '',
          labels: {},
          annotations: {}
        },
        opConfigs: {},
        opInputs: {}
      },
      
    };
  },
  
  computed: {
    /**
     * 算子配置字段列表
     * @computed configFields
     * @returns {Array} 配置字段数组
     */
    configFields() {
      return this.operatorInfo.opConfigMetadata || [];
    },
    
    /**
     * 算子输入字段列表
     * @computed inputFields
     * @returns {Array} 输入字段数组
     */
    inputFields() {
      return this.operatorInfo.inputFields || [];
    },
    
    /**
     * 是否有输入字段
     * @computed hasInputFields
     * @returns {boolean} 是否有输入字段
     */
    hasInputFields() {
      return this.inputFields.length > 0;
    }
  },
  
  watch: {
    /**
     * 监听外部传入的表单数据变化
     * @watch value
     */
    value: {
      handler(newValue) {
        if (newValue) {
          this.internalFormData = this.deepClone(newValue);
          this.initializeInputFields();
        }
      },
      immediate: true,
      deep: true
    },
    
    /**
     * 监听算子输入字段变化，初始化输入连接
     * @watch inputFields
     */
    inputFields: {
      handler() {
        this.initializeInputFields();
      },
      immediate: true
    }
  },
  
  methods: {
    /**
     * 初始化输入字段配置
     * @method initializeInputFields
     * @private
     */
    initializeInputFields() {
      const opInputs = { ...this.internalFormData.opInputs };
      
      // 为每个输入字段初始化空数组（如果不存在）
      this.inputFields.forEach(field => {
        if (!opInputs[field]) {
          this.$set(opInputs, field, []);
        }
      });
      
      this.internalFormData.opInputs = opInputs;
    },
    
    /**
     * 处理节点元数据变化
     * @method handleMetadataChange
     * @private
     */
    handleMetadataChange() {
      this.emitFormDataChange();
    },
    
    /**
     * 处理算子配置变化
     * @method handleConfigChange
     * @private
     * @param {Object} newConfigData - 新的配置数据
     */
    handleConfigChange(newConfigData) {
      this.internalFormData.opConfigs = { ...newConfigData };
      this.emitFormDataChange();
    },
    
    /**
     * 处理输入连接变化
     * @method handleInputChange
     * @private
     */
    handleInputChange() {
      this.emitFormDataChange();
    },
    
    /**
     * 发送表单数据变化事件
     * @method emitFormDataChange
     * @private
     * @fires input
     */
    emitFormDataChange() {
      /**
       * 表单数据变化事件
       * @event input
       * @type {Object} 更新后的表单数据
       */
      this.$emit('input', this.deepClone(this.internalFormData));
    },
    
    /**
     * 深度克隆对象
     * @method deepClone
     * @private
     * @param {Object} obj - 要克隆的对象
     * @returns {Object} 克隆后的对象
     */
    deepClone(obj) {
      return JSON.parse(JSON.stringify(obj));
    },
    
    /**
     * 手动触发表单验证
     * @method validate
     * @public
     * @returns {Promise<boolean>} 验证是否通过
     * @example
     * const isValid = await this.$refs.nodeConfigPanel.validate();
     */
    async validate() {
      try {
        // 验证内部表单
        const metadataValid = await this.validateMetadataForm();
        const configValid = await this.validateConfigForm();
        const inputsValid = await this.validateInputsForm();
        
        const internalValid = metadataValid && configValid && inputsValid;
        
        // 总是调用外部验证回调，即使内部验证失败
        // 这样可以确保业务层面的验证错误消息能够被记录和显示
        let externalValid = true;
        if (this.callbacks && typeof this.callbacks.validate === 'function') {
          const result = await this.callbacks.validate(this.deepClone(this.internalFormData));
          externalValid = result.valid;
        }
        
        // 只有内部验证和外部验证都通过才返回true
        return internalValid && externalValid;
      } catch (error) {
        console.error('NodeConfigPanel: 验证过程中发生错误:', error);
        this.$message.error('表单验证失败: ' + error.message);
        return false;
      }
    },
    
    /**
     * 手动触发保存操作
     * @method save
     * @public
     * @returns {Promise<boolean>} 保存是否成功
     * @example
     * const saved = await this.$refs.nodeConfigPanel.save();
     */
    async save() {
      try {
        // 先验证表单
        const isValid = await this.validate();
        if (!isValid) {
          this.$message.warning('请先修正表单错误后再保存');
          return false;
        }
        
        // 调用保存回调
        if (this.callbacks && typeof this.callbacks.save === 'function') {
          const success = await this.callbacks.save(this.deepClone(this.internalFormData));
          if (success) {
            this.$message.success('节点配置保存成功');
          } else {
            this.$message.error('节点配置保存失败');
          }
          return success;
        }
        
        return false;
      } catch (error) {
        console.error('NodeConfigPanel: 保存过程中发生错误:', error);
        this.$message.error('保存失败: ' + error.message);
        return false;
      }
    },
    
    /**
     * 验证元数据表单
     * @method validateMetadataForm
     * @private
     * @returns {Promise<boolean>} 验证结果
     */
    async validateMetadataForm() {
      if (!this.$refs.metadataForm) {
        console.warn('NodeConfigPanel: metadataForm ref not found');
        return true;
      }
      
      try {
        // 尝试多种验证方法，兼容不同版本的 Ant Design Vue
        if (typeof this.$refs.metadataForm.validate === 'function') {
          return await new Promise((resolve) => {
            this.$refs.metadataForm.validate((valid) => {
              resolve(valid !== false);
            });
          });
        } else if (typeof this.$refs.metadataForm.validateFields === 'function') {
          try {
            await this.$refs.metadataForm.validateFields();
            return true;
          } catch (error) {
            return false;
          }
        } else {
          console.warn('NodeConfigPanel: No validation method found on metadataForm');
          return true;
        }
      } catch (error) {
        console.error('NodeConfigPanel: metadata form validation error:', error);
        return true; // 如果验证方法不存在，认为验证通过
      }
    },
    
    /**
     * 验证配置表单
     * @method validateConfigForm
     * @private
     * @returns {Promise<boolean>} 验证结果
     */
    async validateConfigForm() {
      if (!this.$refs.configForm) {
        console.warn('NodeConfigPanel: configForm ref not found');
        return true;
      }
      
      try {
        if (typeof this.$refs.configForm.validateForm === 'function') {
          return await this.$refs.configForm.validateForm();
        } else {
          console.warn('NodeConfigPanel: configForm.validateForm method not found');
          return true;
        }
      } catch (error) {
        console.error('NodeConfigPanel: config form validation error:', error);
        return true; // 如果验证方法不存在，认为验证通过
      }
    },
    
    /**
     * 验证输入连接表单
     * @method validateInputsForm
     * @private
     * @returns {Promise<boolean>} 验证结果
     */
    async validateInputsForm() {
      if (!this.$refs.inputsForm) {
        console.warn('NodeConfigPanel: inputsForm ref not found');
        return true;
      }
      
      try {
        // 尝试多种验证方法，兼容不同版本的 Ant Design Vue
        if (typeof this.$refs.inputsForm.validate === 'function') {
          return await new Promise((resolve) => {
            this.$refs.inputsForm.validate((valid) => {
              resolve(valid !== false);
            });
          });
        } else if (typeof this.$refs.inputsForm.validateFields === 'function') {
          try {
            await this.$refs.inputsForm.validateFields();
            return true;
          } catch (error) {
            return false;
          }
        } else {
          console.warn('NodeConfigPanel: No validation method found on inputsForm');
          return true;
        }
      } catch (error) {
        console.error('NodeConfigPanel: inputs form validation error:', error);
        return true; // 如果验证方法不存在，认为验证通过
      }
    },
    
    /**
     * 清空所有表单验证状态
     * @method clearValidate
     * @public
     */
    clearValidate() {
      try {
        if (this.$refs.metadataForm && typeof this.$refs.metadataForm.clearValidate === 'function') {
          this.$refs.metadataForm.clearValidate();
        } else if (this.$refs.metadataForm && typeof this.$refs.metadataForm.resetFields === 'function') {
          this.$refs.metadataForm.resetFields();
        }
      } catch (error) {
        console.warn('NodeConfigPanel: Could not clear metadata form validation:', error);
      }
      
      try {
        if (this.$refs.configForm && typeof this.$refs.configForm.clearValidate === 'function') {
          this.$refs.configForm.clearValidate();
        }
      } catch (error) {
        console.warn('NodeConfigPanel: Could not clear config form validation:', error);
      }
      
      try {
        if (this.$refs.inputsForm && typeof this.$refs.inputsForm.clearValidate === 'function') {
          this.$refs.inputsForm.clearValidate();
        } else if (this.$refs.inputsForm && typeof this.$refs.inputsForm.resetFields === 'function') {
          this.$refs.inputsForm.resetFields();
        }
      } catch (error) {
        console.warn('NodeConfigPanel: Could not clear inputs form validation:', error);
      }
    }
  }
};
</script>

<style scoped>
.node-config-panel {
  padding: 16px;
  background: #fff;
}

.section-title {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.metadata-section,
.config-section,
.inputs-section {
  margin-bottom: 24px;
}

.metadata-section:last-child,
.config-section:last-child,
.inputs-section:last-child {
  margin-bottom: 0;
}

.node-config-panel >>> .ant-form-item {
  margin-bottom: 16px;
}

.node-config-panel >>> .ant-form-item-label {
  font-weight: 500;
}

.node-config-panel >>> .dynamic-form {
  padding: 0;
}
</style> 