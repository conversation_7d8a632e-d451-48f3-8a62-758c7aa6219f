<template>
  <div class="node-task-info-panel">
    <!-- 任务基本信息 -->
    <a-descriptions 
      :title="taskInfo ? `任务信息 (${taskInfo.id})` : '暂无任务信息'"
      bordered
      size="small"
      :column="2"
    >
      <a-descriptions-item label="任务状态" :span="1">
        <a-tag :color="getStatusColor(taskInfo?.status)">
          {{ getStatusLabel(taskInfo?.status) }}
        </a-tag>
      </a-descriptions-item>
      
      <a-descriptions-item label="节点ID" :span="1">
        {{ taskInfo?.nodeId || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="会话ID" :span="1">
        {{ taskInfo?.sessionId || '-' }}
      </a-descriptions-item>
      
      <a-descriptions-item label="创建时间" :span="1">
        {{ formatDateTime(taskInfo?.createdAt) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="更新时间" :span="2">
        {{ formatDateTime(taskInfo?.updatedAt) }}
      </a-descriptions-item>
      
      <a-descriptions-item v-if="taskInfo?.message" label="任务消息" :span="2">
        <div class="task-message">
          {{ taskInfo.message }}
        </div>
      </a-descriptions-item>
      
      <a-descriptions-item v-if="taskInfo?.resultResourceId" label="结果资源ID" :span="2">
        <a-tag color="blue">{{ taskInfo.resultResourceId }}</a-tag>
      </a-descriptions-item>
      
      <a-descriptions-item v-if="taskInfo?.isUpstreamChanged !== undefined" label="上游变更" :span="1">
        <a-tag :color="taskInfo.isUpstreamChanged ? 'orange' : 'green'">
          {{ taskInfo.isUpstreamChanged ? '是' : '否' }}
        </a-tag>
      </a-descriptions-item>
    </a-descriptions>

    <!-- 任务进度（仅在运行中显示） -->
    <div v-if="isTaskRunning" class="task-progress-section">
      <h4>任务进度</h4>
      <a-progress 
        :percent="taskProgress" 
        :status="getProgressStatus()"
        :show-info="true"
      />
      <div v-if="taskInfo?.message" class="progress-message">
        {{ taskInfo.message }}
      </div>
    </div>

    <!-- 错误信息展示 -->
    <div v-if="isTaskError" class="task-error-section">
      <h4>错误信息</h4>
      <a-alert
        :message="taskInfo?.message || '任务执行出现错误'"
        type="error"
        show-icon
        banner
      />
    </div>

    <!-- 操作按钮区域 -->
    <div class="task-actions" v-if="taskInfo">
      <a-button 
        v-if="canCancelTask"
        type="danger"
        :loading="cancellingTask"
        @click="handleCancelTask"
      >
        取消任务
      </a-button>
      
      <a-button 
        v-if="isTaskCompleted"
        type="primary" 
        @click="handleViewResult"
      >
        查看结果
      </a-button>
    </div>
  </div>
</template>

<script>
import { TaskStatus } from '@/pages/dpe-web/models/TaskInfo';

/**
 * 任务信息展示面板组件
 * @description 用于显示节点任务的实时状态、进度、消息和日志信息，支持取消任务操作。纯展示组件，数据由父组件提供。
 * @component NodeTaskInfoPanel
 * @example
 * <NodeTaskInfoPanel
 *   :task-info="taskInfo"
 *   :session-id="sessionId"
 *   :node-id="nodeId"
 *   :callbacks="callbacks"
 * />
 * @since 1.0.0
 */
export default {
  name: 'NodeTaskInfoPanel',

  /**
   * 组件属性定义
   * @property {Object} taskInfo - 任务信息对象，TaskInfo 实例
   * @property {string} taskInfo.id - 任务ID
   * @property {string} taskInfo.sessionId - 会话ID
   * @property {string} taskInfo.nodeId - 节点ID
   * @property {string} taskInfo.status - 任务状态
   * @property {string} taskInfo.message - 任务消息
   * @property {Date} taskInfo.createdAt - 创建时间
   * @property {Date} taskInfo.updatedAt - 更新时间
   * @property {string} taskInfo.resultResourceId - 结果资源ID
   * @property {Object} taskInfo.result - 结果数据
   * @property {boolean} taskInfo.isUpstreamChanged - 上游是否发生变更
   * @property {string} sessionId - 当前会话的唯一标识符
   * @property {string} nodeId - 当前节点的唯一标识符
   * @property {Object} callbacks - 回调函数集合
   * @property {Function} callbacks.cancelTask - 取消任务的异步回调函数
   */
  props: {
    /**
     * 任务信息对象
     * @type {Object}
     * @description TaskInfo 实例，包含任务的状态、进度、消息和结果资源ID等信息
     */
    taskInfo: {
      type: Object,
      default: null
    },

    /**
     * 会话ID
     * @type {string}
     * @description 当前会话的唯一标识符，用于传递给 cancelTask 回调函数
     */
    sessionId: {
      type: String,
      required: true
    },

    /**
     * 节点ID
     * @type {string}
     * @description 当前节点的唯一标识符，可能用于日志或特定任务操作
     */
    nodeId: {
      type: String,
      required: true
    },

    /**
     * 回调函数集合
     * @type {Object}
     * @description 包含 cancelTask 异步回调函数
     */
    callbacks: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      cancellingTask: false, // 取消任务的加载状态
      taskProgress: 50 // 默认进度，实际项目中可能从任务消息中解析
    };
  },

  computed: {
    /**
     * 判断任务是否正在运行
     * @computed isTaskRunning
     * @returns {boolean} 任务是否处于运行状态
     */
    isTaskRunning() {
      return this.taskInfo?.status === TaskStatus.RUNNING;
    },

    /**
     * 判断任务是否出错
     * @computed isTaskError
     * @returns {boolean} 任务是否处于错误状态
     */
    isTaskError() {
      return this.taskInfo?.status === TaskStatus.ERROR;
    },

    /**
     * 判断任务是否已完成
     * @computed isTaskCompleted
     * @returns {boolean} 任务是否成功完成
     */
    isTaskCompleted() {
      return this.taskInfo?.status === TaskStatus.SUCCESS;
    },

    /**
     * 判断是否可以取消任务
     * @computed canCancelTask
     * @returns {boolean} 任务是否可以被取消
     */
    canCancelTask() {
      const cancellableStatuses = [TaskStatus.CREATED, TaskStatus.PENDING, TaskStatus.RUNNING];
      return this.taskInfo && cancellableStatuses.includes(this.taskInfo.status);
    }
  },

  methods: {
    /**
     * 获取任务状态对应的颜色
     * @method getStatusColor
     * @param {string} status - 任务状态
     * @returns {string} 对应的颜色值
     */
    getStatusColor(status) {
      const colorMap = {
        [TaskStatus.CREATED]: 'default',
        [TaskStatus.PENDING]: 'processing',
        [TaskStatus.RUNNING]: 'processing',
        [TaskStatus.SUCCESS]: 'success',
        [TaskStatus.ERROR]: 'error',
        [TaskStatus.CANCELLED]: 'warning',
        [TaskStatus.UNKNOWN]: 'default'
      };
      return colorMap[status] || 'default';
    },

    /**
     * 获取任务状态的显示标签
     * @method getStatusLabel
     * @param {string} status - 任务状态
     * @returns {string} 对应的显示文本
     */
    getStatusLabel(status) {
      const labelMap = {
        [TaskStatus.CREATED]: '已创建',
        [TaskStatus.PENDING]: '等待中',
        [TaskStatus.RUNNING]: '运行中',
        [TaskStatus.SUCCESS]: '成功',
        [TaskStatus.ERROR]: '失败',
        [TaskStatus.CANCELLED]: '已取消',
        [TaskStatus.UNKNOWN]: '未知'
      };
      return labelMap[status] || '未知';
    },

    /**
     * 获取进度条状态
     * @method getProgressStatus
     * @returns {string} 进度条状态
     */
    getProgressStatus() {
      if (this.isTaskError) return 'exception';
      if (this.isTaskCompleted) return 'success';
      return 'active';
    },

    /**
     * 格式化日期时间
     * @method formatDateTime
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的日期时间字符串
     */
    formatDateTime(date) {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      if (!(date instanceof Date) || isNaN(date.getTime())) {
        return '-';
      }
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    /**
     * 处理取消任务
     * @method handleCancelTask
     * @async
     */
    async handleCancelTask() {
      if (!this.taskInfo || !this.callbacks.cancelTask) {
        this.$message.warning('无法取消任务：缺少必要信息或回调函数');
        return;
      }

      try {
        this.cancellingTask = true;
        const success = await this.callbacks.cancelTask(this.taskInfo.id);
        
        if (success) {
          this.$message.success('任务取消成功');
        } else {
          this.$message.error('任务取消失败');
        }
      } catch (error) {
        console.error('取消任务时发生错误:', error);
        this.$message.error(`取消任务失败: ${error.message || '未知错误'}`);
      } finally {
        this.cancellingTask = false;
      }
    },

    /**
     * 处理查看结果
     * @method handleViewResult
     */
    handleViewResult() {
      // 触发事件让父组件切换到数据预览标签页
      this.$emit('view-result', this.taskInfo.resultResourceId);
    }
  }
};
</script>

<style scoped>
.node-task-info-panel {
  padding: 16px;
}

.task-progress-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.task-progress-section h4 {
  margin-bottom: 12px;
  color: #262626;
}

.progress-message {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.task-error-section {
  margin-top: 24px;
}

.task-error-section h4 {
  margin-bottom: 12px;
  color: #262626;
}

.task-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-start;
}

.task-message {
  word-break: break-all;
  white-space: pre-wrap;
}
</style> 