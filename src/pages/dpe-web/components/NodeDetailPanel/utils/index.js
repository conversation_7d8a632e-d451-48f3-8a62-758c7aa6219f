/**
 * NodeDetailPanel 组件工具函数库
 * @description 提供 NodeDetailPanel 组件系统使用的通用工具函数
 */

import { validateNodeFormDataStructure, createDefaultNodeFormData } from '../models';

// 重新导出 models 中的函数，方便组件统一导入
export { validateNodeFormDataStructure, createDefaultNodeFormData };

/**
 * 任务状态枚举
 * @readonly
 * @enum {string}
 */
export const TASK_STATUS = {
  CREATED: 'CREATED',
  PENDING: 'PENDING', 
  RUNNING: 'RUNNING',
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
  CANCELLED: 'CANCELLED',
  UNKNOWN: 'UNKNOWN'
};

/**
 * 任务状态显示映射
 * @readonly
 * @type {Object.<string, string>}
 */
export const TASK_STATUS_DISPLAY = {
  [TASK_STATUS.CREATED]: '已创建',
  [TASK_STATUS.PENDING]: '等待中',
  [TASK_STATUS.RUNNING]: '运行中',
  [TASK_STATUS.SUCCESS]: '成功',
  [TASK_STATUS.ERROR]: '失败',
  [TASK_STATUS.CANCELLED]: '已取消',
  [TASK_STATUS.UNKNOWN]: '未知'
};

/**
 * 任务状态颜色映射（Ant Design Vue 的状态颜色）
 * @readonly
 * @type {Object.<string, string>}
 */
export const TASK_STATUS_COLOR = {
  [TASK_STATUS.CREATED]: 'default',
  [TASK_STATUS.PENDING]: 'processing',
  [TASK_STATUS.RUNNING]: 'processing',
  [TASK_STATUS.SUCCESS]: 'success',
  [TASK_STATUS.ERROR]: 'error',
  [TASK_STATUS.CANCELLED]: 'warning',
  [TASK_STATUS.UNKNOWN]: 'default'
};

/**
 * 验证 NodeFormData 的业务规则
 * @param {import('../models').NodeFormData} formData - 节点表单数据
 * @param {import('../../models/OperatorInfo').OperatorInfo} operatorInfo - 算子信息
 * @returns {import('../models').ValidationResult} 验证结果
 * @example
 * const result = validateNodeFormData(formData, operatorInfo);
 * if (!result.valid) {
 *   console.error('业务验证失败:', result.message);
 * }
 */
export const validateNodeFormData = (formData, operatorInfo) => {
  // 首先进行基础结构验证
  const structureValidation = validateNodeFormDataStructure(formData);
  if (!structureValidation.valid) {
    return structureValidation;
  }

  // 验证必需的元数据字段
  if (!formData.metadata.displayName || formData.metadata.displayName.trim() === '') {
    return { valid: false, message: '节点显示名称是必需的' };
  }

  // 验证算子配置
  if (operatorInfo && operatorInfo.opConfigMetadata) {
    for (const configMeta of operatorInfo.opConfigMetadata) {
      const { key, schema } = configMeta;
      
      // 检查必需的配置项
      if (schema.required && (!(key in formData.opConfigs) || formData.opConfigs[key] === null || formData.opConfigs[key] === undefined)) {
        return { valid: false, message: `配置项 "${key}" 是必需的` };
      }

      // 类型验证
      if (key in formData.opConfigs) {
        const value = formData.opConfigs[key];
        const expectedType = schema.type;
        
        if (expectedType && !validateConfigValueType(value, expectedType)) {
          return { 
            valid: false, 
            message: `配置项 "${key}" 的类型不正确，期望: ${expectedType}，实际: ${typeof value}` 
          };
        }
      }
    }
  }

  // 验证输入字段配置
  if (operatorInfo && operatorInfo.inputFields) {
    for (const inputField of operatorInfo.inputFields) {
      if (!(inputField in formData.opInputs)) {
        return { 
          valid: false, 
          message: `缺少输入字段配置: "${inputField}"` 
        };
      }
    }
  }

  return { valid: true, message: '业务验证通过' };
};

/**
 * 验证配置值的类型
 * @param {any} value - 配置值
 * @param {string} expectedType - 期望的类型 ('string', 'number', 'boolean', 'array', 'object')
 * @returns {boolean} 类型是否匹配
 */
const validateConfigValueType = (value, expectedType) => {
  switch (expectedType) {
    case 'string':
      return typeof value === 'string';
    case 'number':
      return typeof value === 'number' && !isNaN(value);
    case 'boolean':
      return typeof value === 'boolean';
    case 'array':
      return Array.isArray(value);
    case 'object':
      return value !== null && typeof value === 'object' && !Array.isArray(value);
    default:
      return true; // 未知类型允许通过
  }
};

/**
 * 格式化任务状态显示文本
 * @param {string} status - 任务状态
 * @returns {string} 格式化后的状态显示文本
 * @example
 * const displayText = formatTaskStatus('RUNNING');
 * console.log(displayText); // '运行中'
 */
export const formatTaskStatus = (status) => {
  return TASK_STATUS_DISPLAY[status] || status || '未知';
};

/**
 * 获取任务状态对应的颜色
 * @param {string} status - 任务状态
 * @returns {string} Ant Design Vue 的状态颜色
 * @example
 * const color = getTaskStatusColor('SUCCESS');
 * console.log(color); // 'success'
 */
export const getTaskStatusColor = (status) => {
  return TASK_STATUS_COLOR[status] || 'default';
};

/**
 * 格式化日期时间
 * @param {string|Date} dateTime - 日期时间字符串或Date对象
 * @param {Object} [options={}] - 格式化选项
 * @param {boolean} [options.showSeconds=true] - 是否显示秒
 * @param {boolean} [options.showDate=true] - 是否显示日期
 * @returns {string} 格式化后的日期时间字符串
 * @example
 * const formatted = formatDateTime('2023-12-01T10:30:45Z');
 * console.log(formatted); // '2023-12-01 10:30:45'
 * 
 * const timeOnly = formatDateTime(new Date(), { showDate: false });
 * console.log(timeOnly); // '10:30:45'
 */
export const formatDateTime = (dateTime, options = {}) => {
  const { showSeconds = true, showDate = true } = options;
  
  if (!dateTime) {
    return '';
  }

  try {
    const date = dateTime instanceof Date ? dateTime : new Date(dateTime);
    
    if (isNaN(date.getTime())) {
      return String(dateTime);
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    let result = '';
    
    if (showDate) {
      result += `${year}-${month}-${day}`;
    }
    
    // 如果显示日期且需要添加时间部分，添加空格分隔
    if (showDate) {
      result += ' ';
    }
    
    result += `${hours}:${minutes}`;
    
    if (showSeconds) {
      result += `:${seconds}`;
    }

    return result;
  } catch (error) {
    console.warn('日期格式化失败:', error);
    return String(dateTime);
  }
};

/**
 * 创建标准化的错误消息
 * @param {string} operation - 操作名称
 * @param {Error|string} error - 错误对象或错误消息
 * @param {Object} [context={}] - 错误上下文信息
 * @returns {string} 标准化的错误消息
 * @example
 * const errorMsg = createErrorMessage('保存节点配置', new Error('网络错误'), { nodeId: 'node-123' });
 * console.log(errorMsg); // '保存节点配置失败: 网络错误'
 */
export const createErrorMessage = (operation, error, context = {}) => {
  let message = `${operation}失败`;
  
  if (error) {
    const errorText = error instanceof Error ? error.message : String(error);
    message += `: ${errorText}`;
  }

  // 添加上下文信息（用于调试）
  if (Object.keys(context).length > 0) {
    const contextStr = Object.entries(context)
      .map(([key, value]) => `${key}=${value}`)
      .join(', ');
    console.error(`${message} [上下文: ${contextStr}]`);
  }

  return message;
};

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 * @example
 * const original = { a: 1, b: { c: 2 } };
 * const cloned = deepClone(original);
 * cloned.b.c = 3;
 * console.log(original.b.c); // 2 (原对象未被修改)
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }

  if (typeof obj === 'object') {
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }

  return obj;
};

/**
 * 检查两个对象是否深度相等
 * @param {any} obj1 - 第一个对象
 * @param {any} obj2 - 第二个对象
 * @returns {boolean} 是否相等
 * @example
 * const equal = isDeepEqual({ a: 1, b: { c: 2 } }, { a: 1, b: { c: 2 } });
 * console.log(equal); // true
 */
export const isDeepEqual = (obj1, obj2) => {
  if (obj1 === obj2) {
    return true;
  }

  if (obj1 === null || obj2 === null) {
    return obj1 === obj2;
  }

  if (typeof obj1 !== typeof obj2) {
    return false;
  }

  if (typeof obj1 !== 'object') {
    return obj1 === obj2;
  }

  if (Array.isArray(obj1) !== Array.isArray(obj2)) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!keys2.includes(key)) {
      return false;
    }

    if (!isDeepEqual(obj1[key], obj2[key])) {
      return false;
    }
  }

  return true;
};

/**
 * 安全地获取嵌套对象属性
 * @param {Object} obj - 目标对象
 * @param {string} path - 属性路径，用点分隔
 * @param {any} [defaultValue] - 默认值
 * @returns {any} 属性值或默认值
 * @example
 * const obj = { a: { b: { c: 42 } } };
 * const value = safeGet(obj, 'a.b.c', 0);
 * console.log(value); // 42
 * 
 * const missing = safeGet(obj, 'a.x.y', 'not found');
 * console.log(missing); // 'not found'
 */
export const safeGet = (obj, path, defaultValue) => {
  if (!obj || typeof obj !== 'object') {
    return defaultValue;
  }

  const keys = path.split('.');
  let current = obj;

  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return defaultValue;
    }
    current = current[key];
  }

  return current;
}; 