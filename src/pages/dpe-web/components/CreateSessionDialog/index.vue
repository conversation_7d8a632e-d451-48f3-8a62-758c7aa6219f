<template>
  <a-modal
    :title="'创建会话'"
    :visible="internalVisible"
    :width="800"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="form"
      :form="form"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <!-- 基本信息 -->
      <a-form-item label="会话名称">
        <a-input
          v-decorator="[
            'name',
            {
              rules: [
                { required: true, message: '请输入会话名称' },
                { min: 2, max: 50, message: '会话名称长度应在2-50个字符之间' }
              ]
            }
          ]"
          placeholder="请输入会话名称"
          data-testid="session-name-input"
        />
      </a-form-item>

      <a-form-item label="会话描述">
        <a-textarea
          v-decorator="[
            'description',
            {
              rules: [
                { max: 500, message: '描述长度不能超过500个字符' }
              ]
            }
          ]"
          placeholder="请输入会话描述（可选）"
          :rows="3"
          data-testid="session-description-input"
        />
      </a-form-item>

      <!-- 执行图选择 -->
      <a-form-item label="执行图">
        <a-select
          v-decorator="[
            'executionGraphId',
            {
              rules: []
            }
          ]"
          placeholder="请选择执行图"
          data-testid="execution-graph-select"
        >
          <a-select-option
            v-for="graph in executionGraphOptions"
            :key="graph.id"
            :value="graph.id"
          >
            {{ graph.metadata.displayName || graph.id }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 后端类型选择 -->
      <a-form-item label="后端类型">
        <a-select
          v-decorator="[
            'backendType',
            {
              rules: [
                { required: true, message: '请选择后端类型' }
              ]
            }
          ]"
          placeholder="请选择后端类型"
          data-testid="backend-type-select"
          @change="handleBackendTypeChange"
        >
          <a-select-option
            v-for="backend in backendTypes"
            :key="backend.type"
            :value="backend.type"
          >
            {{ backend.metadata.displayName || backend.type }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 动态会话配置 -->
      <a-form-item
        v-if="sessionConfigFields.length > 0"
        label="会话配置"
        :wrapper-col="{ span: 24 }"
      >
        <DynamicForm
          ref="dynamicForm"
          :config-fields="sessionConfigFields"
          :value="sessionConfig"
          @input="handleSessionConfigChange"
          data-testid="session-config-form"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import DynamicForm from '@/pages/dpe-web/components/DynamicForm';
import dataProcessService from '@/pages/dpe-web/services/DataProcessService';
import { BackendInfo } from '@/pages/dpe-web/models';

/**
 * 创建会话对话框组件
 * @component CreateSessionDialog
 * @description 用于创建新会话的对话框组件。提供了一个表单界面，允许用户输入会话名称、描述，选择执行图和后端类型，并支持动态会话配置。组件内置了表单验证和错误提示功能。
 * @example
 * <CreateSessionDialog
 *   :visible="isDialogVisible"
 *   :execution-graph-options="myExecutionGraphs"
 *   :session-configs="initialSessionConfigs"
 *   @submit="handleSessionSubmit"
 *   @cancel="handleDialogCancel"
 *   @visibility-change="handleVisibilityChange"
 * />
 * @since 1.0.0
 */
export default {
  name: 'CreateSessionDialog',

  components: {
    DynamicForm
  },

  /**
   * 组件属性定义
   * @property {boolean} visible - 对话框是否可见
   * @property {Array<Object>} executionGraphOptions - 执行图选项列表，每个元素应包含id和metadata.displayName
   * @property {Object} sessionConfigs - 预设的会话配置对象，会作为formData.sessionConfig的初始值
   */
  props: {
    /**
     * 对话框是否可见
     * @type {boolean}
     * @default false
     */
    visible: {
      type: Boolean,
      default: false
    },

    /**
     * 执行图选项列表
     * @type {Array<Object>}
     * @default []
     */
    executionGraphOptions: {
      type: Array,
      default: () => []
    },

    /**
     * 预设的会话配置对象
     * @type {Object}
     * @default {}
     */
    sessionConfigs: {
      type: Object,
      default: () => ({})
    }
  },

  /**
   * 组件数据
   * @returns {Object} 组件响应式数据
   */
  data() {
    return {
      /**
       * 表单实例
       * @type {Object}
       */
      form: this.$form.createForm(this),

      /**
       * 内部控制的可见状态
       * @type {boolean}
       */
      internalVisible: false,

      /**
       * 加载状态
       * @type {boolean}
       */
      loading: false,

      /**
       * 后端类型列表
       * @type {Array<BackendInfo>}
       */
      backendTypes: [],

      /**
       * 会话配置字段
       * @type {Array<ConfigField>}
       */
      sessionConfigFields: [],

      /**
       * 会话配置数据
       * @type {Object}
       */
      sessionConfig: {}
    };
  },

  /**
   * 监听器定义
   */
  watch: {
    /**
     * 监听visible属性变化
     * @param {boolean} newValue - 新的可见状态
     */
    visible: {
      handler(newValue) {
        this.internalVisible = newValue;
        if (newValue) {
          this.initializeDialog();
        }
      },
      immediate: true
    },

    /**
     * 监听sessionConfigs属性变化
     * @param {Object} newValue - 新的会话配置
     */
    sessionConfigs: {
      handler(newValue) {
        this.sessionConfig = { ...newValue };
      },
      deep: true,
      immediate: true
    }
  },

  /**
   * 组件创建时初始化
   */
  created() {
    this.loadBackendTypes();
  },

  methods: {
    /**
     * 显示对话框
     * @method show
     * @public
     * @param {Object} [graphInfo] - 可选的执行图信息，用于预填充表单
     * @example
     * this.$refs.createSessionDialog.show({ id: 'graph123' });
     */
    show(graphInfo) {
      this.internalVisible = true;
      this.emitVisibilityChange(true);
      
      if (graphInfo && graphInfo.id) {
        this.$nextTick(() => {
          this.form.setFieldsValue({
            executionGraphId: graphInfo.id
          });
        });
      }
    },

    /**
     * 隐藏对话框
     * @method hide
     * @public
     * @example
     * this.$refs.createSessionDialog.hide();
     */
    hide() {
      this.internalVisible = false;
      this.emitVisibilityChange(false);
    },

    /**
     * 初始化对话框
     * @method initializeDialog
     * @private
     */
    initializeDialog() {
      this.form.resetFields();
      this.sessionConfigFields = [];
      this.sessionConfig = { ...this.sessionConfigs };
    },

    /**
     * 加载后端类型
     * @method loadBackendTypes
     * @private
     * @async
     */
    async loadBackendTypes() {
      try {
        this.backendTypes = await dataProcessService.getBackendTypes();
      } catch (error) {
        console.error('加载后端类型失败:', error);
        this.$message.error('加载后端类型失败，请重试');
      }
    },

    /**
     * 处理后端类型变化
     * @method handleBackendTypeChange
     * @private
     * @param {string} backendType - 选中的后端类型
     */
    handleBackendTypeChange(backendType) {
      const selectedBackend = this.backendTypes.find(backend => backend.type === backendType);
      if (selectedBackend && selectedBackend.sessionConfigs) {
        // sessionConfigs已经是ConfigField对象数组，直接使用
        this.sessionConfigFields = selectedBackend.sessionConfigs;
      } else {
        this.sessionConfigFields = [];
      }
      
      // 重置会话配置
      this.sessionConfig = { ...this.sessionConfigs };
    },

    /**
     * 处理会话配置变化
     * @method handleSessionConfigChange
     * @private
     * @param {Object} config - 新的会话配置
     */
    handleSessionConfigChange(config) {
      this.sessionConfig = config;
    },

    /**
     * 处理表单提交
     * @method handleSubmit
     * @private
     * @async
     * @fires submit
     */
    async handleSubmit() {
      try {
        this.loading = true;
        
        // 验证表单
        const values = await new Promise((resolve, reject) => {
          this.form.validateFields((err, values) => {
            if (err) {
              reject(err);
            } else {
              resolve(values);
            }
          });
        });
        
        // 验证动态表单
        if (this.$refs.dynamicForm) {
          const configValid = await this.$refs.dynamicForm.validateForm();
          if (!configValid) {
            this.$message.error('请检查会话配置');
            return;
          }
        }
        
        // 创建会话
        let session;
        if (values.executionGraphId) {
           session  = await dataProcessService.createSessionFromGraph(
            values.executionGraphId,
            values.backendType,
            {
              name: values.name,
              description: values.description,
              sessionConfig: this.sessionConfig
            }
          );
        } else {
          session = await dataProcessService.createSession(
            values.backendType,
            {
              name: values.name,
              description: values.description,
              sessionConfig: this.sessionConfig
            }
          );
        }

        /**
         * 会话创建成功事件
         * @event submit
         * @type {Object}
         * @property {Object} session - 创建的会话对象
         * @property {Object} formData - 表单数据
         */
        this.$emit('submit', {
          session,
          formData: {
            ...values,
            sessionConfig: this.sessionConfig
          }
        });

        this.hide();
        this.$message.success('会话创建成功');
        
      } catch (error) {
        // 如果是表单验证错误，只显示验证错误，不显示创建失败的消息
        if (error && error.errorFields) {
          // 表单验证失败时不显示错误消息，因为ant-design会显示字段级错误
          return;
        }
        
        console.error('创建会话失败:', error);
        this.$message.error(error.message || '创建会话失败，请重试');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 处理取消操作
     * @method handleCancel
     * @private
     * @fires cancel
     */
    handleCancel() {
      /**
       * 对话框取消事件
       * @event cancel
       */
      this.$emit('cancel');
      this.hide();
    },

    /**
     * 发出可见性变化事件
     * @method emitVisibilityChange
     * @private
     * @param {boolean} visible - 可见状态
     * @fires visibility-change
     */
    emitVisibilityChange(visible) {
      /**
       * 对话框可见性变化事件
       * @event visibility-change
       * @type {boolean} 对话框的当前可见状态
       */
      this.$emit('visibility-change', visible);
    }
  }
};
</script>

<style scoped>
.ant-modal {
  top: 20px;
}

.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}
</style>
