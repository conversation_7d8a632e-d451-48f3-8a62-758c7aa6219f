<template>
  <div class="graph-editor">
    <!-- 图形容器 -->
    <div class="graph-container" ref="graphContainer"></div>
    
    <!-- 小地图容器 -->
    <div v-if="showMinimap" class="minimap-container" ref="minimapContainer"></div>
    
    <!-- 工具栏 -->
    <div v-if="showToolbar" class="graph-toolbar">
      <div class="zoom-controls">
        <div class="zoom-text">{{ Math.round(zoomLevel * 100) }}%</div>
        <a-button-group class="zoom-buttons">
          <a-button size="small" @click="zoomOut" title="缩小">
            <a-icon type="minus" />
          </a-button>
          <a-button size="small" @click="zoomIn" title="放大">
            <a-icon type="plus" />
          </a-button>
          <a-button size="small" @click="fitView" title="适应画布">
            <a-icon type="border" />
          </a-button>
          <a-button size="small" @click="undo" title="撤销" :disabled="!getCanUndo()">
            <a-icon type="undo" />
          </a-button>
          <a-button size="small" @click="redo" title="重做" :disabled="!getCanRedo()">
            <a-icon type="redo" />
          </a-button>
        </a-button-group>
      </div>
    </div>
  </div>
</template>

<script>
import { Graph } from '@antv/x6';
import { History } from '@antv/x6-plugin-history';
import RichExecutionGraph, { RichGraphNode, RichGraphEdge } from '@/pages/dpe-web/models/RichGraph';
import { createGraphConfig } from './utils/config.js';
import { wouldFormCycle } from './utils/graph-utils.js';

// =============================================================================
// 常量定义
// =============================================================================

/** 组件初始化重试配置 */
const RETRY_CONFIG = {
  MAX_RETRY_COUNT: 10,
  INITIAL_DELAY: 200,
  DEFAULT_WIDTH: 800,
  DEFAULT_HEIGHT: 600
};

/** 缩放配置 */
const ZOOM_CONFIG = {
  STEP: 0.1,
  FIT_PADDING: 20,
  MAX_SCALE: 1
};

/** 延迟配置 */
const TIMING_CONFIG = {
  INITIALIZATION_DELAY: 2000,
  LOADING_CHECK_INTERVAL: 100,
  CLEANUP_DELAY: 100
};

/**
 * 图形编辑器组件
 * 基于@antv/x6实现的通用图形编辑器，支持富执行图数据模型
 * @displayName GraphEditor
 */
export default {
  name: 'GraphEditor',
  
  // =============================================================================
  // 组件属性定义
  // =============================================================================
  
  props: {
    /**
     * 富执行图数据
     * @type {RichExecutionGraph}
     */
    graphData: {
      type: Object,
      default: function() {
        return null;
      },
      validator: function(value) {
        return value === null || value instanceof RichExecutionGraph;
      }
    },
    
    /**
     * 初始的富执行图数据，用于重置操作
     * @type {RichExecutionGraph}
     */
    initialGraphData: {
      type: Object,
      default: function() {
        return null;
      },
      validator: function(value) {
        return value === null || value instanceof RichExecutionGraph;
      }
    },
    
    /**
     * 是否只读模式
     * @type {Boolean}
     */
    readonly: {
      type: Boolean,
      default: false
    },
    
    /**
     * 是否显示小地图
     * @type {Boolean}
     */
    showMinimap: {
      type: Boolean,
      default: true
    },
    
    /**
     * 是否显示工具栏
     * @type {Boolean}
     */
    showToolbar: {
      type: Boolean,
      default: true
    },
    
    /**
     * 图形编辑器配置
     * @type {Object}
     */
    config: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  
  // =============================================================================
  // 组件数据定义
  // =============================================================================
  
  data: function() {
    return {
      /** 
       * X6图形实例
       * @type {Graph}
       * @private
       */
      graph: null,
      
      /** 
       * 当前缩放级别
       * @type {Number}
       * @private
       */
      zoomLevel: 1,
      
      /** 
       * 当前选中的单元格
       * @type {import('@antv/x6').Cell}
       * @private
       */
      selectedCell: null,
      
      /** 
       * 当前的富执行图数据
       * @type {RichExecutionGraph}
       * @private
       */
      currentGraphData: null,
      
      /** 
       * 初始化状态标识
       * @type {Boolean}
       * @private
       */
      isInitialized: false,
      
      /** 
       * 重试计数器
       * @type {Number}
       * @private
       */
      retryCount: 0,
      
      /** 
       * 数据加载状态标识，防止循环加载
       * @type {Boolean}
       * @private
       */
      isLoadingData: false
    };
  },
  
  computed: {
    
  },
  
  // =============================================================================
  // 组件生命周期
  // =============================================================================
  
  /**
   * Vue组件挂载后执行的生命周期钩子
   * @private
   */
  mounted: function() {
    console.log('GraphEditor mounted, 开始初始化');
    this.$nextTick(() => {
      console.log('GraphEditor $nextTick triggered');
      this._initializeGraphWithRetry();
    });
  },
  
  /**
   * Vue组件销毁前执行的生命周期钩子
   * @private
   */
  beforeDestroy: function() {
    this._cleanupResources();
  },
  
  // =============================================================================
  // 数据监听器
  // =============================================================================
  
  watch: {
    graphData: {
      handler: function(newData, oldData) {
        this._handleGraphDataChange(newData, oldData);
      },
      deep: true,
      immediate: true
    },
    
    readonly: function(newVal) {
      if (this.graph) {
        this._updateInteractionSettings();
      }
    },
    
    showMinimap: function(newVal) {
      if (this.graph) {
        this._setupMiniMapPlugin();
      }
    },
    
    showToolbar: function() {
      // 工具栏显示/隐藏通过v-if控制，无需额外处理
    }
  },
  
  methods: {
    
    // =============================================================================
    // 公开API - 历史记录管理
    // =============================================================================
    
    /**
     * 获取是否可以撤销状态
     * @returns {Boolean} 是否可以撤销
     * @private 仅供测试使用，请勿在业务代码中调用
     */
    getCanUndo: function() {
      return !!(this.graph && this.graph.isHistoryEnabled() && this.graph.canUndo());
    },
    
    /**
     * 获取是否可以重做状态
     * @returns {Boolean} 是否可以重做
     * @private 仅供测试使用，请勿在业务代码中调用
     */
    getCanRedo: function() {
      return !!(this.graph && this.graph.isHistoryEnabled() && this.graph.canRedo());
    },

    
    // =============================================================================
    // 测试用API - 历史记录管理
    // =============================================================================
    
    /**
     * 获取历史记录栈（模拟）
     * @returns {Array} 历史记录栈
     * @private 仅供测试使用，请勿在业务代码中调用
     */
    _getHistoryStack: function() {
      if (!this.graph || !this.graph.isHistoryEnabled() || !this.graph.getHistoryStackSize) return [];
      var size = this.graph.getHistoryStackSize();
      return new Array(size).fill({});
    },

    /**
     * 获取历史记录索引（模拟）
     * @returns {Number} 历史记录索引
     * @private 仅供测试使用，请勿在业务代码中调用
     */
    _getHistoryIndex: function() {
      if (!this.graph || !this.graph.isHistoryEnabled() || !this.graph.getUndoStackSize) return -1;
      return this.graph.getUndoStackSize();
    },

    // =============================================================================
    // 组件初始化相关方法
    // =============================================================================

    /**
     * 带重试的初始化图形编辑器
     * 在容器尺寸无效时会进行重试，直到成功或达到最大次数
     * @private
     */
    _initializeGraphWithRetry: function() {
      var self = this;
      var container = this.$refs.graphContainer;
      
      if (!this._validateContainer(container)) {
        return;
      }
      
      var containerSize = this._getContainerSize(container);
      
      if (this._shouldRetryInitialization(containerSize)) {
        this._scheduleRetry();
        return;
      }
      
      this._performInitialization(containerSize);
    },
    
    /**
     * 验证容器是否存在
     * @param {HTMLElement} container - 容器元素
     * @returns {Boolean} 是否有效
     * @private
     */
    _validateContainer: function(container) {
      if (!container) {
        console.error('图形容器未找到');
        return false;
      }
      return true;
    },
    
    /**
     * 获取容器尺寸
     * @param {HTMLElement} container - 容器元素
     * @returns {Object} 容器尺寸对象
     * @private
     */
    _getContainerSize: function(container) {
      return {
        width: container.offsetWidth || container.clientWidth,
        height: container.offsetHeight || container.clientHeight
      };
    },
    
    /**
     * 判断是否需要重试初始化
     * @param {Object} containerSize - 容器尺寸
     * @returns {Boolean} 是否需要重试
     * @private
     */
    _shouldRetryInitialization: function(containerSize) {
      if (containerSize.width === 0 || containerSize.height === 0) {
        this.retryCount++;
        if (this.retryCount < RETRY_CONFIG.MAX_RETRY_COUNT) {
          console.log('容器大小为0，等待重试...', { 
            width: containerSize.width, 
            height: containerSize.height, 
            retry: this.retryCount 
          });
          return true;
        } else {
          console.warn('容器大小始终为0，使用默认大小');
          return false;
        }
      }
      return false;
    },
    
    /**
     * 调度重试操作
     * @private
     */
    _scheduleRetry: function() {
      var self = this;
      setTimeout(() => {
        self._initializeGraphWithRetry();
      }, RETRY_CONFIG.INITIAL_DELAY * this.retryCount);
    },
    
    /**
     * 执行实际的初始化操作
     * @param {Object} containerSize - 容器尺寸
     * @private
     */
    _performInitialization: function(containerSize) {
      var width = containerSize.width || RETRY_CONFIG.DEFAULT_WIDTH;
      var height = containerSize.height || RETRY_CONFIG.DEFAULT_HEIGHT;
      
      try {
        this._initializeGraph(width, height);
        console.log('GraphEditor初始化成功', { width: width, height: height });
        
        // 加载初始数据
        if (this.graphData) {
          this.loadGraphData(this.graphData);
        }
      } catch (error) {
        console.error('GraphEditor初始化失败:', error);
        this.$emit('init-error', error);
      }
    },
    
    /**
     * 初始化图形编辑器实例、插件和事件监听。
     * @param {Number} width - 容器宽度
     * @param {Number} height - 容器高度
     * @private
     */
    _initializeGraph: function(width, height) {
      var self = this;
      var container = this.$refs.graphContainer;
      
      if (!container) {
        throw new Error('图形容器未找到');
      }
      
      console.log('开始初始化Graph', { width: width, height: height });
      
      // 检查@antv/x6是否正确加载
      if (typeof Graph !== 'function') {
        console.error('@antv/x6 Graph导入失败', { Graph: Graph });
        throw new Error('@antv/x6 Graph导入失败');
      }
      
      // 获取图形配置
      var finalConfig = createGraphConfig(container, width, height, this.config);
      
      // 创建图形实例
      this.graph = new Graph(finalConfig);
      
      // 设置事件监听
      this._setupEventListeners();
      
      // 设置插件
      this._setupPlugins();
      
      // 更新交互设置
      this._updateInteractionSettings();
      
      // 监听缩放变化
      this._setupZoomListeners();
      
      // 监听窗口大小变化
      this._setupResizeListener();
      
      // 设置初始化完成标志
      this.isInitialized = true;
      console.log('Graph初始化完成，设置isInitialized = true');
    },

    /**
     * 根据ID或cell对象查找富单元格（节点或边）
     * @param {string|import('@antv/x6').Cell} idOrCell - 单元格ID或X6单元格对象
     * @returns {RichGraphNode|RichGraphEdge|null} 查找到的富单元格，未找到则返回null
     * @private
     */
    _findRichCellById: function(idOrCell) {
      if (!this.currentGraphData || !idOrCell) {
        return null;
      }

      const id = typeof idOrCell === 'string' ? idOrCell : idOrCell.id;

      // 1. 优先在节点中查找
      if (this.currentGraphData.nodes && this.currentGraphData.nodes[id]) {
        return this.currentGraphData.nodes[id];
      }

      // 2. 如果是X6 Cell对象且是边，则在边中查找
      if (typeof idOrCell === 'object' && idOrCell.isEdge) {
        const sourceId = idOrCell.getSourceCellId();
        const targetId = idOrCell.getTargetCellId();
        if (this.currentGraphData.edges) {
          return this.currentGraphData.edges.find(edge => edge.source === sourceId && edge.target === targetId) || null;
        }
      }
      
      return null;
    },
    
    /**
     * 设置缩放相关事件监听器
     * @private
     */
    _setupZoomListeners: function() {
      var self = this;
      
      this.graph.on('scale', function(args) {
        self.zoomLevel = args.sx || args.scale || self.graph.zoom();
      });
      
      this.graph.on('transform', function(args) {
        if (args.options && args.options.sx) {
          self.zoomLevel = args.options.sx;
        }
      });
    },
    
    /**
     * 设置窗口大小变化监听器
     * @private
     */
    _setupResizeListener: function() {
      window.addEventListener('resize', this._handleResize);
    },
    
    /**
     * 统一设置所有图形事件的监听器
     * @private
     */
    _setupEventListeners: function() {
      this._setupCellClickListeners();
      this._setupGraphChangeListeners();
      this._setupHistoryListeners();
    },
    
    /**
     * 设置单元格点击事件监听器
     * @private
     */
    _setupCellClickListeners: function() {
      var self = this;
      
      // 监听单元格点击事件
      this.graph.on('cell:click', function(args) {
        console.debug('cell:click', args);
        var cell = args.cell;
        self.selectedCell = cell;

        // 传入整个cell对象，以便在查找边时获取source/target
        var richCell = self._findRichCellById(cell);
        if (!richCell) {
          console.warn('无法在currentGraphData中找到ID为 ' + cell.id + ' 的单元格');
          return;
        }
        
        if (cell.isNode()) {
          self.$emit('node-click', { node: richCell, event: args.e });
          self.$emit('node-select', { node: richCell, event: args.e });
        } else if (cell.isEdge()) {
          self.$emit('edge-click', { edge: richCell, event: args.e });
          self.$emit('edge-select', { edge: richCell, event: args.e });
        }
      });
      
      // 监听画布空白处点击事件
      this.graph.on('blank:click', function(args) {
        self.selectedCell = null;
        self.$emit('blank-click', { event: args.e });
        self.$emit('nodes-deselect');
      });
    },
    
    /**
     * 设置图形变化事件监听器
     * @private
     */
    _setupGraphChangeListeners: function() {
      var self = this;
      
      // 统一处理图形变化事件以更新 currentGraphData
      var onGraphChanged = function(args) {
        if (args.options && (args.options.skipHistory || args.options.silent)) {
            return;
        }
        // 使用插件后，历史记录自动处理，这里只需更新我们自己的数据模型
        self._updateCurrentGraphData();
        self.$emit('graph-change', { type: args.type, cell: args.cell });
      };

      this.graph.on('cell:added', onGraphChanged.bind(this, { type: 'cell:added' }));
      this.graph.on('cell:removed', onGraphChanged.bind(this, { type: 'cell:removed' }));
      this.graph.on('cell:changed', onGraphChanged.bind(this, { type: 'cell:changed' }));

      // 监听连接创建事件
      this.graph.on('edge:connected', function(args) {
        if (args.options && args.options.silent) return;

        self._updateCurrentGraphData();
        
        var x6edge = args.edge;
        var sourceId = x6edge.getSourceCellId();
        var targetId = x6edge.getTargetCellId();

        // 从X6边数据重构富边对象，确保payload格式正确
        var edgeData = x6edge.getData() || {};
        edgeData.source = sourceId;
        edgeData.target = targetId;
        var richEdge = new RichGraphEdge(edgeData);
        
        self.$emit('node-connect', { 
          edge: richEdge, 
          source: sourceId, 
          target: targetId 
        });
      });
      
      // 监听选择变化事件
      this.graph.on('selection:changed', function(args) {
        var mapToRichCells = (cells) => {
          return cells
            .map(cell => self._findRichCellById(cell)) // 传入整个cell对象
            .filter(Boolean); // 过滤掉未找到的单元格
        };

        self.$emit('selection-change', {
          added: mapToRichCells(args.added || []),
          removed: mapToRichCells(args.removed || []),
          selected: mapToRichCells(args.selected || [])
        });
      });
    },
    
    /**
     * 设置历史记录事件监听器
     * @private
     */
    _setupHistoryListeners: function() {
      var self = this;
      
      // 监听历史记录插件事件
      this.graph.on('history:change', function() {
        // 历史记录变化时的处理，但不再更新historyState
      });
      this.graph.on('history:undo', function() {
        self._updateCurrentGraphData();
      });
      this.graph.on('history:redo', function() {
        self._updateCurrentGraphData();
      });
    },
    
    /**
     * 设置X6插件，如小地图、选择、键盘等。
     * @private
     */
    _setupPlugins: function() {
      try {
        this._setupMiniMapPlugin();
        this._setupSelectionPlugin();
        this._setupKeyboardPlugin();
        this._setupHistoryPlugin();
      } catch (error) {
        console.warn('设置X6插件时出错，但不影响基本功能:', error);
      }
    },
    
    /**
     * 设置小地图插件。
     * @private
     */
    _setupMiniMapPlugin: function() {
      if (!this.showMinimap || !this.$refs.minimapContainer) {
        return;
      }
      
      try {
        var MiniMap = require('@antv/x6-plugin-minimap').MiniMap;
        
        if (this.miniMapInstance) {
          this.graph.removePlugin(this.miniMapInstance);
        }
        
        this.miniMapInstance = new MiniMap({
          container: this.$refs.minimapContainer,
          width: 200,
          height: 120,
          padding: 10,
        });
        
        this.graph.use(this.miniMapInstance);
      } catch (e) {
        console.warn('加载X6小地图插件失败，跳过', e);
      }
    },
    
    /**
     * 设置选择插件。
     * @private
     */
    _setupSelectionPlugin: function() {
      try {
        var Selection = require('@antv/x6-plugin-selection').Selection;
        this.graph.use(new Selection({
          enabled: true,
          multiple: true,
          rubberband: !this.readonly,
          movable: !this.readonly,
          showNodeSelectionBox: true,
          showEdgeSelectionBox: false,
          filter: function(cell) {
            return !cell.isEdge(); // 边不允许被选择移动
          }
        }));
      } catch (e) {
        console.warn('加载X6选择插件失败，跳过', e);
      }
    },
    
    /**
     * 设置键盘快捷键插件。
     * @private
     */
    _setupKeyboardPlugin: function() {
      if (this.readonly) {
        return;
      }
      
      try {
        var Keyboard = require('@antv/x6-plugin-keyboard').Keyboard;
        this.graph.use(new Keyboard({
          enabled: true,
          global: false
        }));
        
        var self = this;
        this.graph.bindKey(['del', 'backspace'], function() {
          if (self.selectedCell) {
            self.removeCell(self.selectedCell);
          }
        });
        
        this.graph.bindKey(['ctrl+z', 'cmd+z'], self.undo.bind(self));
        this.graph.bindKey(['ctrl+shift+z', 'cmd+shift+z'], self.redo.bind(self));

      } catch (e) {
        console.warn('加载X6键盘插件失败，跳过', e);
      }
    },
    
    /**
     * 设置历史记录插件。
     * @private
     */
    _setupHistoryPlugin: function() {
      if (this.readonly) {
        return;
      }
      try {
        this.graph.use(
          new History({
            enabled: true,
          })
        );
      } catch (e) {
        console.warn('加载X6历史记录插件失败，跳过', e);
      }
    },
    
    /**
     * 根据只读状态更新图的交互设置。
     * @private
     */
    _updateInteractionSettings: function() {
      if (!this.graph) return;
      
      this.graph.enablePanning(!this.readonly);
      this.graph.enableMouseWheel(true); 
      
      var selection = this.graph.getPlugin('selection');
      if (selection) {
        selection.options.rubberband = !this.readonly;
        selection.options.movable = !this.readonly;
      }
    },
    
    /**
     * 撤销上一步操作。
     * @returns {void}
     */
    undo: function() {
      if (!this.getCanUndo()) return;
      this.graph.undo();
      console.log('撤销操作完成', 'canUndo:', this.getCanUndo(), 'canRedo:', this.getCanRedo());
    },
    
    /**
     * 重做上一步撤销的操作。
     * @returns {void}
     */
    redo: function() {
      if (!this.getCanRedo()) return;
      this.graph.redo();
      console.log('重做操作完成', 'canUndo:', this.getCanUndo(), 'canRedo:', this.getCanRedo());
    },
    
    /**
     * 重置执行图为 `initialGraphData` 属性所定义的初始状态。
     * @returns {void}
     */
    reset: function() {
      console.log('开始重置图形...');
      var initialData = this.initialGraphData;
      
      if (!initialData || !(initialData instanceof RichExecutionGraph)) {
        console.warn('重置失败：未提供有效的初始数据 (initialGraphData)。将清空画布。');
        // 如果没有初始数据，则调用清空方法
        this.clearGraphData();
        return;
      }
      
      // 使用深拷贝来加载，避免修改原始的prop数据
      var resetData = new RichExecutionGraph(JSON.parse(JSON.stringify(initialData)));
      
      // 使用 loadGraphData 来加载数据，确保所有事件和状态都正确更新
      // 这会将加载操作计入历史记录，但我们稍后会清除它
      this.loadGraphData(resetData);
      
      var self = this;
      // 使用 nextTick 确保加载操作已基本完成
      this.$nextTick(function() {
        if (self.graph && self.graph.isHistoryEnabled()) {
          // 清空历史记录，使得"重置"操作不被记录
          self.graph.cleanHistory();
        }
        console.log('重置到初始状态完成');
      });
    },
    
    /**
     * 清空图形数据并重置历史记录。
     * @param {RichExecutionGraph} emptyGraph - 可选，空的富执行图数据。如不提供将创建默认空图。
     */
    clearGraphData: function(emptyGraph) {
      if (!this.graph) {
        console.warn('图形未初始化，无法清空数据');
        return;
      }

      var emptyState = emptyGraph || new RichExecutionGraph({
          id: 'empty-graph-' + Date.now(),
          metadata: { displayName: '空图', description: '清空后的空图' },
          nodes: {},
          edges: []
      });

      console.log('开始清空图形数据并重置历史记录');

      // 移除silent选项，确保图形状态正确更新
      this._loadGraphData(emptyState, { silent: false });
      
      // 使用setTimeout确保加载完成后再清空历史记录
      var self = this;
      this.$nextTick(function() {
        setTimeout(function() {
          if (self.graph.isHistoryEnabled()) {
            self.graph.cleanHistory();
          }
          
          console.log('清空图形数据完成，历史记录已重置', {
            nodeCount: self.graph.getNodes().length,
            edgeCount: self.graph.getEdges().length,
            canUndo: self.getCanUndo(),
            canRedo: self.getCanRedo()
          });
        }, 100);
      });
    },

    /**
     * 加载富执行图数据到画布。
     * @param {RichExecutionGraph} richGraph - 要加载的富执行图数据。
     */
    loadGraphData: function(richGraph) {
        this._loadGraphData(richGraph, { silent: false });
    },
    
    /**
     * 内部核心方法，用于加载图形数据到X6。
     * @param {RichExecutionGraph} richGraph - 富执行图数据。
     * @param {{silent: boolean}} options - 加载选项，silent为true时不保存历史记录。
     * @private
     */
    _loadGraphData: function(richGraph, options = { silent: false }) {
        if (!this.graph) {
            console.warn('加载图形数据失败：图形未初始化');
            return;
        }
        if (!richGraph || !(richGraph instanceof RichExecutionGraph)) {
            console.warn('无法加载图形数据：数据格式不正确');
            return;
        }

        console.log(`开始加载数据 (silent: ${options.silent})`);
        
        // 设置加载状态，防止循环触发
        this.isLoadingData = true;
        
        try {
            this.graph.fromJSON(richGraph.toX6Format(), { silent: options.silent });
            this.currentGraphData = richGraph;
            
            if (!options.silent) {
                this.$emit('graph-data-change', richGraph);
            }

            var self = this;
            this.$nextTick(function() {
                setTimeout(function() {
                    self.fitView();
                    // 加载完成后重置加载状态
                    self.isLoadingData = false;
                }, 100);
            });

        } catch (error) {
            console.error('加载富执行图数据失败:', error);
            // 出错时也要重置加载状态
            this.isLoadingData = false;
        }
    },
    
    /**
     * 从当前X6图状态更新 `currentGraphData` 属性。
     * @private
     */
    _updateCurrentGraphData: function() {
      if (!this.graph) {
        return;
      }
      
      try {
        var x6Data = this.graph.toJSON();
        var baseId = this.currentGraphData ? this.currentGraphData.id : 'graph-from-update';
        var baseMetadata = this.currentGraphData ? this.currentGraphData.metadata : {};

        var updatedGraph = RichExecutionGraph.fromX6Format(x6Data, baseId, baseMetadata);
        this.currentGraphData = updatedGraph;
        this.$emit('graph-data-change', updatedGraph);
      } catch (error) {
        console.warn('更新当前图形数据失败:', error);
      }
    },
    
    /**
     * 向画布中添加一个新富节点。
     * @public
     * @param {RichGraphNode} richNode - 要添加的富节点实例。
     * @returns {string} 添加成功的节点ID。
     */
    addNode: function(richNode) {
      if (!this.graph || this.readonly || !(richNode instanceof RichGraphNode)) {
        console.warn('无法添加节点：图形未初始化、处于只读模式或节点类型不正确');
        return null;
      }
      
      var x6NodeData = richNode.toX6Node();
      var node = this.graph.addNode(x6NodeData);
      
      this._updateCurrentGraphData();
      
      this.$emit('node-add', { node: richNode });
      return node.id;
    },
    
    /**
     * 根据富节点对象或ID删除画布中的节点。
     * @public
     * @param {RichGraphNode|String} nodeOrNodeId - 富节点实例或节点ID。
     * @returns {void}
     */
    removeNode: function(nodeOrNodeId) {
      this.removeCell(nodeOrNodeId);
    },
    
    /**
     * 向画布中添加一条新富边。
     * @public
     * @param {RichGraphEdge|Object} richEdge - 要添加的富边实例或包含源/目标ID的对象。
     * @returns {string} 添加成功的边ID。
     */
    addEdge: function(richEdge) {
      if (!this.graph || this.readonly || !richEdge) {
        console.warn('无法添加边：图形未初始化、处于只读模式或边数据无效');
        return null;
      }
      
      var sourceId = richEdge.source;
      var targetId = richEdge.target;
      
      if (!sourceId || !targetId) {
        console.warn('无法添加边：缺少源节点或目标节点ID');
        return null;
      }
      
      var existingEdges = this.graph.getEdges().filter(function(edge) {
        return edge.getSourceCellId() === sourceId && edge.getTargetCellId() === targetId;
      });
      
      if (existingEdges.length > 0) {
        console.warn('添加边失败：已存在从源节点到目标节点的连接');
        return null;
      }
      
      if (this.wouldFormCycle(sourceId, targetId)) {
        console.warn('添加边失败：该连接会形成环路');
        return null;
      }
      
      var x6EdgeData = (richEdge instanceof RichGraphEdge)
        ? richEdge.toX6Edge()
        : richEdge;

      // 增加边的可交互区域，使其更容易被点击
      if (!x6EdgeData.attrs.line.strokeWidth || x6EdgeData.attrs.line.strokeWidth < 10) {
        x6EdgeData.attrs.line.stroke = 'transparent';
        x6EdgeData.attrs.line.strokeWidth = 10;
        x6EdgeData.attrs.line.cursor = 'pointer';
      }

      var edge = this.graph.addEdge(x6EdgeData);

      // 修复：直接在此处触发事件，以确保编程方式添加边时事件能够被捕获
      this._updateCurrentGraphData();
      this.$emit('node-connect', { 
        edge: richEdge, 
        source: sourceId, 
        target: targetId 
      });
      return edge.id;
    },
    
    /**
     * 根据富边对象、X6边对象或ID删除画布中的边。
     * @public
     * @param {RichGraphEdge|Object|String} edgeOrId - 富边实例、X6边实例或边ID。
     * @returns {void}
     */
    removeEdge: function(edgeOrId) {
      this.removeCell(edgeOrId);
    },
    
    /**
     * 删除画布中的单元格（节点或边）。
     * @param {import('@antv/x6').Cell|String} cellOrId - X6单元格对象或其ID。
     */
    removeCell: function(cellOrId) {
      if (!this.graph || this.readonly || !cellOrId) {
        return;
      }
      
      var cell = (typeof cellOrId === 'string')
        ? this.graph.getCellById(cellOrId)
        : cellOrId;

      if (cell) {
        this.graph.removeCell(cell);
        if (this.selectedCell === cell) {
            this.selectedCell = null;
        }
        // cell:removed事件会自动保存历史记录和更新数据
        if (cell.isNode()) {
            this.$emit('node-remove', { nodeId: cell.id });
        }
      }
    },
    
    /**
     * 放大画布视图。
     * @public
     * @returns {void}
     */
    zoomIn: function() {
      if (!this.graph) return;
      this.graph.zoom(0.1);
      this.zoomLevel = this.graph.zoom();
    },
    
    /**
     * 缩小画布视图。
     * @public
     * @returns {void}
     */
    zoomOut: function() {
      if (!this.graph) return;
      this.graph.zoom(-0.1);
      this.zoomLevel = this.graph.zoom();
    },
    
    /**
     * 使整个图适应画布可见区域。
     * @public
     * @returns {void}
     */
    fitView: function() {
      if (!this.graph) return;
      this.graph.zoomToFit({ padding: 20, maxScale: 1 });
      this.zoomLevel = this.graph.zoom();
    },
    
    /**
     * 设置画布的缩放级别。
     * @param {Number} scale - 目标缩放比例。
     * @returns {void}
     */
    setZoom: function(scale) {
      if (!this.graph) return;
      this.graph.zoomTo(scale);
      this.zoomLevel = scale;
    },
    
    /**
     * 导出当前画布中的所有图形数据为富执行图格式。
     * @public
     * @returns {RichExecutionGraph} 当前的富执行图实例。
     */
    exportData: function() {
      this._updateCurrentGraphData();
      return this.currentGraphData || new RichExecutionGraph({});
    },
    
    /**
     * 导出当前画布中的数据为基础的执行图格式。
     * @public
     * @returns {import('./models.js').ExecutionGraph} 转换后的基础执行图对象。
     */
    getExecutionGraph: function() {
      var richGraph = this.exportData();
      return richGraph.toExecutionGraph();
    },
    
    /**
     * 导入符合AntV X6 `toJSON`格式的图形数据。
     * @param {Object} data - 符合AntV X6 `toJSON`格式的数据对象 (`{ cells: [...] }`)。
     */
    importData: function(data) {
      if (!this.graph || !data) return;
      
      try {
        var richGraph = RichExecutionGraph.fromX6Format(
          data,
          'imported-graph-' + Date.now(),
          { displayName: '导入的图', description: '通过importData导入' }
        );
        this.loadGraphData(richGraph);
      } catch (error) {
        console.error('导入数据失败:', error);
      }
    },
    
    /**
     * 获取当前选中的单元格。
     * @returns {import('@antv/x6').Cell | null} 当前选中的X6单元格实例或`null`。
     */
    getSelectedCell: function() {
      return this.selectedCell;
    },
    
    /**
     * 根据单元格对象或ID选中画布中的单元格。
     * @param {import('@antv/x6').Cell|String} cellOrId - X6单元格对象或ID。
     */
    selectCell: function(cellOrId) {
      if (!this.graph) return;
      
      var cell = (typeof cellOrId === 'string')
        ? this.graph.getCellById(cellOrId)
        : cellOrId;
      
      if (cell) {
        this.selectedCell = cell;
        this.graph.select(cell);
      }
    },
    
    /**
     * 根据节点ID选中画布中的节点。
     * @public
     * @param {string} nodeId - 要选中的节点ID。
     * @returns {void}
     */
    selectNodeById: function(nodeId) {
      if (!this.graph || !nodeId) return;
      
      var node = this.graph.getCellById(nodeId);
      if (node && node.isNode()) {
        this.selectedCell = node;
        this.graph.select(node);
        // 手动触发node-select事件
        this.$emit('node-select', { node: node, event: null });
      }
    },

    /**
     * 清除画布中所有节点和边的选中状态。
     * @public
     * @returns {void}
     */
    clearGraphSelection: function() {
      if (!this.graph) return;
      
      this.selectedCell = null;
      this.graph.cleanSelection();
      this.$emit('nodes-deselect');
    },

    /**
     * 清除当前所有选中状态。
     * @deprecated 请使用 clearGraphSelection 方法
     * @returns {void}
     */
    clearSelection: function() {
      this.clearGraphSelection();
    },
    
    /**
     * 处理窗口大小变化，自动调整画布大小。
     * @private
     */
    _handleResize: function() {
      if (!this.graph || !this.$refs.graphContainer) return;
      var container = this.$refs.graphContainer;
      this.graph.resize(container.offsetWidth, container.offsetHeight);
    },
    
    /**
     * 清理组件资源，如事件监听器和图形实例。
     * @private
     */
    _cleanupResources: function() {
      if (this.graph) {
        window.removeEventListener('resize', this._handleResize);
        this.graph.dispose();
        this.graph = null;
      }
    },
    
    /**
     * 检查添加连接是否会形成环路。
     * @param {string} sourceId - 源节点ID。
     * @param {string} targetId - 目标节点ID。
     * @returns {boolean} 如果会形成环路返回true，否则返回false。
     * @private
     */
    wouldFormCycle: function(sourceId, targetId) {
      if (!this.graph) {
        return true;
      }
      return wouldFormCycle(this.graph.getNodes(), this.graph.getEdges(), sourceId, targetId);
    },

    /**
     * 检查两个富执行图数据是否相同。
     * @param {RichExecutionGraph} graph1 - 第一个富执行图数据。
     * @param {RichExecutionGraph} graph2 - 第二个富执行图数据。
     * @returns {boolean} 如果两个富执行图数据相同返回true，否则返回false。
     * @private
     */
    isSameGraphData: function(graph1, graph2) {
      if (!graph1 || !graph2) return false;
      return graph1.id === graph2.id && graph1.metadata.displayName === graph2.metadata.displayName;
    },

    // =============================================================================
    // 数据处理相关方法
    // =============================================================================

    /**
     * 处理graphData属性变化
     * @param {RichExecutionGraph} newData - 新的图形数据
     * @param {RichExecutionGraph} oldData - 旧的图形数据
     * @private
     */
    _handleGraphDataChange: function(newData, oldData) {
      console.log('GraphEditor watch triggered:', {
        newData: newData,
        oldData: oldData,
        isInitialized: this.isInitialized,
        hasGraph: !!this.graph,
        isLoadingData: this.isLoadingData
      });
      
      // 如果正在加载数据，跳过此次触发
      if (this.isLoadingData) {
        console.log('正在加载数据，跳过此次watch触发');
        return;
      }
      
      // 如果新数据为空，不处理
      if (!newData) {
        console.log('新数据为空，跳过加载');
        return;
      }
      
      // 检查新数据是否与当前数据相同，避免不必要的重复加载
      if (this.currentGraphData && this.isSameGraphData(newData, this.currentGraphData)) {
        console.log('新数据与当前数据相同，跳过加载');
        return;
      }
      
      if (this.graph && this.isInitialized) {
        console.log('开始加载数据到GraphEditor');
        this.loadGraphData(newData);
      } else if (!this.isInitialized) {
        console.log('组件未初始化，等待初始化完成后加载数据');
        this._waitForInitializationAndLoadData(newData);
      }
    },

    /**
     * 等待组件初始化完成后加载数据
     * @param {RichExecutionGraph} newData - 要加载的数据
     * @private
     */
    _waitForInitializationAndLoadData: function(newData) {
      var self = this;
      var checkInitialized = function() {
        if (self.isInitialized && self.graph && !self.isLoadingData) {
          console.log('组件已初始化，现在加载数据');
          self.loadGraphData(newData);
        } else if (!self.isInitialized) {
          setTimeout(checkInitialized, TIMING_CONFIG.LOADING_CHECK_INTERVAL);
        }
      };
      checkInitialized();
    }
  }
}
</script>

<style scoped>
.graph-editor {
  position: relative;
  width: 100%;
  height: 100%;
}

.graph-container {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
}

.minimap-container {
  position: absolute;
  right: 24px;
  bottom: 100px;
  width: 200px;
  height: 120px;
  border: 1px solid #E5E5E5;
  background-color: #FFFFFF;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  z-index: 10;
}

.graph-toolbar {
  position: absolute;
  left: 24px;
  bottom: 24px;
  z-index: 10;
}

.zoom-controls {
  background: #FFFFFF;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #E5E5E5;
}

.zoom-text {
  font-size: 12px;
  color: #666666;
  min-width: 50px;
  text-align: center;
  font-weight: 500;
}

.zoom-buttons {
  display: flex;
  flex-direction: column;
}

.zoom-buttons >>> .ant-btn {
  width: 32px;
  height: 28px;
  border-color: #D9D9D9;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-buttons >>> .ant-btn:last-child {
  margin-bottom: 0;
}

.zoom-buttons >>> .ant-btn:hover {
  border-color: #40A9FF;
  color: #40A9FF;
}

.zoom-buttons >>> .ant-btn .anticon {
  font-size: 12px;
}
</style>