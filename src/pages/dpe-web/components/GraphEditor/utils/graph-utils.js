/**
 * @file 图形编辑器相关的工具函数
 * @module graph-utils
 */

/**
 * 检查在图中添加一条新边是否会形成环路。
 * 使用深度优先搜索（DFS）算法进行环路检测。
 *
 * @param {Array<Object>} nodes - 图中所有节点的列表，每个节点应有 'id' 属性。
 * @param {Array<Object>} edges - 图中所有边的列表，每个边应有 'source' 和 'target' 属性，
 *                                分别代表源节点ID和目标节点ID。
 * @param {string} newEdgeSourceId - 待添加的新边的源节点ID。
 * @param {string} newEdgeTargetId - 待添加的新边的目标节点ID。
 * @returns {boolean} - 如果添加新边会形成环路，则返回 true；否则返回 false。
 * @private
 */
export function wouldFormCycle(nodes, edges, newEdgeSourceId, newEdgeTargetId) {
  if (!nodes || !edges || !newEdgeSourceId || !newEdgeTargetId) {
    return false;
  }
  
  if (newEdgeSourceId === newEdgeTargetId) {
    return true; // 自环肯定会形成环路
  }

  // 构建邻接表
  var adjList = {};
  edges.forEach(function(edge) {
    var src = edge.getSourceCellId ? edge.getSourceCellId() : edge.source;
    var tgt = edge.getTargetCellId ? edge.getTargetCellId() : edge.target;
    
    if (!adjList[src]) {
      adjList[src] = [];
    }
    adjList[src].push(tgt);
  });
  
  // 临时将新边添加到邻接表
  if (!adjList[newEdgeSourceId]) {
    adjList[newEdgeSourceId] = [];
  }
  adjList[newEdgeSourceId].push(newEdgeTargetId);

  var visited = {};
  var recStack = {};

  /**
   * DFS 环路检测辅助函数
   * @param {string} nodeId - 当前遍历的节点ID
   * @returns {boolean} - 是否发现环路
   */
  function dfsHasCycle(nodeId) {
    if (!visited[nodeId]) {
      visited[nodeId] = true;
      recStack[nodeId] = true;

      if (adjList[nodeId]) {
        for (var i = 0; i < adjList[nodeId].length; i++) {
          var adj = adjList[nodeId][i];
          if (!visited[adj] && dfsHasCycle(adj)) {
            return true;
          } else if (recStack[adj]) {
            return true;
          }
        }
      }
    }
    recStack[nodeId] = false;
    return false;
  }

  // 对图中的每个节点运行DFS
  for (var i = 0; i < nodes.length; i++) {
    var nodeId = nodes[i].id;
    if (!visited[nodeId] && dfsHasCycle(nodeId)) {
      return true;
    }
  }

  return false;
} 