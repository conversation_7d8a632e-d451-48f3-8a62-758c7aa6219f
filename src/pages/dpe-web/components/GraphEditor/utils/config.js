import { wouldFormCycle } from './graph-utils';

/**
 * @file 图形编辑器配置生成器
 * @module config
 */

/**
 * 创建 AntV X6 Graph 的配置对象。
 *
 * @param {HTMLElement} container - 图形容器的 DOM 元素。
 * @param {number} width - 容器宽度。
 * @param {number} height - 容器高度。
 * @param {Object} userConfig - 用户自定义的配置，将与默认配置合并。
 * @returns {Object} 最终的 AntV X6 Graph 配置对象。
 */
export function createGraphConfig(container, width, height, userConfig = {}) {
  var defaultConfig = {
    container: container,
    width: width || 800,
    height: height || 600,
    autoResize: true,
    async: true,
    frozen: false,
    grid: {
      visible: true,
      type: 'doubleMesh',
      args: [
        { color: '#E5E5E5', thickness: 1 },
        { color: '#D0D0D0', thickness: 1, factor: 4 }
      ]
    },
    scroller: {
      enabled: true,
      pageVisible: false,
      pageBreak: false,
      pannable: true
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: { radius: 8 }
      },
      anchor: 'center',
      connectionPoint: 'boundary',
      allowBlank: false,
      allowLoop: false,
      allowNode: false,
      allowEdge: false,
      allowPort: true,
      snap: { radius: 20 },
      validateConnection: function(args) {
        var sourceView = args.sourceView;
        var targetView = args.targetView;
        var sourceMagnet = args.sourceMagnet;
        var targetMagnet = args.targetMagnet;

        if (!sourceMagnet || !targetMagnet) {
          return false;
        }

        var sourceCell = sourceView.cell;
        var targetCell = targetView.cell;
        var sourceId = sourceCell.id;
        var targetId = targetCell.id;
        var graph = sourceView.graph;
        
        // 检查是否已存在从源节点到目标节点的连接
        var existingEdges = graph.getEdges().filter(function(edge) {
            var edgeSource = edge.getSourceCellId();
            var edgeTarget = edge.getTargetCellId();
            return edgeSource === sourceId && edgeTarget === targetId;
        });

        if (existingEdges.length > 0) {
            console.warn('连接验证失败：已存在从源节点到目标节点的连接');
            return false;
        }
        
        // 检查是否会形成环路
        if (wouldFormCycle(graph.getNodes(), graph.getEdges(), sourceId, targetId)) {
            console.warn('连接验证失败：该连接会形成环路');
            return false;
        }

        return true;
      }
    },
    background: {
      color: '#FFFFFF'
    },
    panning: {
      enabled: true,
      eventTypes: ['leftMouseDown', 'mouseWheel']
    },
    mousewheel: {
      enabled: true,
      modifiers: ['ctrl', 'meta'],
      minScale: 0.1,
      maxScale: 3
    }
  };

  // 合并用户配置
  return Object.assign(defaultConfig, userConfig);
} 