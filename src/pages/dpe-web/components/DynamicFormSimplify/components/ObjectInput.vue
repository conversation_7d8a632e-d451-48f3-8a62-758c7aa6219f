<template>
  <div class="object-input">
    <!-- Schema 信息显示 -->
    <a-collapse :bordered="false" :ghost="true">
      <a-collapse-panel key="schema" header="查看 JSON Schema">
        <pre class="schema-display">{{ formattedSchema }}</pre>
      </a-collapse-panel>
    </a-collapse>
    
    <!-- JSON 输入区域 -->
    <a-textarea
      :value="jsonValue"
      :placeholder="placeholder || '请输入 JSON 对象格式的数据'"
      :disabled="disabled"
      :rows="rows"
      :class="{ 'error': hasError }"
      @input="handleInput"
      @change="handleChange"
    />
    
    <!-- 操作按钮 -->
    <div class="action-buttons" data-testid="object-action-buttons">
      <a-button
        type="primary"
        size="small"
        :disabled="disabled"
        @click="formatJson"
        data-testid="object-format-btn"
      >
        格式化
      </a-button>
      <a-button
        size="small"
        :disabled="disabled"
        @click="validateJson"
        data-testid="object-validate-btn"
      >
        校验
      </a-button>
      <a-button
        size="small"
        :disabled="disabled"
        @click="clearValue"
        data-testid="object-clear-btn"
      >
        清空
      </a-button>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <!-- 提示信息 -->
    <div class="hint-message">
      输入格式：JSON 对象，例如：{"key": "value", "number": 123}
    </div>
  </div>
</template>

<script>
import inputMixin from './input-mixin.js';

/**
 * 简化的对象类型输入控件
 * @description 使用 JSON textarea 输入对象数据，提供格式化和校验功能
 */
export default {
  name: 'ObjectInput',
  mixins: [inputMixin],
  
  props: {
    /**
     * 当前值
     * @type {Object}
     */
    value: {
      type: Object,
      default: function() {
        return {};
      }
    },
    
    /**
     * 对象的 Schema
     * @type {Object}
     */
    objectSchema: {
      type: Object,
      default: function() {
        return { type: 'object' };
      }
    },
    
    /**
     * 文本域行数
     * @type {number}
     */
    rows: {
      type: Number,
      default: 8
    }
  },
  
  data: function() {
    return {
      jsonValue: ''
    };
  },
  
  computed: {
    /**
     * 格式化的 Schema 显示
     * @returns {string} 格式化后的 Schema JSON 字符串
     */
    formattedSchema: function() {
      try {
        return JSON.stringify(this.objectSchema, null, 2);
      } catch (error) {
        return JSON.stringify(this.objectSchema);
      }
    }
  },
  
  methods: {
    /**
     * 更新内部值，重写父类方法
     * @param {Object} value - 对象值
     */
    updateInternalValue: function(value) {
      this.internalValue = (value && typeof value === 'object' && !Array.isArray(value)) ? value : {};
      this.updateJsonValue(value);
    },
    
    /**
     * 更新 JSON 值
     * @param {Object} value - 对象值
     */
    updateJsonValue: function(value) {
      try {
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          this.jsonValue = JSON.stringify(value, null, 2);
        } else {
          this.jsonValue = '{}';
        }
        this.clearError();
      } catch (error) {
        this.jsonValue = '{}';
        this.setError('数据格式错误');
      }
    },
    
    /**
     * 处理输入事件
     * @param {Event} event - 输入事件
     */
    handleInput: function(event) {
      this.jsonValue = event.target.value;
      this.parseAndEmit();
    },
    
    /**
     * 处理变化事件
     * @param {Event} event - 变化事件
     */
    handleChange: function(event) {
      this.jsonValue = event.target.value;
      this.parseAndEmit();
    },
    
    /**
     * 解析 JSON 并发出事件
     */
    parseAndEmit: function() {
      try {
        console.log('ObjectInput parseAndEmit: jsonValue =', this.jsonValue);
        
        if (this.jsonValue.trim() === '') {
          console.log('ObjectInput parseAndEmit: empty value, emitting {}');
          this.emitValue({});
          return;
        }
        
        var parsed = JSON.parse(this.jsonValue);
        console.log('ObjectInput parseAndEmit: parsed =', parsed);
        
        if (typeof parsed !== 'object' || Array.isArray(parsed) || parsed === null) {
          console.log('ObjectInput parseAndEmit: invalid object type');
          this.setError('输入的数据必须是对象格式');
          return;
        }
        
        // 如果有 properties 定义，可以进行进一步验证
        if (this.objectSchema.properties) {
          var validationResult = this.validateObjectProperties(parsed);
          if (!validationResult.valid) {
            console.log('ObjectInput parseAndEmit: validation failed:', validationResult.message);
            this.setError(validationResult.message);
            return;
          }
        }
        
        console.log('ObjectInput parseAndEmit: emitting valid object:', parsed);
        this.clearError();
        this.emitValue(parsed);
      } catch (error) {
        console.log('ObjectInput parseAndEmit: JSON parse error:', error.message);
        this.setError('JSON 格式错误: ' + error.message);
      }
    },
    
    /**
     * 验证对象属性
     * @param {Object} obj - 要验证的对象
     * @returns {Object} 验证结果
     */
    validateObjectProperties: function(obj) {
      var properties = this.objectSchema.properties;
      var required = this.objectSchema.required || [];
      
      // 检查必填字段
      for (var i = 0; i < required.length; i++) {
        var requiredField = required[i];
        if (!(requiredField in obj)) {
          return {
            valid: false,
            message: '缺少必填字段: ' + requiredField
          };
        }
      }
      
      // 检查字段类型（简单检查）
      for (var key in obj) {
        if (properties[key]) {
          var expectedType = properties[key].type;
          var actualType = this.getValueType(obj[key]);
          
          if (expectedType && expectedType !== actualType) {
            return {
              valid: false,
              message: '字段 "' + key + '" 类型错误，期望: ' + expectedType + '，实际: ' + actualType
            };
          }
        }
      }
      
      return { valid: true };
    },
    
    /**
     * 获取值的类型
     * @param {any} value - 值
     * @returns {string} 类型字符串
     */
    getValueType: function(value) {
      if (value === null) return 'null';
      if (Array.isArray(value)) return 'array';
      if (typeof value === 'number') {
        return Number.isInteger(value) ? 'integer' : 'number';
      }
      return typeof value;
    },
    

    
    /**
     * 格式化 JSON
     */
    formatJson: function() {
      try {
        if (this.jsonValue.trim() === '') {
          this.jsonValue = '{}';
          return;
        }
        
        var parsed = JSON.parse(this.jsonValue);
        this.jsonValue = JSON.stringify(parsed, null, 2);
        this.clearError();
      } catch (error) {
        this.setError('JSON 格式错误，无法格式化: ' + error.message);
      }
    },
    
    /**
     * 校验 JSON
     */
    validateJson: function() {
      this.parseAndEmit();
      
      if (!this.hasError) {
        // 使用console.log替代$message，避免依赖问题
        console.log('JSON 格式正确');
        // 如果$message可用，则使用它
        if (this.$message && typeof this.$message.success === 'function') {
          this.$message.success('JSON 格式正确');
        }
      }
    },
    
    /**
     * 清空值
     */
    clearValue: function() {
      this.jsonValue = '{}';
      this.clearError();
      this.emitValue({});
    },
    

  }
};
</script>

<style scoped>
.object-input {
  width: 100%;
}

.schema-display {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #586069;
  overflow-x: auto;
}

.ant-textarea.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.action-buttons {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.error-message {
  margin-top: 4px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.4;
}

.hint-message {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.ant-collapse {
  margin-bottom: 8px;
}

.ant-collapse-content-box {
  padding: 8px 0 !important;
}
</style> 