/**
 * 输入控件基础混入
 * @description 定义所有输入控件的公共属性、事件和方法
 * @fires change - 当输入值发生变化时触发
 */
export default {
  props: {
    /**
     * 当前值
     * @type {any}
     */
    value: {
      required: true,
      default: function() {
        return null;
      }
    },
    
    /**
     * 占位符文本
     * @type {string}
     */
    placeholder: {
      type: String,
      default: ''
    },
    
    /**
     * 是否禁用
     * @type {boolean}
     */
    disabled: {
      type: Boolean,
      default: false
    }
  },
  
  data: function() {
    return {
      // 内部值，用于处理v-model
      internalValue: null,
      // 错误状态
      hasError: false,
      // 错误信息
      errorMessage: ''
    };
  },
  
  watch: {
    value: {
      handler: function(newValue) {
        this.updateInternalValue(newValue);
      },
      immediate: true
    }
  },
  
  methods: {
    /**
     * 更新内部值
     * @param {any} value - 新值
     */
    updateInternalValue: function(value) {
      this.internalValue = value;
    },
    
    /**
     * 发出值变化事件
     * @description 统一通过 change 事件发送变更值，确保与父组件的事件处理一致性
     * @param {any} value - 新值
     * @fires change
     */
    emitValue: function(value) {
      /**
       * 值变化事件
       * @event change
       * @type {any} 变化后的新值
       */
      this.$emit('change', value);
    },
    
    /**
     * 处理输入事件
     * @description 将输入事件统一转换为 change 事件处理
     * @param {Event|any} event - 输入事件或直接的值
     */
    handleInput: function(event) {
      var value = event && event.target ? event.target.value : event;
      this.updateInternalValue(value);
      this.emitValue(value);
    },
    
    /**
     * 处理变化事件
     * @description 处理值变化事件，统一的事件处理入口
     * @param {Event|any} event - 变化事件或直接的值
     */
    handleChange: function(event) {
      var value = event && event.target ? event.target.value : event;
      this.updateInternalValue(value);
      this.emitValue(value);
    },
    
    /**
     * 设置错误信息
     * @param {string} message - 错误信息
     */
    setError: function(message) {
      this.errorMessage = message;
      this.hasError = true;
    },
    
    /**
     * 清除错误信息
     */
    clearError: function() {
      this.errorMessage = '';
      this.hasError = false;
    },
    
    /**
     * 验证输入值
     * @returns {boolean} 是否有效
     */
    validate: function() {
      // 子组件可以重写此方法提供具体的验证逻辑
      return !this.hasError;
    },
    
    /**
     * 重置组件状态
     */
    reset: function() {
      this.clearError();
      // 子组件可以重写此方法提供具体的重置逻辑
    },
    
    /**
     * 获取当前值
     * @returns {any} 当前值
     */
    getValue: function() {
      return this.internalValue;
    }
  }
}; 