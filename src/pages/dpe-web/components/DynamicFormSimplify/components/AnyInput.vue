<template>
  <div class="any-input">
    <!-- Schema 信息显示 -->
    <a-collapse :bordered="false" :ghost="true">
      <a-collapse-panel key="schema" header="查看 JSON Schema">
        <pre class="schema-display">{{ formattedSchema }}</pre>
      </a-collapse-panel>
    </a-collapse>
    
    <!-- JSON 输入区域 -->
    <a-textarea
      :value="jsonValue"
      :placeholder="placeholder || '请输入任意 JSON 格式的数据'"
      :disabled="disabled"
      :rows="rows"
      :class="{ 'error': hasError }"
      @input="handleJsonInput"
      @change="handleJsonChange"
    />
    
    <!-- 操作按钮 -->
    <div class="action-buttons" data-testid="any-action-buttons">
      <a-button
        type="primary"
        size="small"
        :disabled="disabled"
        @click="formatJson"
        data-testid="any-format-btn"
      >
        格式化
      </a-button>
      <a-button
        size="small"
        :disabled="disabled"
        @click="validateJson"
        data-testid="any-validate-btn"
      >
        校验
      </a-button>
      <a-button
        size="small"
        :disabled="disabled"
        @click="clearValue"
        data-testid="any-clear-btn"
      >
        清空
      </a-button>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <!-- 提示信息 -->
    <div class="hint-message">
      输入格式：任意有效的 JSON 数据，可以是对象、数组、字符串、数字、布尔值等
    </div>
  </div>
</template>

<script>
import inputMixin from './input-mixin.js';

/**
 * 任意类型输入控件
 * @description 用于未匹配类型的字段，允许输入任意JSON格式的数据
 */
export default {
  name: 'AnyInput',
  mixins: [inputMixin],
  
  props: {
    /**
     * 字段的 Schema
     * @type {Object}
     */
    schema: {
      type: Object,
      default: function() {
        return {};
      }
    },
    
    /**
     * 文本域行数
     * @type {number}
     */
    rows: {
      type: Number,
      default: 8
    }
  },
  
  data: function() {
    return {
      jsonValue: ''
    };
  },
  
  computed: {
    /**
     * 格式化的 Schema 显示
     * @returns {string} 格式化后的 Schema JSON 字符串
     */
    formattedSchema: function() {
      var displaySchema = {
        type: 'any',
        description: '可以输入任意类型的 JSON 数据',
        ...this.schema
      };
      
      try {
        return JSON.stringify(displaySchema, null, 2);
      } catch (error) {
        return JSON.stringify(displaySchema);
      }
    }
  },
  
  methods: {
    /**
     * 更新内部值，重写父类方法
     * @param {any} value - 新值
     */
    updateInternalValue: function(value) {
      this.internalValue = value;
      this.updateJsonValue(value);
    },
    
    /**
     * 更新 JSON 值
     * @param {any} value - 任意值
     */
    updateJsonValue: function(value) {
      try {
        if (value === null || value === undefined) {
          this.jsonValue = 'null';
        } else if (typeof value === 'string') {
          // 检查是否已经是JSON字符串
          try {
            JSON.parse(value);
            this.jsonValue = value;
          } catch (e) {
            // 如果不是有效JSON，则作为字符串处理
            this.jsonValue = JSON.stringify(value, null, 2);
          }
        } else {
          this.jsonValue = JSON.stringify(value, null, 2);
        }
        this.clearError();
      } catch (error) {
        this.jsonValue = 'null';
        this.setError('数据格式错误');
      }
    },
    
    /**
     * 处理JSON输入事件
     * @param {Event} event - 输入事件
     */
    handleJsonInput: function(event) {
      this.jsonValue = event.target.value;
      this.parseAndEmit();
    },
    
    /**
     * 处理JSON变化事件
     * @param {Event} event - 变化事件
     */
    handleJsonChange: function(event) {
      this.jsonValue = event.target.value;
      this.parseAndEmit();
    },
    
    /**
     * 解析 JSON 并发出事件
     */
    parseAndEmit: function() {
      try {
        if (this.jsonValue.trim() === '') {
          this.emitValue(null);
          return;
        }
        
        var parsed = JSON.parse(this.jsonValue);
        this.clearError();
        this.emitValue(parsed);
      } catch (error) {
        this.setError('JSON 格式错误: ' + error.message);
      }
    },
    
    /**
     * 格式化 JSON
     */
    formatJson: function() {
      try {
        if (this.jsonValue.trim() === '') {
          this.jsonValue = 'null';
          return;
        }
        
        var parsed = JSON.parse(this.jsonValue);
        this.jsonValue = JSON.stringify(parsed, null, 2);
        this.clearError();
      } catch (error) {
        this.setError('JSON 格式错误，无法格式化: ' + error.message);
      }
    },
    
    /**
     * 校验 JSON
     */
    validateJson: function() {
      this.parseAndEmit();
      
      if (!this.hasError) {
        // 使用console.log替代$message，避免依赖问题
        console.log('JSON 格式正确');
        // 如果$message可用，则使用它
        if (this.$message && typeof this.$message.success === 'function') {
          this.$message.success('JSON 格式正确');
        }
      }
    },
    
    /**
     * 清空值
     */
    clearValue: function() {
      this.jsonValue = 'null';
      this.clearError();
      this.emitValue(null);
    },
    
    /**
     * 验证输入值，重写父类方法
     * @returns {boolean} 是否有效
     */
    validate: function() {
      try {
        if (this.jsonValue.trim() !== '') {
          JSON.parse(this.jsonValue);
        }
        this.clearError();
        return true;
      } catch (error) {
        this.setError('JSON 格式错误: ' + error.message);
        return false;
      }
    },
    
    /**
     * 重置组件状态，重写父类方法
     */
    reset: function() {
      this.jsonValue = 'null';
      this.clearError();
      this.emitValue(null);
    }
  }
};
</script>

<style scoped>
.any-input {
  width: 100%;
}

.schema-display {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #586069;
  overflow-x: auto;
}

.ant-textarea.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.action-buttons {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.error-message {
  margin-top: 4px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.4;
}

.hint-message {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.ant-collapse {
  margin-bottom: 8px;
}

.ant-collapse-content-box {
  padding: 8px 0 !important;
}
</style> 