<template>
  <div class="number-input">
    <a-input-number
      :value="internalValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :min="min"
      :max="max"
      :precision="precision"
      :step="step"
      style="width: 100%"
      @input="handleNumberInput"
      @change="handleNumberChange"
    />
  </div>
</template>

<script>
import inputMixin from './input-mixin.js';

/**
 * 数字类型输入控件
 * @description 用于输入数字和整数类型的值
 */
export default {
  name: 'NumberInput',
  mixins: [inputMixin],
  
  props: {
    /**
     * 当前值
     * @type {number}
     */
    value: {
      type: Number,
      default: null
    },
    
    /**
     * 最小值
     * @type {number}
     */
    min: {
      type: Number,
      default: undefined
    },
    
    /**
     * 最大值
     * @type {number}
     */
    max: {
      type: Number,
      default: undefined
    },
    
    /**
     * 数值精度
     * @type {number}
     */
    precision: {
      type: Number,
      default: undefined
    },
    
    /**
     * 每次改变步数
     * @type {number}
     */
    step: {
      type: Number,
      default: 1
    }
  },
  
  methods: {
    /**
     * 更新内部值，重写父类方法
     * @param {number} value - 数字值
     */
    updateInternalValue: function(value) {
      this.internalValue = value || null;
    },
    
    /**
     * 处理数字输入事件
     * @param {number} value - 输入的数值
     */
    handleNumberInput: function(value) {
      this.updateInternalValue(value);
      this.emitValue(value);
    },
    
    /**
     * 处理数字变化事件
     * @param {number} value - 变化后的数值
     */
    handleNumberChange: function(value) {
      this.updateInternalValue(value);
      this.emitValue(value);
    }
  }
};
</script>

<style scoped>
.number-input {
  width: 100%;
}
</style> 