<template>
  <div class="array-input">
    <!-- Schema 信息显示 -->
    <a-collapse :bordered="false" :ghost="true">
      <a-collapse-panel key="schema" header="查看 JSON Schema">
        <pre class="schema-display">{{ formattedSchema }}</pre>
      </a-collapse-panel>
    </a-collapse>
    
    <!-- JSON 输入区域 -->
    <a-textarea
      :value="jsonValue"
      :placeholder="placeholder || '请输入 JSON 数组格式的数据'"
      :disabled="disabled"
      :rows="rows"
      :class="{ 'error': hasError }"
      @input="handleInput"
      @change="handleChange"
    />
    
    <!-- 操作按钮 -->
    <div class="action-buttons" data-testid="array-action-buttons">
      <a-button
        type="primary"
        size="small"
        :disabled="disabled"
        @click="formatJson"
        data-testid="array-format-btn"
      >
        格式化
      </a-button>
      <a-button
        size="small"
        :disabled="disabled"
        @click="validateJson"
        data-testid="array-validate-btn"
      >
        校验
      </a-button>
      <a-button
        size="small"
        :disabled="disabled"
        @click="clearValue"
        data-testid="array-clear-btn"
      >
        清空
      </a-button>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <!-- 提示信息 -->
    <div class="hint-message">
      输入格式：JSON 数组，例如：["item1", "item2"] 或 [{"name": "value"}]
    </div>
  </div>
</template>

<script>
import inputMixin from './input-mixin.js';

/**
 * 简化的数组类型输入控件
 * @description 使用 JSON textarea 输入数组数据，提供格式化和校验功能
 */
export default {
  name: 'ArrayInput',
  mixins: [inputMixin],
  
  props: {
    /**
     * 当前值
     * @type {Array}
     */
    value: {
      type: Array,
      default: function() {
        return [];
      }
    },
    
    /**
     * 数组项的 Schema
     * @type {Object}
     */
    itemSchema: {
      type: Object,
      default: function() {
        return { type: 'string' };
      }
    },
    
    /**
     * 最小项数
     * @type {number}
     */
    minItems: {
      type: Number,
      default: undefined
    },
    
    /**
     * 最大项数
     * @type {number}
     */
    maxItems: {
      type: Number,
      default: undefined
    },
    
    /**
     * 文本域行数
     * @type {number}
     */
    rows: {
      type: Number,
      default: 6
    }
  },
  
  data: function() {
    return {
      jsonValue: ''
    };
  },
  
  computed: {
    /**
     * 格式化的 Schema 显示
     * @returns {string} 格式化后的 Schema JSON 字符串
     */
    formattedSchema: function() {
      var schema = {
        type: 'array',
        items: this.itemSchema
      };
      
      if (this.minItems !== undefined) {
        schema.minItems = this.minItems;
      }
      
      if (this.maxItems !== undefined) {
        schema.maxItems = this.maxItems;
      }
      
      try {
        return JSON.stringify(schema, null, 2);
      } catch (error) {
        return JSON.stringify(schema);
      }
    }
  },
  
  methods: {
    /**
     * 更新内部值，重写父类方法
     * @param {Array} value - 数组值
     */
    updateInternalValue: function(value) {
      this.internalValue = Array.isArray(value) ? value : [];
      this.updateJsonValue(value);
    },
    
    /**
     * 更新 JSON 值
     * @param {Array} value - 数组值
     */
    updateJsonValue: function(value) {
      try {
        if (Array.isArray(value)) {
          this.jsonValue = JSON.stringify(value, null, 2);
        } else {
          this.jsonValue = '[]';
        }
        this.clearError();
      } catch (error) {
        this.jsonValue = '[]';
        this.setError('数据格式错误');
      }
    },
    
    /**
     * 处理输入事件
     * @param {Event} event - 输入事件
     */
    handleInput: function(event) {
      this.jsonValue = event.target.value;
      this.parseAndEmit();
    },
    
    /**
     * 处理变化事件
     * @param {Event} event - 变化事件
     */
    handleChange: function(event) {
      this.jsonValue = event.target.value;
      this.parseAndEmit();
    },
    
    /**
     * 解析 JSON 并发出事件
     */
    parseAndEmit: function() {
      try {
        if (this.jsonValue.trim() === '') {
          this.emitValue([]);
          return;
        }
        
        var parsed = JSON.parse(this.jsonValue);
        
        if (!Array.isArray(parsed)) {
          this.setError('输入的数据必须是数组格式');
          return;
        }
        
        // 验证数组长度
        if (this.minItems !== undefined && parsed.length < this.minItems) {
          this.setError('数组长度不能少于 ' + this.minItems + ' 项');
          return;
        }
        
        if (this.maxItems !== undefined && parsed.length > this.maxItems) {
          this.setError('数组长度不能超过 ' + this.maxItems + ' 项');
          return;
        }
        
        this.clearError();
        this.emitValue(parsed);
      } catch (error) {
        this.setError('JSON 格式错误: ' + error.message);
      }
    },
    

    
    /**
     * 格式化 JSON
     */
    formatJson: function() {
      try {
        if (this.jsonValue.trim() === '') {
          this.jsonValue = '[]';
          return;
        }
        
        var parsed = JSON.parse(this.jsonValue);
        this.jsonValue = JSON.stringify(parsed, null, 2);
        this.clearError();
      } catch (error) {
        this.setError('JSON 格式错误，无法格式化: ' + error.message);
      }
    },
    
    /**
     * 校验 JSON
     */
    validateJson: function() {
      this.parseAndEmit();
      
      if (!this.hasError) {
        // 使用console.log替代$message，避免依赖问题
        console.log('JSON 格式正确');
        // 如果$message可用，则使用它
        if (this.$message && typeof this.$message.success === 'function') {
          this.$message.success('JSON 格式正确');
        }
      }
    },
    
    /**
     * 清空值
     */
    clearValue: function() {
      this.jsonValue = '[]';
      this.clearError();
      this.emitValue([]);
    },
    

  }
};
</script>

<style scoped>
.array-input {
  width: 100%;
}

.schema-display {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #586069;
  overflow-x: auto;
}

.ant-textarea.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.action-buttons {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.error-message {
  margin-top: 4px;
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.4;
}

.hint-message {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
  line-height: 1.4;
}

.ant-collapse {
  margin-bottom: 8px;
}

.ant-collapse-content-box {
  padding: 8px 0 !important;
}
</style> 