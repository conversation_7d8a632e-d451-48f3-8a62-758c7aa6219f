<template>
  <div class="boolean-input">
    <a-switch
      :checked="internalValue"
      :disabled="disabled"
      :checked-children="checkedChildren"
      :un-checked-children="unCheckedChildren"
      @change="handleSwitchChange"
    />
  </div>
</template>

<script>
import inputMixin from './input-mixin.js';

/**
 * 布尔类型输入控件
 * @description 用于输入布尔类型的值
 */
export default {
  name: 'BooleanInput',
  mixins: [inputMixin],
  
  props: {
    /**
     * 当前值
     * @type {boolean}
     */
    value: {
      type: Boolean,
      default: false
    },
    
    /**
     * 选中时的内容
     * @type {string}
     */
    checkedChildren: {
      type: String,
      default: '是'
    },
    
    /**
     * 非选中时的内容
     * @type {string}
     */
    unCheckedChildren: {
      type: String,
      default: '否'
    }
  },
  
  methods: {
    /**
     * 更新内部值，重写父类方法
     * @param {boolean} value - 布尔值
     */
    updateInternalValue: function(value) {
      this.internalValue = Boolean(value);
    },
    
    /**
     * 处理开关变化事件
     * @param {boolean} checked - 是否选中
     */
    handleSwitchChange: function(checked) {
      this.updateInternalValue(checked);
      this.emitValue(checked);
    }
  }
};
</script>

<style scoped>
.boolean-input {
  display: inline-block;
}
</style> 