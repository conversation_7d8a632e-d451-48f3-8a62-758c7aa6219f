<template>
  <div class="string-input">
    <!-- 选择框（优先级最高，当有options时使用） -->
    <a-select
      v-if="options && options.length > 0"
      :value="internalValue"
      :placeholder="placeholder"
      :disabled="disabled"
      @change="handleSelectChange"
    >
      <a-select-option
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </a-select-option>
    </a-select>
    
    <!-- 密码输入框 -->
    <a-input-password
      v-else-if="format === 'password'"
      :value="internalValue"
      :placeholder="placeholder"
      :disabled="disabled"
      @input="handleInput"
      @change="handleChange"
    />
    
    <!-- 文本域 -->
    <a-textarea
      v-else-if="format === 'textarea'"
      :value="internalValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :rows="rows"
      @input="handleInput"
      @change="handleChange"
    />
    
    <!-- 默认文本输入框 -->
    <a-input
      v-else
      :value="internalValue"
      :placeholder="placeholder"
      :disabled="disabled"
      @input="handleInput"
      @change="handleChange"
    />
  </div>
</template>

<script>
import inputMixin from './input-mixin.js';

/**
 * 字符串类型输入控件
 * @description 根据不同的format渲染不同的字符串输入控件
 */
export default {
  name: 'StringInput',
  mixins: [inputMixin],
  
  props: {
    /**
     * 当前值
     * @type {string}
     */
    value: {
      type: String,
      default: ''
    },
    
    /**
     * 输入格式
     * @type {string}
     * @enum {'text', 'textarea', 'password'}
     */
    format: {
      type: String,
      default: 'text'
    },
    
    /**
     * 选项列表（用于选择框）
     * @type {Array<Object>}
     */
    options: {
      type: Array,
      default: function() {
        return [];
      }
    },
    
    /**
     * 文本域行数
     * @type {number}
     */
    rows: {
      type: Number,
      default: 4
    }
  },
  
  methods: {
    /**
     * 更新内部值，重写父类方法
     * @param {string} value - 字符串值
     */
    updateInternalValue: function(value) {
      this.internalValue = value || '';
    },
    
    /**
     * 处理选择框变化事件
     * @param {string} value - 选中的值
     */
    handleSelectChange: function(value) {
      this.updateInternalValue(value);
      this.emitValue(value);
    }
  }
};
</script>

<style scoped>
.string-input {
  width: 100%;
}
</style> 