import ObjectMetadata from './ObjectMetadata';

/**
 * 会话状态常量定义
 */
const SessionState = {
  CREATED: 'CREATED',     // 会话已创建但未使用
  RUNNING: 'RUNNING',     // 会话正在运行
  CLOSING: 'CLOSING',     // 会话正在关闭
  CLOSED: 'CLOSED'        // 会话已关闭
};

/**
 * 会话类
 */
class Session {
  /**
   * 会话ID
   * @type {string}
   */
  id;

  /**
   * 后端类型
   * @type {string}
   */
  backendType;

  /**
   * 会话状态，使用SessionState常量
   * @type {string}
   */
  state;

  /**
   * 创建时间
   * @type {Date}
   */
  createdAt;

  /**
   * 更新时间
   * @type {Date}
   */
  updatedAt;

  /**
   * 会话元数据
   * @type {ObjectMetadata}
   */
  metadata;

  constructor(data) {
    data = data || {};
    this.id = data.id || '';
    this.backendType = data.backendType || '';
    this.state = data.state || SessionState.CREATED;
    this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
    this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
    this.metadata = data.metadata;
  }

  /**
   * 从后端模型创建Session实例
   * @param {Object} backendData - 后端返回的会话数据 (SnakeCase)
   * @returns {Session} Session实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端会话数据不能为空');
    }
    return new Session({
      id: backendData.session_id || backendData.id,
      backendType: backendData.backend_type || backendData.backendType,
      state: backendData.state,
      createdAt: backendData.created_at || backendData.createdAt,
      updatedAt: backendData.updated_at || backendData.updatedAt,
      metadata: backendData.metadata ? ObjectMetadata.fromBackendModel(backendData.metadata) : null,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的会话数据 (SnakeCase)
   */
  toBackendModel() {
    return {
      session_id: this.id,
      backend_type: this.backendType,
      state: this.state,
      created_at: this.createdAt ? this.createdAt.toISOString() : null,
      updated_at: this.updatedAt ? this.updatedAt.toISOString() : null,
      metadata: this.metadata ? this.metadata.toBackendModel() : null,
    };
  }
}

export default Session;
export { SessionState }; 