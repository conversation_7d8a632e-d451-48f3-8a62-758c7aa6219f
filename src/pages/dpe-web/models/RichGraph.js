import ExecutionGraph, { GraphNode, GraphEdge } from './ExecutionGraph.js';

/**
 * 富图节点类，在GraphNode基础上增加显示属性
 * 使用组合而不是继承
 */
class RichGraphNode {
  /**
   * 内部的 GraphNode 实例
   * @type {GraphNode}
   * @private
   */
  _graphNode;

  /**
   * 显示属性，用于存储@antv/x6相关的显示信息
   * @type {Object}
   */
  displayAttr;

  constructor(data) {
    data = data || {};
    this._graphNode = new GraphNode(data);
    
    // 默认的显示属性配置
    var defaultDisplayAttr = {
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100,
      width: 120,
      height: 60,
      shape: 'rect',
      attrs: {
        body: {
          stroke: '#1890FF',
          strokeWidth: 2,
          fill: '#E6F7FF',
          rx: 8,
          ry: 8
        },
        label: {
          fill: '#1890FF',
          fontSize: 12,
          textAnchor: 'middle',
          textVerticalAnchor: 'middle'
        }
      },
      ports: {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#1890FF',
                strokeWidth: 2,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            }
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#1890FF',
                strokeWidth: 2,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            }
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#1890FF',
                strokeWidth: 2,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            }
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#1890FF',
                strokeWidth: 2,
                fill: '#fff',
                style: {
                  visibility: 'visible'
                }
              }
            }
          }
        },
        items: [
          { group: 'top' },
          { group: 'right' },
          { group: 'bottom' },
          { group: 'left' }
        ]
      }
    };
    
    // 合并用户提供的 displayAttr 和默认配置
    this.displayAttr = {};
    Object.keys(defaultDisplayAttr).forEach(key => {
      if (key === 'attrs' && data.displayAttr && data.displayAttr.attrs) {
        // 深度合并 attrs 对象
        this.displayAttr.attrs = Object.assign({}, defaultDisplayAttr.attrs, data.displayAttr.attrs);
      } else if (key === 'ports') {
        // 端口配置优先使用用户提供的，如果没有则使用默认的
        this.displayAttr.ports = (data.displayAttr && data.displayAttr.ports) 
          ? data.displayAttr.ports 
          : defaultDisplayAttr.ports;
      } else {
        // 其他属性直接覆盖
        this.displayAttr[key] = (data.displayAttr && data.displayAttr[key] !== undefined) 
          ? data.displayAttr[key] 
          : defaultDisplayAttr[key];
      }
    });
  }

  /**
   * 获取节点ID
   * @returns {string}
   */
  get id() {
    return this._graphNode.id;
  }

  /**
   * 设置节点ID
   * @param {string} value
   */
  set id(value) {
    this._graphNode.id = value;
  }

  /**
   * 获取节点元数据
   * @returns {ObjectMetadata}
   */
  get metadata() {
    return this._graphNode.metadata;
  }

  /**
   * 设置节点元数据
   * @param {ObjectMetadata} value
   */
  set metadata(value) {
    this._graphNode.metadata = value;
  }

  /**
   * 获取算子类型
   * @returns {string}
   */
  get opType() {
    return this._graphNode.opType;
  }

  /**
   * 设置算子类型
   * @param {string} value
   */
  set opType(value) {
    this._graphNode.opType = value;
  }

  /**
   * 获取算子配置
   * @returns {Object}
   */
  get opConfig() {
    return this._graphNode.opConfig;
  }

  /**
   * 设置算子配置
   * @param {Object} value
   */
  set opConfig(value) {
    this._graphNode.opConfig = value;
  }

  /**
   * 转换为X6节点格式
   * @returns {Object} X6节点对象
   */
  toX6Node() {
    var nodeData = {
      id: this.id,
      shape: this.displayAttr.shape || 'rect',
      x: this.displayAttr.x || 0,
      y: this.displayAttr.y || 0,
      width: this.displayAttr.width || 120,
      height: this.displayAttr.height || 60,
      attrs: this.displayAttr.attrs || {},
      data: {
        id: this.id,
        metadata: this.metadata,
        opType: this.opType,
        opConfig: this.opConfig,
        displayAttr: this.displayAttr
      }
    };

    // 添加标签
    if (this.metadata && this.metadata.displayName) {
      nodeData.label = this.metadata.displayName;
    }

    // 添加端口配置
    if (this.displayAttr.ports) {
      nodeData.ports = this.displayAttr.ports;
    }

    return nodeData;
  }

  /**
   * 从X6节点格式创建RichGraphNode实例
   * @param {Object} x6Node - X6节点对象
   * @returns {RichGraphNode} 富图节点实例
   */
  static fromX6Node(x6Node) {
    var nodeData = x6Node.data || {};
    
    // 构建显示属性
    var displayAttr = nodeData.displayAttr || {};
    displayAttr.x = x6Node.x || displayAttr.x || 0;
    displayAttr.y = x6Node.y || displayAttr.y || 0;
    displayAttr.width = x6Node.width || displayAttr.width || 120;
    displayAttr.height = x6Node.height || displayAttr.height || 60;
    displayAttr.shape = x6Node.shape || displayAttr.shape || 'rect';
    displayAttr.attrs = x6Node.attrs || displayAttr.attrs || {};
    
    if (x6Node.ports) {
      displayAttr.ports = x6Node.ports;
    }

    return new RichGraphNode({
      id: nodeData.id || x6Node.id,
      metadata: nodeData.metadata || {},
      opType: nodeData.opType || '',
      opConfig: nodeData.opConfig || {},
      displayAttr: displayAttr
    });
  }

  /**
   * 转换为基础GraphNode
   * @returns {GraphNode} 基础图节点实例
   */
  toGraphNode() {
    return new GraphNode({
      id: this.id,
      metadata: this.metadata,
      opType: this.opType,
      opConfig: this.opConfig
    });
  }

  /**
   * 从基础GraphNode创建RichGraphNode实例
   * @param {GraphNode} graphNode - 基础图节点
   * @param {Object} displayAttr - 显示属性，可选
   * @returns {RichGraphNode} 富图节点实例
   */
  static fromGraphNode(graphNode, displayAttr) {
    return new RichGraphNode({
      id: graphNode.id,
      metadata: graphNode.metadata,
      opType: graphNode.opType,
      opConfig: graphNode.opConfig,
      displayAttr: displayAttr || {}
    });
  }
}

/**
 * 富图边类，在GraphEdge基础上增加显示属性
 * 使用组合而不是继承，支持一个富边对应多个基础边
 */
class RichGraphEdge {
  /**
   * 边ID，用于在X6中唯一标识
   * @type {string}
   */
  id;

  /**
   * 源节点ID
   * @type {string}
   */
  source;

  /**
   * 目标节点ID
   * @type {string}
   */
  target;

  /**
   * 目标节点的目标配置属性数组，一个富边可对应多个基础边
   * @type {string[]}
   */
  targetConfigs;

  /**
   * 显示属性，用于存储@antv/x6相关的显示信息
   * @type {Object}
   */
  displayAttr;

  /**
   * 源端口ID（X6连接点）
   * @type {string}
   */
  sourcePort;

  /**
   * 目标端口ID（X6连接点）
   * @type {string}
   */
  targetPort;

  constructor(data) {
    data = data || {};
    this.id = data.id || '';
    this.source = data.source || '';
    this.target = data.target || '';
    
    // 兼容旧的 targetConfig 属性，转换为 targetConfigs 数组
    if (data.targetConfig && !data.targetConfigs) {
      this.targetConfigs = [data.targetConfig];
    } else {
      this.targetConfigs = data.targetConfigs || ['input'];
    }
    
    this.sourcePort = data.sourcePort || '';
    this.targetPort = data.targetPort || '';
    this.displayAttr = data.displayAttr || {
      shape: 'edge',
      attrs: {
        line: {
          stroke: '#A2B1C3',
          strokeWidth: 2,
          targetMarker: {
            name: 'block',
            width: 12,
            height: 8
          }
        }
      },
      labels: []
    };
  }

  /**
   * 兼容旧API，获取第一个targetConfig
   * @returns {string}
   * @deprecated 使用 targetConfigs 替代
   */
  get targetConfig() {
    return this.targetConfigs.length > 0 ? this.targetConfigs[0] : '';
  }

  /**
   * 兼容旧API，设置第一个targetConfig
   * @param {string} value
   * @deprecated 使用 targetConfigs 替代
   */
  set targetConfig(value) {
    this.targetConfigs = [value || 'input'];
  }

  /**
   * 转换为X6边格式
   * @returns {Object} X6边对象
   */
  toX6Edge() {
    var edgeData = {
      id: this.id || (this.source + '-' + this.target),
      shape: this.displayAttr.shape || 'edge',
      source: {
        cell: this.source,
        port: this.sourcePort
      },
      target: {
        cell: this.target,
        port: this.targetPort
      },
      attrs: this.displayAttr.attrs || {},
      data: {
        source: this.source,
        target: this.target,
        targetConfigs: this.targetConfigs,
        sourcePort: this.sourcePort,
        targetPort: this.targetPort,
        displayAttr: this.displayAttr
      }
    };

    if (this.displayAttr.labels) {
      edgeData.labels = this.displayAttr.labels;
    }

    return edgeData;
  }

  /**
   * 从X6边格式创建RichGraphEdge实例
   * @param {Object} x6Edge - X6边对象
   * @returns {RichGraphEdge} 富图边实例
   */
  static fromX6Edge(x6Edge) {
    var edgeData = x6Edge.data || {};
    var sourceCell = typeof x6Edge.source === 'object' ? x6Edge.source.cell : x6Edge.source;
    var targetCell = typeof x6Edge.target === 'object' ? x6Edge.target.cell : x6Edge.target;
    var sourcePort = typeof x6Edge.source === 'object' ? x6Edge.source.port : '';
    var targetPort = typeof x6Edge.target === 'object' ? x6Edge.target.port : '';

    var displayAttr = edgeData.displayAttr || {};
    displayAttr.shape = x6Edge.shape || displayAttr.shape || 'edge';
    displayAttr.attrs = x6Edge.attrs || displayAttr.attrs || {};
    if (x6Edge.labels) {
      displayAttr.labels = x6Edge.labels;
    }

    return new RichGraphEdge({
      id: x6Edge.id,
      source: edgeData.source || sourceCell,
      target: edgeData.target || targetCell,
      targetConfigs: edgeData.targetConfigs || [edgeData.targetConfig || 'input'],
      sourcePort: edgeData.sourcePort || sourcePort,
      targetPort: edgeData.targetPort || targetPort,
      displayAttr: displayAttr
    });
  }

  /**
   * 转换为基础GraphEdge数组
   * @returns {GraphEdge[]} 基础图边数组
   */
  toGraphEdges() {
    return this.targetConfigs.map(targetConfig => new GraphEdge({
      source: this.source,
      target: this.target,
      targetConfig: targetConfig
    }));
  }

  /**
   * 从基础GraphEdge数组创建RichGraphEdge实例
   * @param {GraphEdge[]} graphEdges - 基础图边数组
   * @param {Object} displayAttr - 显示属性，可选
   * @returns {RichGraphEdge} 富图边实例
   */
  static fromGraphEdges(graphEdges, displayAttr) {
    if (!graphEdges || graphEdges.length === 0) {
      return null;
    }

    var firstEdge = graphEdges[0];
    var targetConfigs = graphEdges.map(edge => edge.targetConfig);

    return new RichGraphEdge({
      source: firstEdge.source,
      target: firstEdge.target,
      targetConfigs: targetConfigs,
      displayAttr: displayAttr || {}
    });
  }

  /**
   * 从单个基础GraphEdge创建RichGraphEdge实例（兼容旧方法）
   * @param {GraphEdge} graphEdge - 基础图边
   * @param {Object} displayAttr - 显示属性，可选
   * @returns {RichGraphEdge} 富图边实例
   */
  static fromGraphEdge(graphEdge, displayAttr) {
    return new RichGraphEdge({
      source: graphEdge.source,
      target: graphEdge.target,
      targetConfigs: [graphEdge.targetConfig],
      displayAttr: displayAttr || {}
    });
  }
}

/**
 * 富执行图类，在ExecutionGraph基础上增加显示属性
 * 使用组合而不是继承
 */
class RichExecutionGraph {
  /**
   * 内部的 ExecutionGraph 实例
   * @type {ExecutionGraph}
   * @private
   */
  _executionGraph;

  /**
   * 图的富节点集合，键为节点ID，值为富节点对象
   * @type {Object<string, RichGraphNode>}
   */
  nodes;

  /**
   * 图的富边集合
   * @type {Array<RichGraphEdge>}
   */
  edges;

  constructor(data) {
    data = data || {};
    
    // 创建内部的 ExecutionGraph 实例
    this._executionGraph = new ExecutionGraph({
      id: data.id || '',
      metadata: data.metadata || {},
      nodes: {},
      edges: []
    });
    
    this.nodes = {};
    this.edges = [];
    
    // 初始化富节点
    if (data.nodes) {
      Object.keys(data.nodes).forEach(nodeId => {
        var nodeData = data.nodes[nodeId];
        if (nodeData instanceof RichGraphNode) {
          this.nodes[nodeId] = nodeData;
        } else {
          this.nodes[nodeId] = new RichGraphNode(nodeData);
        }
      });
    }
    
    // 初始化富边
    if (data.edges) {
      this.edges = data.edges.map(edge => {
        if (edge instanceof RichGraphEdge) {
          return edge;
        } else {
          return new RichGraphEdge(edge);
        }
      });
    }
  }

  /**
   * 获取执行图ID
   * @returns {string}
   */
  get id() {
    return this._executionGraph.id;
  }

  /**
   * 设置执行图ID
   * @param {string} value
   */
  set id(value) {
    this._executionGraph.id = value;
  }

  /**
   * 获取执行图元数据
   * @returns {ObjectMetadata}
   */
  get metadata() {
    return this._executionGraph.metadata;
  }

  /**
   * 设置执行图元数据
   * @param {ObjectMetadata} value
   */
  set metadata(value) {
    this._executionGraph.metadata = value;
  }

  /**
   * 转换为X6格式的数据
   * @returns {Object} X6格式的图数据
   */
  toX6Format() {
    var cells = [];
    
    // 添加节点
    Object.values(this.nodes).forEach(richNode => {
      cells.push(richNode.toX6Node());
    });
    
    // 添加边
    this.edges.forEach(richEdge => {
      cells.push(richEdge.toX6Edge());
    });
    
    return {
      cells: cells
    };
  }

  /**
   * 从X6格式创建RichExecutionGraph实例
   * @param {Object} x6Data - X6格式的图数据
   * @param {string} id - 图ID
   * @param {Object} metadata - 图元数据
   * @returns {RichExecutionGraph} 富执行图实例
   */
  static fromX6Format(x6Data, id, metadata) {
    var richNodes = {};
    var richEdges = [];
    
    if (x6Data && x6Data.cells) {
      x6Data.cells.forEach(cell => {
        if (cell.shape && cell.shape !== 'edge') {
          // 这是一个节点
          var richNode = RichGraphNode.fromX6Node(cell);
          richNodes[richNode.id] = richNode;
        } else {
          // 这是一条边
          var richEdge = RichGraphEdge.fromX6Edge(cell);
          richEdges.push(richEdge);
        }
      });
    }
    
    return new RichExecutionGraph({
      id: id || 'graph-from-x6',
      metadata: metadata || {},
      nodes: richNodes,
      edges: richEdges
    });
  }

  /**
   * 转换为基础ExecutionGraph格式
   * @returns {ExecutionGraph} 基础执行图实例
   */
  toExecutionGraph() {
    var baseNodes = {};
    var baseEdges = [];
    
    // 转换节点
    Object.keys(this.nodes).forEach(nodeId => {
      baseNodes[nodeId] = this.nodes[nodeId].toGraphNode();
    });
    
    // 转换边 - 一个富边可能对应多个基础边
    this.edges.forEach(richEdge => {
      var graphEdges = richEdge.toGraphEdges();
      baseEdges.push(...graphEdges);
    });
    
    return new ExecutionGraph({
      id: this.id,
      metadata: this.metadata,
      nodes: baseNodes,
      edges: baseEdges
    });
  }

  /**
   * 从基础ExecutionGraph创建RichExecutionGraph实例
   * @param {ExecutionGraph} executionGraph - 基础执行图
   * @returns {RichExecutionGraph} 富执行图实例
   */
  static fromExecutionGraph(executionGraph) {
    var richNodes = {};
    var richEdges = [];
    
    // 转换节点
    Object.keys(executionGraph.nodes).forEach(nodeId => {
      var graphNode = executionGraph.nodes[nodeId];
      richNodes[nodeId] = RichGraphNode.fromGraphNode(graphNode);
    });
    
    // 分组和转换边 - 按源节点和目标节点分组，合并相同源目标的边
    var edgeGroups = {};
    executionGraph.edges.forEach(edge => {
      var key = edge.source + '->' + edge.target;
      if (!edgeGroups[key]) {
        edgeGroups[key] = [];
      }
      edgeGroups[key].push(edge);
    });
    
    // 为每个分组创建一个富边
    Object.values(edgeGroups).forEach(edges => {
      var richEdge = RichGraphEdge.fromGraphEdges(edges);
      if (richEdge) {
        richEdges.push(richEdge);
      }
    });
    
    return new RichExecutionGraph({
      id: executionGraph.id,
      metadata: executionGraph.metadata,
      nodes: richNodes,
      edges: richEdges
    });
  }

  /**
   * 向图中添加节点
   * @param {RichGraphNode} node - 要添加的富节点
   */
  addNode(node) {
    if (node instanceof RichGraphNode) {
      this.nodes[node.id] = node;
    }
  }

  /**
   * 从图中移除节点
   * @param {string} nodeId - 要移除的节点ID
   */
  removeNode(nodeId) {
    delete this.nodes[nodeId];
    
    // 移除相关的边
    this.edges = this.edges.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    );
  }

  /**
   * 向图中添加边
   * @param {RichGraphEdge} edge - 要添加的富边
   */
  addEdge(edge) {
    if (edge instanceof RichGraphEdge) {
      this.edges.push(edge);
    }
  }

  /**
   * 从图中移除边
   * @param {string} source - 源节点ID
   * @param {string} target - 目标节点ID
   */
  removeEdge(source, target) {
    this.edges = this.edges.filter(edge => 
      !(edge.source === source && edge.target === target)
    );
  }

  /**
   * 获取指定节点
   * @param {string} nodeId - 节点ID
   * @returns {RichGraphNode} 富节点实例
   */
  getNode(nodeId) {
    return this.nodes[nodeId];
  }

  /**
   * 获取所有节点
   * @returns {RichGraphNode[]} 富节点数组
   */
  getAllNodes() {
    return Object.values(this.nodes);
  }

  /**
   * 获取所有边
   * @returns {RichGraphEdge[]} 富边数组
   */
  getAllEdges() {
    return this.edges;
  }

  /**
   * 从ExecutionGraph合并更新数据，保持原有的displayAttr
   * @param {ExecutionGraph} executionGraph - 要合并的基础执行图
   * @returns {void}
   * @description 用于在后端更新后保持前端显示属性不丢失
   */
  mergeFromExecutionGraph(executionGraph) {
    // 保存现有的displayAttr
    const existingNodesDisplayAttr = {};
    const existingEdgesDisplayAttr = {};
    
    // 保存现有节点的displayAttr
    Object.keys(this.nodes).forEach(nodeId => {
      if (this.nodes[nodeId].displayAttr) {
        existingNodesDisplayAttr[nodeId] = { ...this.nodes[nodeId].displayAttr };
      }
    });
    
    // 保存现有边的displayAttr
    this.edges.forEach(edge => {
      const key = edge.source + '->' + edge.target;
      if (edge.displayAttr) {
        existingEdgesDisplayAttr[key] = { ...edge.displayAttr };
      }
    });

    // 更新基础ExecutionGraph数据
    this._executionGraph = executionGraph;
    
    // 重新构建节点，保持displayAttr
    const newRichNodes = {};
    Object.keys(executionGraph.nodes).forEach(nodeId => {
      const graphNode = executionGraph.nodes[nodeId];
      const displayAttr = existingNodesDisplayAttr[nodeId] || {};
      newRichNodes[nodeId] = RichGraphNode.fromGraphNode(graphNode, displayAttr);
    });
    this.nodes = newRichNodes;
    
    // 重新构建边，保持displayAttr
    const newRichEdges = [];
    const edgeGroups = {};
    executionGraph.edges.forEach(edge => {
      const key = edge.source + '->' + edge.target;
      if (!edgeGroups[key]) {
        edgeGroups[key] = [];
      }
      edgeGroups[key].push(edge);
    });
    
    Object.values(edgeGroups).forEach(edges => {
      const key = edges[0].source + '->' + edges[0].target;
      const displayAttr = existingEdgesDisplayAttr[key] || {};
      const richEdge = RichGraphEdge.fromGraphEdges(edges, displayAttr);
      if (richEdge) {
        newRichEdges.push(richEdge);
      }
    });
    this.edges = newRichEdges;
  }
}

export default RichExecutionGraph;
export { RichGraphNode, RichGraphEdge };
