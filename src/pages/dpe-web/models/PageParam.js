/**
 * 分页参数类
 */
class PageParam {
  /**
   * 当前页码，从0开始
   * @type {number}
   */
  pageIndex;

  /**
   * 每页记录数
   * @type {number}
   */
  limit;

  /**
   * 总页数
   * @type {number}
   */
  pageTotal;

  /**
   * 总记录数
   * @type {number}
   */
  recordTotal;

  /**
   * 排序字段
   * @type {string}
   */
  sortField;

  /**
   * 排序类型（asc/desc）
   * @type {string}
   */
  sortType;

  /**
   * 排序列表
   * @type {Array}
   */
  sortList;

  /**
   * 是否置顶
   * @type {boolean}
   */
  isTop;

  constructor(data) {
    data = data || {};
    this.pageIndex = data.pageIndex || 0;
    this.limit = data.limit || 10;
    this.pageTotal = data.pageTotal || 0;
    this.recordTotal = data.recordTotal || 0;
    this.sortField = data.sortField || '';
    this.sortType = data.sortType || 'asc';
    this.sortList = data.sortList || [];
    this.isTop = data.isTop || false;
  }

  /**
   * 从后端模型创建PageParam实例
   * @param {Object} backendData - 后端返回的分页参数数据 (SnakeCase)
   * @returns {PageParam} PageParam实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端分页参数不能为空');
    }
    return new PageParam({
      pageIndex: backendData.pageIndex,
      limit: backendData.limit,
      pageTotal: backendData.pageTotal,
      recordTotal: backendData.recordTotal,
      sortField: backendData.sort_field,
      sortType: backendData.sort_type,
      sortList: (backendData.sort_list || []).map(item => ({
        field: item.field,
        type: item.type,
      })),
      isTop: backendData.is_top,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的分页参数 (SnakeCase)
   */
  toBackendModel() {
    return {
      pageIndex: this.pageIndex,
      limit: this.limit,
      pageTotal: this.pageTotal,
      recordTotal: this.recordTotal,
      sort_field: this.sortField,
      sort_type: this.sortType,
      sort_list: (this.sortList || []).map(item => ({
        field: item.field,
        type: item.type,
      })),
      is_top: this.isTop,
    };
  }
}

export default PageParam; 