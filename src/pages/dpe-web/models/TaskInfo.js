/**
 * 任务状态常量定义
 */
const TaskStatus = {
  CREATED: 'CREATED',     // 任务已创建但未开始执行
  PENDING: 'PENDING',     // 任务已提交执行但存在需要等待满足的条件
  RUNNING: 'RUNNING',     // 任务正在执行
  SUCCESS: 'SUCCESS',     // 任务成功完成
  ERROR: 'ERROR',         // 任务执行失败
  CANCELLED: 'CANCELLED', // 任务被取消
  UNKNOWN: 'UNKNOWN'      // 状态未知或无法确定
};

/**
 * 任务信息类，表示一个算子的执行任务
 */
class TaskInfo {
  /**
   * 任务ID
   * @type {string}
   */
  id;

  /**
   * 会话ID
   * @type {string}
   */
  sessionId;

  /**
   * 节点ID (与任务关联的图节点ID)
   * @type {string}
   */
  nodeId;

  /**
   * 任务状态，使用TaskStatus常量
   * @type {string}
   */
  status;

  /**
   * 任务消息
   * @type {string}
   */
  message;

  /**
   * 创建时间
   * @type {Date}
   */
  createdAt;

  /**
   * 更新时间
   * @type {Date}
   */
  updatedAt;

  /**
   * 结果资源ID（ResourceId在JavaScript中表示为字符串）
   * @type {string}
   */
  resultResourceId;

  /**
   * 结果数据
   * @type {Object}
   */
  result;

  /**
   * 上游是否发生变更
   * @type {boolean}
   */
  isUpstreamChanged;

  constructor(data) {
    data = data || {};
    this.id = data.id || '';
    this.sessionId = data.sessionId || '';
    this.nodeId = data.nodeId || '';
    this.status = data.status || TaskStatus.PENDING;
    this.message = data.message || '';
    this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
    this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
    this.resultResourceId = data.resultResourceId || '';
    this.result = data.result || null;
    this.isUpstreamChanged = data.isUpstreamChanged || false;
  }

  /**
   * 从后端模型创建TaskInfo实例
   * @param {Object} backendData - 后端返回的任务数据 (SnakeCase)
   * @returns {TaskInfo} TaskInfo实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端任务数据不能为空');
    }
    return new TaskInfo({
      id: backendData.task_id || backendData.id,
      sessionId: backendData.session_id || backendData.sessionId,
      nodeId: backendData.node_id || backendData.nodeId,
      status: backendData.status,
      message: backendData.message,
      createdAt: backendData.created_at || backendData.createdAt,
      updatedAt: backendData.updated_at || backendData.updatedAt,
      resultResourceId: backendData.result_resource_id || backendData.resultResourceId,
      result: backendData.result,
      isUpstreamChanged: backendData.is_upstream_changed || backendData.isUpstreamChanged,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的任务数据 (SnakeCase)
   */
  toBackendModel() {
    return {
      task_id: this.id,
      session_id: this.sessionId,
      node_id: this.nodeId,
      status: this.status,
      message: this.message,
      created_at: this.createdAt ? this.createdAt.toISOString() : null,
      updated_at: this.updatedAt ? this.updatedAt.toISOString() : null,
      result_resource_id: this.resultResourceId,
      result: this.result,
      is_upstream_changed: this.isUpstreamChanged,
    };
  }
}

export default TaskInfo;
export { TaskStatus }; 