import ObjectMetadata from './ObjectMetadata.js';
import { ConfigField } from './OperatorInfo.js';

/**
 * 后端信息类，描述数据处理后端的信息和配置
 */
class BackendInfo {
  /**
   * 后端类型
   * @type {string}
   */
  type;

  /**
   * 后端元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 会话配置字段，用于创建会话时动态渲染表单
   * @type {Array<ConfigField>}
   */
  sessionConfigs;

  constructor(data) {
    data = data || {};
    this.type = data.type || '';
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.sessionConfigs = (data.sessionConfigs || []).map(field => new ConfigField(field));
  }

  /**
   * 从后端模型创建BackendInfo实例
   * @param {Object} backendData - 后端返回的后端信息数据 (SnakeCase)
   * @returns {BackendInfo} BackendInfo实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端信息不能为空');
    }
    return new BackendInfo({
      type: backendData.backend_type || backendData.type, // 后端可能返回type或backend_type
      metadata: backendData.metadata ? ObjectMetadata.fromBackendModel(backendData.metadata) : null,
      sessionConfigs: (backendData.session_configs || backendData.sessionConfigs || []).map(field => ConfigField.fromBackendModel(field)), // 后端可能返回session_configs或sessionConfigs
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的后端信息 (SnakeCase)
   */
  toBackendModel() {
    return {
      backend_type: this.type,
      metadata: this.metadata ? this.metadata.toBackendModel() : null,
      session_configs: (this.sessionConfigs || []).map(field => field.toBackendModel()),
    };
  }
}

export default BackendInfo; 