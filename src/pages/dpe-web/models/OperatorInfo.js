import ObjectMetadata from './ObjectMetadata.js';

/**
 * 配置字段定义
 */
class ConfigField {
  /**
   * 字段键
   * @type {string}
   */
  key;

  /**
   * 字段的json-schema模式定义
   * @type {Object}
   */
  schema;

  constructor(data) {
    data = data || {};
    this.key = data.key || '';
    this.schema = data.schema || {};
  }

  /**
   * 从后端模型创建ConfigField实例
   * @param {Object} backendData - 后端返回的配置字段数据 (SnakeCase)
   * @returns {ConfigField} ConfigField实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端配置字段不能为空');
    }
    return new ConfigField({
      key: backendData.key,
      schema: backendData.schema,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的配置字段 (SnakeCase)
   */
  toBackendModel() {
    return {
      key: this.key,
      schema: this.schema,
    };
  }
}

/**
 * 算子配置类
 */
class OperatorInfo {
  /**
   * 算子类型
   * @type {string}
   */
  type;

  /**
   * 元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 配置元数据，定义算子的配置项
   * @type {Array<ConfigField>}
   */
  opConfigMetadata;

  /**
   * 输入字段列表，作为上游输入的字段
   * @type {Array<string>}
   */
  inputFields;

  constructor(data) {
    data = data || {};
    this.type = data.type || '';
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.opConfigMetadata = (data.opConfigMetadata || []).map(field => new ConfigField(field));
    this.inputFields = data.inputFields || [];
  }

  /**
   * 从后端模型创建OperatorInfo实例
   * @param {Object} backendData - 后端返回的算子信息数据 (SnakeCase)
   * @returns {OperatorInfo} OperatorInfo实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端算子信息不能为空');
    }
    return new OperatorInfo({
      type: backendData.type,
      metadata: backendData.metadata ? ObjectMetadata.fromBackendModel(backendData.metadata) : null,
      opConfigMetadata: (backendData.op_config_metadata || []).map(field => ConfigField.fromBackendModel(field)),
      inputFields: backendData.input_fields,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的算子信息 (SnakeCase)
   */
  toBackendModel() {
    return {
      type: this.type,
      metadata: this.metadata ? this.metadata.toBackendModel() : null,
      op_config_metadata: (this.opConfigMetadata || []).map(field => field.toBackendModel()),
      input_fields: this.inputFields,
    };
  }
}

export default OperatorInfo;
export { ConfigField }; 