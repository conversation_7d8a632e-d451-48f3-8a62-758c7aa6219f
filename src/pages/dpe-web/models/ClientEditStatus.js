import ExecutionGraph from './ExecutionGraph';
import PendingChanges from './PendingChanges';

/**
 * 客户端编辑状态信息
 * @class
 */
class ClientEditStatus {
  /**
   * 是否有未提交的变更
   * @type {boolean}
   */
  hasPendingChanges;

  /**
   * 当前会话实际生效的执行图
   * @type {ExecutionGraph}
   */
  currentEffectiveGraph;

  /**
   * 当前客户端对应的执行图（包含未提交的变更）
   * @type {ExecutionGraph}
   */
  currentGraph;

  /**
   * 未提交的执行图变更详情
   * @type {PendingChanges}
   */
  pendingChanges;

  /**
   * @param {Object} data - 数据
   * @param {boolean} data.hasPendingChanges - 是否有未提交的变更
   * @param {Object} [data.currentEffectiveGraph] - 当前会话实际生效的执行图
   * @param {Object} [data.currentGraph] - 当前客户端对应的执行图
   * @param {Object} [data.pendingChanges] - 未提交的执行图变更详情
   */
  constructor(data) {
    this.hasPendingChanges = data.hasPendingChanges;
    
    // 保持 currentEffectiveGraph 的原始类型，如果不是 ExecutionGraph 实例则创建新的
    this.currentEffectiveGraph = data.currentEffectiveGraph instanceof ExecutionGraph 
      ? data.currentEffectiveGraph 
      : new ExecutionGraph(data.currentEffectiveGraph || {});
    
    // 保持 currentGraph 的原始类型，如果不是 ExecutionGraph 实例则创建新的
    this.currentGraph = data.currentGraph instanceof ExecutionGraph 
      ? data.currentGraph 
      : new ExecutionGraph(data.currentGraph || {});
    
    this.pendingChanges = new PendingChanges(data.pendingChanges || {});
  }

  /**
   * 从后端模型创建ClientEditStatus实例
   * @param {Object} backendData - 后端返回的客户端编辑状态数据 (SnakeCase)
   * @returns {ClientEditStatus} ClientEditStatus实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端客户端编辑状态数据不能为空');
    }
    return new ClientEditStatus({
      hasPendingChanges: backendData.has_pending_changes,
      currentEffectiveGraph: backendData.current_effective_graph ? ExecutionGraph.fromBackendModel(backendData.current_effective_graph) : null,
      currentGraph: backendData.current_graph ? ExecutionGraph.fromBackendModel(backendData.current_graph) : null,
      pendingChanges: backendData.pending_changes ? PendingChanges.fromBackendModel(backendData.pending_changes) : null,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的客户端编辑状态数据 (SnakeCase)
   */
  toBackendModel() {
    return {
      has_pending_changes: this.hasPendingChanges,
      current_effective_graph: this.currentEffectiveGraph ? this.currentEffectiveGraph.toBackendModel() : null,
      current_graph: this.currentGraph ? this.currentGraph.toBackendModel() : null,
      pending_changes: this.pendingChanges ? this.pendingChanges.toBackendModel() : null,
    };
  }
}

export default ClientEditStatus; 