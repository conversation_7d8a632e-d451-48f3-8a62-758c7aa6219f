/**
 * 对象元数据类
 */
class ObjectMetadata {
  /**
   * 显示名称
   * @type {string}
   */
  displayName;

  /**
   * 描述
   * @type {string}
   */
  description;

  /**
   * 标签
   * @type {Object<string, string>}
   */
  labels;

  /**
   * 注解
   * @type {Object<string, string>}
   */
  annotations;

  constructor(data) {
    data = data || {};
    this.displayName = data.displayName || '';
    this.description = data.description || '';
    this.labels = data.labels || {};
    this.annotations = data.annotations || {};
  }

  /**
   * 从后端模型创建ObjectMetadata实例
   * @param {Object} backendData - 后端返回的元数据数据 (SnakeCase)
   * @returns {ObjectMetadata} ObjectMetadata实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端元数据不能为空');
    }
    return new ObjectMetadata({
      displayName: backendData.displayname,
      description: backendData.description,
      labels: backendData.labels,
      annotations: backendData.annotations,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的元数据 (SnakeCase)
   */
  toBackendModel() {
    return {
      displayname: this.displayName,
      description: this.description,
      labels: this.labels,
      annotations: this.annotations,
    };
  }
}

export default ObjectMetadata; 