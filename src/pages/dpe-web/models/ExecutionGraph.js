import ObjectMetadata from './ObjectMetadata.js';

/**
 * 图边类，表示节点之间的连接
 */
class GraphEdge {
  /**
   * 源节点ID
   * @type {string}
   */
  source;

  /**
   * 目标节点ID
   * @type {string}
   */
  target;

  /**
   * 目标节点的目标配置属性
   * @type {string}
   */
  targetConfig;

  constructor(data) {
    data = data || {};
    this.source = data.source || '';
    this.target = data.target || '';
    this.targetConfig = data.targetConfig || '';
  }

  /**
   * 从后端模型创建GraphEdge实例
   * @param {Object} backendData - 后端返回的图边数据 (SnakeCase)
   * @returns {GraphEdge} GraphEdge实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端图边数据不能为空');
    }
    return new GraphEdge({
      source: backendData.source,
      target: backendData.target,
      targetConfig: backendData.target_config,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的图边数据 (SnakeCase)
   */
  toBackendModel() {
    return {
      source: this.source,
      target: this.target,
      target_config: this.targetConfig,
    };
  }
}

/**
 * 图节点类，表示一个算子实例
 */
class GraphNode {
  /**
   * 节点ID
   * @type {string}
   */
  id;

  /**
   * 节点元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 算子类型
   * @type {string}
   */
  opType;

  /**
   * 算子配置字典，key为配置项名称，value为配置项值
   * @type {Object<string, any>}
   */
  opConfig;

  constructor(data) {
    data = data || {};
    this.id = data.id || '';
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.opType = data.opType || '';
    this.opConfig = data.opConfig || {};
  }

  /**
   * 从后端模型创建GraphNode实例
   * @param {Object} backendData - 后端返回的图节点数据 (SnakeCase)
   * @returns {GraphNode} GraphNode实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端图节点数据不能为空');
    }
    return new GraphNode({
      id: backendData.id,
      metadata: backendData.metadata ? ObjectMetadata.fromBackendModel(backendData.metadata) : null,
      opType: backendData.op_type,
      opConfig: backendData.op_config,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的图节点数据 (SnakeCase)
   */
  toBackendModel() {
    return {
      id: this.id,
      metadata: this.metadata ? this.metadata.toBackendModel() : null,
      op_type: this.opType,
      op_config: this.opConfig,
    };
  }
}

/**
 * 执行图类，表示一个数据处理工作流
 */
class ExecutionGraph {
  /**
   * 执行图ID
   * @type {string}
   */
  id;

  /**
   * 图的元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 图的节点集合，键为节点ID，值为节点对象
   * @type {Object<string, GraphNode>}
   */
  nodes;

  /**
   * 图的边集合
   * @type {Array<GraphEdge>}
   */
  edges;

  /**
   * 创建时间
   * @type {Date}
   */
  createdAt;

  /**
   * 更新时间
   * @type {Date}
   */
  updatedAt;

  constructor(data) {
    data = data || {};
    this.id = data.id || '';
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.nodes = {};
    this.edges = (data.edges || []).map(edge => new GraphEdge(edge));
    this.createdAt = data.createdAt ? new Date(data.createdAt) : null;
    this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : null;

    // 初始化节点
    if (data.nodes) {
      Object.keys(data.nodes).forEach(nodeId => {
        this.nodes[nodeId] = new GraphNode(data.nodes[nodeId]);
      });
    }
  }

  /**
   * 从后端模型创建ExecutionGraph实例
   * @param {Object} backendData - 后端返回的执行图数据 (SnakeCase)
   * @returns {ExecutionGraph} ExecutionGraph实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端执行图数据不能为空');
    }
    const nodes = {};
    if (backendData.nodes) {
      Object.keys(backendData.nodes).forEach(nodeId => {
        nodes[nodeId] = GraphNode.fromBackendModel(backendData.nodes[nodeId]);
      });
    }
    return new ExecutionGraph({
      id: backendData.id || backendData.graph_id,
      metadata: backendData.metadata ? ObjectMetadata.fromBackendModel(backendData.metadata) : null,
      nodes: nodes,
      edges: (backendData.edges || []).map(edge => GraphEdge.fromBackendModel(edge)),
      createdAt: backendData.create_time || backendData.createdAt,
      updatedAt: backendData.update_time || backendData.updatedAt,
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的执行图数据 (SnakeCase)
   */
  toBackendModel() {
    const nodes = {};
    if (this.nodes) {
      Object.keys(this.nodes).forEach(nodeId => {
        nodes[nodeId] = this.nodes[nodeId].toBackendModel();
      });
    }
    return {
      id: this.id,
      metadata: this.metadata ? this.metadata.toBackendModel() : null,
      nodes: nodes,
      edges: (this.edges || []).map(edge => edge.toBackendModel()),
      create_time: this.createdAt ? this.createdAt.toISOString() : null,
      update_time: this.updatedAt ? this.updatedAt.toISOString() : null,
    };
  }
}

export default ExecutionGraph;
export { GraphNode, GraphEdge }; 