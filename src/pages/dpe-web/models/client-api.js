import {
  BackendInfo,
  Session,
  SessionState,
  TaskInfo,
  TaskStatus,
  OperatorInfo,
  PageParam,
  ExecutionGraph,
  GraphNode,
  GraphEdge,
  ObjectMetadata,
  ClientEditStatus,
  PendingChanges
} from './index';

/**
 * 订阅对象，用于取消监听
 * @class
 */
class Subscription {
  /**
   * 创建订阅对象
   * @param {Function} unsubscribeFn - 取消订阅的函数
   */
  constructor(unsubscribeFn) {
    /**
     * @private
     * @type {Function}
     */
    this._unsubscribeFn = unsubscribeFn;
    /**
     * @private
     * @type {boolean}
     */
    this._isUnsubscribed = false;
  }

  /**
   * 取消订阅
   * @returns {void}
   */
  unsubscribe() {
    if (!this._isUnsubscribed && this._unsubscribeFn) {
      this._unsubscribeFn();
      this._isUnsubscribed = true;
    }
  }
}

/**
 * 会话生命周期监听器接口
 * @interface
 */
class SessionLifecycleListener {
  /**
   * 当会话被创建时调用
   * @param {Session} session - 创建的会话
   * @returns {void}
   */
  onSessionCreated(session) {
    throw new Error('Method \'onSessionCreated()\' must be implemented.');
  }

  /**
   * 当会话状态发生变更时调用
   * @param {Session} session - 会话
   * @param {string} oldState - 变更前的会话状态
   * @param {string} newState - 变更后的会话状态
   * @returns {void}
   */
  onSessionStateChanged(session, oldState, newState) {
    throw new Error('Method \'onSessionStateChanged()\' must be implemented.');
  }

  /**
   * 当会话被关闭时调用
   * @param {Session} session - 关闭的会话
   * @returns {void}
   */
  onSessionClosed(session) {
    throw new Error('Method \'onSessionClosed()\' must be implemented.');
  }
}

/**
 * 任务生命周期监听器接口
 * @interface
 */
class TaskLifecycleListener {
  /**
   * 当任务状态发生变更时调用
   * @param {TaskInfo} task - 任务
   * @param {string} oldStatus - 变更前的任务状态
   * @param {string} newStatus - 变更后的任务状态
   * @returns {void}
   */
  onTaskStatusChanged(task, oldStatus, newStatus) {
    throw new Error('Method \'onTaskStatusChanged()\' must be implemented.');
  }

  /**
   * 当任务完成时调用
   * @param {TaskInfo} task - 完成的任务
   * @returns {void}
   */
  onTaskCompleted(task) {
    throw new Error('Method \'onTaskCompleted()\' must be implemented.');
  }

  /**
   * 当任务出错时调用
   * @param {TaskInfo} task - 出错的任务
   * @param {Error} error - 错误信息
   * @returns {void}
   */
  onTaskError(task, error) {
    throw new Error('Method \'onTaskError()\' must be implemented.');
  }
}

// #region 数据资源相关客户端接口定义

/**
 * 数据资源基类
 * @class
 */
class DataResource {
  /**
   * 资源ID
   * @type {string}
   */
  resourceId;

  /**
   * 资源类型
   * @type {string}
   */
  resourceType;

  /**
   * 资源元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * @param {Object} data - 数据
   * @param {string} data.resourceId - 资源ID
   * @param {string} data.resourceType - 资源类型
   * @param {ObjectMetadata} [data.metadata] - 元数据
   */
  constructor(data) {
    this.resourceId = data.resourceId;
    this.resourceType = data.resourceType;
    this.metadata = data.metadata;
  }
}

/**
 * 结构化数据资源
 * @class
 * @extends DataResource
 */
class StructuredDataResource extends DataResource {
  /**
   * 数据结构定义
   * @type {Array<SchemaField>}
   */
  schema;

  /**
   * @param {Object} data - 数据
   * @param {string} data.resourceId - 资源ID
   * @param {string} data.resourceType - 资源类型
   * @param {ObjectMetadata} [data.metadata] - 元数据
   * @param {Array<SchemaField>} [data.schema] - 数据结构
   */
  constructor(data) {
    super(data);
    this.schema = data.schema;
  }
}

/**
 * 数据结构字段定义
 * @class
 */
class SchemaField {
  /**
   * 字段名称
   * @type {string}
   */
  name;

  /**
   * 字段类型
   * @type {string}
   */
  type;

  /**
   * 是否可为空
   * @type {boolean}
   */
  nullable;

  /**
   * 字段元数据
   * @type {Object}
   */
  metadata;

  /**
   * @param {Object} data - 数据
   * @param {string} data.name - 字段名称
   * @param {string} data.type - 字段类型
   * @param {boolean} [data.nullable=false] - 是否可为空
   * @param {Object} [data.metadata] - 字段元数据
   */
  constructor(data) {
    this.name = data.name;
    this.type = data.type;
    this.nullable = data.nullable || false;
    this.metadata = data.metadata || {};
  }
}

/**
 * 数据资源客户端接口，提供数据资源访问和清理能力
 * @interface
 */
class ResourceClient {
  /**
   * 获取资源ID
   * @abstract
   * @returns {string} 资源ID
   */
  getResourceId() {
    throw new Error('Method \'getResourceId()\' must be implemented.');
  }

  /**
   * 关闭客户端并释放资源
   * @abstract
   * @async
   * @returns {Promise<void>}
   */
  async close() {
    throw new Error('Method \'close()\' must be implemented.');
  }
}

/**
 * 结构化数据资源客户端接口，提供结构化数据访问能力
 * @interface
 * @extends ResourceClient
 */
class StructuredDataResourceClient extends ResourceClient {
  /**
   * 预览数据资源内容
   * 对应API: POST /api/resources/data-resource/structured/preview
   * @abstract
   * @async
   * @param {number} page - 页码
   * @param {number} pageSize - 每页条数
   * @returns {Promise<{total: number, schema: Array<SchemaField>, data: Array<Object>}>} 数据预览
   */
  async previewData(page, pageSize) {
    throw new Error('Method \'previewData()\' must be implemented.');
  }
  
  /**
   * 获取数据结构
   * 对应API: POST /api/resources/data-resource/structured/schema
   * @abstract
   * @async
   * @returns {Promise<Array<SchemaField>>} 数据结构
   */
  async getSchema() {
    throw new Error('Method \'getSchema()\' must be implemented.');
  }
}

// #endregion

// #region 执行图相关客户端接口定义

class UpdateNodeParam {
  /**
   * 节点元数据，null表示不更新
   * @type {ObjectMetadata | null}
   */
  metadata;

  /**
   * 节点配置，null表示不更新
   * @type {Object<string, any> | null}
   */
  opConfig;


  constructor(data) {
    this.metadata = data.metadata ?  new ObjectMetadata(data.metadata) : null;
    this.opConfig = data.opConfig ? data.opConfig : null;
  }


  toBackendModel() {
    return {
      metadata: this.metadata ? this.metadata.toBackendModel() : null,
      op_config: this.opConfig
    };
  }

  static fromBackendModel(data) {
    return new UpdateNodeParam({
      metadata: data.metadata ? ObjectMetadata.fromBackendModel(data.metadata) : null,
      opConfig: data.op_config
    });
  }
}


/**
 * 执行图加载时的合并模式枚举
 * 定义当加载图时发生冲突时的处理策略
 * @readonly
 * @enum {string}
 */
const MergeMode = {
  /** 遇到冲突时失败 */
  FAILED_ON_CONFLICT: 'failed_on_conflict',
  /** 覆盖现有内容 */
  OVERWRITE_EXISTING: 'overwrite_existing',
  /** 合并并更新内容 */
  MERGE_AND_UPDATE: 'merge_and_update'
};

/**
 * 执行图验证结果
 * 表示对执行图进行验证后的结果信息
 * @class
 */
class VerifyResult {
  /**
   * 验证是否通过
   * @type {boolean}
   */
  valid;

  /**
   * 验证失败的详细信息列表
   * @type {Array<VerifyFailureDetail>}
   */
  failedDetails;

  /**
   * 创建验证结果实例
   * @param {boolean} [valid=true] - 是否验证通过
   * @param {Array<VerifyFailureDetail>} [failedDetails=[]] - 验证失败详情列表
   */
  constructor(valid = true, failedDetails = []) {
    this.valid = valid;
    this.failedDetails = failedDetails;
  }
}

/**
 * 验证失败详情
 * 描述验证失败的具体原因和相关信息
 * @class
 */
class VerifyFailureDetail {
  /**
   * 验证规则类型
   * @type {string}
   */
  ruleType;

  /**
   * 失败消息
   * @type {string}
   */
  message;

  /**
   * 额外信息
   * @type {Object}
   */
  extraInfo;

  /**
   * 创建验证失败详情实例
   * @param {string} ruleType - 验证规则类型
   * @param {string} message - 失败消息
   * @param {Object} [extraInfo={}] - 额外的错误信息
   */
  constructor(ruleType, message, extraInfo = {}) {
    this.ruleType = ruleType;
    this.message = message;
    this.extraInfo = extraInfo;
  }
}

/**
 * 冲突错误类
 * 当操作发生冲突时抛出的异常
 * @class
 * @extends Error
 */
class ConflictError extends Error {
  /**
   * 冲突原因
   * @type {string}
   */
  reason;

  /**
   * 冲突类型
   * @type {string}
   */
  conflictType;

  /**
   * 创建冲突错误实例
   * @param {string} reason - 冲突原因
   * @param {string} conflictType - 冲突类型
   */
  constructor(reason, conflictType) {
    super(reason);
    this.name = 'ConflictError';
    this.reason = reason;
    this.conflictType = conflictType;
  }
}

/**
 * 算子未找到错误类
 * 当指定的算子不存在时抛出的异常
 * @class
 * @extends Error
 */
class OperatorNotFoundError extends Error {
  /**
   * 创建算子未找到错误实例
   * @param {string} message - 错误消息
   */
  constructor(message) {
    super(message);
    this.name = 'OperatorNotFoundError';
  }
}

/**
 * 图验证错误类
 * 当执行图验证失败时抛出的异常
 * @class
 * @extends Error
 */
class GraphValidationError extends Error {
  /**
   * 验证失败的原因
   * @type {string}
   */
  reason;

  /**
   * 创建图验证错误实例
   * @param {string} reason - 验证失败的原因
   */
  constructor(reason) {
    super(reason);
    this.name = 'GraphValidationError';
    this.reason = reason;
  }
}

/**
 * 执行图客户端接口，提供图的编辑和管理功能
 * @interface
 */
class ExecutionGraphClient {
  /**
   * 连接到WebSocket
   * @abstract
   * @async
   * @returns {Promise<void>}
   */
  async connect() {
    throw new Error('Method \'connect()\' must be implemented.');
  }

  /**
   * 获取当前编辑状态
   * @abstract
   * @async
   * @returns {Promise<ClientEditStatus>} 当前编辑状态
   */
  async get_edit_status() {
    throw new Error('Method \'get_edit_status()\' must be implemented.');
  }

  /**
   * 添加节点
   * @abstract
   * @async
   * @param {Array<GraphNode>} nodes - 要添加的节点列表
   * @returns {Promise<Array<string>>} 添加成功的节点ID列表
   */
  async add_nodes(nodes) {
    throw new Error('Method \'add_nodes()\' must be implemented.');
  }

  /**
   * 更新节点的配置
   * @abstract
   * @async
   * @param {Object<string, UpdateNodeParam>} updates - 键为节点ID，值为节点更新参数
   * @returns {Promise<Array<string>>} 更新成功的节点ID列表
   */
  async update_nodes(updates) {
    throw new Error('Method \'update_nodes()\' must be implemented.');
  }

  /**
   * 删除节点
   * @abstract
   * @async
   * @param {Array<string>} nodeIds - 要删除的节点ID列表
   * @returns {Promise<Array<string>>} 删除成功的节点ID列表
   */
  async delete_nodes(nodeIds) {
    throw new Error('Method \'delete_nodes()\' must be implemented.');
  }

  /**
   * 添加边
   * @abstract
   * @async
   * @param {Array<GraphEdge>} edges - 要添加的边列表
   * @returns {Promise<Array<string>>} 添加成功的边ID列表
   */
  async add_edges(edges) {
    throw new Error('Method \'add_edges()\' must be implemented.');
  }

  /**
   * 删除边
   * @abstract
   * @async
   * @param {Array<GraphEdge>} edges - 要删除的边列表
   * @returns {Promise<Array<string>>} 删除成功的边ID列表
   */
  async delete_edges(edges) {
    throw new Error('Method \'delete_edges()\' must be implemented.');
  }

  /**
   * 添加算子
   * @abstract
   * @async
   * @param {OperatorInfo} operator - 要添加的算子信息
   * @returns {Promise<OperatorInfo>} 添加的算子信息
   */
  async add_op(operator) {
    throw new Error('Method \'add_op()\' must be implemented.');
  }

  /**
   * 加载执行图
   * @abstract
   * @async
   * @param {ExecutionGraph} graph - 要加载的执行图
   * @param {string} mergeMode - 合并模式
   * @returns {Promise<Array<string>>} 受影响的节点ID列表
   */
  async load_graph(graph, mergeMode) {
    throw new Error('Method \'load_graph()\' must be implemented.');
  }

  /**
   * 验证执行图
   * @abstract
   * @async
   * @returns {Promise<Object>} 验证结果
   */
  async verify() {
    throw new Error('Method \'verify()\' must be implemented.');
  }

  /**
   * 保存执行图
   * @abstract
   * @async
   * @returns {Promise<Array<string>>} 保存结果
   */
  async save() {
    throw new Error('Method \'save()\' must be implemented.');
  }

  /**
   * 导出执行图
   * @abstract
   * @async
   * @returns {Promise<ExecutionGraph>} 导出的执行图
   */
  async export_graph() {
    throw new Error('Method \'export_graph()\' must be implemented.');
  }

  /**
   * 解析算子对应的任务
   * @abstract
   * @async
   * @param {OperatorInfo} operator - 算子信息
   * @returns {Promise<Object>} DAG任务信息
   */
  async resolve_task(operator) {
    throw new Error('Method \'resolve_task()\' must be implemented.');
  }

  /**
   * 关闭客户端连接
   * @abstract
   * @async
   * @returns {Promise<void>}
   */
  async close() {
    throw new Error('Method \'close()\' must be implemented.');
  }
}

// #endregion

export {
  Subscription,
  SessionLifecycleListener,
  TaskLifecycleListener,
  DataResource,
  StructuredDataResource,
  SchemaField,
  ResourceClient,
  StructuredDataResourceClient,
  MergeMode,
  VerifyResult,
  VerifyFailureDetail,
  ConflictError,
  OperatorNotFoundError,
  GraphValidationError,
  ExecutionGraphClient,
  UpdateNodeParam
}
