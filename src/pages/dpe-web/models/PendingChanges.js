/**
 * 待提交的图变更集合
 * @class
 */
class PendingChanges {
  /**
   * 要添加的节点映射：节点ID -> GraphNode
   * @type {Object<string, GraphNode>}
   */
  nodesToAdd;

  /**
   * 要更新的节点配置映射：节点ID -> 配置更新字典
   * @type {Object<string, Object>}
   */
  nodesToUpdate;

  /**
   * 要删除的节点ID集合
   * @type {Array<string>}
   */
  nodesToDelete;

  /**
   * 要添加的边列表
   * @type {Array<GraphEdge>}
   */
  edgesToAdd;

  /**
   * 要删除的边列表
   * @type {Array<GraphEdge>}
   */
  edgesToDelete;

  /**
   * @param {Object} data - 数据
   * @param {Object<string, Object>} [data.nodesToAdd] - 要添加的节点
   * @param {Object<string, Object>} [data.nodesToUpdate] - 要更新的节点配置
   * @param {Array<string>} [data.nodesToDelete] - 要删除的节点ID
   * @param {Array<Object>} [data.edgesToAdd] - 要添加的边
   * @param {Array<Object>} [data.edgesToDelete] - 要删除的边
   */
  constructor(data) {
    const { GraphNode, GraphEdge } = require('./ExecutionGraph');
    this.nodesToAdd = {};
    if (data.nodesToAdd) {
      Object.keys(data.nodesToAdd).forEach(nodeId => {
        this.nodesToAdd[nodeId] = new GraphNode(data.nodesToAdd[nodeId]);
      });
    }
    this.nodesToUpdate = data.nodesToUpdate || {};
    this.nodesToDelete = data.nodesToDelete || [];
    this.edgesToAdd = (data.edgesToAdd || []).map(edge => new GraphEdge(edge));
    this.edgesToDelete = (data.edgesToDelete || []).map(edge => new GraphEdge(edge));
  }

  /**
   * 从后端模型创建PendingChanges实例
   * @param {Object} backendData - 后端返回的待提交变更数据 (SnakeCase)
   * @returns {PendingChanges} PendingChanges实例
   * @throws {Error} 如果backendData为null或undefined
   */
  static fromBackendModel(backendData) {
    if (!backendData) {
      throw new Error('后端待提交变更数据不能为空');
    }
    const { GraphNode, GraphEdge } = require('./ExecutionGraph'); // 动态导入以避免循环依赖
    const nodesToAdd = {};
    if (backendData.nodes_to_add) {
      Object.keys(backendData.nodes_to_add).forEach(nodeId => {
        nodesToAdd[nodeId] = GraphNode.fromBackendModel(backendData.nodes_to_add[nodeId]);
      });
    }
    return new PendingChanges({
      nodesToAdd: nodesToAdd,
      nodesToUpdate: backendData.nodes_to_update,
      nodesToDelete: backendData.nodes_to_delete,
      edgesToAdd: (backendData.edges_to_add || []).map(edge => GraphEdge.fromBackendModel(edge)),
      edgesToDelete: (backendData.edges_to_delete || []).map(edge => GraphEdge.fromBackendModel(edge)),
    });
  }

  /**
   * 将当前实例转换为后端模型格式
   * @returns {Object} 后端模型格式的待提交变更数据 (SnakeCase)
   */
  toBackendModel() {
    const nodesToAdd = {};
    if (this.nodesToAdd) {
      Object.keys(this.nodesToAdd).forEach(nodeId => {
        nodesToAdd[nodeId] = this.nodesToAdd[nodeId].toBackendModel();
      });
    }
    return {
      nodes_to_add: nodesToAdd,
      nodes_to_update: this.nodesToUpdate,
      nodes_to_delete: this.nodesToDelete,
      edges_to_add: (this.edgesToAdd || []).map(edge => edge.toBackendModel()),
      edges_to_delete: (this.edgesToDelete || []).map(edge => edge.toBackendModel()),
    };
  }
}

export default PendingChanges; 