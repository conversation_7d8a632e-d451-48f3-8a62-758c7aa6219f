<template>
  <div class="execution-graph-list-page">
    <!-- 页面标题区 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title" data-testid="page-title">执行图列表</h1>
        <p class="page-description" data-testid="page-description">管理和运行所有执行图</p>
      </div>
      <div class="header-right">
        <a-button 
          type="primary" 
          icon="upload"
          data-testid="import-graph-btn"
          @click="handleImportClick"
        >
          导入执行图
        </a-button>
      </div>
    </div>

    <!-- 搜索和操作区 -->
    <div class="search-section">
      <div class="search-form">
        <a-input
          v-model="searchKeyword"
          placeholder="搜索执行图名称..."
          style="width: 300px; margin-right: 8px;"
          data-testid="search-input"
          @pressEnter="handleSearch"
        />
        <a-button 
          type="primary" 
          icon="search"
          data-testid="search-btn"
          @click="handleSearch"
        >
          搜索
        </a-button>
        <a-button 
          style="margin-left: 8px;"
          data-testid="reset-btn"
          @click="handleReset"
        >
          重置
        </a-button>
      </div>
    </div>

    <!-- 执行图列表 -->
    <div class="list-section">
      <a-table
        :dataSource="graphs"
        :columns="columns"
        :loading="loading"
        :pagination="tablePagination"
        rowKey="id"
        data-testid="graphs-table"
        @change="handleTableChange"
      >
        <!-- 执行图名称列 -->
        <template slot="nameSlot" slot-scope="text, record">
          <div class="graph-name-cell">
            <div class="graph-title" data-testid="graph-title">{{ record.metadata.displayName }}</div>
            <div class="graph-description" data-testid="graph-description">{{ record.metadata.description }}</div>
          </div>
        </template>

        <!-- 节点数量列 -->
        <template slot="nodesSlot" slot-scope="text, record">
          <span data-testid="node-count">{{ getNodeCount(record) }}</span>
        </template>

        <!-- 标签列 -->
        <template slot="labelsSlot" slot-scope="text, record">
          <div class="labels-cell" data-testid="graph-labels">
            <a-tag 
              v-for="(value, key) in record.metadata.labels"
              :key="key"
              color="blue"
              class="graph-label"
            >
              {{ key }}:{{ value }}
            </a-tag>
          </div>
        </template>

        <!-- 操作列 -->
        <template slot="actionsSlot" slot-scope="text, record">
          <div class="actions-cell">
            <a-button 
              type="primary" 
              size="small"
              data-testid="create-session-btn"
              @click="handleCreateSession(record)"
            >
              创建会话
            </a-button>
            <a-button 
              type="link" 
              size="small"
              data-testid="view-detail-btn"
              @click="handleViewDetail(record)"
            >
              查看详情
            </a-button>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 文件上传（隐藏） -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      style="display: none;"
      data-testid="file-input"
      @change="handleFileSelected"
    />

    <!-- 创建会话对话框 -->
    <CreateSessionDialog
      ref="createSessionDialog"
      v-if="showCreateDialog"
      :visible="showCreateDialog"
      :executionGraphOptions="graphs"
      :sessionConfigs="{}"
      data-testid="create-session-dialog"
      @cancel="handleDialogClose"
      @submit="handleSessionCreated"
    />
  </div>
</template>

<script>
/**
 * ExecutionGraphListPage - 执行图列表页面
 * @description 用于展示和管理所有已保存的执行图，支持搜索、导入和基于执行图创建会话
 * @component ExecutionGraphListPage
 * @example
 * <ExecutionGraphListPage />
 */

import ExecutionGraphRepositoryService from '../../services/ExecutionGraphRepositoryService';
import { PageParam } from '../../models';
import CreateSessionDialog from '../../components/CreateSessionDialog';

export default {
  name: 'ExecutionGraphListPage',
  
  components: {
    CreateSessionDialog
  },

  /**
   * 组件数据
   * @returns {Object} 组件的响应式数据
   */
  data() {
    return {
      /**
       * 执行图列表数据
       * @type {Array<ExecutionGraph>}
       */
      graphs: [],
      
      /**
       * 数据加载状态
       * @type {boolean}
       */
      loading: false,
      
      /**
       * 搜索关键词
       * @type {string}
       */
      searchKeyword: '',
      
      /**
       * 分页参数
       * @type {PageParam}
       */
      pagination: new PageParam({
        pageIndex: 0,
        limit: 10,
        sortField: 'metadata.displayName',
        sortType: 'asc'
      }),
      
      /**
       * 是否显示创建会话对话框
       * @type {boolean}
       */
      showCreateDialog: false,
      
      /**
       * 选中的执行图
       * @type {ExecutionGraph|null}
       */
      selectedGraph: null,
      
      /**
       * 表格列配置
       * @type {Array}
       */
      columns: [
        {
          title: '执行图名称',
          dataIndex: 'metadata.displayName',
          key: 'name',
          scopedSlots: { customRender: 'nameSlot' },
          width: '40%'
        },
        {
          title: '节点数量',
          dataIndex: 'nodes',
          key: 'nodeCount',
          scopedSlots: { customRender: 'nodesSlot' },
          width: '15%',
          align: 'center'
        },
        {
          title: '标签',
          dataIndex: 'metadata.labels',
          key: 'labels',
          scopedSlots: { customRender: 'labelsSlot' },
          width: '25%'
        },
        {
          title: '操作',
          key: 'actions',
          scopedSlots: { customRender: 'actionsSlot' },
          width: '20%',
          align: 'center'
        }
      ]
    };
  },

  /**
   * 计算属性
   */
  computed: {
    /**
     * 表格分页配置
     * @returns {Object} Ant Design Table 分页配置
     */
    tablePagination() {
      return {
        current: this.pagination.pageIndex + 1,
        pageSize: this.pagination.limit,
        total: this.pagination.recordTotal,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`,
        pageSizeOptions: ['10', '20', '50', '100']
      };
    }
  },

  /**
   * 监听器
   */
  watch: {
    /**
     * 监听对话框显示状态
     * @param {boolean} newValue - 新的显示状态
     */
    showCreateDialog(newValue) {
      if (newValue && this.selectedGraph && this.$refs.createSessionDialog) {
        this.$nextTick(() => {
          // 使用CreateSessionDialog的show方法预选执行图
          this.$refs.createSessionDialog.show({ id: this.selectedGraph.id });
        });
      }
    }
  },

  /**
   * 组件挂载后获取数据
   */
  async mounted() {
    await this.fetchGraphs();
  },

  methods: {
    /**
     * 获取执行图列表
     * @async
     * @returns {Promise<void>}
     */
    async fetchGraphs() {
      try {
        this.loading = true;
        
        const params = {
          pageParam: this.pagination,
          filters: this.searchKeyword ? {
            displayNamePattern: this.searchKeyword
          } : {}
        };

        const response = await ExecutionGraphRepositoryService.listGraphs(params);
        this.graphs = response.data;
        this.pagination = response.pageParam;
      } catch (error) {
        console.error('获取执行图列表失败:', error);
        this.$message.error('获取执行图列表失败：' + error.message);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 处理搜索操作
     * @returns {Promise<void>}
     */
    async handleSearch() {
      this.pagination.pageIndex = 0; // 重置到第一页
      await this.fetchGraphs();
    },

    /**
     * 处理重置操作
     * @returns {Promise<void>}
     */
    async handleReset() {
      this.searchKeyword = '';
      this.pagination.pageIndex = 0; // 重置到第一页
      await this.fetchGraphs();
    },

    /**
     * 处理表格变化（分页、排序等）
     * @param {Object} pagination - 分页信息
     * @param {Object} filters - 过滤信息
     * @param {Object} sorter - 排序信息
     * @returns {Promise<void>}
     */
    async handleTableChange(pagination, filters, sorter) {
      // 更新分页参数
      this.pagination.pageIndex = pagination.current - 1;
      this.pagination.limit = pagination.pageSize;
      
      // 更新排序参数
      if (sorter.field && sorter.order) {
        this.pagination.sortField = sorter.field;
        this.pagination.sortType = sorter.order === 'ascend' ? 'asc' : 'desc';
      }
      
      await this.fetchGraphs();
    },

    /**
     * 处理导入按钮点击
     * @returns {void}
     */
    handleImportClick() {
      this.$refs.fileInput.click();
    },

    /**
     * 处理文件选择
     * @param {Event} event - 文件选择事件
     * @returns {Promise<void>}
     */
    async handleFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      try {
        this.loading = true;
        await ExecutionGraphRepositoryService.importGraph(file);
        this.$message.success('执行图导入成功');
        
        // 清空文件输入
        this.$refs.fileInput.value = '';
        
        // 刷新列表
        await this.fetchGraphs();
      } catch (error) {
        console.error('导入执行图失败:', error);
        this.$message.error('导入执行图失败：' + error.message);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 处理创建会话操作
     * @param {ExecutionGraph} graph - 选中的执行图
     * @returns {void}
     */
    handleCreateSession(graph) {
      this.selectedGraph = graph;
      this.showCreateDialog = true;
    },

    /**
     * 处理查看详情操作
     * @param {ExecutionGraph} graph - 选中的执行图
     * @returns {void}
     */
    handleViewDetail(graph) {
      // TODO: 实现查看详情功能
      this.$message.info(`查看执行图详情: ${graph.metadata.displayName}`);
    },

    /**
     * 处理对话框关闭
     * @returns {void}
     */
    handleDialogClose() {
      this.showCreateDialog = false;
      this.selectedGraph = null;
    },

    /**
     * 处理会话创建成功
     * @param {Object} data - 创建会话的返回数据
     * @param {Session} data.session - 创建的会话
     * @param {Object} data.formData - 表单数据
     * @returns {void}
     */
    handleSessionCreated(data) {
      this.handleDialogClose();
      this.$message.success('会话创建成功');
      
      // 跳转到会话详情页
      this.$router.push({
        name: 'SessionDetailIndex',
        params: { sessionId: data.session.id }
      });
    },

    /**
     * 获取执行图节点数量
     * @param {ExecutionGraph} graph - 执行图对象
     * @returns {number} 节点数量
     */
    getNodeCount(graph) {
      return graph.nodes ? Object.keys(graph.nodes).length : 0;
    }
  }
};
</script>

<style lang="less" scoped>
.execution-graph-list-page {
  padding: 24px;
  background: #fff;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .header-left {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: #8c8c8c;
        margin: 0;
      }
    }

    .header-right {
      // 右侧操作按钮样式
    }
  }

  .search-section {
    margin-bottom: 16px;

    .search-form {
      display: flex;
      align-items: center;
    }
  }

  .list-section {
    .graph-name-cell {
      .graph-title {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }

      .graph-description {
        font-size: 12px;
        color: #8c8c8c;
        line-height: 1.4;
      }
    }

    .labels-cell {
      .graph-label {
        margin-bottom: 4px;
      }
    }

    .actions-cell {
      .ant-btn {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
