<template>
  <div class="session-detail-page" data-testid="session-detail-page">
    <!-- 加载状态 -->
    <div 
      v-if="isInitializing" 
      class="loading-container"
      data-testid="loading-container"
    >
      <a-spin size="large" tip="正在加载会话数据...">
        <div class="loading-content"></div>
      </a-spin>
    </div>
    
    <!-- 错误状态 -->
    <div 
      v-else-if="hasInitializationError" 
      class="error-container"
      data-testid="error-container"
    >
      <a-result
        status="error"
        :title="initializationError.title"
        :sub-title="initializationError.message"
      >
        <template #extra>
          <a-button 
            type="primary" 
            @click="retryInitialization"
            data-testid="retry-button"
          >
            重试
          </a-button>
          <a-button 
            @click="goToSessionList"
            data-testid="back-to-list-button"
          >
            返回会话列表
          </a-button>
        </template>
      </a-result>
    </div>
    
    <!-- 正常状态 -->
    <SessionDetailContent
      v-else-if="isInitialized"
      :session-info="sessionInfo"
      :execution-graph-client="executionGraphClient"
      :operators="operators"
      :can-save="canSave"
      :can-run="canRun"
      @error="handleContentError"
      data-testid="session-detail-content"
    />
  </div>
</template>

<script>
import SessionDetailContent from './content.vue';
import dataProcessService from '../../services/DataProcessService.js';

/**
 * SessionDetailIndex 组件
 * 
 * @description
 * SessionDetailIndex 作为页面的路由入口和初始化控制器，承担以下核心职责：
 * 1. 路由参数解析：从 $route.params 获取 sessionId
 * 2. 数据初始化：并发加载会话信息、创建客户端、获取算子列表
 * 3. 错误边界处理：捕获初始化过程中的所有错误并提供恢复机制
 * 4. 状态传递：将初始化完成的数据传递给 Content 组件
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 * 
 * @component SessionDetailIndex
 * @example
 * // 通过路由访问：/dpe-web/sessions/detail/:sessionId
 */
export default {
  name: 'SessionDetailIndex',
  
  components: {
    SessionDetailContent
  },

  /**
   * 组件数据状态
   */
  data() {
    return {
      // 路由相关
      /**
       * 会话ID，从路由参数获取
       * @type {string|null}
       */
      sessionId: null,
      
      // 初始化状态
      /**
       * 是否正在初始化
       * @type {boolean}
       */
      isInitializing: true,

      /**
       * 初始化错误信息
       * @type {Object|null}
       */
      initializationError: null,
      
      // 初始化数据
      /**
       * 会话信息对象
       * @type {Session|null}
       */
      sessionInfo: null,

      /**
       * 执行图客户端实例
       * @type {ExecutionGraphClient|null}
       */
      executionGraphClient: null,

      /**
       * 算子列表
       * @type {Array<OperatorInfo>}
       */
      operators: []
    };
  },

  /**
   * 计算属性
   */
  computed: {
    /**
     * 是否初始化完成
     * @returns {boolean} 初始化是否完成且核心数据都已加载
     */
    isInitialized() {
      return !this.isInitializing && 
             this.sessionInfo && 
             this.executionGraphClient &&
             this.operators.length > 0;
    },

    /**
     * 是否处于错误状态
     * @returns {boolean} 是否有初始化错误
     */
    hasInitializationError() {
      return !this.isInitializing && this.initializationError !== null;
    },

    /**
     * 是否可以保存图
     * @returns {boolean} 基于初始化状态判断保存权限
     */
    canSave() {
      return this.isInitialized && this.executionGraphClient !== null;
    },

    /**
     * 是否可以运行图
     * @returns {boolean} 基于初始化状态判断运行权限
     */
    canRun() {
      return this.isInitialized && this.executionGraphClient !== null;
    }
  },

  /**
   * 生命周期钩子 - 组件挂载后
   */
  async mounted() {
    // 获取路由参数中的sessionId
    this.sessionId = this.$route.params.sessionId;
    
    console.log('SessionDetailIndex mounted: sessionId =', this.sessionId, ', 完整路由参数:', this.$route.params);
    
    // 验证sessionId是否存在
    if (!this.sessionId || this.sessionId.trim() === '') {
      console.warn('SessionDetailIndex: sessionId 参数缺失或为空，跳转到会话列表页');
      this.goToSessionList();
      return;
    }
    
    // 开始初始化数据
    await this.loadInitialData();
  },

  /**
   * 生命周期钩子 - 组件销毁前
   */
  beforeDestroy() {
    // 清理执行图客户端连接
    if (this.executionGraphClient) {
      try {
        this.executionGraphClient.close();
      } catch (error) {
        console.warn('SessionDetailIndex: 关闭执行图客户端时发生错误:', error);
      }
    }
  },

  methods: {
    // ==================== 初始化方法 ====================

    /**
     * 加载初始化数据
     * @async
     * @returns {Promise<void>}
     * @description 按照TDS设计的初始化流程加载所有必需数据
     */
    async loadInitialData() {
      try {
        this.isInitializing = true;
        this.initializationError = null;
        
        // 串行：先加载会话信息（因为后续操作需要会话信息）
        await this.loadSessionInfo();
        
        // 串行：加载算子列表（需要会话信息中的backendType）
        // 算子列表是必需的，加载失败应该停止初始化
        await this.loadOperators();
        
        // 单独处理客户端初始化（可能失败但不影响其他功能）
        try {
          await this.initializeGraphClient();
          console.log('SessionDetailIndex: ExecutionGraphClient初始化成功，类型:', typeof this.executionGraphClient);
        } catch (error) {
          console.warn('SessionDetailIndex: ExecutionGraphClient初始化失败，但继续加载其他功能:', error);
          // 设置一个模拟的客户端，避免组件渲染失败
          this.executionGraphClient = {
            isConnected: false,
            close: () => Promise.resolve(),
            get_edit_status: () => Promise.resolve({
              currentGraph: { id: null, metadata: {}, nodes: {}, edges: {} },
              hasUnsavedChanges: false
            }),
            export_graph: () => Promise.resolve({ id: null, metadata: {}, nodes: {}, edges: {} })
          };
          console.log('SessionDetailIndex: 设置了fallback ExecutionGraphClient');
        }
        
      } catch (error) {
        this.handleInitializationError(error);
      } finally {
        this.isInitializing = false;
      }
    },

    /**
     * 加载会话信息
     * @async
     * @returns {Promise<void>}
     * @throws {Error} 会话加载失败时抛出错误
     */
    async loadSessionInfo() {
      try {
        this.sessionInfo = await dataProcessService.getSession(this.sessionId);
        console.log('SessionDetailIndex: 会话信息加载成功:', this.sessionInfo);
      } catch (error) {
        console.error('SessionDetailIndex: 加载会话信息失败:', error);
        throw new Error(`加载会话信息失败: ${error.message}`);
      }
    },

    /**
     * 初始化执行图客户端
     * @async
     * @returns {Promise<void>}
     * @throws {Error} 客户端初始化失败时抛出错误
     */
    async initializeGraphClient() {
      try {
        this.executionGraphClient = await dataProcessService.openGraphClient(this.sessionId);
        
        // 设置客户端错误处理
        if (this.executionGraphClient && this.executionGraphClient.onerror) {
          this.executionGraphClient.onerror = (error) => {
            console.error('SessionDetailIndex: ExecutionGraphClient 错误:', error);
            this.handleContentError(error);
          };
        }
        
        console.log('SessionDetailIndex: 执行图客户端初始化成功');
      } catch (error) {
        console.error('SessionDetailIndex: 初始化执行图客户端失败:', error);
        throw new Error(`初始化执行图客户端失败: ${error.message}`);
      }
    },

    /**
     * 加载算子列表
     * @async
     * @returns {Promise<void>}
     * @throws {Error} 算子列表加载失败时抛出错误
     */
    async loadOperators() {
      try {
        // 从会话信息中获取后端类型
        const backendType = this.sessionInfo?.backendType || 'duckdb';
        
        // 使用简单版本获取所有算子，或使用分页版本
        try {
          // 优先使用简单版本
          this.operators = await dataProcessService.listOperatorTypes(backendType);
        } catch (error) {
          // 如果简单版本失败，使用分页版本
          const result = await dataProcessService.queryOperatorTypes(backendType, {
            pageParam: { pageIndex: 0, limit: 1000 }
          });
          this.operators = result.data;
        }
        
        console.log('SessionDetailIndex: 算子列表加载成功，共', this.operators.length, '个算子');
      } catch (error) {
        console.error('SessionDetailIndex: 加载算子列表失败:', error);
        throw new Error(`加载算子列表失败: ${error.message}`);
      }
    },

    // ==================== 错误处理方法 ====================

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     * @description 设置错误状态并提供用户友好的错误信息
     */
    handleInitializationError(error) {
      this.initializationError = {
        title: '页面初始化失败',
        message: error.message || '未知错误，请稍后重试',
        action: 'retry'
      };
      
      console.error('SessionDetailIndex 初始化失败:', error);
    },

    /**
     * 处理Content组件错误
     * @param {Error} error - Content组件传递的错误
     * @description 处理来自SessionDetailContent组件的错误事件
     */
    handleContentError(error) {
      console.error('SessionDetailIndex: Content组件错误:', error);
      
      // 可以选择显示全局错误提示或其他处理方式
      this.$message.error('操作失败：' + (error.message || '未知错误'));
    },

    // ==================== 用户交互方法 ====================

    /**
     * 重试初始化
     * @async
     * @returns {Promise<void>}
     * @description 清除错误状态并重新执行初始化流程
     */
    async retryInitialization() {
      console.log('SessionDetailIndex: 用户触发重试初始化');
      this.initializationError = null;
      await this.loadInitialData();
    },

    /**
     * 跳转到会话列表页
     * @description 导航回会话列表页面
     */
    goToSessionList() {
      console.log('SessionDetailIndex: 尝试跳转到会话列表页 /dpe-web/sessions');
      this.$router.replace('/dpe-web/sessions');
    }
  }
};
</script>

<style scoped>
.session-detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.loading-container,
.error-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .loading-content {
    width: 200px;
    height: 100px;
  }
}

.error-container {
  padding: 40px 20px;
}

/* 确保SessionDetailContent组件占满剩余空间 */
.session-detail-page >>> .session-detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
