<template>
  <div class="session-detail-content" data-testid="session-detail-content">
    <!-- 顶部工具栏 -->
    <div class="content-header">
      <SessionToolbar
        :session-info="sessionInfo"
        :graph-data="graphData"
        :can-save="canSaveGraph"
        :can-run="canRunGraph"
        @save-graph="handleSaveGraph"
        @run-graph="handleRunGraph"
        @export-graph="handleExportGraph"
        @graph-settings="handleGraphSettings"
        @view-task-list="handleViewTaskList"
        data-testid="session-toolbar"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="content-body">
      <!-- 左侧算子面板 -->
      <div class="content-left">
        <OperatorPanel
          :operators="operators"
          @operator-drop="handleOperatorDrop"
          data-testid="operator-panel"
        />
      </div>

      <!-- 中间图编辑区域 -->
      <div class="content-center">
        <div class="graph-container">
          <!-- 使用 skeleton 而不是 spin 来提供初始高度 -->
          <a-skeleton 
            v-if="!isGraphInitialized" 
            active 
            :paragraph="{ rows: 8 }"
            class="graph-skeleton"
          />
          <GraphEditor
            v-show="isGraphInitialized"
            :graph-data="graphData"
            :initial-graph-data="initialGraphData"
            :readonly="isGraphReadonly"
            :show-minimap="true"
            :show-toolbar="true"
            :config="graphEditorConfig"
            @node-click="handleNodeSelect"
            @node-select="handleNodeSelect"
            @edge-click="handleEdgeClick"
            @edge-select="handleEdgeSelect"
            @blank-click="handleNodesDeselect"
            @graph-data-change="handleGraphDataChange"
            @selection-change="handleGraphSelectionChange"
            @node-add="handleGraphNodeAdded"
            @node-remove="handleGraphNodeRemoved"
            @node-connect="handleGraphEdgeConnected"
            @init-error="handleGraphInitError"
            data-testid="graph-editor"
            class="graph-editor"
          />
        </div>
      </div>

      <!-- 右侧节点详情面板 -->
      <div class="content-right">
        <NodeDetailPanel
          v-if="showNodeDetailPanel"
          :session-id="sessionInfo.id"
          :node-id="currentNodeId"
          :operator-info="currentNodeOperatorInfo"
          :editable="isNodeDetailEditable"
          v-model="nodeFormData"
          :callbacks="nodeDetailCallbacks"
          :available-upstream-nodes="availableUpstreamNodes"
          @input="handleNodeFormDataChange"
          @visibility-change="handleNodeDetailVisibilityChange"
          @view-result="handleViewTaskResult"
          data-testid="node-detail-panel"
          class="node-detail-panel"
        />
      </div>
    </div>

    <!-- 图设置对话框 -->
    <a-modal
      v-model="showGraphSettingsModal"
      title="图设置"
      :width="600"
      @ok="handleGraphSettingsOk"
      @cancel="handleGraphSettingsCancel"
      data-testid="graph-settings-modal"
    >
      <a-form :form="graphSettingsForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="图名称">
          <a-input
            v-decorator="['displayName', { 
              initialValue: graphData?.metadata?.displayName || '',
              rules: [{ required: true, message: '请输入图名称' }]
            }]"
            placeholder="请输入图名称"
          />
        </a-form-item>
        <a-form-item label="图描述">
          <a-textarea
            v-decorator="['description', { 
              initialValue: graphData?.metadata?.description || ''
            }]"
            placeholder="请输入图描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
/**
 * SessionDetailContent 组件
 * 
 * @description
 * SessionDetailContent 是 SessionDetailPage 页面的核心业务逻辑协调者和 UI 交互管理者。
 * 它承载了大部分的业务逻辑，并协调 GraphEditor、OperatorPanel、NodeDetailPanel 
 * 和 SessionToolbar 之间的交互。
 * 
 * 主要职责：
 * - 业务逻辑协调：作为中心枢纽，处理来自各个子组件的事件
 * - UI 交互管理：根据用户操作和后端状态，更新 UI 组件的展示状态和数据
 * - 状态同步：确保前端的执行图数据始终与后端 ExecutionGraphClient 保持同步
 * - 图数据管理：管理 RichExecutionGraph 实例的生命周期和变更
 * - 子组件事件处理：响应各类事件，并执行相应的业务逻辑
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 * 
 * @component SessionDetailContent
 * @example
 * <SessionDetailContent
 *   :session-info="sessionInfo"
 *   :execution-graph-client="executionGraphClient"
 *   :operators="operators"
 *   :can-save="canSave"
 *   :can-run="canRun"
 *   @error="handleContentError"
 * />
 */

import SessionToolbar from './components/SessionToolbar.vue';
import OperatorPanel from '@/pages/dpe-web/components/OperatorPanel';
import GraphEditor from '@/pages/dpe-web/components/GraphEditor';
import NodeDetailPanel from '@/pages/dpe-web/components/NodeDetailPanel';

import RichExecutionGraph, { RichGraphNode, RichGraphEdge } from '@/pages/dpe-web/models/RichGraph';
import { ObjectMetadata } from '@/pages/dpe-web/models';

export default {
  name: 'SessionDetailContent',

  components: {
    SessionToolbar,
    OperatorPanel,
    GraphEditor,
    NodeDetailPanel
  },

  /**
   * Props 接口定义
   */
  props: {
    /**
     * 当前会话的详细信息
     * @type {Session}
     * @required
     */
    sessionInfo: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.id === 'string';
      }
    },

    /**
     * 与后端执行图服务进行交互的客户端实例
     * @type {ExecutionGraphClient}
     * @required
     */
    executionGraphClient: {
      type: Object,
      required: true
    },

    /**
     * 可用的算子类型列表
     * @type {Array<OperatorInfo>}
     * @required
     */
    operators: {
      type: Array,
      required: true,
      default: () => []
    },

    /**
     * 指示当前执行图是否可保存
     * @type {boolean}
     * @required
     */
    canSave: {
      type: Boolean,
      required: true
    },

    /**
     * 指示当前执行图是否可运行
     * @type {boolean}
     * @required
     */
    canRun: {
      type: Boolean,
      required: true
    }
  },

  /**
   * 组件数据状态
   */
  data() {
    return {
      /**
       * 当前会话的执行图数据，包含 UI 相关的"富"信息
       * @type {RichExecutionGraph|null}
       */
      graphData: null,

      /**
       * 当前在 GraphEditor 中被选中的节点
       * @type {RichGraphNode|null}
       */
      currentNode: null,

      /**
       * 从 ExecutionGraphClient 获取的最新编辑状态
       * @type {ClientEditStatus|null}
       */
      clientEditStatus: null,

      /**
       * 业务逻辑处理中发生的错误状态
       * @type {Object|null}
       */
      errorState: null,

      /**
       * GraphEditor 初始加载的图数据，用于判断图是否发生变更
       * @type {RichExecutionGraph|null}
       */
      initialGraphData: null,

      /**
       * 指示当前是否正在保存执行图
       * @type {boolean}
       */
      isSaving: false,

      /**
       * 指示当前是否正在运行执行图或单个节点
       * @type {boolean}
       */
      isRunning: false,

      /**
       * 指示图编辑器是否已初始化
       * @type {boolean}
       */
      isGraphInitialized: false,

      /**
       * 节点详情面板是否显示
       * @type {boolean}
       */
      showNodeDetailPanel: false,

      /**
       * 节点表单数据（用于 v-model）
       * @type {Object}
       */
      nodeFormData: {},

      /**
       * 图设置对话框是否显示
       * @type {boolean}
       */
      showGraphSettingsModal: false,

      /**
       * 图设置表单
       * @type {Object}
       */
      graphSettingsForm: null,

      /**
       * GraphEditor 配置
       * @type {Object}
       */
      graphEditorConfig: {
        // GraphEditor 的配置参数
        grid: true,
        snapline: true,
        keyboard: true,
        clipboard: false
      }
    };
  },

  /**
   * 计算属性
   */
  computed: {
    /**
     * 当前选中节点的 ID
     * @returns {string|null}
     */
    currentNodeId() {
      return this.currentNode ? this.currentNode.id : null;
    },

    /**
     * 当前选中节点的算子信息
     * @returns {OperatorInfo|null}
     */
    currentNodeOperatorInfo() {
      if (!this.currentNode) return null;
      
      return this.operators.find(op => op.type === this.currentNode.opType) || null;
    },

    /**
     * 指示 NodeDetailPanel 是否可编辑
     * @returns {boolean}
     */
    isNodeDetailEditable() {
      return Boolean(this.currentNode && !this.isGraphReadonly);
    },

    /**
     * 传递给 NodeDetailPanel 的回调函数对象
     * @returns {Object}
     */
    nodeDetailCallbacks() {
      return {
        save: this.handleSaveNodeConfig,
        run: this.handleRunNode
      };
    },

    /**
     * 可供当前选中节点连接的上游节点列表
     * @returns {Array<GraphNode>}
     */
    availableUpstreamNodes() {
      if (!this.graphData || !this.currentNode) return [];
      
      // 返回除当前节点外的所有节点
      return Object.values(this.graphData.nodes)
        .filter(node => node.id !== this.currentNode.id)
        .map(richNode => richNode.toGraphNode());
    },

    /**
     * 指示 GraphEditor 是否处于只读模式
     * @returns {boolean}
     */
    isGraphReadonly() {
      // 根据会话状态等判断是否只读
      return this.sessionInfo.state === 'CLOSED';
    },

    /**
     * 指示是否可以保存图
     * @returns {boolean}
     */
    canSaveGraph() {
      return this.canSave && Boolean(this.graphData);
    },

    /**
     * 指示是否可以运行图
     * @returns {boolean}
     */
    canRunGraph() {
      return this.canRun && Boolean(this.graphData) && this.nodeCount > 0;
    },

    /**
     * 当前图的节点数量
     * @returns {number}
     */
    nodeCount() {
      if (!this.graphData || !this.graphData.nodes) return 0;
      return Object.keys(this.graphData.nodes).length;
    }
  },

  /**
   * 生命周期钩子
   */
  async created() {
    try {
      // 创建图设置表单
      this.graphSettingsForm = this.$form.createForm(this);
      
      // 初始化图数据
      await this.initializeGraphData();
      
      // 订阅 executionGraphClient 的状态变更事件
      this.setupClientEventListeners();
      
    } catch (error) {
      this.handleError('组件初始化失败', error);
    }
  },

  async mounted() {
    try {
      // 获取初始的图编辑状态来同步 graphData
      await this.syncGraphDataFromClientEditStatus();
      
      // 标记图编辑器已初始化
      this.isGraphInitialized = true;
      
    } catch (error) {
      this.handleError('图数据同步失败', error);
    }
  },

  async beforeDestroy() {
    // 取消订阅 executionGraphClient 的状态变更事件
    this.cleanupClientEventListeners();
  },

  methods: {
    // ==================== 初始化和清理方法 ====================

    /**
     * 初始化图数据
     * @async
     * @returns {Promise<void>}
     */
    async initializeGraphData() {
      try {
        // 创建空的 RichExecutionGraph 作为初始数据
        this.graphData = new RichExecutionGraph({
          id: 'empty-graph',
          metadata: new ObjectMetadata({
            displayName: '未命名图',
            description: ''
          }),
          nodes: {},
          edges: []
        });

        // 保存初始图数据的副本
        this.initialGraphData = new RichExecutionGraph({
          id: this.graphData.id,
          metadata: this.graphData.metadata,
          nodes: {},
          edges: []
        });

      } catch (error) {
        throw new Error(`初始化图数据失败: ${error.message}`);
      }
    },

    /**
     * 设置客户端事件监听器
     */
    setupClientEventListeners() {
      // 注意：这里需要根据实际的 ExecutionGraphClient 接口来实现
      // 当前 MockExecutionGraphClient 没有提供事件监听接口
      // 在真实环境中，应该监听 WebSocket 事件或其他状态变更通知
    },

    /**
     * 清理客户端事件监听器
     */
    cleanupClientEventListeners() {
      // 清理事件监听器，防止内存泄漏
    },

    // ==================== 图数据管理方法 ====================

    /**
     * 从 executionGraphClient 获取最新状态并同步 graphData
     * @async
     * @returns {Promise<void>}
     */
    async syncGraphDataFromClientEditStatus() {
      try {
        // 获取最新的客户端编辑状态
        this.clientEditStatus = await this.executionGraphClient.get_edit_status();
        
        // 将 ExecutionGraph 转换为 RichExecutionGraph
        if (this.clientEditStatus.currentGraph) {
          this.graphData = RichExecutionGraph.fromExecutionGraph(this.clientEditStatus.currentGraph);
        }

      } catch (error) {
        throw new Error(`同步图数据失败: ${error.message}`);
      }
    },

    /**
     * 响应 GraphEditor 的 graph-data-change 事件
     * @param {RichExecutionGraph} newGraphData - 更新的图数据
     */
    handleGraphDataChange(newGraphData) {
      if (newGraphData instanceof RichExecutionGraph) {
        this.graphData = newGraphData;
      }
    },

    /**
     * 响应 GraphEditor 的 node-add 事件
     * @param {Object} payload - 事件数据
     * @param {RichGraphNode} payload.node - 添加的节点
     */
    handleGraphNodeAdded({ node }) {
      if (node instanceof RichGraphNode) {
        // 前端更新 graphData，但不立即调用后端 add_nodes
        this.graphData.addNode(node);
        
        // 选中新添加的节点
        this.selectNode(node);
      }
    },

    /**
     * 响应 GraphEditor 的 node-remove 事件
     * @param {Object} payload - 事件数据
     * @param {string} payload.nodeId - 删除的节点ID
     */
    handleGraphNodeRemoved({ nodeId }) {
      // 前端更新 graphData，不立即调用后端 delete_nodes
      this.graphData.removeNode(nodeId);
      
      // 如果删除的是当前选中节点，清除选择
      if (this.currentNode && this.currentNode.id === nodeId) {
        this.currentNode = null;
        this.showNodeDetailPanel = false;
      }
    },

    /**
     * 响应 GraphEditor 的 edge-connected 事件
     * @param {Object} payload - 事件数据
     * @param {RichGraphEdge} payload.edge - 连接的边
     * @param {string} payload.source - 源节点ID
     * @param {string} payload.target - 目标节点ID
     */
    handleGraphEdgeConnected({ edge, source, target }) {
      if (edge instanceof RichGraphEdge) {
        // 前端更新 graphData，不立即调用后端 add_edges
        this.graphData.addEdge(edge);
      }
    },

    // ==================== 节点交互处理方法 ====================

    /**
     * 响应 OperatorPanel 的 operator-drop 事件
     * @param {Object} payload - 事件数据
     * @param {OperatorInfo} payload.operator - 拖拽的算子
     * @param {Object} payload.position - 拖拽位置
     */
    async handleOperatorDrop({ operator, position }) {
      if (!operator) {
        console.warn('无效的算子拖拽事件');
        return;
      }

      try {
        // 创建新的 RichGraphNode
        const newRichNode = new RichGraphNode({
          id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          metadata: new ObjectMetadata({
            displayName: operator.metadata?.displayName || operator.type,
            description: operator.metadata?.description || `${operator.type} 节点`
          }),
          opType: operator.type,
          opConfig: {},
          displayAttr: {
            x: position.x || 200,
            y: position.y || 150
          }
        });

        // 转换为基础节点并添加到客户端
        const graphNode = newRichNode.toGraphNode();
        await this.executionGraphClient.add_nodes([graphNode]);
        
        // 重新加载图数据和状态以同步最新状态
        await this.loadGraphAndStatus();
        
        // 从重新加载的图数据中找到新添加的节点并选中
        const addedNode = this.graphData.getNode(newRichNode.id);
        if (addedNode) {
          this.selectNode(addedNode);
          console.log('算子已添加并选中:', newRichNode.id);
        } else {
          console.warn('新添加的节点未找到:', newRichNode.id);
        }

      } catch (error) {
        console.error('添加算子失败:', error);
        this.$message.error('添加算子失败：' + error.message);
      }
    },

    /**
     * 响应 GraphEditor 的节点选择事件
     * @param {Object} payload - 事件数据
     * @param {RichGraphNode} payload.node - 选中的节点
     */
    handleNodeSelect({ node }) {
      this.selectNode(node);
    },

    /**
     * 响应 GraphEditor 的边选择事件
     * @param {Object} payload - 事件数据
     * @param {RichGraphEdge} payload.edge - 选中的边
     */
    handleEdgeClick({ edge }) {
      // 暂时不处理边选择
    },

    /**
     * 响应 GraphEditor 的边选择事件
     * @param {Object} payload - 事件数据
     * @param {RichGraphEdge} payload.edge - 选中的边
     */
    handleEdgeSelect({ edge }) {
      // 暂时不处理边选择
    },

    /**
     * 响应 GraphEditor 的空白点击事件
     */
    handleNodesDeselect() {
      this.currentNode = null;
      this.showNodeDetailPanel = false;
      this.nodeFormData = {};
    },

    /**
     * 响应 GraphEditor 的选择变更事件
     * @param {Object} payload - 事件数据
     * @param {Array} payload.added - 新增选中项
     * @param {Array} payload.removed - 移除选中项
     * @param {Array} payload.selected - 当前选中项
     */
    handleGraphSelectionChange({ added, removed, selected }) {
      // 查找选中的节点
      const selectedNode = selected.find(item => item instanceof RichGraphNode);
      if (selectedNode) {
        this.selectNode(selectedNode);
      } else if (selected.length === 0) {
        this.handleNodesDeselect();
      }
    },

    /**
     * 选中指定节点
     * @param {RichGraphNode} node - 要选中的节点
     */
    selectNode(node) {
      this.currentNode = node;
      this.showNodeDetailPanel = true;
      
      // 准备节点表单数据
      this.nodeFormData = {
        metadata: {
          displayName: node.metadata.displayName || '',
          description: node.metadata.description || ''
        },
        opConfig: { ...node.opConfig },
        opInputs: this.getNodeInputs(node)
      };
    },

    /**
     * 获取节点的输入连接
     * @param {RichGraphNode} node - 节点
     * @returns {Object} 输入连接映射
     */
    getNodeInputs(node) {
      const inputs = {};
      
      if (this.graphData && this.graphData.edges) {
        // 查找以该节点为目标的边
        this.graphData.edges
          .filter(edge => edge.target === node.id)
          .forEach(edge => {
            edge.targetConfigs.forEach(targetConfig => {
              if (!inputs[targetConfig]) {
                inputs[targetConfig] = [];
              }
              inputs[targetConfig].push(edge.source);
            });
          });
      }
      
      return inputs;
    },

    /**
     * 响应 GraphEditor 的初始化错误事件
     * @param {Error} error - 错误对象
     */
    handleGraphInitError(error) {
      this.handleError('图编辑器初始化失败', error);
    },

    // ==================== NodeDetailPanel 回调实现 ====================

    /**
     * 响应 NodeDetailPanel 的输入事件
     * @param {Object} formData - 表单数据
     */
    handleNodeFormDataChange(formData) {
      this.nodeFormData = { ...formData };
    },

    /**
     * 响应 NodeDetailPanel 的可见性变更事件
     * @param {boolean} visible - 是否可见
     */
    handleNodeDetailVisibilityChange(visible) {
      this.showNodeDetailPanel = visible;
      if (!visible) {
        this.currentNode = null;
        this.nodeFormData = {};
      }
    },

    /**
     * 保存节点配置
     * @async
     * @param {Object} formData - 表单数据
     * @returns {Promise<void>}
     */
    async handleSaveNodeConfig(formData) {
      if (!this.currentNode) return;

      try {
        this.isSaving = true;

        // 更新节点的元数据和配置
        if (formData.metadata) {
          this.currentNode.metadata.displayName = formData.metadata.displayName || '';
          this.currentNode.metadata.description = formData.metadata.description || '';
        }

        if (formData.opConfig) {
          this.currentNode.opConfig = { ...formData.opConfig };
        }

        // 处理输入连接变化
        if (formData.opInputs) {
          await this.updateNodeConnections(this.currentNode, formData.opInputs);
        }

        // 检查节点是否已存在于后端
        const existsInBackend = this.clientEditStatus && 
          this.clientEditStatus.currentEffectiveGraph &&
          this.clientEditStatus.currentEffectiveGraph.nodes[this.currentNode.id];

        if (existsInBackend) {
          // 更新现有节点
          const updateParam = {
            metadata: this.currentNode.metadata,
            opConfig: this.currentNode.opConfig
          };
          await this.executionGraphClient.update_nodes({
            [this.currentNode.id]: updateParam
          });
        } else {
          // 添加新节点
          const graphNode = this.currentNode.toGraphNode();
          await this.executionGraphClient.add_nodes([graphNode]);
        }

        // 同步最新状态
        await this.syncGraphDataFromClientEditStatus();

        this.$message.success('节点配置保存成功');

      } catch (error) {
        this.handleError('保存节点配置失败', error);
      } finally {
        this.isSaving = false;
      }
    },

    /**
     * 更新节点连接
     * @async
     * @param {RichGraphNode} node - 节点
     * @param {Object} newInputs - 新的输入连接
     * @returns {Promise<void>}
     */
    async updateNodeConnections(node, newInputs) {
      const currentInputs = this.getNodeInputs(node);
      
      // 计算需要添加和删除的边
      const edgesToAdd = [];
      const edgesToDelete = [];

      // 检查新的连接
      Object.keys(newInputs).forEach(inputField => {
        const newSources = newInputs[inputField] || [];
        const currentSources = currentInputs[inputField] || [];

        // 添加新连接
        newSources.forEach(sourceId => {
          if (!currentSources.includes(sourceId)) {
            edgesToAdd.push({
              source: sourceId,
              target: node.id,
              targetConfig: inputField
            });
          }
        });

        // 删除不存在的连接
        currentSources.forEach(sourceId => {
          if (!newSources.includes(sourceId)) {
            edgesToDelete.push({
              source: sourceId,
              target: node.id,
              targetConfig: inputField
            });
          }
        });
      });

      // 检查删除的输入字段
      Object.keys(currentInputs).forEach(inputField => {
        if (!newInputs[inputField]) {
          const currentSources = currentInputs[inputField] || [];
          currentSources.forEach(sourceId => {
            edgesToDelete.push({
              source: sourceId,
              target: node.id,
              targetConfig: inputField
            });
          });
        }
      });

      // 执行边的添加和删除
      if (edgesToAdd.length > 0) {
        await this.executionGraphClient.add_edges(edgesToAdd);
      }

      if (edgesToDelete.length > 0) {
        await this.executionGraphClient.delete_edges(edgesToDelete);
      }
    },

    /**
     * 运行单个节点
     * @async
     * @returns {Promise<void>}
     */
    async handleRunNode() {
      if (!this.currentNode) return;

      try {
        this.isRunning = true;

        // 这里需要根据实际的 ExecutionGraphClient 接口实现
        // 暂时使用模拟实现
        this.$message.info('节点运行功能暂未实现');

      } catch (error) {
        this.handleError('运行节点失败', error);
      } finally {
        this.isRunning = false;
      }
    },

    /**
     * 查看任务结果
     * @param {string} resourceId - 资源ID
     */
    handleViewTaskResult(resourceId) {
      // 导航到数据预览页面或打开预览组件
      this.$message.info(`查看资源: ${resourceId}`);
    },

    // ==================== SessionToolbar 事件处理 ====================

    /**
     * 保存整个执行图
     * @async
     * @returns {Promise<void>}
     */
    async handleSaveGraph() {
      try {
        this.isSaving = true;

        // 调用客户端保存方法
        await this.executionGraphClient.save();

        // 同步最新状态
        await this.syncGraphDataFromClientEditStatus();

        this.$message.success('执行图保存成功');

      } catch (error) {
        this.handleError('保存执行图失败', error);
      } finally {
        this.isSaving = false;
      }
    },

    /**
     * 运行整个执行图
     * @async
     * @returns {Promise<void>}
     */
    async handleRunGraph() {
      try {
        this.isRunning = true;

        // 这里需要根据实际的 ExecutionGraphClient 接口实现
        this.$message.info('执行图运行功能暂未实现');

      } catch (error) {
        this.handleError('运行执行图失败', error);
      } finally {
        this.isRunning = false;
      }
    },

    /**
     * 导出执行图
     * @async
     * @returns {Promise<void>}
     */
    async handleExportGraph() {
      try {
        const graph = await this.executionGraphClient.export_graph();
        
        // 创建下载链接
        const dataStr = JSON.stringify(graph.toBackendModel(), null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${this.graphData.metadata.displayName || 'execution-graph'}.json`;
        link.click();
        
        this.$message.success('执行图导出成功');

      } catch (error) {
        this.handleError('导出执行图失败', error);
      }
    },

    /**
     * 打开图设置对话框
     */
    handleGraphSettings() {
      this.showGraphSettingsModal = true;
    },

    /**
     * 图设置对话框确认
     */
    handleGraphSettingsOk() {
      this.graphSettingsForm.validateFields((err, values) => {
        if (!err) {
          // 更新图元数据
          if (this.graphData && this.graphData.metadata) {
            this.graphData.metadata.displayName = values.displayName || '';
            this.graphData.metadata.description = values.description || '';
          }
          
          this.showGraphSettingsModal = false;
          this.$message.success('图设置更新成功');
        }
      });
    },

    /**
     * 图设置对话框取消
     */
    handleGraphSettingsCancel() {
      this.showGraphSettingsModal = false;
    },

    /**
     * 查看任务列表
     */
    handleViewTaskList() {
      // 导航到任务列表页面或打开任务列表组件
      this.$message.info('任务列表功能暂未实现');
    },

    // ==================== 错误处理方法 ====================

    /**
     * 统一错误处理
     * @param {string} operation - 操作描述
     * @param {Error} error - 错误对象
     */
    handleError(operation, error) {
      const errorMessage = `${operation}: ${error.message}`;
      
      this.errorState = {
        operation,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      console.error(errorMessage, error);
      this.$message.error(errorMessage);

      // 向父组件发送错误事件
      this.$emit('error', new Error(errorMessage));
    }
  }
};
</script>

<style scoped>
.session-detail-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.content-header {
  flex: 0 0 auto;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.content-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-left {
  flex: 0 0 280px;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.content-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
}

.graph-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.graph-skeleton {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
  background-color: #fff;
}

.graph-editor {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.content-right {
  flex: 0 0 400px;
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.node-detail-panel {
  flex: 1;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-left {
    flex: 0 0 240px;
  }
  
  .content-right {
    flex: 0 0 360px;
  }
}

@media (max-width: 768px) {
  .content-body {
    flex-direction: column;
  }
  
  .content-left,
  .content-right {
    flex: 0 0 auto;
    height: 200px;
  }
  
  .content-center {
    flex: 1;
    min-height: 400px;
  }
}

/* 确保子组件正确填充容器 */
.content-left >>> .operator-panel,
.content-right >>> .node-detail-panel {
  height: 100%;
}

/* 加载状态样式 */
.graph-skeleton >>> .ant-skeleton-content {
  height: 100%;
}

.graph-skeleton >>> .ant-skeleton-paragraph {
  margin-bottom: 24px;
}

/* 图设置对话框样式 */
.ant-modal-body .ant-form-item {
  margin-bottom: 16px;
}
</style>
