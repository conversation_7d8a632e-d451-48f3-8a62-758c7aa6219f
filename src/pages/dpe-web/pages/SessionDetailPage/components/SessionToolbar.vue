<template>
  <div class="session-toolbar">
    <div class="toolbar-left">
      <!-- 会话信息 -->
      <div class="session-info">
        <a-icon type="database" class="session-icon" />
        <span class="session-title">{{ sessionInfo.metadata?.displayName || `会话 ${sessionInfo.id}` }}</span>
        <a-tag :color="getSessionStateColor(sessionInfo.state)" size="small">
          {{ sessionInfo.state }}
        </a-tag>
      </div>
    </div>
    
    <div class="toolbar-center">
      <!-- 图状态信息 -->
      <div v-if="graphData" class="graph-info">
        <span class="graph-stats">
          <a-icon type="apartment" />
          节点: {{ nodeCount }}
        </span>
        <span class="graph-stats">
          <a-icon type="arrows-alt" />
          连接: {{ edgeCount }}
        </span>
      </div>
    </div>
    
    <div class="toolbar-right">
      <!-- 操作按钮组 -->
      <a-button-group>
        <!-- 图设置按钮 (UC-SDP-12) -->
        <a-button
          icon="setting"
          @click="handleGraphSettings"
          :disabled="!graphData"
        >
          图设置
        </a-button>
        
        <!-- 任务列表按钮 (UC-SDP-14) -->
        <a-button
          icon="unordered-list"
          @click="handleViewTaskList"
        >
          任务列表
        </a-button>
        
        <!-- 导出按钮 (UC-SDP-13) -->
        <a-button
          icon="download"
          @click="handleExportGraph"
          :disabled="!graphData"
        >
          导出
        </a-button>
      </a-button-group>
      
      <!-- 主要操作按钮 -->
      <a-button-group class="main-actions">
        <!-- 保存按钮 -->
        <a-button
          type="primary"
          icon="save"
          @click="handleSaveGraph"
          :disabled="!canSave || !graphData"
          :loading="isSaving"
        >
          保存
        </a-button>
        
        <!-- 运行按钮 (UC-SDP-11) -->
        <a-button
          type="primary"
          icon="play-circle"
          @click="handleRunGraph"
          :disabled="!canRun || !hasExecutableNodes"
          :loading="isRunning"
        >
          运行
        </a-button>
      </a-button-group>
    </div>
  </div>
</template>

<script>
/**
 * SessionToolbar 组件
 * 
 * @description
 * SessionToolbar 作为会话详情页的顶部工具栏组件，承担以下职责：
 * 1. 显示会话基本信息和状态
 * 2. 显示当前执行图的统计信息
 * 3. 提供图操作按钮（保存、运行、导出、设置等）
 * 4. 管理操作按钮的状态和可用性
 * 5. 向父组件发送操作事件
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 * 
 * @component SessionToolbar
 * @example
 * <SessionToolbar
 *   :session-info="sessionInfo"
 *   :graph-data="graphData"
 *   :can-save="canSave"
 *   :can-run="canRun"
 *   @save-graph="handleSaveGraph"
 *   @run-graph="handleRunGraph"
 *   @export-graph="handleExportGraph"
 *   @graph-settings="handleGraphSettings"
 *   @view-task-list="handleViewTaskList"
 * />
 */

export default {
  name: 'SessionToolbar',

  /**
   * Props 接口定义
   */
  props: {
    /**
     * 会话信息
     * @type {Session}
     * @required
     * @description 包含会话ID、状态、元数据等信息的会话对象
     */
    sessionInfo: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.id === 'string';
      }
    },

    /**
     * 执行图数据
     * @type {Object|null}
     * @required
     * @description 当前加载的执行图数据，用于显示图统计信息
     */
    graphData: {
      type: Object,
      default: null
    },

    /**
     * 是否可以保存图
     * @type {boolean}
     * @required
     * @description 控制保存按钮的可用状态
     */
    canSave: {
      type: Boolean,
      required: true
    },

    /**
     * 是否可以运行图
     * @type {boolean}
     * @required
     * @description 控制运行按钮的可用状态
     */
    canRun: {
      type: Boolean,
      required: true
    }
  },

  /**
   * 组件数据状态
   */
  data() {
    return {
      /**
       * 是否正在保存
       * @type {boolean}
       */
      isSaving: false,

      /**
       * 是否正在运行
       * @type {boolean}
       */
      isRunning: false
    };
  },

  /**
   * 计算属性
   */
  computed: {
    /**
     * 当前图的节点数量
     * @returns {number} 节点数量
     */
    nodeCount() {
      if (!this.graphData || !this.graphData.nodes) {
        return 0;
      }
      return Object.keys(this.graphData.nodes).length;
    },

    /**
     * 当前图的连接数量
     * @returns {number} 连接数量
     */
    edgeCount() {
      if (!this.graphData || !this.graphData.edges) {
        return 0;
      }
      return Object.keys(this.graphData.edges).length;
    },

    /**
     * 是否有可执行的节点
     * @returns {boolean} 是否有可执行节点
     */
    hasExecutableNodes() {
      return this.nodeCount > 0;
    }
  },

  methods: {
    // ==================== 事件处理方法 ====================

    /**
     * 处理保存图操作
     * @async
     * @returns {Promise<void>}
     * @description 向父组件发送保存图事件，并管理保存状态
     * @emits save-graph
     */
    async handleSaveGraph() {
      try {
        this.isSaving = true;
        /**
         * 保存图事件
         * @event save-graph
         * @description 用户点击保存按钮时触发
         */
        this.$emit('save-graph');
      } finally {
        // 延迟重置状态，给用户反馈时间
        setTimeout(() => {
          this.isSaving = false;
        }, 1000);
      }
    },

    /**
     * 处理运行图操作
     * @async
     * @returns {Promise<void>}
     * @description 向父组件发送运行图事件，并管理运行状态
     * @emits run-graph
     */
    async handleRunGraph() {
      try {
        this.isRunning = true;
        /**
         * 运行图事件
         * @event run-graph
         * @description 用户点击运行按钮时触发
         */
        this.$emit('run-graph');
      } finally {
        // 延迟重置状态，给用户反馈时间
        setTimeout(() => {
          this.isRunning = false;
        }, 2000);
      }
    },

    /**
     * 处理导出图操作
     * @description 向父组件发送导出图事件
     * @emits export-graph
     */
    handleExportGraph() {
      /**
       * 导出图事件
       * @event export-graph
       * @description 用户点击导出按钮时触发
       */
      this.$emit('export-graph');
    },

    /**
     * 处理图设置操作
     * @description 向父组件发送图设置事件
     * @emits graph-settings
     */
    handleGraphSettings() {
      /**
       * 图设置事件
       * @event graph-settings
       * @description 用户点击图设置按钮时触发
       */
      this.$emit('graph-settings');
    },

    /**
     * 处理查看任务列表操作
     * @description 向父组件发送查看任务列表事件
     * @emits view-task-list
     */
    handleViewTaskList() {
      /**
       * 查看任务列表事件
       * @event view-task-list
       * @description 用户点击任务列表按钮时触发
       */
      this.$emit('view-task-list');
    },

    // ==================== 工具方法 ====================

    /**
     * 获取会话状态对应的颜色
     * @param {string} state - 会话状态
     * @returns {string} 颜色名称
     * @description 根据会话状态返回对应的标签颜色
     */
    getSessionStateColor(state) {
      const colorMap = {
        'CREATED': 'default',
        'RUNNING': 'processing',
        'CLOSING': 'warning',
        'CLOSED': 'error'
      };
      return colorMap[state] || 'default';
    }
  }
};
</script>

<style scoped>
.session-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 56px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.toolbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 0 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 0 0 auto;
}

/* 会话信息样式 */
.session-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.session-icon {
  color: #1890ff;
  font-size: 16px;
}

.session-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 图信息样式 */
.graph-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.graph-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.graph-stats .anticon {
  font-size: 14px;
}

/* 按钮样式 */
.main-actions {
  margin-left: 8px;
}

.ant-btn-group .ant-btn + .ant-btn {
  margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar-center {
    margin: 0 8px;
  }
  
  .session-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .session-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    justify-content: center;
    margin: 0;
  }
  
  .toolbar-center {
    order: -1;
  }
  
  .graph-info {
    justify-content: center;
  }
  
  .session-title {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .toolbar-right {
    flex-direction: column;
    gap: 8px;
  }
  
  .ant-btn-group {
    width: 100%;
  }
  
  .ant-btn-group .ant-btn {
    flex: 1;
  }
  
  .main-actions {
    width: 100%;
    margin-left: 0;
  }
}

/* 深色主题支持（可选） */
@media (prefers-color-scheme: dark) {
  .session-toolbar {
    background-color: #141414;
    border-bottom-color: #303030;
  }
  
  .session-title {
    color: #f0f0f0;
  }
  
  .graph-stats {
    color: #a6a6a6;
  }
}

/* 高对比度模式支持（可选） */
@media (prefers-contrast: high) {
  .session-toolbar {
    border-bottom: 2px solid #000;
  }
  
  .session-title {
    font-weight: 600;
  }
}

/* 动画效果 */
.ant-btn {
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.session-toolbar {
  transition: box-shadow 0.3s ease;
}

.session-toolbar:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 加载状态样式增强 */
.ant-btn-loading {
  position: relative;
}

.ant-btn-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
  pointer-events: none;
}
</style>
