<template>
  <div class="session-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 data-testid="page-title" class="page-title">会话列表</h1>
          <p class="page-description">管理和查看所有数据处理会话</p>
        </div>
        <div class="header-actions">
          <a-button 
            type="primary" 
            icon="plus"
            data-testid="create-session-btn"
            @click="showCreateDialog"
          >
            创建会话
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model="searchKeyword"
            placeholder="搜索会话ID或名称关键字..."
            data-testid="search-input"
            @pressEnter="handleSearch"
          />
        </a-col>
        <a-col :span="4">
          <a-select
            v-model="stateFilter"
            placeholder="全部"
            style="width: 100%"
            data-testid="state-filter"
            allowClear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="CREATED">已创建</a-select-option>
            <a-select-option value="RUNNING">运行中</a-select-option>
            <a-select-option value="CLOSING">关闭中</a-select-option>
            <a-select-option value="CLOSED">已关闭</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model="backendTypeFilter"
            placeholder="全部"
            style="width: 100%"
            data-testid="backend-filter"
            allowClear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option 
              v-for="backend in backendTypes" 
              :key="backend.type"
              :value="backend.type"
            >
              {{ backend.metadata.displayName || backend.type }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-button type="primary" data-testid="search-btn" @click="handleSearch">搜索</a-button>
        </a-col>
        <a-col :span="4">
          <a-button data-testid="reset-btn" @click="handleReset">重置</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :dataSource="sessions"
        :loading="loading"
        :pagination="false"
        rowKey="id"
        data-testid="session-table"
      >
        <!-- 会话ID列 -->
        <template slot="sessionId" slot-scope="text">
          <span data-testid="session-id">{{ text }}</span>
          <a-icon 
            type="copy" 
            data-testid="copy-session-id"
            style="margin-left: 8px; cursor: pointer; color: #1890ff;"
            @click="copyToClipboard(text)"
          />
        </template>

        <!-- 后端类型列 -->
        <template slot="backendType" slot-scope="text">
          <span data-testid="backend-type">
            {{ getBackendDisplayName(text) }}
          </span>
        </template>

        <!-- 状态列 -->
        <template slot="state" slot-scope="text">
          <a-tag 
            :color="getStateColor(text)"
            data-testid="session-state"
          >
            {{ getStateText(text) }}
          </a-tag>
        </template>

        <!-- 创建时间列 -->
        <template slot="createdAt" slot-scope="text">
          <span data-testid="created-time">
            {{ formatTime(text) }}
          </span>
        </template>

        <!-- 操作列 -->
        <template slot="actions" slot-scope="text, record">
          <div class="action-buttons">
            <a-button 
              size="small" 
              data-testid="view-detail-btn"
              @click="viewDetail(record)"
            >
              查看详情
            </a-button>
            
            <a-button 
              v-if="record.state === 'CREATED' || record.state === 'CLOSED'"
              size="small" 
              type="primary"
              data-testid="open-session-btn"
              @click="openSession(record)"
            >
              打开会话
            </a-button>
            
            <a-button 
              v-if="record.state === 'RUNNING'"
              size="small" 
              type="danger"
              data-testid="close-session-btn"
              @click="closeSession(record)"
            >
              关闭会话
            </a-button>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <a-pagination
        v-model="currentPage"
        :total="totalRecords"
        :pageSize="pageSize"
        :showSizeChanger="true"
        :showQuickJumper="true"
        :showTotal="(total, range) => `共 ${total} 条，第 ${range[0]}-${range[1]} 条`"
        data-testid="pagination"
        @change="handlePageChange"
        @showSizeChange="handlePageSizeChange"
      />
    </div>

    <!-- 创建会话对话框 -->
    <CreateSessionDialog
      :visible="createDialogVisible"
      @close="createDialogVisible = false"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script>
import DataProcessService from '@/pages/dpe-web/services/DataProcessService';
import CreateSessionDialog from '@/pages/dpe-web/components/CreateSessionDialog';
import { SessionState } from '@/pages/dpe-web/models/Session';

export default {
  name: 'SessionListPage',
  components: {
    CreateSessionDialog
  },
  data() {
    return {
      // 数据相关
      sessions: [],
      backendTypes: [],
      loading: false,
      
      // 搜索筛选相关
      searchKeyword: '',
      stateFilter: '',
      backendTypeFilter: '',
      
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalRecords: 0,
      
      // 对话框相关
      createDialogVisible: false,
      
      // 表格列定义
      columns: [
        {
          title: '会话ID',
          dataIndex: 'id',
          key: 'id',
          scopedSlots: { customRender: 'sessionId' },
          width: 300
        },
        {
          title: '后端类型',
          dataIndex: 'backendType',
          key: 'backendType',
          scopedSlots: { customRender: 'backendType' },
          width: 150
        },
        {
          title: '状态',
          dataIndex: 'state',
          key: 'state',
          scopedSlots: { customRender: 'state' },
          width: 100
        },
        {
          title: '创建时间',
          dataIndex: 'createdAt',
          key: 'createdAt',
          scopedSlots: { customRender: 'createdAt' },
          width: 180
        },
        {
          title: '操作',
          key: 'actions',
          scopedSlots: { customRender: 'actions' },
          width: 250
        }
      ]
    };
  },
  
  created() {
    this.loadBackendTypes();
    this.loadSessions();
  },
  
  methods: {
    /**
     * 加载后端类型列表
     */
    async loadBackendTypes() {
      try {
        this.backendTypes = await DataProcessService.getBackendTypes();
      } catch (error) {
        this.$message.error(`加载后端类型失败: ${error.message}`);
      }
    },
    
    /**
     * 加载会话列表
     */
    async loadSessions() {
      this.loading = true;
      try {
        const params = {
          pageParam: {
            pageIndex: this.currentPage - 1,
            limit: this.pageSize,
            sortField: 'createdAt',
            sortType: 'desc'
          },
          filters: {}
        };
        
        // 添加搜索条件
        if (this.searchKeyword) {
          params.filters.session_id_pattern = this.searchKeyword;
        }
        
        if (this.stateFilter) {
          params.filters.states = [this.stateFilter];
        }
        
        if (this.backendTypeFilter) {
          params.filters.backend_types = [this.backendTypeFilter];
        }
        
        const result = await DataProcessService.querySessions(params);
        this.sessions = result.data;
        this.totalRecords = result.pageParam.recordTotal;
      } catch (error) {
        this.$message.error(`加载会话列表失败: ${error.message}`);
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 搜索会话
     */
    handleSearch() {
      this.currentPage = 1;
      this.loadSessions();
    },
    
    /**
     * 重置搜索条件
     */
    handleReset() {
      this.searchKeyword = '';
      this.stateFilter = '';
      this.backendTypeFilter = '';
      this.currentPage = 1;
      this.loadSessions();
    },
    
    /**
     * 页码变化处理
     */
    handlePageChange(page) {
      this.currentPage = page;
      this.loadSessions();
    },
    
    /**
     * 页大小变化处理
     */
    handlePageSizeChange(current, size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.loadSessions();
    },
    
    /**
     * 显示创建会话对话框
     */
    showCreateDialog() {
      this.createDialogVisible = true;
    },
    
    /**
     * 创建会话成功处理
     */
    handleCreateSuccess(session) {
      this.createDialogVisible = false;
      this.$message.success('会话创建成功');
      
      // 跳转到会话详情页
      this.$router.push({
        name: 'SessionDetailIndex',
        params: { sessionId: session.id }
      });
    },
    
    /**
     * 查看会话详情
     */
    viewDetail(session) {
      this.$router.push({
        name: 'SessionDetailIndex',
        params: { sessionId: session.id }
      });
    },
    
    /**
     * 打开会话
     */
    openSession(session) {
      this.$router.push({
        name: 'SessionDetailIndex',
        params: { sessionId: session.id }
      });
    },
    
    /**
     * 关闭会话
     */
    async closeSession(session) {
      try {
        await DataProcessService.closeSession(session.id);
        this.$message.success('会话已关闭');
        this.loadSessions();
      } catch (error) {
        this.$message.error(`关闭会话失败: ${error.message}`);
      }
    },
    
    /**
     * 复制到剪贴板
     */
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('已复制到剪贴板');
        });
      } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.$message.success('已复制到剪贴板');
      }
    },
    
    /**
     * 获取后端显示名称
     */
    getBackendDisplayName(backendType) {
      const backend = this.backendTypes.find(b => b.type === backendType);
      return backend ? (backend.metadata.displayName || backendType) : backendType;
    },
    
    /**
     * 获取状态颜色
     */
    getStateColor(state) {
      const colorMap = {
        [SessionState.CREATED]: 'blue',
        [SessionState.RUNNING]: 'green',
        [SessionState.CLOSING]: 'orange',
        [SessionState.CLOSED]: 'default'
      };
      return colorMap[state] || 'default';
    },
    
    /**
     * 获取状态文本
     */
    getStateText(state) {
      const textMap = {
        [SessionState.CREATED]: '已创建',
        [SessionState.RUNNING]: '运行中',
        [SessionState.CLOSING]: '关闭中',
        [SessionState.CLOSED]: '已关闭'
      };
      return textMap[state] || state;
    },
    
    /**
     * 格式化时间
     */
    formatTime(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  }
};
</script>

<style scoped>
.session-list-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.header-actions {
  flex-shrink: 0;
}

.search-section {
  background: white;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .ant-btn {
  margin: 0;
}

.pagination-section {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}
</style>
