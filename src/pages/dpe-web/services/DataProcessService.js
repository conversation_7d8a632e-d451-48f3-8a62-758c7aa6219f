/**
 * 数据处理服务，提供与后端DataProcessEngine交互的方法
 * 整合了后端管理、会话管理、算子管理、任务管理等功能
 * @description 根据数据处理引擎API设计文档实现的前端服务层
 */

import axios from 'axios';
import {
  BackendInfo,
  Session,
  SessionState,
  TaskInfo,
  TaskStatus,
  OperatorInfo,
  PageParam,
  ClientEditStatus,
  ObjectMetadata,
  ExecutionGraph, // Added
  GraphNode,      // Added
  GraphEdge,      // Added
  PendingChanges, // Added
} from '../models';
import {
  Subscription,
  SessionLifecycleListener,
  TaskLifecycleListener,
  ExecutionGraphClient,
  MergeMode,
  VerifyResult, // Added
  VerifyFailureDetail, // Added
  UpdateNodeParam,
} from '../models/client-api';
import resourceService from './ResourceService';

/**
 * 执行图客户端，提供与DAG会话中执行图的WebSocket交互功能
 * @class
 * @extends ExecutionGraphClient
 */
class ExecutionGraphClientImpl extends ExecutionGraphClient {
  /**
   * 创建执行图客户端实例
   * @param {string} sessionId - 会话ID
   * @param {string} baseURL - WebSocket基础URL
   */
  constructor(sessionId, baseURL = 'ws://localhost:8000') {
    super();
    this.sessionId = sessionId;
    this.baseURL = baseURL;
    this.websocket = null;
    this.messageHandlers = new Map();
    this.messageId = 0;
    this.pendingMessages = new Map();
    this.isConnected = false;
  }

  /**
   * 连接到WebSocket
   * @async
   * @returns {Promise<void>}
   */
  async connect() {
    return new Promise((resolve, reject) => {
      const wsUrl = `${this.baseURL}/api/sessions/${this.sessionId}/dag/graph-client`.replace('http://', 'ws://').replace('https://', 'wss://');
      console.debug('wsUrl', wsUrl);
      this.websocket = new WebSocket(wsUrl);

      this.websocket.onopen = () => {
        this.isConnected = true;
        resolve();
      };

      this.websocket.onclose = () => {
        this.isConnected = false;
      };

      this.websocket.onerror = (error) => {
        reject(new Error(`WebSocket连接失败: ${error.message}`));
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this._handleMessage(message);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };
    });
  }

  /**
   * 处理收到的消息
   * @private
   * @param {Object} message - 收到的消息
   */
  _handleMessage(message) {
    const { message_id, success, result, error } = message;

    if (this.pendingMessages.has(message_id)) {
      const { resolve, reject } = this.pendingMessages.get(message_id);
      this.pendingMessages.delete(message_id);

      if (success) {
        resolve(result);
      } else {
        reject(new Error(error ? error.message : '操作失败'));
      }
    }
  }

  /**
   * 发送消息并等待响应
   * @private
   * @param {string} action - 操作类型
   * @param {Object} payload - 操作数据
   * @returns {Promise<*>} 操作结果
   */
  _sendMessage(action, payload) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        reject(new Error('WebSocket未连接'));
        return;
      }

      const messageId = `msg-${++this.messageId}`;
      const message = {
        message_id: messageId,
        action,
        payload
      };

      this.pendingMessages.set(messageId, { resolve, reject });

      try {
        this.websocket.send(JSON.stringify(message));
      } catch (error) {
        this.pendingMessages.delete(messageId);
        reject(new Error(`发送消息失败: ${error.message}`));
      }

      // 设置超时
      setTimeout(() => {
        if (this.pendingMessages.has(messageId)) {
          this.pendingMessages.delete(messageId);
          reject(new Error('操作超时'));
        }
      }, 30000);
    });
  }

  /**
   * 添加节点
   * @async
   * @param {Array<GraphNode>} nodes - 要添加的节点列表
   * @returns {Promise<Array<string>>} 添加成功的节点ID列表
   */
  async add_nodes(nodes) {
    const backendNodes = nodes.map(node => new GraphNode(node).toBackendModel());
    return await this._sendMessage('add_nodes', { nodes: backendNodes });
  }

  /**
   * 更新节点配置
   * @async
   * @param {Object<string, UpdateNodeParam>} updates - 键为节点ID，值为节点更新参数
   * @returns {Promise<Array<string>>} 更新成功的节点ID列表
   */
  async update_nodes(updates) {
    if (!updates) {
      return [];
    }
    const backendUpdates = Object.fromEntries(Object.entries(updates).map(([nodeId, updateParam]) => [nodeId, new UpdateNodeParam(updateParam).toBackendModel()]));
    return await this._sendMessage('update_nodes', backendUpdates);
  }

  /**
   * 删除节点
   * @async
   * @param {Array<string>} nodeIds - 要删除的节点ID列表
   * @returns {Promise<Array<string>>} 删除成功的节点ID列表
   */
  async delete_nodes(nodeIds) {
    return await this._sendMessage('delete_nodes', { node_ids: nodeIds });
  }

  /**
   * 添加边
   * @async
   * @param {Array<GraphEdge>} edges - 要添加的边列表
   * @returns {Promise<Array<string>>} 添加成功的边ID列表
   */
  async add_edges(edges) {
    const backendEdges = edges.map(edge => new GraphEdge(edge).toBackendModel());
    return await this._sendMessage('add_edges', { edges: backendEdges });
  }

  /**
   * 删除边
   * @async
   * @param {Array<GraphEdge>} edges - 要删除的边列表
   * @returns {Promise<Array<string>>} 删除成功的边ID列表
   */
  async delete_edges(edges) {
    const backendEdges = edges.map(edge => new GraphEdge(edge).toBackendModel());
    return await this._sendMessage('delete_edges', { edges: backendEdges });
  }

  /**
   * 添加算子
   * @async
   * @param {OperatorInfo} operator - 要添加的算子信息
   * @returns {Promise<OperatorInfo>} 添加的算子信息
   */
  async add_op(operator) {
    const backendOperator = new OperatorInfo(operator).toBackendModel();
    const result = await this._sendMessage('add_op', { operator: backendOperator });
    return OperatorInfo.fromBackendModel(result);
  }

  /**
   * 加载执行图
   * @async
   * @param {ExecutionGraph} graph - 要加载的执行图
   * @param {string} [mergeMode=MergeMode.FAILED_ON_CONFLICT] - 合并模式
   * @returns {Promise<Array<string>>} 受影响的节点ID列表
   */
  async load_graph(graph, mergeMode = MergeMode.FAILED_ON_CONFLICT) {
    const backendGraph = new ExecutionGraph(graph).toBackendModel();
    return await this._sendMessage('load_graph', { graph: backendGraph, merge_mode: mergeMode });
  }

  /**
   * 验证执行图
   * @async
   * @returns {Promise<VerifyResult>} 验证结果
   */
  async verify() {
    const result = await this._sendMessage('verify', {});
    return new VerifyResult(result.valid, (result.failed_details || []).map(detail => new VerifyFailureDetail(detail.rule_type, detail.message, detail.extra_info)));
  }

  /**
   * 保存执行图
   * @async
   * @returns {Promise<Array<string>>} 保存结果
   */
  async save() {
    return await this._sendMessage('save', {});
  }

  /**
   * 导出执行图
   * @async
   * @returns {Promise<ExecutionGraph>} 导出的执行图
   */
  async export_graph() {
    const result = await this._sendMessage('export_graph', {});
    return ExecutionGraph.fromBackendModel(result);
  }

  /**
   * 解析算子对应的任务
   * @async
   * @param {OperatorInfo} operator - 算子信息
   * @returns {Promise<TaskInfo>} DAG任务信息
   */
  async resolve_task(operator) {
    const backendOperator = new OperatorInfo(operator).toBackendModel();
    const result = await this._sendMessage('resolve_task', { operator: backendOperator });
    return TaskInfo.fromBackendModel(result);
  }

  /**
   * 关闭客户端连接
   * @async
   * @returns {Promise<void>}
   */
  async close() {
    if (this.websocket && this.isConnected) {
      this.websocket.close();
      this.isConnected = false;
    }
    this.pendingMessages.clear();
  }

  /**
   * 获取当前编辑状态
   * @async
   * @returns {Promise<ClientEditStatus>} 当前编辑状态
   */
  async get_edit_status() {
    const result = await this._sendMessage('current_edit_status', {});
    return ClientEditStatus.fromBackendModel(result);
  }
}

/**
 * 订阅对象，用于取消监听
 * @class
 */
class SubscriptionImpl extends Subscription {
  /**
   * 创建订阅对象
   * @param {Function} unsubscribeFn - 取消订阅的函数
   */
  constructor(unsubscribeFn) {
    super();
    this._unsubscribeFn = unsubscribeFn;
    this._isUnsubscribed = false;
  }

  /**
   * 取消订阅
   * @returns {void}
   */
  unsubscribe() {
    if (!this._isUnsubscribed && this._unsubscribeFn) {
      this._unsubscribeFn();
      this._isUnsubscribed = true;
    }
  }
}

/**
 * 会话查询参数
 * @typedef {Object} ListSessionParams
 * @property {PageParam} pageParam - 分页参数
 * @property {string} backendType - 后端类型过滤
 * @property {Object} filters - 其它过滤条件
 */

/**
 * 算子类型查询参数
 * @typedef {Object} ListOperatorTypesParams
 * @property {PageParam} pageParam - 分页参数
 * @property {string} category - 算子类别
 */

/**
 * 任务查询参数
 * @typedef {Object} ListTasksParams
 * @property {PageParam} pageParam - 分页参数
 * @property {string} sessionId - 会话ID
 * @property {string} status - 任务状态
 * @property {boolean} withDetails - 是否包含详情
 */

/**
 * 数据处理服务
 */
class DataProcessService {
  /**
   * 创建数据处理服务实例
   * @param {string} baseURL - API基础URL，默认为'/api'
   */
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // SSE连接管理
    this.sseConnections = new Map();
  }

  /**
   * 映射Metadata字段：后端下划线格式转前端驼峰格式
   * @private
   * @param {Object} metadataData - 后端返回的元数据数据
   * @returns {ObjectMetadata} 映射后的元数据数据
   */
  _mapMetadataFields(metadataData) {
    return ObjectMetadata.fromBackendModel(metadataData);
  }

  /**
   * 映射OperatorInfo字段：后端下划线格式转前端驼峰格式
   * @private
   * @param {Object} operatorData - 后端返回的算子信息数据
   * @returns {OperatorInfo} 映射后的算子信息数据
   */
  _mapOperatorInfoFields(operatorData) {
    return OperatorInfo.fromBackendModel(operatorData);
  }

  /**
   * 映射BackendInfo字段：后端下划线格式转前端驼峰格式
   * @private
   * @param {Object} backendData - 后端返回的后端信息数据
   * @returns {BackendInfo} 映射后的后端信息数据
   */
  _mapBackendInfoFields(backendData) {
    return BackendInfo.fromBackendModel(backendData);
  }

  /**
   * 映射Session字段：后端下划线格式转前端驼峰格式
   * @private
   * @param {Object} sessionData - 后端返回的会话数据
   * @returns {Session} 映射后的会话数据
   */
  _mapSessionFields(sessionData) {
    return Session.fromBackendModel(sessionData);
  }

  /**
   * 映射TaskInfo字段：后端下划线格式转前端驼峰格式
   * @private
   * @param {Object} taskData - 后端返回的任务数据
   * @returns {TaskInfo} 映射后的任务数据
   */
  _mapTaskFields(taskData) {
    return TaskInfo.fromBackendModel(taskData);
  }

  /**
   * 获取所有可用的后端类型列表
   * @async
   * @returns {Promise<Array<BackendInfo>>} 后端类型信息列表，包含类型、元数据和会话配置
   * @throws {Error} 当获取后端列表失败时抛出错误
   */
  async getBackendTypes() {
    try {
      const response = await this.client.get('/backends');
      return response.data.backends.map(backend => BackendInfo.fromBackendModel(backend));
    } catch (error) {
      throw new Error(`获取后端类型列表失败: ${error.message}`);
    }
  }

  /**
   * 查询后端类型列表（支持分页和过滤）
   * @async
   * @param {Object} params - 查询参数
   * @param {PageParam} [params.pageParam] - 分页参数
   * @param {Object} [params.filters] - 过滤条件
   * @returns {Promise<{data: Array<BackendInfo>, pageParam: PageParam}>} 分页的后端列表
   * @throws {Error} 当查询后端列表失败时抛出错误
   */
  async queryBackendTypes(params = {}) {
    try {
      const backendParams = {
        pageParam: params.pageParam ? new PageParam(params.pageParam).toBackendModel() : undefined,
        filters: params.filters,
      };
      const response = await this.client.post('/backends/query', backendParams);
      return {
        data: response.data.data.map(backend => BackendInfo.fromBackendModel(backend)),
        pageParam: PageParam.fromBackendModel(response.data.pageParam)
      };
    } catch (error) {
      throw new Error(`查询后端类型列表失败: ${error.message}`);
    }
  }

  /**
   * 创建新的数据处理会话
   * @async
   * @param {string} [backendType] - 后端类型
   * @param {Object} [options={}] - 会话选项
   * @param {string} [options.sessionId] - 指定会话ID
   * @param {Object} [options.config] - 会话配置参数
   * @returns {Promise<Session>} 创建的会话信息
   * @throws {Error} 当创建会话失败时抛出错误
   */
  async createSession(backendType, options = {}) {
    try {
      const payload = {};
      if (backendType) payload.backend_name = backendType;
      if (options.sessionId) payload.session_id = options.sessionId;
      if (options.config) payload.config = options.config;

      const response = await this.client.post('/sessions', payload);
      return Session.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`创建会话失败: ${error.message}`);
    }
  }

  /**
   * 创建DAG会话
   * @async
   * @param {string} [backendType] - 后端类型
   * @param {Object} [options={}] - 会话选项
   * @param {string} [options.sessionId] - 指定会话ID
   * @param {Object} [options.config] - 会话配置参数
   * @param {ExecutionGraph} [options.graph] - 初始执行图
   * @returns {Promise<Session>} 创建的会话信息
   * @throws {Error} 当创建DAG会话失败时抛出错误
   */
  async createDagSession(backendType, options = {}) {
    try {
      const payload = {};
      if (backendType) payload.backend_name = backendType;
      if (options.sessionId) payload.session_id = options.sessionId;
      if (options.config) payload.config = options.config;
      if (options.graph) payload.graph = new ExecutionGraph(options.graph).toBackendModel();

      const response = await this.client.post('/sessions/dag', payload);
      return Session.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`创建DAG会话失败: ${error.message}`);
    }
  }

  /**
   * 获取会话列表
   * @async
   * @returns {Promise<Array<Session>>} 会话列表
   * @throws {Error} 当获取会话列表失败时抛出错误
   */
  async listSessions() {
    try {
      const response = await this.client.get('/sessions');
      return response.data.sessions.map(session => Session.fromBackendModel(session));
    } catch (error) {
      throw new Error(`获取会话列表失败: ${error.message}`);
    }
  }

  /**
   * 查询会话列表（支持分页和过滤）
   * @async
   * @param {ListSessionParams} [params={}] - 查询参数
   * @returns {Promise<{data: Array<Session>, pageParam: PageParam}>} 分页的会话列表
   * @throws {Error} 当查询会话列表失败时抛出错误
   */
  async querySessions(params = {}) {
    try {
      const backendParams = {
        pageParam: params.pageParam ? new PageParam(params.pageParam).toBackendModel() : undefined,
        filters: params.filters,
      };
      const response = await this.client.post('/sessions/query', backendParams);
      return {
        data: response.data.data.map(session => Session.fromBackendModel(session)),
        pageParam: PageParam.fromBackendModel(response.data.pageParam)
      };
    } catch (error) {
      throw new Error(`查询会话列表失败: ${error.message}`);
    }
  }

  /**
   * 获取会话详情
   * @async
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Session>} 会话详情
   * @throws {Error} 当获取会话详情失败时抛出错误
   */
  async getSession(sessionId) {
    try {
      const response = await this.client.get(`/sessions/${sessionId}`);
      return Session.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`获取会话详情失败: ${error.message}`);
    }
  }

  /**
   * 关闭会话
   * @async
   * @param {string} sessionId - 会话ID
   * @returns {Promise<boolean>} 是否成功
   * @throws {Error} 当关闭会话失败时抛出错误
   */
  async closeSession(sessionId) {
    try {
      const response = await this.client.delete(`/sessions/${sessionId}`);
      return response.data;
    } catch (error) {
      throw new Error(`关闭会话失败: ${error.message}`);
    }
  }

  /**
   * 获取后端支持的算子类型
   * @async
   * @param {string} backendType - 后端类型
   * @returns {Promise<Array<OperatorInfo>>} 算子类型列表
   * @throws {Error} 当获取算子列表失败时抛出错误
   */
  async listOperatorTypes(backendType) {
    try {
      const response = await this.client.get(`/backends/${backendType}/supported_operators`);
      return response.data?.operators?.map(op => OperatorInfo.fromBackendModel(op)) || [];
    } catch (error) {
      throw new Error(`获取算子类型列表失败: ${error.message}`);
    }
  }

  /**
   * 查询后端支持的算子类型（支持分页和过滤）
   * @async
   * @param {string} backendType - 后端类型
   * @param {ListOperatorTypesParams} [params={}] - 查询参数
   * @returns {Promise<{data: Array<OperatorInfo>, pageParam: PageParam}>} 分页的算子列表
   * @throws {Error} 当查询算子类型列表失败时抛出错误
   */
  async queryOperatorTypes(backendType, params = {}) {
    try {
      const backendParams = {
        pageParam: params.pageParam ? new PageParam(params.pageParam).toBackendModel() : undefined,
        filters: params.filters,
      };
      const response = await this.client.post(`/backends/${backendType}/operators/query`, backendParams);
      return {
        data: response.data.data.map(op => OperatorInfo.fromBackendModel(op)),
        pageParam: PageParam.fromBackendModel(response.data.pageParam)
      };
    } catch (error) {
      throw new Error(`查询算子类型列表失败: ${error.message}`);
    }
  }

  /**
   * 获取算子详情配置
   * @async
   * @param {string} backendType - 后端类型
   * @param {string} operatorType - 算子类型
   * @returns {Promise<OperatorInfo>} 算子配置
   * @throws {Error} 当获取算子配置失败时抛出错误
   */
  async getOperatorInfo(backendType, operatorType) {
    try {
      const response = await this.client.get(`/backends/${backendType}/operators/${operatorType}`);
      return OperatorInfo.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`获取算子配置失败: ${error.message}`);
    }
  }

  /**
   * 提交运行算子任务
   * @async
   * @param {string} sessionId - 会话ID
   * @param {Object} operatorConfig - 算子配置
   * @param {boolean} [wait=false] - 是否等待任务完成
   * @returns {Promise<TaskInfo>} 任务信息
   * @throws {Error} 当提交任务失败时抛出错误
   */
  async runOperator(sessionId, operatorConfig, wait = false) {
    try {
      const params = `?wait=${wait}`;
      const response = await this.client.post(`/sessions/${sessionId}/tasks/run${params}`, operatorConfig);

      return TaskInfo.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`提交运行算子任务失败: ${error.message}`);
    }
  }

  /**
   * 获取任务列表
   * @async
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array<TaskInfo>>} 任务列表
   * @throws {Error} 当获取任务列表失败时抛出错误
   */
  async listTasks(sessionId) {
    try {
      const response = await this.client.get(`/sessions/${sessionId}/tasks`);
      return response.data.tasks.map(task => TaskInfo.fromBackendModel(task));
    } catch (error) {
      throw new Error(`获取任务列表失败: ${error.message}`);
    }
  }

  /**
   * 查询任务列表（支持分页和过滤）
   * @async
   * @param {string} sessionId - 会话ID
   * @param {ListTasksParams} [params={}] - 查询参数
   * @returns {Promise<{data: Array<TaskInfo>, pageParam: PageParam}>} 分页的任务列表
   * @throws {Error} 当查询任务列表失败时抛出错误
   */
  async queryTasks(sessionId, params = {}) {
    try {
      const backendParams = {
        pageParam: params.pageParam ? new PageParam(params.pageParam).toBackendModel() : undefined,
        filters: params.filters,
      };
      const response = await this.client.post(`/sessions/${sessionId}/tasks/query`, backendParams);
      return {
        data: response.data.data.map(task => TaskInfo.fromBackendModel(task)),
        pageParam: PageParam.fromBackendModel(response.data.pageParam)
      };
    } catch (error) {
      throw new Error(`查询任务列表失败: ${error.message}`);
    }
  }

  /**
   * 获取任务详情
   * @async
   * @param {string} sessionId - 会话ID
   * @param {string} taskId - 任务ID
   * @param {boolean} [wait=false] - 是否等待任务完成
   * @param {number} [timeout=60] - 等待超时时间（秒）
   * @returns {Promise<TaskInfo>} 任务详情
   * @throws {Error} 当获取任务详情失败时抛出错误
   */
  async getTaskInfo(sessionId, taskId, wait = false, timeout = 60) {
    try {
      const params = new URLSearchParams();
      params.append('wait', wait.toString());
      params.append('timeout', timeout.toString());

      const url = `/sessions/${sessionId}/tasks/${taskId}?${params.toString()}`;

      const response = await this.client.get(url);

      return TaskInfo.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`获取任务详情失败: ${error.message}`);
    }
  }

  /**
   * 取消任务
   * @async
   * @param {string} sessionId - 会话ID
   * @param {string} taskId - 任务ID
   * @returns {Promise<boolean>} 是否成功
   * @throws {Error} 当取消任务失败时抛出错误
   */
  async cancelTask(sessionId, taskId) {
    try {
      const response = await this.client.post(`/sessions/${sessionId}/tasks/${taskId}/cancel`);
      return response.data;
    } catch (error) {
      throw new Error(`取消任务失败: ${error.message}`);
    }
  }

  /**
   * 批量运行DAG任务
   * @async
   * @param {string} sessionId - 会话ID
   * @param {Array<string>} taskIds - 任务ID列表
   * @param {string} [rerunMode='NEVER'] - 重跑模式
   * @returns {Promise<Array<TaskInfo>>} 执行的任务列表
   * @throws {Error} 当批量运行任务失败时抛出错误
   */
  async runDagTasks(sessionId, taskIds, rerunMode = 'NEVER') {
    try {
      const payload = {
        task_ids: taskIds,
        rerun_mode: rerunMode
      };
      const response = await this.client.post(`/sessions/${sessionId}/dag/run_task`, payload);

      return response.data.executed_tasks.map(task => {
        return TaskInfo.fromBackendModel(task);
      });
    } catch (error) {
      throw new Error(`批量运行DAG任务失败: ${error.message}`);
    }
  }

  /**
   * 运行所有DAG任务
   * @async
   * @param {string} sessionId - 会话ID
   * @param {string} [rerunMode='NEVER'] - 重跑模式
   * @returns {Promise<Array<TaskInfo>>} 执行的任务列表
   * @throws {Error} 当运行所有任务失败时抛出错误
   */
  async runAllDagTasks(sessionId, rerunMode = 'NEVER') {
    try {
      const payload = { rerun_mode: rerunMode };
      const response = await this.client.post(`/sessions/${sessionId}/dag/run_all_tasks`, payload);

      return response.data.executed_tasks.map(task => {
        return TaskInfo.fromBackendModel(task);
      });
    } catch (error) {
      throw new Error(`运行所有DAG任务失败: ${error.message}`);
    }
  }

  /**
   * 批量删除DAG任务
   * @async
   * @param {string} sessionId - 会话ID
   * @param {Array<string>} taskIds - 任务ID列表
   * @param {boolean} [force=false] - 是否强制删除
   * @param {boolean} [cleanupResources=true] - 是否清理关联资源
   * @returns {Promise<Object>} 删除结果
   * @throws {Error} 当批量删除任务失败时抛出错误
   */
  async deleteDagTasks(sessionId, taskIds, force = false, cleanupResources = true) {
    try {
      const payload = {
        task_ids: taskIds,
        force: force,
        cleanup_resources: cleanupResources
      };
      const response = await this.client.delete(`/sessions/${sessionId}/dag/tasks`, { data: payload });
      return response.data;
    } catch (error) {
      throw new Error(`批量删除DAG任务失败: ${error.message}`);
    }
  }

  /**
   * 导出执行图
   * @async
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Object>} 执行图对象
   * @throws {Error} 当导出执行图失败时抛出错误
   */
  async exportExecutionGraph(sessionId) {
    try {
      const response = await this.client.get(`/sessions/${sessionId}/dag/export_graph`);
      return ExecutionGraph.fromBackendModel(response.data);
    } catch (error) {
      throw new Error(`导出执行图失败: ${error.message}`);
    }
  }

  /**
   * 注册会话生命周期监听器
   * @param {SessionLifecycleListener} listener - 生命周期监听器
   * @param {string} [sessionId] - 指定监听的会话ID
   * @param {Array<string>} [eventTypes] - 监听的事件类型
   * @returns {Subscription} 订阅对象，用于取消监听
   */
  listenSessionLifecycle(listener, sessionId, eventTypes) {
    const params = new URLSearchParams();
    if (sessionId) params.append('session_id', sessionId);
    if (eventTypes && eventTypes.length > 0) params.append('event_types', eventTypes.join(','));

    const url = `/sessions/lifecycle-subscribe${params.toString() ? '?' + params.toString() : ''}`;
    const eventSource = new EventSource(`${this.baseURL}${url}`);

    eventSource.addEventListener('session_lifecycle', (event) => {
      const data = JSON.parse(event.data);
      const session = Session.fromBackendModel(data);

      switch (data.event_type) {
        case 'session_created':
          if (listener.onSessionCreated) {
            listener.onSessionCreated(session);
          }
          break;
        case 'session_state_changed':
          if (listener.onSessionStateChanged) {
            listener.onSessionStateChanged(session, data.data.old_state, data.data.new_state);
          }
          break;
        case 'session_closed':
          if (listener.onSessionClosed) {
            listener.onSessionClosed(session);
          }
          break;
      }
    });

    eventSource.addEventListener('error', (event) => {
      console.error('Session lifecycle SSE error:', event);
    });

    const connectionId = Date.now().toString();
    this.sseConnections.set(connectionId, eventSource);

    return new SubscriptionImpl(() => { // Changed to SubscriptionImpl
      eventSource.close();
      this.sseConnections.delete(connectionId);
    });
  }

  /**
   * 注册任务生命周期监听器
   * @param {string} sessionId - 会话ID，如果提供则只监听该会话的任务
   * @param {TaskLifecycleListener} listener - 生命周期监听器
   * @param {Array<string>} [eventTypes] - 监听的事件类型
   * @returns {Subscription} 订阅对象，用于取消监听
   */
  listenTaskLifecycle(sessionId, listener, eventTypes) {
    const params = new URLSearchParams();
    if (sessionId) params.append('session_id', sessionId);
    if (eventTypes && eventTypes.length > 0) params.append('event_types', eventTypes.join(','));

    const url = `/sessions/lifecycle-subscribe${params.toString() ? '?' + params.toString() : ''}`;
    const eventSource = new EventSource(`${this.baseURL}${url}`);

    eventSource.addEventListener('task_lifecycle', (event) => {
      const data = JSON.parse(event.data);
      const task = TaskInfo.fromBackendModel(data);

      switch (data.event_type) {
        case 'task_status_changed':
          if (listener.onTaskStatusChanged) {
            listener.onTaskStatusChanged(task, data.data.old_status, data.data.new_status);
          }
          break;
        case 'task_completed':
          if (listener.onTaskCompleted) {
            listener.onTaskCompleted(task);
          }
          break;
        case 'task_error':
          if (listener.onTaskError) {
            listener.onTaskError(task, new Error(data.data.error_message));
          }
          break;
      }
    });

    eventSource.addEventListener('error', (event) => {
      console.error('Task lifecycle SSE error:', event);
    });

    const connectionId = Date.now().toString();
    this.sseConnections.set(connectionId, eventSource);

    return new SubscriptionImpl(() => { // Changed to SubscriptionImpl
      eventSource.close();
      this.sseConnections.delete(connectionId);
    });
  }

  /**
   * 关闭所有SSE连接
   * @returns {void}
   */
  closeAllConnections() {
    for (const [connectionId, eventSource] of this.sseConnections) {
      eventSource.close();
    }
    this.sseConnections.clear();
  }

  /**
   * 打开执行图客户端，用于与特定会话中的执行图进行交互
   * @async
   * @param {string} sessionId - 会话ID
   * @returns {Promise<ExecutionGraphClientImpl>} 执行图客户端实例
   * @throws {Error} 当打开执行图客户端失败时抛出错误
   */
  async openGraphClient(sessionId) {
    try {
      const client = new ExecutionGraphClientImpl(
        sessionId,
        `${location.protocol}//${location.host}${this.baseURL.replace('/api', '')}`
      );

      // 添加连接超时机制
      const connectPromise = client.connect();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('WebSocket连接超时'));
        }, 5000); // 5秒超时
      });

      await Promise.race([connectPromise, timeoutPromise]);
      return client;
    } catch (error) {
      throw new Error(`打开执行图客户端失败: ${error.message}`);
    }
  }

  /**
   * 根据执行图创建会话
   * @async
   * @param {string} graphId - 执行图ID
   * @param {string} [backendType] - 后端类型
   * @param {Object} [options={}] - 会话选项
   * @returns {Promise<Session>} 创建的会话信息
   * @throws {Error} 当根据执行图创建会话失败时抛出错误
   */
  async createSessionFromGraph(graphId, backendType, options = {}) {
    try {
      // 这个方法需要结合ExecutionGraphRepositoryService来实现
      // 首先获取执行图
      const { default: executionGraphRepositoryService } = await import('./ExecutionGraphRepositoryService.js');
      const graph = await executionGraphRepositoryService.getGraph(graphId);

      // 然后创建DAG会话并加载图
      const session = await this.createDagSession(backendType, {
        ...options,
        graph: graph
      });

      return session;
    } catch (error) {
      throw new Error(`根据执行图创建会话失败: ${error.message}`);
    }
  }

  /**
   * 打开结构化数据资源客户端
   * @async
   * @param {string} resourceId - 资源ID
   * @returns {Promise<StructuredDataResourceClient>} 结构化数据资源客户端
   * @throws {Error} 当打开资源客户端失败时抛出错误
   */
  async openStructuredDataResourceClient(resourceId) {
    try {
      const client = await resourceService.openStructuredDataResourceClient(resourceId);
      return client;
    } catch (error) {
      throw new Error(`打开结构化数据资源客户端失败: ${error.message}`);
    }
  }
}

// 创建并导出服务实例
const dataProcessService = new DataProcessService();

export default dataProcessService;
export { DataProcessService, SubscriptionImpl as Subscription }; // Renamed Subscription export
