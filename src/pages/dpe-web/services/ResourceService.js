import axios from 'axios';
import { StructuredDataResourceClient, SchemaField } from '../models/client-api';

/**
 * 结构化数据资源客户端，提供数据预览和Schema访问功能
 * @class
 * @implements {StructuredDataResourceClient}
 */
class StructuredDataResourceClientImpl extends StructuredDataResourceClient {
  /**
   * 创建结构化数据资源客户端实例
   * @param {string} resourceId - 资源ID
   * @param {Object} httpClient - HTTP客户端实例
   */
  constructor(resourceId, httpClient) {
    super(); // Call parent constructor of the interface, no arguments expected
    this.resourceId = resourceId; // Explicitly set resourceId here
    this.client = httpClient;
  }

  /**
   * 获取资源ID
   * @returns {string} 资源ID
   */
  getResourceId() {
    return this.resourceId;
  }

  /**
   * 预览数据资源内容
   * @async
   * @param {number} [page=0] - 页码
   * @param {number} [pageSize=100] - 每页条数
   * @returns {Promise<{total: number, schema: Array<SchemaField>, data: Array<Object>}>} 数据预览
   */
  async previewData(page = 0, pageSize = 100) {
    try {
      const response = await this.client.post('/resources/data-resource/structured/preview', {
        resource_id: this.resourceId,
        limit: pageSize,
        offset: page * pageSize
      });

      return {
        total: response.data.total_rows || 0,
        schema: response.data.data_schema ? response.data.data_schema.fields.map(field => new SchemaField(field)) : [],
        data: response.data.data || []
      };
    } catch (error) {
      throw new Error(`预览数据失败: ${error.message}`);
    }
  }

  /**
   * 获取数据结构
   * @async
   * @returns {Promise<Array<SchemaField>>} 数据结构字段列表
   */
  async getSchema() {
    try {
      const response = await this.client.post('/resources/data-resource/structured/schema', {
        resource_id: this.resourceId
      });

      return response.data.data_schema ? response.data.data_schema.fields.map(field => new SchemaField(field)) : [];
    } catch (error) {
      throw new Error(`获取数据结构失败: ${error.message}`);
    }
  }

  /**
   * 关闭客户端
   * @async
   * @returns {Promise<void>}
   */
  async close() {
    // 结构化数据资源客户端暂时不需要特殊的清理逻辑
    return Promise.resolve();
  }
}

/**
 * 资源服务，提供数据资源的访问功能
 * @description 根据数据处理引擎API设计文档实现的前端资源服务层
 */
class ResourceService {
  /**
   * 创建资源服务实例
   * @param {string} baseURL - API基础URL，默认为'/api'
   */
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * 打开结构化数据资源客户端
   * @async
   * @param {string} resourceId - 资源ID
   * @returns {Promise<StructuredDataResourceClient>} 结构化数据资源客户端
   * @throws {Error} 当打开资源客户端失败时抛出错误
   */
  async openStructuredDataResourceClient(resourceId) {
    try {
      const client = new StructuredDataResourceClientImpl(resourceId, this.client);
      return client;
    } catch (error) {
      throw new Error(`打开结构化数据资源客户端失败: ${error.message}`);
    }
  }
}

// 创建并导出服务实例
const resourceService = new ResourceService();

export default resourceService;
export { ResourceService };
