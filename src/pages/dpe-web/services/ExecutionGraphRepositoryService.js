/**
 * 执行图服务，提供执行图的管理功能
 * @description 管理执行图模板，注意：这是前端特有的外部持久化服务，不是数据处理引擎的职责
 */

import axios from 'axios';
import { ExecutionGraph, PageParam } from '../models';

/**
 * 执行图查询参数
 * @typedef {Object} ListGraphParams
 * @property {PageParam} pageParam - 分页参数
 * @property {Object} filters - 过滤条件
 */

/**
 * 执行图仓库服务
 */
class ExecutionGraphRepositoryService {
  /**
   * 创建执行图仓库服务实例
   * @param {string} baseURL - API基础URL，默认为'/api/graphs'
   */
  constructor(baseURL = '/api/graphs') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 本地存储的执行图（用于演示和测试）
    this.localGraphs = new Map();
    this._initializeLocalGraphs();
  }

  /**
   * 映射ExecutionGraph字段：后端下划线格式转前端驼峰格式
   * @private
   * @param {Object} graphData - 后端返回的执行图数据
   * @returns {Object} 映射后的执行图数据
   */
  _mapExecutionGraphFields(graphData) {
    return {
      id: graphData.graph_id || graphData.id,
      metadata: graphData.metadata,
      nodes: graphData.nodes,
      edges: graphData.edges,
      createTime: graphData.create_time || graphData.createTime,
      updateTime: graphData.update_time || graphData.updateTime
    };
  }

  /**
   * 初始化本地执行图数据（用于演示）
   * @private
   * @returns {void}
   */
  _initializeLocalGraphs() {
    // 添加一些示例执行图
    const sampleGraph1 = new ExecutionGraph({
      id: 'sample-sales-analysis',
      metadata: {
        displayName: '销售数据分析工作流',
        description: '完整的销售数据处理和分析工作流',
        labels: {
          version: '1.0',
          team: 'data-analytics',
          environment: 'production'
        },
        annotations: {
          created_by: 'user123',
          last_modified: new Date().toISOString()
        }
      },
      nodes: {
        'read-sales': {
          id: 'read-sales',
          metadata: {
            displayName: '读取销售数据',
            description: '从数据仓库读取原始销售数据',
            labels: { step: 'input' },
            annotations: {}
          },
          opType: 'ReadStructuredDataResource',
          opConfig: {
            source: 'data-resource://sales-2023',
            format: 'parquet',
            options: { compression: 'snappy' }
          }
        },
        'filter-valid': {
          id: 'filter-valid',
          metadata: {
            displayName: '过滤有效数据',
            description: '过滤掉无效或测试数据',
            labels: { step: 'cleaning' },
            annotations: {}
          },
          opType: 'FilterStructuredData',
          opConfig: {
            resourceId: 'session://__SESSION_ID__/data-resource/read-sales',
            condition: {
              type: 'FuncCall',
              name: 'and',
              args: [
                { type: 'FuncCall', name: 'is_not_null', args: [{ type: 'Identifier', name: 'customer_id' }] },
                { type: 'FuncCall', name: 'gt', args: [{ type: 'Identifier', name: 'amount' }, { type: 'Literal', val: 0 }] }
              ]
            }
          }
        }
      },
      edges: [
        {
          source: 'read-sales',
          target: 'filter-valid',
          targetConfig: 'resourceId'
        }
      ]
    });

    const sampleGraph2 = new ExecutionGraph({
      id: 'sample-data-transformation',
      metadata: {
        displayName: '数据转换流水线',
        description: '基础数据转换和清洗流水线',
        labels: {
          version: '1.0',
          team: 'data-engineering',
          complexity: 'simple'
        },
        annotations: {
          created_by: 'user456',
          last_modified: new Date().toISOString()
        }
      },
      nodes: {
        'read-data': {
          id: 'read-data',
          metadata: {
            displayName: '读取数据',
            description: '从CSV文件读取数据',
            labels: { step: 'input' },
            annotations: {}
          },
          opType: 'ReadStructuredDataResource',
          opConfig: {
            source: 'data-resource://raw-data',
            format: 'csv',
            options: { header: true, delimiter: ',' }
          }
        }
      },
      edges: []
    });

    this.localGraphs.set(sampleGraph1.id, sampleGraph1);
    this.localGraphs.set(sampleGraph2.id, sampleGraph2);
  }

  /**
   * 获取执行图列表
   * @async
   * @param {ListGraphParams} [params={}] - 查询参数
   * @returns {Promise<{data: Array<ExecutionGraph>, pageParam: PageParam}>} 分页的执行图列表
   * @throws {Error} 当获取执行图列表失败时抛出错误
   */
  async listGraphs(params = {}) {
    try {
      // 先尝试从后端API获取
      try {
        const response = await this.client.post('/query', params);
        return {
          data: response.data.data.map(graph => new ExecutionGraph(this._mapExecutionGraphFields(graph))),
          pageParam: new PageParam(response.data.pageParam)
        };
      } catch (apiError) {
        // 如果API不可用，使用本地数据
        console.warn('执行图API不可用，使用本地数据:', apiError.message);
        return this._getLocalGraphs(params);
      }
    } catch (error) {
      throw new Error(`获取执行图列表失败: ${error.message}`);
    }
  }

  /**
   * 获取本地执行图列表
   * @private
   * @param {ListGraphParams} params - 查询参数
   * @returns {{data: Array<ExecutionGraph>, pageParam: PageParam}} 分页的执行图列表
   */
  _getLocalGraphs(params) {
    const { pageParam = {}, filters = {} } = params;
    const { pageIndex = 0, limit = 10 } = pageParam;

    let graphs = Array.from(this.localGraphs.values());

    // 应用过滤条件
    if (filters.displayNamePattern) {
      const pattern = filters.displayNamePattern.toLowerCase();
      graphs = graphs.filter(graph => 
        graph.metadata.displayName.toLowerCase().includes(pattern)
      );
    }

    if (filters.labels) {
      graphs = graphs.filter(graph => {
        return Object.entries(filters.labels).every(([key, value]) => 
          graph.metadata.labels[key] === value
        );
      });
    }

    // 排序
    graphs.sort((a, b) => {
      const aTime = new Date(a.metadata.annotations.last_modified || 0);
      const bTime = new Date(b.metadata.annotations.last_modified || 0);
      return bTime - aTime; // 降序排列
    });

    // 分页
    const total = graphs.length;
    const start = pageIndex * limit;
    const end = start + limit;
    const paginatedGraphs = graphs.slice(start, end);

    return {
      data: paginatedGraphs,
      pageParam: new PageParam({
        pageIndex,
        limit,
        pageTotal: Math.ceil(total / limit),
        recordTotal: total
      })
    };
  }

  /**
   * 获取执行图详情
   * @async
   * @param {string} graphId - 执行图ID
   * @returns {Promise<ExecutionGraph>} 执行图详情
   * @throws {Error} 当获取执行图详情失败时抛出错误
   */
  async getGraph(graphId) {
    try {
      // 先尝试从后端API获取
      try {
        const response = await this.client.get(`/${graphId}`);
        return new ExecutionGraph(this._mapExecutionGraphFields(response.data));
      } catch (apiError) {
        // 如果API不可用，使用本地数据
        console.warn('执行图API不可用，使用本地数据:', apiError.message);
        
        const localGraph = this.localGraphs.get(graphId);
        if (!localGraph) {
          throw new Error(`执行图 '${graphId}' 未找到`);
        }
        return localGraph;
      }
    } catch (error) {
      throw new Error(`获取执行图详情失败: ${error.message}`);
    }
  }

  /**
   * 保存执行图
   * @async
   * @param {ExecutionGraph} graph - 执行图对象
   * @returns {Promise<ExecutionGraph>} 保存后的执行图
   * @throws {Error} 当保存执行图失败时抛出错误
   */
  async saveGraph(graph) {
    try {
      // 验证执行图格式
      if (!graph || !graph.id) {
        throw new Error('执行图ID不能为空');
      }

      // 更新最后修改时间
      if (!graph.metadata) {
        graph.metadata = {};
      }
      if (!graph.metadata.annotations) {
        graph.metadata.annotations = {};
      }
      graph.metadata.annotations.last_modified = new Date().toISOString();

      // 先尝试保存到后端API
      try {
        const response = await this.client.put(`/${graph.id}`, graph);
        return new ExecutionGraph(this._mapExecutionGraphFields(response.data));
      } catch (apiError) {
        // 如果API不可用，保存到本地
        console.warn('执行图API不可用，保存到本地存储:', apiError.message);
        
        this.localGraphs.set(graph.id, graph);
        return graph;
      }
    } catch (error) {
      throw new Error(`保存执行图失败: ${error.message}`);
    }
  }

  /**
   * 创建新的执行图
   * @async
   * @param {Object} graphData - 执行图数据
   * @returns {Promise<ExecutionGraph>} 创建的执行图
   * @throws {Error} 当创建执行图失败时抛出错误
   */
  async createGraph(graphData) {
    try {
      // 生成新的图ID（如果没有提供）
      if (!graphData.id) {
        graphData.id = `graph-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      }

      // 设置创建时间
      if (!graphData.metadata) {
        graphData.metadata = {};
      }
      if (!graphData.metadata.annotations) {
        graphData.metadata.annotations = {};
      }
      graphData.metadata.annotations.created_at = new Date().toISOString();
      graphData.metadata.annotations.last_modified = new Date().toISOString();

      const graph = new ExecutionGraph(this._mapExecutionGraphFields(graphData));
      return await this.saveGraph(graph);
    } catch (error) {
      throw new Error(`创建执行图失败: ${error.message}`);
    }
  }

  /**
   * 删除执行图
   * @async
   * @param {string} graphId - 执行图ID
   * @returns {Promise<boolean>} 是否成功删除
   * @throws {Error} 当删除执行图失败时抛出错误
   */
  async deleteGraph(graphId) {
    try {
      // 先尝试从后端API删除
      try {
        await this.client.delete(`/${graphId}`);
        return true;
      } catch (apiError) {
        // 如果API不可用，从本地删除
        console.warn('执行图API不可用，从本地存储删除:', apiError.message);
        
        if (this.localGraphs.has(graphId)) {
          this.localGraphs.delete(graphId);
          return true;
        } else {
          throw new Error(`执行图 '${graphId}' 未找到`);
        }
      }
    } catch (error) {
      throw new Error(`删除执行图失败: ${error.message}`);
    }
  }

  /**
   * 导入执行图
   * @async
   * @param {File|Object} graphData - 执行图数据或文件
   * @returns {Promise<ExecutionGraph>} 导入的执行图
   * @throws {Error} 当导入执行图失败时抛出错误
   */
  async importGraph(graphData) {
    try {
      let parsedData;

      if (graphData instanceof File) {
        // 处理文件上传
        let text;
        if (graphData.text && typeof graphData.text === 'function') {
          // 现代浏览器支持File.text()方法
          text = await graphData.text();
        } else {
          // 回退到FileReader
          text = await this._readFileAsText(graphData);
        }
        try {
          parsedData = JSON.parse(text);
        } catch (parseError) {
          throw new Error(`无效的JSON格式: ${parseError.message}`);
        }
      } else {
        // 直接使用提供的对象或解析JSON字符串
        if (typeof graphData === 'string') {
          try {
            parsedData = JSON.parse(graphData);
          } catch (parseError) {
            throw new Error(`无效的JSON格式: ${parseError.message}`);
          }
        } else {
          parsedData = graphData;
        }
      }

      // 验证导入的数据格式
      if (!parsedData || typeof parsedData !== 'object' || !parsedData.nodes || !parsedData.edges) {
        throw new Error('无效的执行图结构');
      }

      if (!parsedData.id) {
        parsedData.id = `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      }

      // 标记为导入的图
      if (!parsedData.metadata) {
        parsedData.metadata = {};
      }
      if (!parsedData.metadata.annotations) {
        parsedData.metadata.annotations = {};
      }
      parsedData.metadata.annotations.imported_at = new Date().toISOString();
      parsedData.metadata.annotations.last_modified = new Date().toISOString();

      const graph = new ExecutionGraph(this._mapExecutionGraphFields(parsedData));
      return await this.saveGraph(graph);
    } catch (error) {
      throw new Error(`导入执行图失败: ${error.message}`);
    }
  }

  /**
   * 导出执行图
   * @async
   * @param {string} graphId - 执行图ID
   * @param {string} [format='json'] - 导出格式
   * @returns {Promise<string>} 导出的执行图JSON字符串
   * @throws {Error} 当导出执行图失败时抛出错误
   */
  async exportGraph(graphId, format = 'json') {
    try {
      const graph = await this.getGraph(graphId);
      
      if (format === 'json') {
        return JSON.stringify(graph, null, 2);
      } else {
        throw new Error(`不支持的导出格式: ${format}`);
      }
    } catch (error) {
      throw new Error(`导出执行图失败: ${error.message}`);
    }
  }

  /**
   * 复制执行图
   * @async
   * @param {string} graphId - 源执行图ID
   * @param {Object} [options={}] - 复制选项
   * @param {string} [options.newId] - 新执行图ID
   * @param {string} [options.newName] - 新执行图名称
   * @returns {Promise<ExecutionGraph>} 复制的执行图
   * @throws {Error} 当复制执行图失败时抛出错误
   */
  async copyGraph(graphId, options = {}) {
    try {
      const sourceGraph = await this.getGraph(graphId);
      
      const newGraphData = JSON.parse(JSON.stringify(sourceGraph)); // 深拷贝
      newGraphData.id = options.newId || `${graphId}-copy-${Date.now()}`;
      
      if (options.newName) {
        newGraphData.metadata.displayName = options.newName;
      } else {
        newGraphData.metadata.displayName = `${sourceGraph.metadata.displayName} (副本)`;
      }
      
      // 更新元数据
      newGraphData.metadata.annotations.copied_from = graphId;
      newGraphData.metadata.annotations.created_at = new Date().toISOString();
      newGraphData.metadata.annotations.last_modified = new Date().toISOString();
      
      return await this.saveGraph(new ExecutionGraph(this._mapExecutionGraphFields(newGraphData)));
    } catch (error) {
      throw new Error(`复制执行图失败: ${error.message}`);
    }
  }

  /**
   * 将文件读取为文本
   * @private
   * @param {File} file - 文件对象
   * @returns {Promise<string>} 文件内容
   */
  _readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => resolve(event.target.result);
      reader.onerror = (error) => reject(error);
      reader.readAsText(file);
    });
  }

  /**
   * 搜索执行图
   * @async
   * @param {string} keyword - 搜索关键词
   * @param {Object} [options={}] - 搜索选项
   * @param {Array<string>} [options.fields=['displayName', 'description']] - 搜索字段
   * @returns {Promise<Array<ExecutionGraph>>} 搜索结果
   * @throws {Error} 当搜索失败时抛出错误
   */
  async searchGraphs(keyword, options = {}) {
    try {
      const { 
        searchFields = ['metadata.displayName', 'metadata.description'],
        caseSensitive = false,
        exactMatch = false
      } = options;
      
      const allGraphsResult = await this.listGraphs();
      const allGraphs = allGraphsResult.data;
      
      const searchKeyword = caseSensitive ? keyword : keyword.toLowerCase();
      
      const results = allGraphs.filter(graph => {
        return searchFields.some(field => {
          const value = this._getNestedValue(graph, field);
          if (!value) return false;
          
          const searchValue = caseSensitive ? value : value.toLowerCase();
          
          if (exactMatch) {
            return searchValue === searchKeyword;
          } else {
            return searchValue.includes(searchKeyword);
          }
        });
      });
      
      return results;
    } catch (error) {
      throw new Error(`搜索执行图失败: ${error.message}`);
    }
  }

  /**
   * 获取嵌套对象的值
   * @private
   * @param {Object} obj - 源对象
   * @param {string} path - 路径（如 'metadata.displayName'）
   * @returns {*} 值
   */
  _getNestedValue(obj, path) {
    if (!obj) return undefined;
    if (!path) return obj; // Return the object itself if path is empty

    const parts = path.split('.');
    let current = obj;

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (current === null || typeof current !== 'object' || !current.hasOwnProperty(part)) {
        return undefined;
      }
      current = current[part];
    }
    return current;
  }

  /**
   * 获取执行图统计信息
   * @async
   * @returns {Promise<Object>} 统计信息
   * @throws {Error} 当获取统计信息失败时抛出错误
   */
  async getGraphStatistics() {
    try {
      const allGraphsResult = await this.listGraphs();
      const allGraphs = allGraphsResult.data;
      
      const statistics = {
        totalGraphs: allGraphs.length,
        byTeam: {},
        byVersion: {},
        recentlyModified: allGraphs
          .sort((a, b) => {
            const aTime = new Date(a.metadata.annotations.last_modified || 0);
            const bTime = new Date(b.metadata.annotations.last_modified || 0);
            return bTime - aTime;
          })
          .slice(0, 5)
      };
      
      // 按团队统计
      allGraphs.forEach(graph => {
        const team = graph.metadata.labels.team || 'unknown';
        statistics.byTeam[team] = (statistics.byTeam[team] || 0) + 1;
      });
      
      // 按版本统计
      allGraphs.forEach(graph => {
        const version = graph.metadata.labels.version || 'unknown';
        statistics.byVersion[version] = (statistics.byVersion[version] || 0) + 1;
      });
      
      return statistics;
    } catch (error) {
      throw new Error(`获取执行图统计信息失败: ${error.message}`);
    }
  }
}

// 创建并导出服务实例
const executionGraphRepositoryService = new ExecutionGraphRepositoryService();

export default executionGraphRepositoryService;
export { ExecutionGraphRepositoryService };
