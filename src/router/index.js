import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../HomeIndexPage.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  
  // docs: SessionListPage页面路由 - 会话列表页
  {
    path: '/dpe-web/sessions',
    name: 'SessionListPage',
    component: () => import('@/pages/dpe-web/pages/SessionListPage')
  },
  
  // docs: SessionDetailIndex页面路由 - 会话详情页入口
  {
    path: '/dpe-web/sessions/detail/:sessionId',
    name: 'SessionDetailIndex',
    component: () => import('@/pages/dpe-web/pages/SessionDetailPage')
  },
  
  // docs: ExecutionGraphListPage页面路由 - 执行图列表页
  {
    path: '/dpe-web/graphs',
    name: 'ExecutionGraphListPage',
    component: () => import('@/pages/dpe-web/pages/ExecutionGraphListPage')
  },
  // 其他路由...
]


// docs: 在测试环境下加载测试相关路由
// Check if the environment is 'test' or 'development' before adding test-specific routes
if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development' || process.env.VUE_APP_E2E_TEST === 'true') {
  try {
    // Dynamically import test routes using require for conditional loading
    // Ensure the path is correct relative to src/router/index.js
    // If using ES Module import, it would need to be at the top level,
    // but conditional require works well here to prevent bundling in non-test environments.
    const testRoutesModule = require('../../tests/router/test-routes');
    if (testRoutesModule && testRoutesModule.default) {
      routes.push(...testRoutesModule.default);
      console.log('主人😊: 测试路由已成功加载 🎉, 环境:', process.env.NODE_ENV);
    } else {
       console.warn('主人😥: 未找到测试路由模块或其默认导出 😟');
    }
  } catch (e) {
    console.error('主人😟: 加载测试路由时发生错误 💥:', e);
  }
} else {
  console.log('主人😊: 非测试环境，测试路由未加载 👍, 环境:', process.env.NODE_ENV);
}

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

export default router
