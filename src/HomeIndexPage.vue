<template>
  <div class="home-index-container">
    <h1>导航页面</h1>
    <div class="button-container">
      <a-button type="primary" @click="goToIdConfig" class="nav-button">ID编码生成规则配置</a-button>
      <a-button type="primary" @click="goToLogViewer" class="nav-button">显示日志</a-button>
      <a-button type="primary" @click="goToMixConfig" class="nav-button">重复数据检测配置</a-button>
      <a-button type="primary" @click="goToMixHuman" class="nav-button">重复数据手工复核</a-button>
      <a-button type="primary" @click="goToRecordMix" class="nav-button">数据表记录融合配置</a-button>
      <a-button type="primary" @click="goToCustomTest" class="nav-button">数据工厂</a-button>
      <a-button type="primary" @click="goToRobotMonitor" class="nav-button">采集机器人工作监视器</a-button>
      <a-button type="primary" @click="goToIcebergService" class="nav-button">Iceberg数据服务</a-button>
      <a-button type="primary" @click="goToNodeTopology" class="nav-button">节点拓扑</a-button>
      <a-button type="primary" @click="goToDataAudit" class="nav-button">数据稽核</a-button>
      <a-button type="primary" @click="goToRobot" class="nav-button">机器人页面</a-button>
      <a-button type="primary" @click="goToDataDashboard" class="nav-button">数据看板</a-button>
      <a-button type="primary" @click="goToInitArchitecture" class="nav-button">初始化算存架构</a-button>
      <a-button type="primary" @click="goToPendingManual" class="nav-button">待人工处理数据表</a-button>
      <a-button type="primary" @click="goToAIChat" class="nav-button">大模型对话</a-button>
      <a-button type="primary" @click="goToMultiModal" class="nav-button">多模态对话</a-button>
      <a-button type="primary" @click="goToCreator" class="nav-button">创作家</a-button>
      <a-button type="primary" @click="goToFusionDesign" class="nav-button">融合设计</a-button>
      <a-button type="primary" @click="goToProcessDesigner" class="nav-button">流程设计器</a-button>
      <a-button type="primary" @click="goToIntelligentModeling" class="nav-button">智能建模</a-button>
      <a-button type="primary" @click="goToKafkaDocs" class="nav-button">Kafka生产消费数据帮助文档</a-button>
      <a-button type="primary" @click="goToSwimlaneDiagram" class="nav-button">泳道血缘图</a-button>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goToIdConfig() {
      this.$router.push('/idConfig');
    },
    goToLogViewer() {
      this.$router.push('/logViewer');
    },
    goToMixConfig() {
      this.$router.push('/MixConfig');
    },
    goToMixHuman() {
      this.$router.push('/MixHuman');
    },
    goToRecordMix() {
      this.$router.push('/RecordMix');
    },
    goToCustomTest() {
      this.$router.push('/CustomTest');
    },
    goToRobotMonitor() {
      this.$router.push('/RobotMonitor');
    },
    goToIcebergService() {
      this.$router.push('/IcebergService');
    },
    goToNodeTopology() {
      this.$router.push('/NodeTopology');
    },
    goToDataAudit() {
      this.$router.push('/dataAudit');
    },
    goToRobot() {
      this.$router.push('/Robot');
    },
    goToDataDashboard() {
      this.$router.push('/dataDashboard');
    },
    goToInitArchitecture() {
      this.$router.push('/initArchitecture');
    },
    goToPendingManual() {
      this.$router.push('/pendingManual');
    },
    goToAIChat() {
      this.$router.push('/aiChat');
    },
    goToMultiModal() {
      this.$router.push('/multiModal');
    },
    goToCreator() {
      this.$router.push('/creator');
    },
    goToFusionDesign() {
      this.$router.push('/fusionDesign');
    },
    goToProcessDesigner() {
      this.$router.push('/processDesigner');
    },
    goToIntelligentModeling() {
      this.$router.push('/intelligentModeling');
    },
    goToKafkaDocs() {
      this.$router.push('/kafkaDocs');
    },
    goToSwimlaneDiagram() {
      this.$router.push('/SwimlaneDiagramDemo');
    }
  }
};
</script>

<style scoped>
.home-index-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.nav-button {
  width: 240px;
  margin-bottom: 10px;
}
</style>
