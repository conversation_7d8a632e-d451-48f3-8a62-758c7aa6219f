<template>
  <a-button :type="type" :disabled="disabled" @click="handleClick">
    <slot>{{ label }}</slot>
  </a-button>
</template>

<script>
import { Button } from 'ant-design-vue';

/**
 * 通用按钮组件
 * 
 * 提供可定制的按钮样式和交互，适用于各种点击操作。支持不同的样式类型和禁用状态。
 * 
 * @displayName ExampleButton
 */
export default {
  name: 'ExampleButton',
  components: {
    AButton: Button,
  },
  props: {
    /**
     * 按钮上显示的文本
     * @property {string} label
     * @default 'Button'
     */
    label: {
      type: String,
      default: 'Button',
    },
    /**
     * 按钮的样式类型
     * @property {'primary'|'dashed'|'danger'|'link'} type
     * @default 'primary'
     */
    type: {
      type: String,
      default: 'primary',
      validator: function (value) {
        return ['primary', 'dashed', 'danger', 'link'].indexOf(value) !== -1;
      },
    },
    /**
     * 是否禁用按钮
     * @property {boolean} disabled
     * @default false
     */
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    /**
     * 处理按钮点击事件
     * @access public
     * @param {MouseEvent} event - 原生点击事件对象
     * @fires click
     */
    handleClick: function (event) {
      if (!this.disabled) {
        /**
         * 按钮点击时触发
         * @event click
         * @type {MouseEvent}
         * @property {number} clientX - 鼠标点击位置X坐标
         * @property {number} clientY - 鼠标点击位置Y坐标
         * @property {HTMLElement} target - 触发事件的元素
         */
        this.$emit('click', event);
      }
    },
    /**
     * 这是一个私有方法，不应该出现在文档中。
     * @access private
     */
    _privateMethod: function () {
      console.log('This is a private method.');
    },
  },
  /**
   * @slot default - 默认插槽，用于放置按钮的文本内容或自定义 HTML。
   * @example
   * ```html
   * <ExampleButton>点击我</ExampleButton>
   * ```
   */
};
</script>
