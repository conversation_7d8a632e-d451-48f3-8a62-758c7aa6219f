<template>
  <div class="pagination-wrapper">
    <!-- 排序选项区域 -->
    <div v-if="showSortOptions && availableSortFields.length > 0" class="sort-section">
      <a-form layout="inline">
        <a-form-item label="排序字段">
          <a-select
            v-model="currentSortField"
            placeholder="选择排序字段"
            style="width: 150px"
            @change="handleSortFieldChange"
            :disabled="loading"
          >
            <a-select-option
              v-for="field in availableSortFields"
              :key="field.value"
              :value="field.value"
            >
              {{ field.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="排序方式">
          <a-select
            v-model="currentSortType"
            style="width: 100px"
            @change="handleSortTypeChange"
            :disabled="loading"
          >
            <a-select-option value="asc">升序</a-select-option>
            <a-select-option value="desc">降序</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>

    <!-- 分页控制区域 -->
    <div class="pagination-controls">
      <div class="pagination-info" v-if="showTotal">
        <span>共 {{ pageParam.recordTotal || 0 }} 条记录</span>
        <span v-if="pageParam.pageTotal > 0" class="page-info">
          第 {{ currentPageDisplay }} / {{ pageParam.pageTotal }} 页
        </span>
      </div>

      <div class="pagination-actions">
        <!-- 刷新按钮 -->
        <a-button
          v-if="showRefresh"
          type="default"
          icon="reload"
          :loading="loading"
          :disabled="loading"
          @click="handleRefresh"
          class="refresh-btn"
        >
          刷新
        </a-button>

        <!-- 页面大小选择器 -->
        <div v-if="showSizeChanger" class="page-size-selector">
          <span>每页显示</span>
          <a-select
            v-model="currentPageSize"
            :disabled="loading"
            style="width: 80px; margin: 0 8px"
            @change="handleSizeChange"
          >
            <a-select-option
              v-for="size in pageSizeOptions"
              :key="size"
              :value="size"
            >
              {{ size }}
            </a-select-option>
          </a-select>
          <span>条</span>
        </div>

        <!-- 核心分页组件 -->
        <a-pagination
          v-model="currentPageIndex"
          :total="pageParam.recordTotal || 0"
          :page-size="pageParam.limit || 10"
          :show-size-changer="false"
          :show-quick-jumper="showQuickJumper"
          :show-total="false"
          :disabled="loading"
          @change="handlePageChange"
          @showSizeChange="handleSizeChange"
          size="default"
        />
      </div>
    </div>
  </div>
</template>

<script>
/**
 * Pagination 分页组件
 * @description 通用的分页组件，专门用于与数据处理引擎的后端分页接口进行对接
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
export default {
  name: 'Pagination',
  
  props: {
    /**
     * 分页参数对象，与后端 PageParam 结构完全对应
     * @type {Object}
     * @required
     */
    pageParam: {
      type: Object,
      required: true,
      default: () => ({
        pageIndex: 0,
        limit: 10,
        pageTotal: 0,
        recordTotal: 0,
        sortField: '',
        sortType: 'desc',
        sortList: [],
        isTop: false
      })
    },

    /**
     * 业务筛选条件对象，会传递给后端接口
     * @type {Object}
     */
    filters: {
      type: Object,
      default: () => ({})
    },

    /**
     * 加载状态，控制分页组件的禁用状态
     * @type {Boolean}
     */
    loading: {
      type: Boolean,
      default: false
    },

    /**
     * 是否显示页面大小选择器
     * @type {Boolean}
     */
    showSizeChanger: {
      type: Boolean,
      default: true
    },

    /**
     * 是否显示快速跳转输入框
     * @type {Boolean}
     */
    showQuickJumper: {
      type: Boolean,
      default: true
    },

    /**
     * 是否显示排序选项
     * @type {Boolean}
     */
    showSortOptions: {
      type: Boolean,
      default: false
    },

    /**
     * 可用的排序字段配置
     * @type {Array<{value: string, label: string}>}
     */
    availableSortFields: {
      type: Array,
      default: () => []
    },

    /**
     * 页面大小选项
     * @type {Array<number>}
     */
    pageSizeOptions: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },

    /**
     * 是否显示总数信息
     * @type {Boolean}
     */
    showTotal: {
      type: Boolean,
      default: true
    },

    /**
     * 是否显示刷新按钮
     * @type {Boolean}
     */
    showRefresh: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      /** @type {number} 当前页码（从1开始，用于显示） */
      currentPageIndex: 1,
      /** @type {number} 当前页面大小 */
      currentPageSize: 10,
      /** @type {string} 当前排序字段 */
      currentSortField: '',
      /** @type {string} 当前排序类型 */
      currentSortType: 'desc'
    };
  },

  computed: {
    /**
     * 当前页面显示（从1开始）
     * @returns {number}
     */
    currentPageDisplay() {
      return (this.pageParam.pageIndex || 0) + 1;
    }
  },

  watch: {
    /**
     * 监听 pageParam 变化，同步内部状态
     * @param {Object} newVal - 新的分页参数
     */
    pageParam: {
      handler(newVal) {
        if (newVal) {
          this.currentPageIndex = (newVal.pageIndex || 0) + 1;
          this.currentPageSize = newVal.limit || 10;
          this.currentSortField = newVal.sortField || '';
          this.currentSortType = newVal.sortType || 'desc';
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    /**
     * 处理页码变化
     * @param {number} page - 新页码（从1开始）
     * @public
     */
    handlePageChange(page) {
      const pageIndex = page - 1; // 转换为从0开始的索引
      this.currentPageIndex = page;
      
      const newPageParam = {
        pageIndex,
        limit: this.pageParam.limit
      };

      /**
       * 页码变化事件
       * @event page-change
       * @param {Object} pageParam - 包含 pageIndex 的分页参数对象
       */
      this.$emit('page-change', newPageParam);
    },

    /**
     * 处理页面大小变化
     * @param {number} pageSize - 新的页面大小
     * @public
     */
    handleSizeChange(pageSize) {
      this.currentPageSize = pageSize;
      this.currentPageIndex = 1; // 重置到第一页

      /**
       * 页面大小变化事件
       * @event size-change
       * @param {number} pageSize - 新的页面大小
       */
      this.$emit('size-change', pageSize);
    },

    /**
     * 处理排序字段变化
     * @param {string} field - 新的排序字段
     * @private
     */
    handleSortFieldChange(field) {
      this.currentSortField = field;
      this.emitSortChange();
    },

    /**
     * 处理排序类型变化
     * @param {string} type - 新的排序类型
     * @private
     */
    handleSortTypeChange(type) {
      this.currentSortType = type;
      this.emitSortChange();
    },

    /**
     * 发送排序变化事件
     * @private
     */
    emitSortChange() {
      const sortConfig = {
        sortField: this.currentSortField,
        sortType: this.currentSortType,
        sortList: [
          { field: this.currentSortField, type: this.currentSortType }
        ]
      };

      /**
       * 排序变化事件
       * @event sort-change
       * @param {Object} sortConfig - 包含 sortField, sortType, sortList 的排序配置
       */
      this.$emit('sort-change', sortConfig);
    },

    /**
     * 处理刷新按钮点击
     * @public
     */
    handleRefresh() {
      /**
       * 刷新事件
       * @event refresh
       */
      this.$emit('refresh');
    },

    /**
     * 跳转到指定页面
     * @param {number} pageIndex - 页面索引（从0开始）
     * @public
     */
    goToPage(pageIndex) {
      if (pageIndex >= 0 && pageIndex < this.pageParam.pageTotal) {
        this.handlePageChange(pageIndex + 1);
      }
    },

    /**
     * 更改页面大小
     * @param {number} pageSize - 新的页面大小
     * @public
     */
    changePageSize(pageSize) {
      this.handleSizeChange(pageSize);
    },

    /**
     * 重置到第一页
     * @public
     */
    resetToFirstPage() {
      this.handlePageChange(1);
    },

    /**
     * 更新排序配置
     * @param {Object} sortConfig - 排序配置对象
     * @param {string} sortConfig.sortField - 排序字段
     * @param {string} sortConfig.sortType - 排序类型
     * @public
     */
    updateSortConfig(sortConfig) {
      if (sortConfig.sortField) {
        this.currentSortField = sortConfig.sortField;
      }
      if (sortConfig.sortType) {
        this.currentSortType = sortConfig.sortType;
      }
      this.emitSortChange();
    }
  }
};
</script>

<style scoped>
.pagination-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sort-section {
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #666;
  font-size: 14px;
}

.page-info {
  color: #999;
}

.pagination-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.refresh-btn {
  min-width: 80px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pagination-info {
    justify-content: center;
  }
  
  .pagination-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .sort-section {
    padding: 8px;
  }
  
  /* 在小屏幕上隐藏页面大小选择器 */
  .page-size-selector {
    display: none;
  }
}

@media (max-width: 480px) {
  .pagination-wrapper {
    gap: 12px;
  }
  
  .pagination-actions {
    gap: 8px;
  }
  
  .refresh-btn {
    min-width: 60px;
    font-size: 12px;
  }
}
</style> 