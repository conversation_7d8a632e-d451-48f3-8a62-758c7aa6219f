/**
 * @file 这是一个示例用户服务文件。
 * @module services/UserService
 * @description 用于处理用户相关操作的服务。
 */

/**
 * @typedef {object} UserInfo
 * @property {string} id - 用户ID。
 * @property {string} name - 用户名称。
 * @property {string} email - 用户邮箱。
 */

/**
 * 用户服务类
 * @const {object} UserService
 * @description 封装了用户相关的 API 调用和业务逻辑。
 */
const UserService = {
  /**
   * 假的用户数据存储
   * @private
   * @type {UserInfo[]}
   */
  _users: [
    { id: '1', name: 'Alice', email: '<EMAIL>' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>' },
  ],

  /**
   * 获取所有用户列表。
   * @public
   * @returns {Promise<UserInfo[]>} 用户信息列表。
   * @example
   * ```javascript
   * UserService.getUsers().then(users => {
   *   console.log(users);
   * });
   * ```
   */
  async getUsers() {
    return Promise.resolve(this._users);
  },

  /**
   * 根据用户ID获取用户信息。
   * @public
   * @param {string} userId - 用户ID。
   * @returns {Promise<UserInfo|null>} 匹配的用户信息，如果没有找到则返回null。
   * @example
   * ```javascript
   * UserService.getUserById('1').then(user => {
   *   console.log(user);
   * });
   * ```
   */
  async getUserById(userId) {
    const user = this._users.find(u => u.id === userId);
    return Promise.resolve(user || null);
  },

  /**
   * 创建一个新用户。
   * @public
   * @param {object} userData - 用户数据。
   * @param {string} userData.name - 用户名称。
   * @param {string} userData.email - 用户邮箱。
   * @returns {Promise<UserInfo>} 新创建的用户信息。
   * @example
   * ```javascript
   * UserService.createUser({ name: 'Charlie', email: '<EMAIL>' }).then(newUser => {
   *   console.log(newUser);
   * });
   * ```
   */
  async createUser(userData) {
    const newUser = { id: String(this._users.length + 1), ...userData };
    this._users.push(newUser);
    return Promise.resolve(newUser);
  },

  /**
   * 这是一个内部辅助方法，不应该通过公共API访问。
   * @private
   * @returns {string} 内部消息。
   */
  _internalHelper() {
    return "This is an internal helper.";
  },
};

export default UserService;
