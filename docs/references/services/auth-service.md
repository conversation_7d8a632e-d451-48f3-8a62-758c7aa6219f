<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

### Table of Contents

*   [services/AuthService][1]
*   [LoginCredentials][2]
    *   [Properties][3]
*   [AuthToken][4]
    *   [Properties][5]
*   [AuthService][6]
    *   [login][7]
        *   [Parameters][8]
        *   [Examples][9]
    *   [logout][10]
        *   [Examples][11]
    *   [refreshToken][12]
        *   [Parameters][13]
        *   [Examples][14]

## services/AuthService

处理用户认证相关的操作

## LoginCredentials

Type: [object][15]

### Properties

*   `username` **[string][16]** 用户名
*   `password` **[string][16]** 密码

## AuthToken

Type: [object][15]

### Properties

*   `token` **[string][16]** 访问令牌
*   `expiresIn` **[number][17]** 过期时间（秒）
*   `refreshToken` **[string][16]** 刷新令牌

## AuthService

提供用户登录、登出、令牌刷新等认证功能

Type: [object][15]

### login

用户登录

#### Parameters

*   `credentials` **[LoginCredentials][2]** 登录凭据

#### Examples

````javascript
```javascript
AuthService.login({ username: 'admin', password: '123456' })
  .then(token => console.log(token));
```
````

Returns **[Promise][18]<[AuthToken][4]>** 认证令牌信息

### logout

用户登出

#### Examples

````javascript
```javascript
AuthService.logout().then(() => {
  console.log('已登出');
});
```
````

Returns **[Promise][18]\<void>** 登出结果

### refreshToken

刷新访问令牌

#### Parameters

*   `refreshToken` **[string][16]** 刷新令牌

#### Examples

````javascript
```javascript
AuthService.refreshToken('refresh-token')
  .then(newToken => console.log(newToken));
```
````

Returns **[Promise][18]<[AuthToken][4]>** 新的认证令牌

[1]: #servicesauthservice

[2]: #logincredentials

[3]: #properties

[4]: #authtoken

[5]: #properties-1

[6]: #authservice

[7]: #login

[8]: #parameters

[9]: #examples

[10]: #logout

[11]: #examples-1

[12]: #refreshtoken

[13]: #parameters-1

[14]: #examples-2

[15]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[16]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[17]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[18]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise
