<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

### Table of Contents

*   [services/UserService][1]
*   [UserInfo][2]
    *   [Properties][3]
*   [UserService][4]
    *   [getUsers][5]
        *   [Examples][6]
    *   [getUserById][7]
        *   [Parameters][8]
        *   [Examples][9]
    *   [createUser][10]
        *   [Parameters][11]
        *   [Examples][12]

## services/UserService

用于处理用户相关操作的服务。

## UserInfo

Type: [object][13]

### Properties

*   `id` **[string][14]** 用户ID。
*   `name` **[string][14]** 用户名称。
*   `email` **[string][14]** 用户邮箱。

## UserService

封装了用户相关的 API 调用和业务逻辑。

Type: [object][13]

### getUsers

获取所有用户列表。

#### Examples

````javascript
```javascript
UserService.getUsers().then(users => {
  console.log(users);
});
```
````

Returns **[Promise][15]<[Array][16]<[UserInfo][2]>>** 用户信息列表。

### getUserById

根据用户ID获取用户信息。

#### Parameters

*   `userId` **[string][14]** 用户ID。

#### Examples

````javascript
```javascript
UserService.getUserById('1').then(user => {
  console.log(user);
});
```
````

Returns **[Promise][15]<([UserInfo][2] | null)>** 匹配的用户信息，如果没有找到则返回null。

### createUser

创建一个新用户。

#### Parameters

*   `userData` **[object][13]** 用户数据。

    *   `userData.name` **[string][14]** 用户名称。
    *   `userData.email` **[string][14]** 用户邮箱。

#### Examples

````javascript
```javascript
UserService.createUser({ name: 'Charlie', email: '<EMAIL>' }).then(newUser => {
  console.log(newUser);
});
```
````

Returns **[Promise][15]<[UserInfo][2]>** 新创建的用户信息。

[1]: #servicesuserservice

[2]: #userinfo

[3]: #properties

[4]: #userservice

[5]: #getusers

[6]: #examples

[7]: #getuserbyid

[8]: #parameters

[9]: #examples-1

[10]: #createuser

[11]: #parameters-1

[12]: #examples-2

[13]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[14]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[15]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Promise

[16]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array
