# ExampleButton

> 通用按钮组件

提供可定制的按钮样式和交互，适用于各种点击操作。支持不同的样式类型和禁用状态。

## Props

| Name     | Type    | Default     | Description      |
| -------- | ------- | ----------- | ---------------- |
| label    | string  | `'Button'`  | 按钮上显示的文本 |
| type     | string  | `'primary'` | 按钮的样式类型   |
| disabled | boolean | `false`     | 是否禁用按钮     |

## Methods

| Name        | Parameters        | Description      |
| ----------- | ----------------- | ---------------- |
| handleClick | event: MouseEvent | 处理按钮点击事件 |

## Events

| Name    | Description    | Parameters                                                                                                                    |
| ------- | -------------- | ----------------------------------------------------------------------------------------------------------------------------- |
| `click` | 按钮点击时触发 | clientX `undefined` - 鼠标点击位置 X 坐标<br>clientY `undefined` - 鼠标点击位置 Y 坐标<br>target `undefined` - 触发事件的元素 |

## Slots

| Name      | Description |
| --------- | ----------- |
| `default` |             |

---
