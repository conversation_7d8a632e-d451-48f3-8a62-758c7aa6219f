## 总体说明
面向批处理的异构数据统一处理逻辑描述DSL语言，底层基于 DAG 结构组织逻辑

## 资源描述
### 资源描述结构
采用 uri 作为任意资源的id
资源类型：
- 已经存在的数据资源： `data-resource://{data resource id}`
- 存在与某个作业运行会话内部的数据资源: `session://{session id}/data-resource/{data resource id}`

```yaml
# 资源标识
id: <a uuid>
# 资源的元数据
metadata:
  # ****展示名称
  displayname: 过滤
  # 描述
  desciption: 过滤不符合条件的数据
  # 分类标签
  labels:
    category: transformation
  # 扩展元数据信息
  annotations: 
    version: "1.0"
# 资源输入输出信息
io_info:
  # 输入输出类型
  type:
  # 输出输出其他描述属性

# 资源提供的操作方法API定义
operationMethodMetadata:
- name: validateExpression
  # 方法的元数据
  metadata:
    displayname: 表达式验证
    description: 验证过滤条件表达式的语法正确性
  # 方法的输入参数，使用JSON-Schema
  inputSchema:
    type: object
    properties:
      expression:
        type: string
        description: 待验证的表达式
    required: ["expression"]
  # 方法的输出参数，使用JSON-Schema
  outputSchema:
    type: object
    properties:
      isValid:
        type: boolean
        description: 表达式是否有效
      errorMessage:
        type: string
        description: 错误信息，如果表达式有效则为空
    required: ["isValid"]
  # 可能的错误定义
  possibleErrors:
    - code: "EXPR_SYNTAX_ERROR"
      description: "表达式的语法不正确，无法解析"
    - code: "EXPR_FIELD_NOT_FOUND"
      description: "表达式中引用了不存在的字段"

```
### 2.4 Resource IOType

资源的 IOType 用于描述资源的结构元数据信息，算子输入输出类型以及对应的属性信息结构，主要包含以下几种：

```mermaid
classDiagram
    class IOType {
        +String type
    }
    
    class Scalar {
      +DataStruct struct
    }
    
    class DataFrame {
        +List~SchemaField~ schema
        +partitionInfo
        +sortInfo
    }
    
    class Graph {
        +List~SchemaField~ nodeSchema
        +List~SchemaField~ edgeSchema
    }
    
    IOType <|-- Scalar
    IOType <|-- DataFrame
    IOType <|-- Graph
    
    class SchemaField {
        +String name
        +Metadata metadata
        +DataStruct struct
    }
  
  DataFrame *-- SchemaField
  Graph *-- SchemaField
```
#### 2.4.1 Scalar

标量值，表示单个结构化对象。

类型属性：
- **struct**：对象的结构定义

#### 2.4.2 DataFrame

代表有限的结构化数据集。

类型属性：
- **schema**：数据集的结构定义
- **partitionInfo**：数据集的分区信息
- **sortInfo**：数据集的排序信息


#### 2.4.5 Graph

图结构数据，可以包含节点和边的集合。

类型属性：
- **nodeSchema**：数据节点的结构定义
- **edgeSchema**：数据节点的结构定义
## 算子描述结构
包含输入输出类型要求和节点配置格式描述。

```yaml
# 节点类型标识
type: filter
# 节点的元数据
metadata:
  # 节点展示名称
  displayname: 过滤
  # 节点描述
  desciption: 过滤不符合条件的数据
  # 分类标签
  labels:
    category: transformation
  # 扩展元数据信息
  annotations: 
    version: "1.0"
# 节点输入元数据信息定义
inputsMetadata:
- name: input_1
  # 输入端口的元数据
  metadata:
    displayname: 输入数据
    description: 需要过滤的数据流
  # 允许接收的输入输出类型列表
  acceptableIOTypes: 
    - DataFrame
  # 该输入是否允许为空，即是否允许不连接上游
  nullable: false
  # 是否允个输入，即是否允许连接多个上游
  multiple: false
# 节点输出定义
outputMetadata:
  # 输出的元数据
  metadata:
    displayname: 过滤结果
    description: 符合条件的数据
  # 该输出可能的类型列表
  possibleIOTypes: 
    - DataFrame
# 节点配置项元数据定义
configsMetadata:
- name: condition
  # 配置项的元数据
  metadata:
    displayname: 过滤条件
    description: 用于过滤数据的表达式
  # 配置项的结构定义，使用JSON-Schema
  schema: 
    type: string
    minLength: 1
    description: 表达式字符串
  # 配置项是否可为空
  nullable: false
- name: projection
  metadata:
    displayname: 投影字段
    description: 需要保留的字段
  schema:
    type: array
    items:
      type: string
    description: 字段名称列表
  nullable: true

# 节点提供的编辑时可调用方法API定义
helperMethodMetadata:
- name: validateExpression
  # 方法的元数据
  metadata:
    displayname: 表达式验证
    description: 验证过滤条件表达式的语法正确性
  # 方法的输入参数，使用JSON-Schema
  inputSchema:
    type: object
    properties:
      expression:
        type: string
        description: 待验证的表达式
    required: ["expression"]
  # 方法的输出参数，使用JSON-Schema
  outputSchema:
    type: object
    properties:
      isValid:
        type: boolean
        description: 表达式是否有效
      errorMessage:
        type: string
        description: 错误信息，如果表达式有效则为空
    required: ["isValid"]
  # 可能的错误定义
  possibleErrors:
    - code: "EXPR_SYNTAX_ERROR"
      description: "表达式的语法不正确，无法解析"
    - code: "EXPR_FIELD_NOT_FOUND"
      description: "表达式中引用了不存在的字段"

# 可能的错误定义
possibleErrors:
- code: "TASK_NOT_RUNNING"
  description: "任务实例当前未处于运行状态，无法获取执行统计信息"
  # 错误详情的结构定义（可选），使用JSON-Schema
  detailSchema: 
```


```mermaid
classDiagram

  class ObjectMetadata {
        +String displayname
        +String description
        +Dict~String,String~ labels
        +Dict~String,String~ annotations
    }

    class OperatorTypeMetadata {
        +String type
        +ObjectMetadata metadata
        +List~InputMetadata~ inputsMetadata
        +OutputMetadata outputMetadata
        +List~ConfigMetadata~ configsMetadata
        +List~MethodMetadata~ editorMethodMetadata
        +List~MethodMetadata~ runtimeMethodMetadata
    }
    
    class InputMetadata {
        +String name
        +ObjectMetadata metadata
        +List~String~ acceptableIOTypes
        +Boolean nullable
        +Boolean multiple
    }
    
    class OutputMetadata {
        +String name
        +ObjectMetadata metadata
        +List~String~ possibleIOTypes
    }
    
    class ConfigMetadata {
        +String name
        +ObjectMetadata metadata
        +JSONSchema schema
        +Boolean nullable
    }

  class MethodMetadata {
        +String name
        +ObjectMetadata metadata
        +JSONSchema inputSchema
        +JSONSchema outputSchema
        +List~ErrorInfo~ possibleErrors
    }

    class ErrorInfo {
        +String code
        +String description
        +Optional~JSONSchema~ detailSchema
    }

    OperatorTypeMetadata *-- InputMetadata
    OperatorTypeMetadata *-- OutputMetadata
    OperatorTypeMetadata *-- ConfigMetadata
    OperatorTypeMetadata *-- MethodMetadata
    OperatorTypeMetadata *-- ErrorInfo
    MethodMetadata *-- ErrorInfo
```

## 数据处理工具设计

### 工具调用公共参数定义

输入参数：
- sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
输出参数：
- sessionId (str): 当前操作所在会话的id


### 结构化数据

#### 工具定义
- 读取结构化数据资源（`readStructuredDataResource`）：读取某个结构化数据资源的数据
  - 参数：
    - resourceId (str): uri 格式的数据资源id
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - filterExpression (Optional[str]): 过滤表达式
    - columnsOnly (Optional[List[str]]): 限制返回的列，空代表返回所有字段
    - previewData (Optional[bool]): 是否预览数据，默认不预览
    - previewOptions (Optional[PreviewOption]): 数据预览选项
      - head (Optional[int]): 限制返回前n行
      - filterExpression (Optional[str]): 过滤表达式
      - columnsOnly (Optional[List[str]]): 限制返回的列，空代表返回所有字段
      - orderBy (Optional[List[SortOption]]): 排序规则
        - column (str): 字段名称
      - desc (Optional[bool]): 是否倒序
      - formatter (Optional[str]): 格式化模式，可选 json、markdown、xml
      - formatterArgs (Optional[Dict[str, Any]]): 格式化参数
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - schema (List[SchemaField]): 结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
        - displayname (str): 字段展示名称
        - description (str): 字段描述
        - labels (Dict[str, str]): 字段标签
        - annotations (Dict[str, str]): 字段扩展元数据
      - struct (DataStruct): 字段结构定义
    - data (Optional[List[List[Any]] | str]): 只有在 previewData 为 True 时才有内容。具体内容根据 formatter 和 formatterArgs 的配置决定

数据转换相关方法
- 数据转换（`transformStructuredData`）：行级数据转换逻辑
  - 参数：
    - resourceId (str): uri 格式的数据资源id
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - outputColumns (List[OutputColumn]): 输出字段映射
      - key (str): 输出字段名称
      - metadata (Optional[ObjectMetadata]): 字段元数据描述, 如果为空则使用原字段元数据
      - evalOption (Optional[RowEval]): 行级求值配置实体
        - expression (Optional[JsonExpression]): 列计算表达式，如果为空则使用原字段值
        - windowing (Optional[Window]): 窗口定义
          - groupBy (List[str]): 分组字段
          - orderBy (List[SortOption]): 排序规则
            - column (str): 字段名称
            - desc (Optional[bool]): 是否倒序
      - struct (Optional[DataStruct]): 字段结构定义, 如果为空则使用原字段结构定义或者自动推断
    - condition (Optional[RowEval]): 过滤条件表达式
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - schema (List[SchemaField]): 结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
        - displayname (str): 字段展示名称
        - description (str): 字段描述
      - struct (DataStruct): 字段结构定义
  - 其他备注：
    - 如果resourceId是不在会话中则自动创建读取节点
    - 表达式支持开窗函数，如：`row_number() over (partition by id order by create_time)`
- 过滤数据（`filterStructuredData`）：根据条件过滤数据
  - 参数：
    - resourceId (str): uri 格式的数据资源id
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - condition (RowEval): 过滤条件配置
      - expression (JsonExpression): 过滤条件表达式
      - windowing (Optional[Window]): 窗口定义
        - groupBy (List[str]): 分组字段
        - orderBy (List[SortOption]): 排序规则
          - column (str): 字段名称
          - desc (Optional[bool]): 是否倒序
    - columnsOnly (Optional[List[str]]): 限制输出的数据资源的字段，空代表返回所有字段
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - schema (List[SchemaField]): 结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
        - displayname (str): 字段展示名称
        - description (str): 字段描述
        - labels (Dict[str, str]): 字段标签
        - annotations (Dict[str, str]): 字段扩展元数据
      - struct (DataStruct): 字段结构定义
  - 其他备注：
    - 如果resourceId是不在会话中则自动创建读取节点
- 数据关联（`joinStructuredData`）：将两个数据资源关联起来
  - 参数：
    - leftResourceId (str): uri 格式的数据资源id
    - rightResourceId (str): uri 格式的数据资源id
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - joinType (str): 关联类型，可选值为：
      - "inner": 内连接
      - "left": 左连接
      - "right": 右连接
      - "full": 全连接
    - joinCondition (RowEval): 关联条件, 左表字段为`left.{field name}`，右表字段为`right.{field name}`
      - expression (JsonExpression): 关联条件表达式
      - windowing (Optional[Window]): 窗口定义
        - groupBy (List[str]): 分组字段
        - orderBy (List[SortOption]): 排序规则
          - column (str): 字段名称
          - desc (Optional[bool]): 是否倒序
    - outputColumns (List[OutputColumn]): 输出字段映射
      - key (str): 输出字段名称
      - metadata (Optional[ObjectMetadata]): 字段元数据描述, 如果为空则使用原字段元数据
      - evalOption (Optional[RowEval]): 列计算表达式, 左表字段为`left.{field name}`，右表字段为`right.{field name}`
        - expression (JsonExpression): 列计算表达式
        - windowing (Optional[Window]): 窗口定义
          - groupBy (List[str]): 分组字段
        - orderBy (List[SortOption]): 排序规则
          - column (str): 字段名称
          - desc (Optional[bool]): 是否倒序
      - struct (Optional[DataStruct]): 字段结构定义, 如果为空则使用原字段结构定义或者自动推断
    - condition (Optional[RowEval]): 过滤条件表达式, 左表字段为`left.{field name}`，右表字段为`right.{field name}`
      - expression (JsonExpression): 过滤条件表达式
      - windowing (Optional[Window]): 窗口定义
        - groupBy (List[str]): 分组字段
        - orderBy (List[SortOption]): 排序规则
          - column (str): 字段名称
          - desc (Optional[bool]): 是否倒序
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - schema (List[SchemaField]): 结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
      - struct (DataStruct): 字段结构定义
  - 其他备注：
    - 如果leftResourceId和rightResourceId不在会话中则自动创建读取节点
    - 如果leftResourceId和rightResourceId不在同一个会话中则报错
- 数据聚合（`aggregateStructuredData`）：将数据资源中的数据进行聚合
  - 参数：
    - resourceId (str): uri 格式的数据资源id
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - aggregateOptions (AggregateOptions): 聚合选项
      - groupBy (List[str]): 分组字段
    - outputColumns (List[OutputColumn]): 输出字段映射
      - key (str): 输出字段名称
      - metadata (Optional[ObjectMetadata]): 字段元数据描述, 如果为空则使用原字段元数据
      - evalOption (Optional[AggregateEval]): 聚合计算表达式
        - expression (Optional[JsonExpression]): 聚合计算表达式，如果为空则使用原字段值
        - aggregateOptions (AggregateOptions): 聚合选项
          - orderBy (List[SortOption]): 组内排序规则
            - column (str): 字段名称
            - desc (Optional[bool]): 是否倒序
      - struct (Optional[DataStruct]): 字段结构定义, 如果为空则使用原字段结构定义或者自动推断 
    - condition (Optional[AggregateEval]): 过滤条件表达式
      - expression (Optional[JsonExpression]): 过滤条件表达式，如果为空则使用原字段值
      - aggregateOptions (AggregateOptions): 聚合选项
        - orderBy (List[SortOption]): 组内排序规则
          - column (str): 字段名称
          - desc (Optional[bool]): 是否倒序
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - schema (List[SchemaField]): 结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
      - struct (DataStruct): 字段结构定义
- 结构化数据转图结构（`structuredDataToGraph`）：将结构化数据转换为图结构
  - 参数：
    - edgeResourceId (str): uri 格式的数据资源id，代表边数据资源
    - nodeResourceId (str): uri 格式的数据资源id，代表节点数据资源
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - edgeIdField (str): 边数据资源中的边id字段
    - nodeIdField (str): 节点数据资源中的节点id字段
    - edgeColumns (Optional[List[str]]): 边字段, 如果为空则使用所有除了边id字段的其他字段
    - nodeColumns (Optional[List[str]]): 节点字段, 如果为空则使用所有除了节点id字段的其他字段
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - nodeSchema (List[SchemaField]): 节点结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
      - struct (DataStruct): 字段结构定义
    - edgeSchema (List[SchemaField]): 边结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
      - struct (DataStruct): 字段结构定义

数据分析相关方法
- 关联规则分析（`associationRuleAnalysis`）：关联规则分析逻辑
  - 参数：
    - resourceId (str): uri 格式的数据资源id
    - sessionId (Optional[str]): 指定当前操作所在会话，不指定则根据实际情况按需创建会话
    - associationRuleAnalysisOptions (AssociationRuleAnalysisOptions): 关联规则分析选项
      - minSupport (float): 最小支持度
      - minConfidence (float): 最小置信度
      - minLift (float): 最小提升度
      - maxLength (int): 最大长度
    - columnsOnly (Optional[List[ResultColumnEnum]]): 限制输出的数据资源的字段，空代表返回所有字段.
  - 返回结果：
    - sessionId (str): 对应的会话id
    - resourceId (str): uri 格式的数据资源id
    - schema (List[SchemaField]): 结构描述
      - name (str): 字段名称
      - metadata (ObjectMetadata): 字段元数据描述
      - struct (DataStruct): 字段结构定义

#### 工具调用示例
以购车关联规则分析为例：
```python
# -*- coding: utf-8 -*-
"""
示例：购物车关联规则分析流程（使用JSON AST表达式）
"""

from mcp_tools import (
    readStructuredDataResource,
    filterStructuredData,
    transformStructuredData,
    joinStructuredData,
    aggregateStructuredData,
    # structuredDataToGraph, # 根据需要取消注释
    associationRuleAnalysis
)

# 1. 初始化 sessionId 为 None，由首次调用自动创建
session_id = None

# 2. 读取购物车原始交易数据
# SQL: SELECT * FROM shopping_cart;
res1 = readStructuredDataResource(
    resourceId='data-resource://shopping_cart',
    sessionId=session_id,
    previewData=False
)
session_id = res1['sessionId']
cart_res_id = res1['resourceId']
print(f"🛒 购物车数据资源ID: {cart_res_id}")
print(f"📄 会话ID: {session_id}")
print("📊 购物车数据 schema:", [f['name'] for f in res1['schema']])


# 3. 预览前 5 条交易记录（可选）
# SQL: SELECT * FROM shopping_cart LIMIT 5;
res_preview = readStructuredDataResource(
    resourceId=cart_res_id,
    sessionId=session_id,
    previewData=True,
    previewOptions={
        'head': 5,
        'formatter': 'markdown' # 让输出更美观 😉
    }
)
print("\n📋 购物车数据预览 (前5条):")
print(res_preview['data'])


# 4. 过滤测试或异常交易：只保留金额大于 0 的记录
# SQL: SELECT * FROM shopping_cart WHERE total_amount > 0;
res2 = filterStructuredData(
    resourceId=cart_res_id,
    sessionId=session_id,
    condition={
        "expression": {
            "type": "FuncCall",
            "name": "gt", # greater than
            "args": [
                {"type": "Identifier", "name": "total_amount"},
                {"type": "Literal", "valueInJson": 0} # valueStruct 可选
            ]
        }
    }
)
filtered_res_id = res2['resourceId']
print(f"\n🧹 过滤后资源ID: {filtered_res_id}")


# 5. 转换数据：
#    a. 拆分商品列表字段 (假设原字段 "items" 是逗号分隔字符串)
#    b. 计算用户每次交易的序号 (使用开窗函数)
# SQL: SELECT
#        transaction_id,
#        user_id,
#        split(items, ',') as item_list,
#        total_amount,
#        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY transaction_time) as transaction_rank
#      FROM filtered_shopping_cart;
res3 = transformStructuredData(
    resourceId=filtered_res_id,
    sessionId=session_id,
    outputColumns=[
        {
            'key': 'transaction_id',
            'eval': {'expression': {'type': 'Identifier', 'name': 'transaction_id'}}
        },
        {
            'key': 'user_id',
            'eval': {'expression': {'type': 'Identifier', 'name': 'user_id'}}
        },
        {
            'key': 'item_list',
            'eval': {
                'expression': {
                    "type": "FuncCall",
                    "name": "split",
                    "args": [
                        {"type": "Identifier", "name": "items"},
                        {"type": "Literal", "valueInJson": ","}
                    ]
                }
            },
            # 假设推断出的类型是 List<String>
            'struct': {"type": "List", "nullable": True, "itemType": {"type": "String", "nullable": False}}
        },
        {
            'key': 'total_amount',
            'eval': {'expression': {'type': 'Identifier', 'name': 'total_amount'}}
        },
        {
            'key': 'transaction_rank', # 新增：用户交易排名
            'eval': {
                'expression': {
                    "type": "FuncCall",
                    "name": "row_number",
                    "args": [] # row_number 不需要参数
                },
                'windowing': { # 定义窗口
                    'groupBy': ['user_id'], # PARTITION BY user_id
                    'orderBy': [{'column': 'transaction_time', 'desc': False}] # ORDER BY transaction_time ASC
                    # size 和 offset 对 row_number() 无意义
                }
            },
            'struct': {"type": "Int", "bitWidth": 64, "nullable": False} # 指定输出类型
        }
    ]
)
transformed_res_id = res3['resourceId']
print(f"\n✨ 转换(含开窗)后资源ID: {transformed_res_id}")


# 6. 关联用户画像数据：按 user_id 内连接，使用更复杂的条件
# SQL: SELECT
#        t.user_id, t.item_list, t.total_amount, t.transaction_rank,
#        p.age, p.gender, p.city
#      FROM transformed_cart t
#      INNER JOIN user_profiles p
#      ON (t.user_id = p.user_id AND p.is_active = true) OR (t.guest_id = p.guest_id AND p.city = 'Unknown');
res4 = joinStructuredData(
    leftResourceId=transformed_res_id,
    rightResourceId='data-resource://user_profiles', # 假设存在用户画像资源
    sessionId=session_id,
    joinType='inner', # 内连接
    joinCondition={ # 复杂的关联条件
        "expression": {
            "type": "FuncCall",
            "name": "or",
            "args": [
                { # 条件1: (t.user_id = p.user_id AND p.is_active = true)
                    "type": "FuncCall",
                    "name": "and",
                    "args": [
                        {
                            "type": "FuncCall",
                            "name": "equal",
                            "args": [
                                {"type": "Identifier", "name": "left", "nameParts": ["user_id"]}, # t.user_id
                                {"type": "Identifier", "name": "right", "nameParts": ["user_id"]} # p.user_id
                            ]
                        },
                        {
                            "type": "FuncCall",
                            "name": "equal",
                            "args": [
                                {"type": "Identifier", "name": "right", "nameParts": ["is_active"]}, # p.is_active
                                {"type": "Literal", "valueInJson": True}
                            ]
                        }
                    ]
                },
                { # 条件2: (t.guest_id = p.guest_id AND p.city = 'Unknown') - 假设有guest_id字段
                    "type": "FuncCall",
                    "name": "and",
                     "args": [
                        {
                            "type": "FuncCall",
                            "name": "equal",
                            "args": [
                                {"type": "Identifier", "name": "left", "nameParts": ["guest_id"]}, # t.guest_id
                                {"type": "Identifier", "name": "right", "nameParts": ["guest_id"]} # p.guest_id
                            ]
                        },
                        {
                            "type": "FuncCall",
                            "name": "equal",
                            "args": [
                                {"type": "Identifier", "name": "right", "nameParts": ["city"]}, # p.city
                                {"type": "Literal", "valueInJson": "Unknown"}
                            ]
                        }
                    ]
                }
            ]
        }
    },
    outputColumns=[ # 选择输出列
        {'key': 'user_id',        'eval': {'expression': {'type': 'Identifier', 'name': 'left', 'nameParts': ['user_id']}}},
        {'key': 'item_list',      'eval': {'expression': {'type': 'Identifier', 'name': 'left', 'nameParts': ['item_list']}}},
        {'key': 'total_amount',   'eval': {'expression': {'type': 'Identifier', 'name': 'left', 'nameParts': ['total_amount']}}},
        {'key': 'transaction_rank','eval': {'expression': {'type': 'Identifier', 'name': 'left', 'nameParts': ['transaction_rank']}}},
        {'key': 'age',            'eval': {'expression': {'type': 'Identifier', 'name': 'right', 'nameParts': ['age']}}},
        {'key': 'gender',         'eval': {'expression': {'type': 'Identifier', 'name': 'right', 'nameParts': ['gender']}}},
        {'key': 'city',           'eval': {'expression': {'type': 'Identifier', 'name': 'right', 'nameParts': ['city']}}}
    ]
)
joined_res_id = res4['resourceId']
print(f"\n🤝 关联后资源ID: {joined_res_id}")


# 7. 聚合：按性别和年龄段计算总销售额和平均交易排名
# SQL: SELECT
#        gender,
#        CASE WHEN age < 18 THEN 'Under 18' WHEN age BETWEEN 18 AND 30 THEN '18-30' ELSE 'Over 30' END as age_group,
#        SUM(total_amount) as total_sales,
#        AVG(transaction_rank) as avg_rank
#      FROM joined_data
#      GROUP BY gender, age_group
#      HAVING COUNT(*) > 5; -- 过滤掉分组记录数小于等于5的分组
res5 = aggregateStructuredData(
    resourceId=joined_res_id,
    sessionId=session_id,
    aggregateOptions={ # GROUP BY 子句
        'groupBy': [
            'gender',
            'age_group' # 需要先在 outputColumns 中定义 age_group
        ]
    },
    outputColumns=[
        {
            'key': 'gender',
            'eval': {'expression': {'type': 'Identifier', 'name': 'gender'}} # 直接从 Group By 字段获取
        },
        {
            'key': 'age_group', # 定义年龄分组
            'eval': {
                'expression': {
                    "type": "FuncCall",
                    "name": "case",
                    "args": [
                        # WHEN age < 18 THEN 'Under 18'
                        {"type": "FuncCall", "name": "lt", "args": [{"type": "Identifier", "name": "age"}, {"type": "Literal", "valueInJson": 18}]},
                        {"type": "Literal", "valueInJson": "Under 18"},
                        # WHEN age >= 18 AND age <= 30 THEN '18-30'
                        {"type": "FuncCall", "name": "and", "args": [
                            {"type": "FuncCall", "name": "gte", "args": [{"type": "Identifier", "name": "age"}, {"type": "Literal", "valueInJson": 18}]},
                            {"type": "FuncCall", "name": "lte", "args": [{"type": "Identifier", "name": "age"}, {"type": "Literal", "valueInJson": 30}]}
                        ]},
                        {"type": "Literal", "valueInJson": "18-30"},
                        # ELSE 'Over 30'
                        {"type": "Literal", "valueInJson": "Over 30"}
                    ]
                }
            },
            'struct': {"type": "String", "nullable": False}
        },
        {
            'key': 'total_sales', # SUM(total_amount)
            'eval': {
                'expression': {
                    "type": "FuncCall",
                    "name": "sum",
                    "args": [{"type": "Identifier", "name": "total_amount"}]
                }
                # aggregateOptions (如orderBy) 对 SUM 无意义
            },
             'struct': {"type": "Float", "precision": "DOUBLE", "nullable": True} # 聚合结果可能为 NULL
        },
        {
            'key': 'avg_rank', # AVG(transaction_rank)
            'eval': {
                'expression': {
                    "type": "FuncCall",
                    "name": "avg",
                    "args": [{"type": "Identifier", "name": "transaction_rank"}]
                }
            },
            'struct': {"type": "Float", "precision": "DOUBLE", "nullable": True}
        }
    ],
    condition={ # HAVING 子句: COUNT(*) > 5
        'expression': {
            "type": "FuncCall",
            "name": "gt",
            "args": [
                {"type": "FuncCall", "name": "count", "args": []}, # count(*)
                {"type": "Literal", "valueInJson": 5}
            ]
        }
    }
)
aggregated_res_id = res5['resourceId']
print(f"\n📈 聚合后资源ID: {aggregated_res_id}")

# 8. 预览聚合结果
# SQL: SELECT * FROM aggregated_data LIMIT 10;
res_preview_agg = readStructuredDataResource(
    resourceId=aggregated_res_id,
    sessionId=session_id,
    previewData=True,
    previewOptions={
        'head': 10,
        'formatter': 'markdown'
    }
)
print("\n📊 聚合结果预览:")
print(res_preview_agg['data'])


# 9. 关联规则分析：使用原始转换后的数据（包含 item_list）进行分析
#    注意：关联规则分析通常作用于包含“购物篮”列表的数据上
# SQL: (关联规则没有直接SQL等价物，它是一个数据挖掘算法)
res6 = associationRuleAnalysis(
    resourceId=transformed_res_id, # 使用包含 item_list 的转换后数据
    sessionId=session_id,
    associationRuleAnalysisOptions={
        'minSupport': 0.01,   # 降低支持度阈值以发现更多规则
        'minConfidence': 0.3, # 降低置信度阈值
        'minLift': 1.1,
        'maxLength': 3       # 规则中项的最大数量
    },
    # columnsOnly=None # 默认需要 antecedents, consequents, support, confidence, lift 等列
)
ar_res_id = res6['resourceId']
print(f"\n🔍 关联规则分析资源ID: {ar_res_id}")


# 10. 预览关联规则结果
# SQL: SELECT * FROM association_rules LIMIT 10;
res_preview_ar = readStructuredDataResource(
    resourceId=ar_res_id,
    sessionId=session_id,
    previewData=True,
    previewOptions={
        'head': 10,
        'formatter': 'markdown'
    }
)
print("\n📝 关联规则结果预览:")
print(res_preview_ar['data'])

# 可选：如果需要将购物篮关系可视化，可以取消注释下面的步骤
# res_graph = structuredDataToGraph(...)
# print("已生成图结构资源：", res_graph['resourceId'])

print("\n🎉 任务完成！")
```
