# 数据处理引擎详细设计

## 简介

DataProcessEngine 是一套面向批处理的异构数据统一处理框架，提供了标准化的数据处理算子 API 和执行环境。框架的核心价值在于：

- **标准化算子接口**：提供统一的算子调用 API，使得可以使用不同实现后端运行同一套算子
- **异步执行能力**：支持长耗时任务的异步执行，提供任务状态查询、取消和结果获取
- **声明式算子定义**：通过继承基类和类型注解，实现声明式的算子定义
- **类型安全**：利用泛型系统，实现算子输入参数与返回结果的强类型约束
- **多后端支持**：设计支持多种执行后端（如 DuckDB、Spark、Ray 等）
- **表达式处理解耦**：本框架专注于任务调度和算子执行流程，表达式的具体解析、语义分析和执行将由独立的表达式DSL框架负责处理，本框架负责接收符合规范的表达式结构并传递给后端。

### 整体架构设计

```mermaid
flowchart LR
    User("用户代码") --> |"1\. 获取后端"| DataProcessEngine("DataProcessEngine")
    DataProcessEngine --> |"2\. 返回后端实例"| PB("DataProcessBackend")
    User --> |"3\. 创建Session (可选)"| PB
    PB --> |"4\. 返回Session"| PS("DataProcessSession")
    User --> |"5a\. 定义算子"| OP("Operator[T_Result, T_Resource]")
    User --> |"6a\. 执行算子 backend.run_op()"| PB
    User --> |"6b\. 执行算子 session.run_op()"| PS
    PB --> |"7a\. 创建并返回Task"| Task("Task[T_Result, T_Resource]") 
    PS --> |"7b\. 创建并返回Task"| Task
    User --> |"8\. 获取结果 task.result()"| Task
    Task --> |"9\. 返回TypedResult"| TypedResult("Operator结果")
    User --> |"10\. 获取资源"| DataProcessEngine
    DataProcessEngine --> |"11\. 返回资源"| DR("DataResource")
```

框架的整体业务流程如下：

1. **配置后端**：初始化时通过配置文件或代码方式配置可用的执行后端
2. **获取后端**：用户通过 `DataProcessEngine.backend("backend_name")` 获取指定后端实例
3. **创建会话**：用户可选择通过 `backend.create_session()` 创建会话
4. **构造算子**：构造数据处理算子的实例，如 `ReadStructuredDataResource(...)` （注意：算子类名不带 `Operator` 后缀）
5. **执行任务**：通过后端或会话执行算子，如 `backend.run_op(op)`，获取任务句柄
6. **获取结果**：通过任务句柄获取结果，如 `task.result()`
7. **操作数据资源**：获取或操作数据资源，如 `DataProcessEngine.data_resource(...)`

### 核心业务对象

```mermaid
classDiagram
    class DataProcessEngine {
        <<Facade>>
        +backend(backend_type: str) DataProcessBackend
        +list_backends() Dict[str, BackendInfo]
        +register_backend(backend: DataProcessBackend)
        +set_default_backend(backend_type: str)
        +get_default_backend() Optional[str]
        +create_session(session_config: SessionConfig) DataProcessSession
        +get_session(session_id: str) DataProcessSession
        +list_sessions() Dict[str, SessionInfo]
        +close_session(session_id: str) None
        +get_resource_accessor(resource_id: str | ResourceId, backend_name: Optional[str] = None) ResourceAccessor[IResource]
        +shutdown() None
        
    }

    class DataProcessBackend {
        <<Interface>>
        +backend_type: str # Backend type
        +backend_info BackendInfo
        +get_supported_operators() List[OperatorInfo]
        +run_op~T_Result, T_Resource~(operator: Operator[T_Result, T_Resource]) Task[T_Result, T_Resource]
        +create_session(session_config: SessionConfig) DataProcessSession
        +get_session(session_id: str) DataProcessSession
        +list_sessions() Dict[str, SessionInfo]
        +get_resource_accessor(resource_id: str | ResourceId) ResourceAccessor[IResource]
        +listen_session_lifecycle(listener: SessionLifecycleListener) Subscription
        +shutdown() None
    }

    class DataProcessSession {
        <<Interface>>
        +session_id: str
        +session_info: SessionInfo
        +run_op(operator: Operator[T_Result, T_Resource]) Task[T_Result, T_Resource]
        +list_tasks() Dict[str,Task[T_Result, T_Resource]]
        +close()
    }

    class Operator~T_Result, T_Resource~ {
        <<Abstract>>
        session_id: Optional[str]
        task_id: Optional[str]
        +result_type() Type[T_Result]
        +output_resource_info() OutputResourceInfo
        +validate_op() bool
        +build_output_resource_id(): SessionResourceId
    }

    class OperatorResult {
        <<BaseModel>>
        +session_id: str
        +task_id: str
        +resource_id: Optional[ResourceId]
    }
    class OutputResourceInfo {
        <<BaseModel>>
        +has_resource: bool
        +resource_type: Optional[ResourceType]
    }

    class Task~T_Result, T_Resource~ {
        <<Interface>>
        +task_id: str
        +session_id: str
        +status() TaskStatus
        +cancel() bool
        +result(timeout: Optional[float]) T_Result
        +exception(timeout: Optional[float]) Optional[Exception]
        +get_resource_accessor() ResourceAccessor[T_Resource]
    }

    class ResourceAccessor~T_Resource~ {
        <<Interface>>
        # Waits for associated task/resource and returns the handle
        +get_resource(timeout: Optional[float] = None) T_Resource
    }

    class IResource {
        <<Interface>>
        +resource_id: ResourceId
        +resource_type: str
    }
    
    class DataResource {
        <<Interface>>
        +data_resource_type: str
    }

    class StructuredDataResource {
        +schema: List[SchemaField]
        +as_arrow_ds() pyarrow.dataset.Dataset
    }

    DataProcessEngine o-- DataProcessBackend : provides
    DataProcessBackend o-- DataProcessSession : manages
    DataProcessBackend --> Task : creates >
    DataProcessSession --> Task : creates >
    DataProcessBackend --> ResourceAccessor : manages access to
    ResourceAccessor --> IResource : wait and access to
    Operator --> Task : executed via
    Operator o-- OutputResourceInfo : provides
    Task --> OperatorResult : returns
    IResource <|-- DataResource : extends
    DataResource <|-- StructuredDataResource : extends
```

核心业务对象详细描述：

#### DataProcessEngine 入口类

DataProcessEngine 是整个框架的入口类，采用单例模式设计，负责：
- 管理所有已注册的后端实例
- 提供快捷方法获取后端实例
- 提供统一的数据资源访问入口
- 负责后端的全局配置和生命周期管理

#### DataProcessBackend

DataProcessBackend 是后端接口抽象，代表一种执行引擎的实现，负责：
- 执行算子并返回任务句柄
- 管理会话的创建和生命周期
- 提供数据资源访问的能力
- 实现特定后端与框架标准接口的适配

#### DataProcessSession

DataProcessSession 表示在特定后端上的一个执行会话，负责：
- 维护会话状态和上下文
- 在当前会话上下文中执行算子
- 管理会话资源和生命周期
- 提供会话级别的配置和优化能力

#### Operator 数据处理算子

Operator 是数据处理算子的抽象基类，采用泛型设计，负责：
- 声明算子的输入参数
- 指定算子执行结果的类型
- 提供算子的元数据和文档
- 支持算子参数的验证和序列化
**重点说明：算子只负责通过描述、参数、返回值等描述算子，不负责算子的具体执行逻辑。算子的具体执行逻辑由后端实现。**

#### Task

Task 是异步任务的抽象接口，采用泛型设计，负责：
- 提供统一的任务状态查询接口
- 支持任务取消
- 提供等待任务完成并获取结果的能力
- 支持异常处理和超时控制
#### ResourceAccessor
ResourceAccessor 是数据资源访问器的抽象接口，采用泛型设计，负责：
- 提供统一的等待资源就绪再访问的能力

#### DataResource 数据资源

DataResource 是数据资源的抽象接口，负责：
- **提供对数据资源元数据的访问**
- **支持数据资源的读取和操作**
- 按需支持不同类型的数据资源格式化和转换
- 提供资源特定的操作方法

#### 核心业务对象交互方式

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Engine as DataProcessEngine
    participant Backend as DataProcessBackend
    participant Session as DataProcessSession
    participant Op as Operator
    participant Tk as Task
    participant DR as DataResource

    User->>Engine: backend("duckdb")
    Engine-->>User: DuckDBBackend
    User->>Backend: create_session()
    Backend-->>User: session
    
    User->>Op: 创建算子实例 (如ReadStructuredDataResource)
    User->>Session: run_op(operator)
    Session->>Backend: 内部调用后端实现
    Backend->>Tk: 创建任务
    Session-->>User: task
    
    User->>Tk: result()
    Tk-->>User: 类型化结果 (如ReadStructuredDataResourceResult)
    
    User->>Engine: data_resource("data-resource://id")
    Engine->>Backend: 内部查找合适的后端
    Backend->>DR: 创建资源访问对象
    Engine-->>User: StructuredDataResource
```

业务对象之间遵循以下交互原则：
- **单向依赖**：下层组件不依赖上层组件，确保模块解耦
- **接口隔离**：通过接口抽象隔离实现细节
- **泛型传递**：从算子定义到任务执行全流程保持类型安全
- **上下文传递**：会话信息从用户代码到结果全流程传递
- **资源标识统一**：使用统一的URI格式标识资源

## DataProcessEngine 详细设计

```mermaid
classDiagram
    class DataProcessEngine {
        <<Facade>>
        -Dict[str, DataProcessBackend] _backends
        +backend(backend_type: str) DataProcessBackend
        +list_backends() Dict[str, BackendInfo]
        +register_backend(backend: DataProcessBackend)
        +set_default_backend(backend_type: str)
        +get_default_backend() Optional[str]
        +create_session(session_config: SessionConfig) DataProcessSession
        +get_session(session_id: str) DataProcessSession
        +list_sessions() Dict[str, SessionInfo]
        +close_session(session_id: str) None
        +get_resource_accessor(resource_id: str | ResourceId, backend_name: Optional[str] = None) ResourceAccessor[IResource]
        +shutdown() None
        
    }
    class ResourceAccessor~IResource~ {
        <<Generic>>
        # Placeholder for generic type
    }
    DataProcessEngine o-- DataProcessBackend : manages & delegates to
    DataProcessEngine --> ResourceAccessor : "returns ResourceAccessor<DataResource> via get_resource_accessor"

```

DataProcessEngine  作为框架的入口类和Backend注册表。

### Python 接口定义

```python
from __future__ import annotations
from typing import Dict, List, Optional, Type, TypeVar, Any
from abc import ABC, abstractmethod

# 假设 DataProcessBackend, DataResource, ResourceAccessor 已定义

class DataProcessEngine:
    """数据处理引擎入口类，采用管理后端实例和资源访问"""
    

    def backend(self, backend_type: str) -> DataProcessBackend:
        """
        获取指定名称的后端实例
        
        Args:
            backend_type: 后端名称
            
        Returns:
            后端实例
            
        Raises:
            BackendNotFoundError: 如果指定名称的后端未注册
        """
        pass

    
    def list_backends(self) -> Dict[str, BackendInfo]:
        """
        获取所有已注册的后端名称列表
        
        Returns:
            后端名称列表
        """
        pass

    def register_backend(self, backend: DataProcessBackend) -> None:
        """
        注册后端实例
        
        Args:
            backend: 后端实例
            
        Raises:
            ValueError: 如果同名后端已存在
        """
        pass
    
    def set_default_backend(self, name: str) -> None:
        """
        设置默认后端
        
        Args:
            name: 后端名称
            
        Raises:
            BackendNotFoundError: 如果指定名称的后端未注册
        """
        pass

    def get_default_backend(self) -> Optional[str]:
        """
        获取默认后端
        
        Returns:
            Optional[str]: 默认后端名称
        """
        pass

    async def create_session(self, session_config: SessionConfig) -> DataProcessSession:
        """
        异步创建一个新会话
        
        Args:
            session_config: 会话配置
            
        Returns:
            会话实例
            
        Raises:
            SessionCreationError: 如果会话创建失败
        """
        pass
    
    async def get_session(self, session_id: str) -> DataProcessSession:
	    """
        异步获取指定会话ID的会话实例
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话实例
        
        Raises:
            SessionNotFoundError: 如果指定ID的会话不存在
        """
	    pass
	    
    async def list_sessions(self) -> Dict[str, SessionInfo]:
        """
        异步获取所有会话列表
        
        Returns:
            会话列表
        """
        pass

    async def close_session(self, session_id: str) -> None:
        """
        异步关闭指定会话
        
        Args:
            session_id: 会话ID
            
        Raises:
            SessionNotFoundError: 如果指定ID的会话不存在
        """
        pass

    async def get_resource_accessor(self, resource_id: str | ResourceId, backend_name: Optional[str] = None) -> ResourceAccessor[IResource]:
        """
        异步获取用于访问资源的 ResourceAccessor。
        通过资源URI定位资源，并返回一个可以异步获取资源句柄 (IResource) 的访问器。
        内部会根据 resource_id 和 backend_name (如果提供) 委托给合适的 Backend。
        
        Phase 1: 仅支持 session:// URIs。
        
        Args:
            resource_id: 资源ID (格式为URI, 如 'session://sid/data-resource/tid')
            backend_name: 可选，指定处理该资源的后端名称。
                          如果为 None，对于 session:// URI，引擎会尝试自动查找管理该会话的后端。
            
        Returns:
            一个 ResourceAccessor[IResource] 实例。
            
        Raises:
            ValueError: 如果资源ID格式不正确，或无法确定处理后端。
            ResourceNotFoundError: 如果无法定位到资源信息。
            BackendError: 如果在查找或创建访问器时发生后端错误。
        """
        # 接口定义，实现移至具体实现示例部分
        pass

    
    
    async def shutdown(self) -> None:
        """异步关闭所有后端并释放资源"""
        pass
    

```

### 示例使用方式

```python
import asyncio
# 配置和初始化引擎
from data_process_engine import DataProcessEngine, DuckDBBackend, DataResource, ResourceAccessor

engine = DataProcessEngine()
# 注册后端 (假设 DuckDBBackend 已实现)
duckdb_backend = DuckDBBackend()
engine.register_backend(duckdb_backend)
engine.set_default_backend("duckdb")

# 获取后端
backend = engine.backend("duckdb")

# 假设有一个 session resource_id: session://sid/data-resource/tid
session_resource_id = "session://some_session_id/data-resource/some_task_id"

async def main():
    # 获取数据资源的访问器 (通过 Engine)
    try:
        resource_accessor: ResourceAccessor[IResource] = await engine.get_resource_accessor(session_resource_id)
        print(f"通过 Engine 获取 Accessor 成功 for {session_resource_id}")
        
        # 使用访问器获取实际的 DataResource (会阻塞等待)
        data_resource: IResource = await resource_accessor.get_resource(timeout=60)
        print(f"通过 Engine Accessor 获取资源成功: {data_resource.resource_id}")
        # data_resource.head() ...
        
    except (ValueError, ResourceNotFoundError, TimeoutError) as e:
        print(f"通过 Engine 获取资源 {session_resource_id} 失败: {e}")

    # 关闭引擎 (会关闭所有后端)
    # await engine.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
```

## DataProcessBackend 详细设计

```mermaid
classDiagram
    direction LR
    class DataProcessBackend {
        <<Interface>>
        +backend_type: str # Backend type
        +backend_info BackendInfo
        +get_supported_operators() List[OperatorInfo]
        +run_op~T_Result, T_Resource~(operator: Operator[T_Result, T_Resource]) Task[T_Result, T_Resource]
        +create_session(session_config: SessionConfig) DataProcessSession
        +get_session(session_id: str) DataProcessSession
        +list_sessions() Dict[str, SessionInfo]
        +get_resource_accessor(resource_id: str | ResourceId) ResourceAccessor[IResource]
        +listen_session_lifecycle(listener: SessionLifecycleListener) Subscription
        +shutdown() None
    }
    
    class Subscription {
        <<Interface>>
        +cancel() None
    }
    
    class SessionEvent {
        <<BaseModel>>
        +event_type: str
        +timestamp: datetime
        +backend_type: str
        +session_id: str
    }
    
    class SessionCreatedEvent {
        +event_type: "SessionCreatedEvent"
    }
    
    class SessionStateChangedEvent {
        +event_type: "SessionStateChangedEvent"
        +old_state: SessionState
        +new_state: SessionState
    }
    
    class SessionClosedEvent {
        +event_type: "SessionClosedEvent"
    }
    
    class TaskStatusChangedEvent {
        +event_type: "TaskStatusChangedEvent"
        +task_id: str
        +old_status: TaskStatus
        +new_status: TaskStatus
        +operator_type: Optional[str]
        +node_id: Optional[str]
    }
    
    class TaskCompletedEvent {
        +event_type: "TaskCompletedEvent"
        +task_id: str
        +result_resource_id: Optional[str]
        +execution_time_ms: Optional[int]
        +operator_type: Optional[str]
        +node_id: Optional[str]
    }
    
    class TaskErrorEvent {
        +event_type: "TaskErrorEvent"
        +task_id: str
        +error_message: str
        +error_code: Optional[str]
        +operator_type: Optional[str]
        +node_id: Optional[str]
    }
    
    class SessionLifecycleListener {
        <<Protocol>>
        +on_session_created(event: SessionCreatedEvent) None
        +on_session_state_changed(event: SessionStateChangedEvent) None
        +on_session_closed(event: SessionClosedEvent) None
        +on_task_status_changed(event: TaskStatusChangedEvent) None
        +on_task_completed(event: TaskCompletedEvent) None
        +on_task_error(event: TaskErrorEvent) None
    }
    
    class DuckDBBackend
    
    class SparkBackend 

    class Task
    class DataProcessSession
    class ResourceAccessor
    class BackendInfo {
        +backend_type: str
        +metadata: ObjectMetadata
        +sessionConfigs: List[ConfigField]
    }
    class OperatorInfo {
        +type: str
        +metadata: ObjectMetadata
        +opConfigMetadata: List[ConfigField]
        +inputFields: List[str]
    }
    class ConfigField {
        +key: str
        +schema: Dict[str, Any] # 字段 JSON-schema 定义
    }
    class SessionConfig {
        +session_id: Optional[str]
        +metadata: Optional[ObjectMetadata]
        +sessionConfigs: Dict[str, Any]
    }
    
    DataProcessBackend <|-- DuckDBBackend : implements
    DataProcessBackend <|-- SparkBackend : implements
    DataProcessBackend --> Task : creates via run_op
    DataProcessBackend --> DataProcessSession : manages
    DataProcessBackend ..> SessionLifecycleListener : notifies
    DataProcessBackend --> Subscription : returns via listen_session_lifecycle
    %% Now returns an accessor, not the resource directly
    DataProcessBackend --> ResourceAccessor : creates ResourceAccessor[IResource] via get_resource_accessor
    
    SessionEvent <|-- SessionCreatedEvent : extends
    SessionEvent <|-- SessionStateChangedEvent : extends
    SessionEvent <|-- SessionClosedEvent : extends
    SessionEvent <|-- TaskStatusChangedEvent : extends
    SessionEvent <|-- TaskCompletedEvent : extends
    SessionEvent <|-- TaskErrorEvent : extends
    
    SessionLifecycleListener ..> SessionEvent : receives
```

DataProcessBackend 是后端接口定义，负责具体的算子执行、会话管理和资源访问。

### Python 接口定义

```python
from __future__ import annotations
from typing import Dict, List, Optional, Type, TypeVar, Any, Generic, Protocol, Union
from abc import ABC, abstractmethod
from datetime import datetime
from pydantic import BaseModel, Field
import uuid

# 假设 Operator, Task, DataProcessSession, IResource, ResourceAccessor, OperatorResult 等已定义
# 泛型定义，用于表示算子结果类型
T_Result = TypeVar('T', bound=OperatorResult) 
# 泛型定义，用于表示算子的输出资源类型
T_Resource = TypeVar('T', bound=Union[IResource, None]) 

class Subscription(Protocol):
    """
    订阅接口，提供取消订阅的方法
    """
    def cancel(self) -> None:
        """
        取消订阅
        """
        ...

class SessionEvent(BaseModel):
    """
    会话事件基类，包含所有事件共有的属性
    """
    # 事件类型，子类中自动设置为类名
    event_type: str = Field(..., description="事件类型")
    # 事件发生的时间戳
    timestamp: datetime = Field(default_factory=datetime.now, description="事件发生时间")
    # 后端类型
    backend_type: str = Field(..., description="后端类型")
    # 会话ID
    session_id: str = Field(..., description="会话ID")
    
    class Config:
        frozen = True  # 使事件对象不可变

class SessionCreatedEvent(SessionEvent):
    """
    会话创建事件
    """
    event_type: str = "SessionCreatedEvent"

class SessionStateChangedEvent(SessionEvent):
    """
    会话状态变更事件
    """
    event_type: str = "SessionStateChangedEvent"
    # 变更前的会话状态
    old_state: SessionState = Field(..., description="变更前的会话状态")
    # 变更后的会话状态
    new_state: SessionState = Field(..., description="变更后的会话状态")

class SessionClosedEvent(SessionEvent):
    """
    会话关闭事件
    """
    event_type: str = "SessionClosedEvent"

class TaskStatusChangedEvent(SessionEvent):
    """
    任务状态变更事件
    """
    event_type: str = "TaskStatusChangedEvent"
    # 任务ID
    task_id: str = Field(..., description="任务ID")
    # 变更前的任务状态
    old_status: TaskStatus = Field(..., description="变更前的任务状态")
    # 变更后的任务状态
    new_status: TaskStatus = Field(..., description="变更后的任务状态")
    # 可选，算子类型
    operator_type: Optional[str] = Field(default=None, description="算子类型")
    # 可选，DAG节点ID (仅DAG会话)
    node_id: Optional[str] = Field(default=None, description="DAG节点ID")

class TaskCompletedEvent(SessionEvent):
    """
    任务完成事件
    """
    event_type: str = "TaskCompletedEvent"
    # 任务ID
    task_id: str = Field(..., description="任务ID")
    # 任务产生的结果资源ID (如果有)
    result_resource_id: Optional[str] = Field(default=None, description="任务产生的结果资源ID")
    # 可选，任务执行时间 (毫秒)
    execution_time_ms: Optional[int] = Field(default=None, description="任务执行时间(毫秒)")
    # 可选，算子类型
    operator_type: Optional[str] = Field(default=None, description="算子类型")
    # 可选，DAG节点ID (仅DAG会话)
    node_id: Optional[str] = Field(default=None, description="DAG节点ID")

class TaskErrorEvent(SessionEvent):
    """
    任务错误事件
    """
    event_type: str = "TaskErrorEvent"
    # 任务ID
    task_id: str = Field(..., description="任务ID")
    # 错误消息
    error_message: str = Field(..., description="错误消息")
    # 可选，错误代码
    error_code: Optional[str] = Field(default=None, description="错误代码")
    # 可选，算子类型
    operator_type: Optional[str] = Field(default=None, description="算子类型")
    # 可选，DAG节点ID (仅DAG会话)
    node_id: Optional[str] = Field(default=None, description="DAG节点ID")

class SessionLifecycleListener(Protocol):
    """
    会话生命周期监听器协议，定义了会话生命周期事件和任务生命周期事件的回调方法
    实现该协议的类可以监听会话的创建、状态变更、关闭事件，以及会话内任务的状态变化
    """
    
    def on_session_created(self, event: SessionCreatedEvent) -> None:
        """
        当会话被创建时调用
        
        Args:
            event: 会话创建事件
        """
        ...
        
    def on_session_state_changed(self, event: SessionStateChangedEvent) -> None:
        """
        当会话状态发生变更时调用
        
        Args:
            event: 会话状态变更事件
        """
        ...
        
    def on_session_closed(self, event: SessionClosedEvent) -> None:
        """
        当会话被关闭时调用
        
        Args:
            event: 会话关闭事件
        """
        ...

    def on_task_status_changed(self, event: TaskStatusChangedEvent) -> None:
        """
        当任务状态发生变更时调用
        
        Args:
            event: 任务状态变更事件
        """
        ...
        
    def on_task_completed(self, event: TaskCompletedEvent) -> None:
        """
        当任务成功完成时调用
        
        Args:
            event: 任务完成事件
        """
        ...
        
    def on_task_error(self, event: TaskErrorEvent) -> None:
        """
        当任务执行失败时调用
        
        Args:
            event: 任务错误事件
        """
        ...

class DataProcessBackend(ABC):
    """
    数据处理后端接口，定义了后端必须实现的功能
    """
    
    @property
    @abstractmethod
    def backend_type(self) -> str:
        """后端类型"""
        pass
    
    @property
    @abstractmethod
    def backend_info(self) -> BackendInfo:
        """获取后端信息"""
        pass

    
    @abstractmethod
    def get_supported_operators(self) -> List[OperatorInfo]:
        """获取支持的算子信息"""
        pass

    @abstractmethod
    async def run_op(self, operator: Operator[T_Result, T_Resource]) -> Task[T_Result, T_Resource]:
        """
        执行数据处理算子
        
        Args:
            operator: 要执行的算子实例
            
        Returns:
            任务句柄 (Task)，可用于获取 OperatorResult 和 ResourceAccessor
            
        Raises:
            ValueError: 如果指定的会话ID无效
            BackendError: 如果执行过程中出现后端错误
        """
        pass
    
    @abstractmethod
    async def create_session(self, session_config: SessionConfig) -> DataProcessSession:
        """
        创建数据处理会话
        
        Args:
            session_config: 会话配置
            
        Returns:
            创建的会话实例
            
        Raises:
            ValueError: 如果指定ID的会话已存在
            BackendError: 如果创建会话过程中出现后端错误
        """
        pass
    
    @abstractmethod
    async def get_session(self, session_id: str) -> DataProcessSession:
        """
        获取指定ID的会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话实例
            
        Raises:
            ValueError: 如果指定ID的会话不存在
        """
        pass
    
    @abstractmethod
    async def list_sessions(self) -> Dict[str, SessionInfo]:
        """
        列出所有活动会话
        """
        pass

    @abstractmethod
    async def get_resource_accessor(self, resource_id: str | ResourceId) -> ResourceAccessor[IResource]:
        """
        获取用于访问数据资源的 ResourceAccessor。
        这是一个统一的入口，通过资源URI定位资源并返回一个可以异步获取资源句柄的访问器。
        
        Phase 1: 仅支持 session:// URIs。
        未来: 支持 data-resource:// 全局资源 URIs。
        
        Args:
            resource_id: 资源ID (格式为URI, 如 'session://sid/data-resource/tid')
            
        Returns:
            一个 ResourceAccessor[IResource] 实例，调用其 get_resource() 方法可获取 IResource。
            
        Raises:
            ValueError: 如果资源ID格式不正确
            ResourceNotFoundError: 如果无法根据 resource_id 定位到资源信息 (例如，找不到对应的任务或全局资源元数据)。注意：此时不等待任务完成。
            BackendError: 如果在尝试定位资源或创建访问器时出现后端错误。
        """
        pass
    
    @abstractmethod
    def listen_session_lifecycle(self, listener: SessionLifecycleListener) -> Subscription:
        """
        注册会话生命周期监听器，并返回可用于取消订阅的对象
        
        注册成功后，listener 将收到所有会话的生命周期事件通知
        
        Args:
            listener: 实现了 SessionLifecycleListener 协议的监听器对象
            
        Returns:
            订阅对象，可通过调用其 cancel() 方法取消订阅
        """
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """
        关闭后端并释放所有关联资源（包括会话等）
        
        Raises:
            BackendError: 如果关闭过程中出现错误
        """
        pass


class BackendError(Exception):
    """后端操作过程中的通用错误"""
    pass

class ResourceNotFoundError(Exception):
    """当无法找到或访问请求的资源时抛出"""
    pass
```

### 示例使用方式

```python
import asyncio
# 假设 DataProcessEngine, FilterStructuredData, TransformStructuredData, IResource 等已导入

# 获取后端并创建会话
backend = engine.backend("duckdb")

# 定义会话生命周期监听器
class MySessionListener:
    def on_session_created(self, event: SessionCreatedEvent) -> None:
        print(f"会话已创建: {event.session_id} 在后端类型 {event.backend_type}, 时间: {event.timestamp}")
    
    def on_session_state_changed(self, event: SessionStateChangedEvent) -> None:
        print(f"会话状态变更: {event.session_id} 从 {event.old_state} 变为 {event.new_state}, 时间: {event.timestamp}")
    
    def on_session_closed(self, event: SessionClosedEvent) -> None:
        print(f"会话已关闭: {event.session_id}, 时间: {event.timestamp}")
        
    def on_task_status_changed(self, event: TaskStatusChangedEvent) -> None:
        print(f"任务状态变更: {event.task_id} 在会话 {event.session_id} 从 {event.old_status} 变为 {event.new_status}")
        
    def on_task_completed(self, event: TaskCompletedEvent) -> None:
        print(f"任务完成: {event.task_id} 在会话 {event.session_id}, 执行时间: {event.execution_time_ms}ms")
        
    def on_task_error(self, event: TaskErrorEvent) -> None:
        print(f"任务错误: {event.task_id} 在会话 {event.session_id}, 错误信息: {event.error_message}")

async def main():
    # 注册监听器并获取订阅对象
    listener = MySessionListener()
    subscription = backend.listen_session_lifecycle(listener)

    # 创建会话 (会触发 on_session_created 回调)
    session = await backend.create_session()

    # --- 通过后端执行算子 (需要 session_id) ---
    filter_op = FilterStructuredData(
        session_id=session.session_id,
        resourceId="session://{sid}/data-resource/{tid}", # 需要一个已存在的资源ID
        condition=RowEval(expression=...) # 假设RowEval定义存在
    )
    filter_task = await backend.run_op(filter_op)

    # --- 或通过会话执行算子 (隐式使用当前会话) ---
    # 等待上一个任务的结果以获取 resource_id
    filter_op_result = await filter_task.result()
    transform_op = TransformStructuredData(
        resourceId=filter_op_result.resource_id, # 使用上一步任务结果中的 resource_id
        outputColumns=[...] # 假设OutputColumn定义存在
    )
    transform_task = await session.run_op(transform_op)

    # 关闭会话 (会触发 on_session_closed 回调)
    await session.close()

    # 取消订阅 - 不再接收事件
    subscription.cancel()

    # 关闭后端 (如果不再需要)
    # await backend.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
```

## DataProcessSession 详细设计

```mermaid
classDiagram
    class DataProcessSession {
        <<Interface>>
        +session_id: str
        metadata: ObjectMetadata
        +run_op~T_Result, T_Resource~(operator: Operator[T_Result, T_Resource]) Task[T_Result, T_Resource]
        +list_tasks() Dict[str, Task[T_Result, T_Resource]]
        +get_task_output_resource(task_id: str) async ResourceAccessor[IResource]
        +close() async None
    }
    
    class DuckDBSession {
    }
    class SparkSession {
    }
    
    class SessionState {
        <<Enumeration>>
        CREATED
        RUNNING
        CLOSING
        CLOSED
    }
    
    DataProcessSession <|-- DuckDBSession : implements
    DataProcessSession <|-- SparkSession : implements
    DataProcessSession --> SessionState : has
```

DataProcessSession 表示在特定后端上的一个执行上下文，负责在该上下文中执行算子和管理会话资源。
**注意:** 会话内部资源（由任务产生）的访问现在通过任务返回的 `Task` 对象的 `get_resource_accessor()` 方法进行，而不是通过会话直接获取。

### Python 接口定义

```python
from __future__ import annotations
from typing import Dict, List, Optional, Type, TypeVar, Any, Generic, Union
from abc import ABC, abstractmethod
from enum import Enum, auto

# 泛型定义，用于表示算子结果类型
T_Result = TypeVar('T', bound=OperatorResult) 
# 泛型定义，用于表示算子的输出资源类型
T_Resource = TypeVar('T', bound=Union[IResource, None]) 

class SessionInfo(BaseModel):
    """
    会话信息
    """
    backend_type: str = Field(..., description="后端类型")
    session_id: str = Field(..., description="会话ID")
    metadata: ObjectMetadata = Field(..., description="会话元数据")
    state: SessionState = Field(..., description="会话状态")
    configs: Dict[str, Any] = Field(..., description="会话配置")
    created_at: datetime = Field(..., description="会话创建时间")

# 会话状态枚举
class SessionState(Enum):
    """会话状态枚举"""
    CREATED = auto()    # 会话已创建但未使用
    RUNNING = auto()    # 会话正在运行
    CLOSING = auto()    # 会话正在关闭
    CLOSED = auto()     # 会话已关闭

class DataProcessSession(ABC):
    """
    数据处理会话接口，表示在特定后端上的一个执行上下文
    """
    
    @property
    @abstractmethod
    def session_id(self) -> str:
        """会话ID"""
        pass
    
    @property
    @abstractmethod
    def state(self) -> SessionState:
        """当前会话状态"""
        pass
    
    @property
    @abstractmethod
    def session_info(self) -> SessionInfo:
        """会话信息"""
        pass

    @abstractmethod
    def run_op(self, operator: Operator[T_Result, T_Resource]) -> Task[T_Result, T_Resource]:
        """
        在当前会话上下文中执行算子
        
        Args:
            operator: 要执行的算子实例
            
        Returns:
            任务句柄 (Task)，可通过其 get_resource_accessor() 获取输出
            
        Raises:
            SessionError: 如果会话状态不允许执行算子或执行过程中出错
        """
        pass

    @abstractmethod
    def list_tasks(self) -> Dict[str, Task[T_Result, T_Resource]]:
        """
        获取会话中所有任务
        
        Returns:
            一个包含所有任务的列表

        Raises:
            SessionError: 如果会话状态不允许获取任务列表
        """
        pass

    @abstractmethod
    def get_task_output_resource(self, task_id: str) -> ResourceAccessor[IResource]:
        """
        获取指定任务对应的输出资源

        Returns:
            一个 ResourceAccessor[IResource] 实例，调用其 get_resource() 方法可获取 IResource。        

        Raises:
            SessionError: 如果关闭过程中出错
            ResourceNotFoundError: 如果无法根据 resource_id 定位到资源信息 (例如，找不到对应的任务或全局资源元数据)。注意：此时不等待任务完成。
        """
        pass    
		    
    @abstractmethod
    def close(self) -> None:
        """
        关闭会话并释放资源
        
        Raises:
            SessionError: 如果关闭过程中出错
        """
        pass


class SessionError(Exception):
    """会话操作过程中的错误"""
    pass
```


### 使用示例 (调整后)

```python
import asyncio
# 假设 DataProcessEngine, ReadStructuredDataResource, FilterStructuredData, RowEval, DataResource, StructuredDataResource, ResourceAccessor 已导入

async def main():
    backend = DataProcessEngine.backend("duckdb")
    session = await backend.create_session()

    # 定义读取CSV的算子
    read_op = ReadStructuredDataResource(
        resourceId="data-resource://abc", # 使用全局资源ID
        previewData=False
    )

    # 执行读取算子，获取 Task
    read_task = await session.run_op(read_op)
    print(f"提交读取任务: {read_task.task_id}")

    # 等待任务完成并获取 OperatorResult (主要用于获取task_id和可能的resource_id)
    read_op_result = None
    try:
        read_op_result = await read_task.result(timeout=10)
        print(f"读取任务完成，结果: {read_op_result}")
        if not read_op_result.resource_id:
            raise ValueError("读取任务未生成资源ID")
    except Exception as e:
        print(f"读取任务失败: {e}")
        await session.close()
        return # 提前退出

    # 定义过滤算子，使用上一个任务产生的资源ID
    # 格式: session://{session_id}/data-resource/{task_id}
    filter_op = FilterStructuredData(
        resourceId=read_op_result.resource_id, # 直接使用上一步的结果ID
        condition=RowEval( # 假设RowEval定义存在
            expression={"type": "FuncCall", "name": "gt", "args": [
                {"type": "Identifier", "name": "total_amount"},
                {"type": "Literal", "val": 1000}
            ]}
        )
    )

    # 执行过滤算子
    filter_task = await session.run_op(filter_op)
    print(f"提交过滤任务: {filter_task.task_id}")

    # 获取过滤任务的资源访问器
    filter_accessor = filter_task.get_resource_accessor()

    # 异步获取过滤任务的实际输出 (可能是 DuckDB 表名或 DataResource)
    try:
        filter_output_resource = await filter_accessor.get_resource(timeout=60) # 方法名是 get_resource
        print(f"过滤任务输出获取成功: {filter_output_resource}")
        
        # 如果需要进一步操作，需要判断 filter_output_resource 类型
        if isinstance(filter_output_resource, DataResource): # 检查是否为DataResource的实例
            print(f"获取到资源: {filter_output_resource.resource_id} 类型: {type(filter_output_resource)}")
            if isinstance(filter_output_resource, StructuredDataResource): # 检查是否为StructuredDataResource的实例
                # preview = await filter_output_resource.head(5) # 假设有 async head 方法
                # print("\n过滤后数据预览:\n", preview)
                pass # 假设的 head 方法未定义，暂时跳过

    except Exception as e:
        print(f"获取过滤任务输出失败: {e}")

    # 关闭会话
    await session.close()
    print("会话已关闭")

if __name__ == "__main__":
    asyncio.run(main())
```

## Operator 详细设计

```mermaid
classDiagram
    class BaseOperator {
        <<BaseModel>>
        +type: str
        +validate_op()
        +model_dump_json() str
        +model_dump() dict
    }
    
    class StructuredDataOperator {
        <<BaseModel>>
        +type: str
    }
    
    class GraphOperator {
        <<BaseModel>>
        +type: str
    }
    
    class ReadStructuredDataResource {
        +type: "read_structured_data"
        +source: str
        +format: str
        +options: Dict[str, Any]
    }
    
    class FilterStructuredData {
        +type: "filter_structured_data"
        +resourceId: str
        +condition: RowEval
    }
    
    class TransformStructuredData {
        +type: "transform_structured_data"
        +resourceId: str
        +outputColumns: List[OutputColumn]
    }
    
    class ReadGraphResource {
        +type: "read_graph_resource"
        +source: str
        +format: str
        +options: Dict[str, Any]
    }
    
    class OperatorResult {
        <<BaseModel>>
        +session_id: str
        +task_id: str
        +resource_id: Optional[str | ResourceId]
    }
	
	class StructuredDataResourceResult {
        +resource_id: Optional[str | ResourceId]
        +schema: List[SchemaField]
        +row_count: Optional[int]
    }
	
	class ReadStructuredDataResourceResult {
        +data: List[Dict[str, Any]]
    }
	
	class GraphDataResourceResult {
	}

    BaseOperator <|-- StructuredDataOperator : extends
    BaseOperator <|-- GraphOperator : extends
    StructuredDataOperator <|-- ReadStructuredDataResource : extends
    StructuredDataOperator <|-- FilterStructuredData : extends
    StructuredDataOperator <|-- TransformStructuredData : extends
    GraphOperator <|-- ReadGraphResource : extends

	OperatorResult <|-- StructuredDataResourceResult : extends
	StructuredDataResourceResult <|-- ReadStructuredDataResourceResult : extends
	OperatorResult <|-- GraphDataResourceResult : extends
```

Operator 是算子的抽象基类，使用泛型设计，用于定义对数据的处理操作。

### ResourceId 类型体系

框架采用强类型的资源标识符设计，通过 `ResourceId` 类型体系提供统一而类型安全的资源引用机制。ResourceId 设计旨在替代直接使用字符串标识资源，在编译时提供类型检查并支持与字符串的透明转换。

#### 类型层次结构

```mermaid
classDiagram
    class ResourceId {
        <<Abstract>>
        +from_string(str) ResourceId~static~
        +parse_string(str) ResourceId~abstract~
        +to_string() str~abstract~
        +pattern Pattern~abstract~
    }
    
    class GlobalResourceId {
        +resource_type: ResourceType
        +resource_id: str
        +pattern: "^(data-resource|ml-model)://([a-zA-Z0-9_-]+)$"
        +parse_string(str) GlobalResourceId
        +to_string() str
    }
    
    class SessionResourceId {
        +session_id: str
        +resource_type: ResourceType
        +sub_id: str
        +pattern: "^session://([a-zA-Z0-9_-]+)/(data-resource|ml-model)/([a-zA-Z0-9_-]+)$"
        +parse_string(str) SessionResourceId
        +to_string() str
    }
    
    class ResourceType {
        <<Enumeration>>
        DATA_RESOURCE
        ML_MODEL
    }
    
    ResourceId <|-- GlobalResourceId
    ResourceId <|-- SessionResourceId
    ResourceId --> ResourceType : uses
```

#### 两种资源ID类型

**1. GlobalResourceId (全局资源ID)**
- **格式**: `{resource_type}://{resource_id}`
- **用途**: 标识系统中的全局共享资源，不依赖于特定会话
- **示例**:
  - `data-resource://customer_data_2023` - 全局数据资源
  - `ml-model://fraud_detection_v2` - 全局机器学习模型
- **使用场景**: 预定义数据集、共享模型、系统级配置等

**2. SessionResourceId (会话资源ID)**
- **格式**: `session://{session_id}/{resource_type}/{sub_id}`
- **用途**: 标识在特定会话中生成的临时资源，通常是算子执行的中间结果
- **示例**:
  - `session://sess_abc123/data-resource/task_xyz789` - 会话中的数据处理结果
  - `session://sess_abc123/ml-model/task_model_456` - 会话中训练的模型
- **使用场景**: 算子输出结果、中间数据表、临时文件等

#### 设计特点

**类型安全性**：
- 在Pydantic模型中可直接使用ResourceId类型注解
- 自动验证资源ID格式的正确性
- 支持从字符串自动转换，保持API的简洁性

**透明转换**：
```python
# 在算子定义中使用强类型
class FilterOperator(StructuredDataOperator):
    input_resource: ResourceId = Field(..., description="输入资源")
    
# 可以使用字符串初始化，自动转换为相应的ResourceId子类
filter_op = FilterOperator(input_resource="session://sess123/data-resource/task456")

# 序列化时自动转换回字符串
json_data = filter_op.model_dump_json()
# 结果: {"input_resource": "session://sess123/data-resource/task456", ...}
```

**格式验证**：
- GlobalResourceId 自动验证资源类型是否为支持的枚举值
- SessionResourceId 验证会话ID、资源类型和子ID的格式
- 错误时提供详细的格式要求说明

**相等性比较**：
```python
resource_id = GlobalResourceId(resource_type=ResourceType.DATA_RESOURCE, resource_id="abc123")

# 支持与字符串的直接比较
assert resource_id == "data-resource://abc123"

# 支持与同类型ResourceId的比较
assert resource_id == GlobalResourceId(resource_type=ResourceType.DATA_RESOURCE, resource_id="abc123")
```

#### 在算子中的应用

算子通过ResourceId类型明确区分不同类型的资源引用：

```python
class TransformStructuredData(StructuredDataOperator[StructuredDataResourceResult]):
    # 输入资源，可以是全局资源或会话资源
    input_resource: ResourceId = Field(..., description="输入数据资源")
    
    # 如果需要限制只接受特定类型的资源ID，可以使用具体子类
    reference_data: GlobalResourceId = Field(..., description="参考数据集(仅限全局资源)")
```

这种设计确保了资源引用的类型安全性，同时保持了API使用的便利性，为算子的输入输出管理提供了坚实的类型基础。

### Python 接口定义

```python
from __future__ import annotations
from typing import Dict, List, Optional, Type, TypeVar, Any, Generic, ClassVar
from abc import ABC, abstractmethod
from pydantic import BaseModel, Field, validator

# 假设 DataResource, ResourceId 等已定义 

class OperatorResult(BaseModel):
    """
    算子执行结果的基类
    """
    session_id: str = Field(..., description="会话ID")
    task_id: str = Field(..., description="任务ID")
    # 将 resource_id 设为可选，因为不是所有算子都产生资源ID
    resource_id: Optional[ResourceId] = Field(default=None, description="输出数据资源ID (如果产生)") 

# 泛型定义，用于表示算子结果类型
T_Result = TypeVar('T', bound=OperatorResult) 
# 泛型定义，用于表示算子的输出资源类型
T_Resource = TypeVar('T', bound=Union[IResource, None]) 

class Operator(Generic[T_Result, T_Resource], ABC):
    """
    数据处理算子的抽象基类，采用泛型设计
    
    泛型参数T_Result表示算子执行结果的类型，必须是OperatorResult的子类
    泛型参数T_Resource表示算子的输出资源类型，必须是IResource的子类，其中None代表算子不生成输出资源
    """
    
    @classmethod
    def result_type(cls) -> Type[T_Result]:
        """
        获取算子结果类型
        
        Returns:
            算子结果的类型类
        """
        ...

	@classmethod
    def output_resource_type(cls) -> Type[T_Resource]:
        """
        获取算子输出资源类型
        
        Returns:
            算子结果的输出资源类型
        """
        ...
        
    def validate_op(self) -> None:
        """
        验证算子参数是否有效
        
        Raises:
            OperatorValidationError: 当参数无效时抛出，包含详细的验证错误信息
        """
        ...

    def build_output_resource_id(self) -> Optional[SessionResourceId]:
        """
        构建输出资源ID

        Raises:
            OperatorValidationError: 当输出资源ID构建失败时抛出，包含详细的错误信息. 如果算子不生成输出资源，则返回None
        """
        ...

    @staticmethod
    def from_json(json_data: Dict[str, Any] | str) -> Self:
        """
        通过json数据解析出算子实例
        
        Args:
            json_data: JSON字典或字符串
            
        Returns:
            解析后的算子实例
            
        Raises:
            OperatorValidationError: 当JSON数据不合法时抛出
        """
		...

class StructuredDataOperator(Operator[T_Result, StructuredDataResource], ABC):
    """
    结构化数据算子的抽象基类
    """
    pass

class OptionalOutputResourceStructuredDataOperator(Operator[T_Result, Optional[StructuredDataResource]], ABC):
    """
    结构化数据算子的抽象基类
    """
    pass

class GraphOperator(Operator[T_Result, GraphResource], ABC):
    """
    图数据算子的抽象基类
    """
    pass


class StructuredDataResourceResult(OperatorResult):
    """
    结构化数据资源的通用结果
    """
    # resource_id 从父类继承，已经是 Optional[ResourceId]
    schema: List[SchemaField] = Field(..., description="输出资源的结构")
    # 可以添加特定于读取操作的其他结果字段，例如行数
    row_count: Optional[int] = Field(default=None, description="读取的行数 (如果可用)")


class ReadStructuredDataResource(StructuredDataOperator[StructuredDataResourceResult]):
    """
    读取结构化数据资源的算子
    """
    _result_type = StructuredDataResourceResult
    
    source: ResourceId = Field(..., description="数据源路径")
    format: str = Field(..., description="数据格式，如csv、parquet等")
    options: Dict[str, Any] = Field(default_factory=dict, description="读取选项")
    
    def validate_op(self) -> None:
        """验证读取参数"""
        if not self.source:
            raise ValueError("数据源路径不能为空")
        
        if self.format not in ["csv", "parquet", "json", "arrow", "duckdb"]:
            raise ValueError(f"不支持的数据格式: {self.format}")
        
        return True


class FilterStructuredData(StructuredDataOperator[StructuredDataResourceResult]):
    """
    过滤结构化数据的算子
    """
    _result_type = StructuredDataResourceResult
    
    resourceId: ResourceId = Field(..., description="输入数据资源ID")
    condition: RowEval = Field(..., description="过滤条件 (JSON表达式结构)")
    
    def validate_op(self) -> None:
        """验证过滤参数"""
        if not self.resourceId:
            raise ValueError("输入数据资源ID不能为空")
        
        if not self.condition:
            raise ValueError("过滤条件不能为空")
        
        # 表达式的具体校验由表达式DSL框架负责
        # if "expression" not in self.condition:
        #     raise ValueError("过滤条件必须包含'expression'字段")
        
        return True


class TransformStructuredData(StructuredDataOperator[StructuredDataResourceResult]):
    """
    转换结构化数据的算子
    """
    _result_type = TransformStructuredDataResult
    
    resourceId: ResourceId = Field(..., description="输入数据资源ID")
    outputColumns: List[Dict[str, Any]] = Field(..., description="输出列定义 (包含JSON表达式)")
    
    def validate_op(self) -> None:
        """验证转换参数"""
        if not self.resourceId:
            raise ValueError("输入数据资源ID不能为空")
        
        if not self.outputColumns:
            raise ValueError("输出列定义不能为空")
        
        # 具体的列定义和表达式校验由表达式DSL框架负责
        # for col in self.outputColumns:
        #     if "key" not in col:
        #         raise ValueError("每个输出列必须包含'key'字段")
        #     if "eval" not in col and "source" not in col:
        #         raise ValueError("每个输出列必须包含'eval'或'source'字段")
        
        return True


# 定义自定义算子
class JoinStructuredData(StructuredDataOperator[StructuredDataResourceResult]):
    _result_type = JoinDataResult
    
    leftResourceId: ResourceId = Field(..., description="左侧数据资源ID")
    rightResourceId: ResourceId = Field(..., description="右侧数据资源ID")
    joinType: str = Field(default="inner", description="连接类型")
    joinCondition: Dict[str, Any] = Field(..., description="连接条件 (JSON表达式结构)")
    
    def validate_op(self) -> None:
        if not self.leftResourceId or not self.rightResourceId:
            raise ValueError("左右数据资源ID不能为空")
        
        if self.joinType not in ["inner", "left", "right", "full", "cross"]:
            raise ValueError(f"不支持的连接类型: {self.joinType}")
        
        if not self.joinCondition:
            raise ValueError("连接条件不能为空")
        
        # 表达式校验由表达式DSL框架负责
        
        return True
```

### Operator 执行流程

算子执行的核心流程是，构造算子实例，通过后端或会话的`run_op`方法接收算子实例执行，具体后端根据算子的配置开始启动任务并返回一个Task对象，用户可以通过该Task对象查询状态和获取结果。

```python
# 构建算子实例方式1：直接构造
read_op = ReadStructuredDataResource(
    resource_id="data-resource://abc",
)

# 构建算子实例方式2：直接通过json解析算子，根据 type 字段自动判断算子类型
read_op = Operator.from_json(json.loads('<some json string>'))

# 后端执行算子
task = backend.run_op(read_op)

# 检查任务状态
status = task.status()  # 返回TaskStatus.RUNNING等状态

# 等待任务完成并获取结果
try:
    result = task.result(timeout=30.0)  # 最多等待30秒
    print(f"数据资源ID: {result.resource_id}")
except TimeoutError:
    print("任务执行超时")
except Exception as e:
    print(f"任务执行失败: {str(e)}")
```

### 定义自定义算子

用户可以通过继承`Operator`类及其子类来定义自定义算子：

```python
# 定义自定义算子结果
class JoinDataResult(OperatorResult):
    resource_id: ResourceId = Field(..., description="连接后的数据资源ID")

# 定义自定义算子
class JoinStructuredData(StructuredDataOperator[JoinDataResult]):
    
    leftResourceId: ResourceId = Field(..., description="左侧数据资源ID")
    rightResourceId: ResourceId = Field(..., description="右侧数据资源ID")
    joinType: JoinType = Field(default="inner", description="连接类型")
    joinCondition: Dict[str, Any] = Field(..., description="连接条件")
    
    def validate_op(self) -> None:
        if not self.leftResourceId or not self.rightResourceId:
            raise ValueError("左右数据资源ID不能为空")
        
        if self.joinType not in ["inner", "left", "right", "full", "cross"]:
            raise ValueError(f"不支持的连接类型: {self.joinType}")
        
        if not self.joinCondition:
            raise ValueError("连接条件不能为空")
        
```

## Task 详细设计

```mermaid
classDiagram
    class Task~T_Result,T_Resource~ {
        <<Interface>>
        # T should bound to OperatorResult
        +task_id: str
        +session_id: str
        +status() TaskStatus
        +cancel() bool
        +result(timeout: Optional[float]) async T_Result
        +exception(timeout: Optional[float]) async Optional[Exception]
        +get_resource_accessor() ResourceAccessor~T_Resource~
    }

    Task <|-- ConcreteFutureTask : 实现

    class TaskStatus {
        <<enumeration>>
        CREATED
        PENDING
        RUNNING
        SUCCESS
        ERROR
        CANCELLED
        UNKNOWN
    }

    class OperatorResult {
        +session_id: str
        +task_id: str
        +resource_id: Optional[ResourceId]
    }

    Task o-- TaskStatus : 使用
    Task --> ResourceAccessor : provides
    Task --> OperatorResult : "returns via result() (T_Result)"
```

Task（任务）接口采用泛型设计，其类型参数 T 绑定到 `OperatorResult`，表示任务成功完成后的**元数据结果类型**。Task 具有以下核心方法：

### Python 接口定义

```python
from typing import Dict, List, Optional, Type, TypeVar, Any, Generic, Union
from abc import ABC, abstractmethod
from enum import Enum
from concurrent.futures import Future, TimeoutError, CancelledError

from .operator import OperatorResult
# 假设 IResource, ResourceAccessor 等已定义
T_Result_co = TypeVar('T', bound=OperatorResult, covariant=True) 
T_Resource_co = TypeVar('T', bound=Union[IResource, None], covariant=True) 

class TaskStatus(Enum):
    """任务状态枚举"""
    CREATED = "CREATED"       # 任务已创建但未开始执行
	PENDING = "PENDING"       # 任务已提交执行但存在需要等待满足的条件
    RUNNING = "RUNNING"       # 任务正在执行
    SUCCESS = "SUCCESS"       # 任务成功完成
    ERROR = "ERROR"           # 任务执行失败 (代替 FAILED)
    CANCELLED = "CANCELLED"   # 任务被取消
    UNKNOWN = "UNKNOWN"       # 状态未知或无法确定

class Task(Generic[T_Result_co, T_Resource_co], ABC):
    """任务抽象基类，提供统一的异步任务接口"""
    
    @property
    @abstractmethod
    def task_id(self) -> str:
        """获取任务的唯一标识符"""
        pass
    
    @property
    @abstractmethod
    def session_id(self) -> str:
        """获取关联的会话ID"""
        pass
    
    @abstractmethod
    def status(self) -> TaskStatus:
        """获取当前任务状态 (非阻塞)"""
        pass
    
    @abstractmethod
    def cancel(self) -> bool:
        """尝试取消任务，成功提交取消请求或任务已完成/取消则返回True"""
        pass
    
    @abstractmethod
    async def result(self, timeout: Optional[float] = None) -> T_Result_co:
        """
        异步等待任务完成并获取其 OperatorResult (元数据结果)。
        这是一个阻塞当前异步上下文的操作。
        
        Args:
            timeout: 最长等待时间（秒），None表示无限等待
        
        Returns:
            任务执行的 OperatorResult 子类实例。
            
        Raises:
            CancelledError: 如果任务被取消
            TimeoutError: 如果在超时前未完成
            Exception: 任务执行过程中的原始异常 (如果任务状态为 ERROR)
        """
        pass
    
    @abstractmethod
    async def exception(self, timeout: Optional[float] = None) -> Optional[Exception]:
        """
        异步等待任务完成并获取其执行过程中抛出的异常（如果有）。
        这是一个阻塞当前异步上下文的操作。如果任务成功完成 (SUCCESS)，返回 None。
        
        Args:
            timeout: 最长等待时间（秒），None表示无限等待
            
        Returns:
            异常实例 (如果任务状态为 ERROR)，否则为 None。
            
        Raises:
            CancelledError: 如果任务被取消
            TimeoutError: 如果在超时前未完成
        """
        pass
        
    @abstractmethod
    def get_resource_accessor(self) -> ResourceAccessor[T_Resource_co]:
        """
        获取与此任务关联的资源访问器 (ResourceAccessor)。
        通过访问器的 get_resource() 方法可以异步获取任务输出的 DataResource。
        
        Returns:
            资源访问器实例。
        """
        pass
```

## ResourceAccessor 详细设计

ResourceAccessor（资源访问器）提供了一种与 Task 分离的方式来**异步获取任务实际产出的资源操作句柄** (`IResource`) 或其他类型资源。它是一个泛型接口 `ResourceAccessor[T]`，封装了等待关联任务成功完成（或其他资源加载逻辑）以及后续获取类型为 `T` 的资源实例的逻辑。

与 `Task` 的主要区别在于：
- `ResourceAccessor` 没有 `cancel()` 方法，它只关注获取结果资源。
- `Task.result()` 返回 `OperatorResult`（元数据），而 `ResourceAccessor[IResource].get_resource()` 返回 `IResource`（操作句柄）。

```mermaid
classDiagram
    class ResourceAccessor~T~ {
        <<Interface>>
        # Waits for associated task/resource and returns the handle
        +get_resource(timeout: Optional[float] = None) async T
    }
    
    class FutureResourceAccessor~T~ {
        -Task _task # Associated Task (Task's result() returns OperatorResult)
        # Waits for _task's future, then gets/creates resource of type T
        +get_resource(timeout: Optional[float] = None) async T
    }

	class DataResource {
	    +resource_id: ResourceId
	}
	
	class StructuredDataResource {
        +schema: List[SchemaField]
        +as_arrow_ds() pyarrow.dataset.Dataset
    }
    
    Task --> ResourceAccessor : provides via get_resource_accessor()
    ResourceAccessor <|-- FutureResourceAccessor : implements
    FutureResourceAccessor ..> Task : uses internally
    ResourceAccessor ..> DataResource : returns (when T=DataResource)
	DataResource <|-- StructuredDataResource : extends
```

### Python 接口定义
ResourceAccessor:
```python
from typing import Any, Optional, TypeVar, Generic
from abc import ABC, abstractmethod
from concurrent.futures import TimeoutError, CancelledError # 引入异常

# 假设 DataResource, ResourceNotFoundError 已定义
T_Resource_co = TypeVar('T_Resource_co', bound=Union[IResource, None], covariant=True) # 定义协变泛型类型变量

class ResourceAccessor(Generic[T_Resource_co], ABC):
    """
    资源访问器泛型接口 ResourceAccessor[T]。
    提供等待并获取类型为 T 的实际资源实例的方法。
    将资源获取的等待/加载逻辑与发现资源的逻辑分离。
    """
    
    @abstractmethod
    async def get_resource(self, timeout: Optional[float] = None) -> T_Resource_co:
        """
        异步等待关联的任务成功完成（或等待其他资源加载），并获取类型为 T 的实际资源实例。
        这是一个阻塞当前异步上下文的操作，直到资源实例可用、超时或获取失败。
        
        Args:
            timeout: 可选的超时时间（秒），None表示无限等待。
            
        Returns:
            类型为 T_Resource_co 的资源实例。
            
        Raises:
            TimeoutError: 如果等待资源就绪超时。
            CancelledError: 如果关联的任务已被取消 (仅适用于基于Task的Accessor)。
            ResourceNotFoundError: 如果关联的任务执行失败，或资源无法定位/加载。
            Exception: 其他在获取资源过程中发生的异常。
        """
        pass
```
IResource 相关接口：
```python
from typing import Any, Optional, TypeVar, Generic
from abc import ABC, abstractmethod

# 假设 DataResource, ResourceNotFoundError, ResourceId 已定义



class IResource(ABC):
    """
    资源抽象基类
    """
    @property
    @abstractmethod
    def resource_id(self) -> ResourceId:
        """获取数据资源的唯一标识符"""
        pass

    @property
    @abstractmethod
    def metadata(self) -> ObjectMetadata:
        """获取资源元数据。

        返回:
            ObjectMetadata: 包含资源元数据信息
        """
        pass

class DataResource(IResource):
    """
    数据资源
    """

    @property
    @abstractmethod
    def data_resource_type(self) -> str:
        """获取数据资源的类型"""
        pass


class StructuredDataResource(DataResource):
    """
    结构化数据资源
    """
    @property
    @abstractmethod
    def schema(self) -> List[SchemaField]:
        """获取数据资源的Schema"""
        pass

    @abstractmethod
    def as_arrow_ds(self) -> pyarrow.dataset.Dataset:
        """将数据资源转换为Arrow Dataset"""
        pass
```
