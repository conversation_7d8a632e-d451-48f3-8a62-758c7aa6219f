#### 2.4.6 数据结构设计

树状数据类型描述语言是一套用于描述复杂、嵌套数据结构的规范，它使用JSON格式进行表达，适用于各种数据交换、验证和转换场景。本设计文档详细阐述了该语言的结构、类型系统和SDK实现方案。
##### 2.4.6.1 设计目标

- 提供统一、直观的数据类型描述方式
- 支持复杂的嵌套和组合类型
- 便于与现有数据处理框架集成
- 提供简洁易用的API进行类型验证和转换


##### 2.4.6.2 基本结构

每个类型描述使用JSON对象表示，包含以下核心属性：

- `type`: 数据类型名称（采用驼峰命名）
- `nullable`: 是否可为空
- `name`: 字段名称（可选）
- `metadata`: 用户自定义元数据（可选）

不同类型拥有其特定属性，如整数类型的位宽、列表类型的元素定义等。

##### ******* JSON Schema 定义

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "通用数据格式描述语言",
  "description": "用于描述各种数据类型结构的JSON Schema，支持复杂的嵌套和组合类型",
  "$id": "#/definitions/DataType",
  
  "definitions": {
    "BaseType": {
      "type": "object",
      "required": ["type", "nullable"],
      "properties": {
        "name": {
          "type": "string",
          "description": "字段名称，用于标识结构中的字段"
        },
        "type": {
          "type": "string",
          "description": "数据类型名称，采用驼峰命名，如Boolean、Int、Float等"
        },
        "nullable": {
          "type": "boolean",
          "description": "指示该字段是否可为空"
        },
        "metadata": {
          "description": "用户自定义元数据信息，用于存储额外的描述性信息",
          "oneOf": [
            {"type": "null"},
            {
              "type": "array",
              "items": {
                "type": "object",
                "required": ["key", "value"],
                "properties": {
                  "key": {
                    "type": "string",
                    "description": "元数据的键名"
                  },
                  "value": {
                    "type": "string",
                    "description": "元数据的值"
                  }
                }
              }
            }
          ]
        }
      }
    },
    
    "Boolean": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Boolean"] }
          },
          "description": "布尔类型不需要额外属性"
        }
      ]
    },
    
    "Int": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Int"] },
            "bitWidth": {
              "enum": [8, 16, 32, 64],
              "description": "整数位宽，支持8、16、32或64位"
            },
            "isSigned": {
              "type": "boolean",
              "default": true,
              "description": "是否有符号整数，默认为true"
            }
          },
          "required": ["bitWidth"]
        }
      ]
    },
    
    "Float": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Float"] },
            "precision": {
              "enum": ["HALF", "SINGLE", "DOUBLE"],
              "description": "浮点数精度，支持半精度(16位)、单精度(32位)和双精度(64位)"
            }
          },
          "required": ["precision"]
        }
      ]
    },
    
    "String": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["String"] }
          },
          "description": "字符串类型不需要额外属性"
        }
      ]
    },
    
    "Binary": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Binary"] }
          },
          "description": "二进制类型不需要额外属性"
        }
      ]
    },
    
    "FixedSizeBinary": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["FixedSizeBinary"] },
            "byteWidth": {
              "type": "integer",
              "minimum": 1,
              "description": "二进制数据的固定字节宽度，最小为1"
            }
          },
          "required": ["byteWidth"]
        }
      ]
    },
    
    "Decimal": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Decimal"] },
            "precision": {
              "type": "integer",
              "minimum": 1,
              "description": "十进制数的精度（总位数），最小为1"
            },
            "scale": {
              "type": "integer",
              "minimum": 0,
              "default": 0,
              "description": "小数点后的位数，最小为0，默认为0"
            }
          },
          "required": ["precision"]
        }
      ]
    },
    
    "Date": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Date"] },
            "unit": {
              "enum": ["DAY", "MILLISECOND"],
              "description": "日期的单位，支持按天或毫秒计"
            }
          },
          "required": ["unit"]
        }
      ]
    },
    
    "Time": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Time"] },
            "unit": {
              "enum": ["SECOND", "MILLISECOND", "MICROSECOND", "NANOSECOND"],
              "description": "时间的单位，支持秒、毫秒、微秒和纳秒"
            },
            "bitWidth": {
              "enum": [32, 64],
              "description": "时间值的位宽，支持32位或64位"
            }
          },
          "required": ["unit", "bitWidth"]
        }
      ]
    },
    
    "Timestamp": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Timestamp"] },
            "unit": {
              "enum": ["SECOND", "MILLISECOND", "MICROSECOND", "NANOSECOND"],
              "description": "时间戳的单位，支持秒、毫秒、微秒和纳秒"
            },
            "timezone": {
              "type": "string",
              "description": "时区信息，例如 'UTC' 或 '+08:00'"
            }
          },
          "required": ["unit"]
        }
      ]
    },
    
    "Interval": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Interval"] },
            "unit": {
              "enum": ["YEAR_MONTH", "DAY_TIME"],
              "description": "时间间隔的单位，支持年月或天时"
            }
          },
          "required": ["unit"]
        }
      ]
    },
    
    "Duration": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Duration"] },
            "unit": {
              "enum": ["SECOND", "MILLISECOND", "MICROSECOND", "NANOSECOND"],
              "description": "持续时间的单位，支持秒、毫秒、微秒和纳秒"
            }
          },
          "required": ["unit"]
        }
      ]
    },
    
    "List": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["List"] },
            "itemType": {
              "$ref": "#/definitions/DataType",
              "description": "列表项的类型定义"
            }
          },
          "required": ["itemType"]
        }
      ]
    },
    
    "LargeList": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["LargeList"] },
            "itemType": {
              "$ref": "#/definitions/DataType",
              "description": "大型列表项的类型定义"
            }
          },
          "required": ["itemType"]
        }
      ]
    },
    
    "FixedSizeList": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["FixedSizeList"] },
            "itemType": {
              "$ref": "#/definitions/DataType",
              "description": "固定大小列表项的类型定义"
            },
            "size": {
              "type": "integer",
              "minimum": 0,
              "description": "列表的固定大小，最小为0"
            }
          },
          "required": ["itemType", "size"]
        }
      ]
    },
    
    "Map": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Map"] },
            "keyType": {
              "$ref": "#/definitions/DataType",
              "description": "映射键的类型定义"
            },
            "valueType": {
              "$ref": "#/definitions/DataType",
              "description": "映射值的类型定义"
            },
            "keysSorted": {
              "type": "boolean",
              "default": false,
              "description": "键是否已排序，默认为false"
            }
          },
          "required": ["keyType", "valueType"]
        }
      ]
    },
    
    "Struct": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Struct"] },
            "fields": {
              "type": "array",
              "items": {
                "allOf": [
                  { "$ref": "#/definitions/DataType" },
                  {
                    "required": ["name"],
                    "properties": {
                      "name": {
                        "type": "string",
                        "minLength": 1
                      }
                    }
                  }
                ]
              },
              "minItems": 1,
              "description": "结构体的字段列表，至少需要一个字段，且每个字段必须有非空的name"
            }
          },
          "required": ["fields"]
        }
      ]
    },
    
    "Union": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Union"] },
            "mode": {
              "enum": ["SPARSE", "DENSE"],
              "description": "联合类型的模式，可以是稀疏或密集"
            },
            "typeIds": {
              "type": "array",
              "items": {"type": "integer"},
              "description": "类型ID数组，用于标识每个类型"
            },
            "types": {
              "type": "array",
              "items": { "$ref": "#/definitions/DataType" },
              "minItems": 1,
              "description": "联合类型中包含的类型列表，至少需要一个类型"
            }
          },
          "required": ["mode", "types"]
        }
      ]
    },
    
    "Enum": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Enum"] },
            "symbols": {
              "type": "array",
              "items": {"type": "string"},
              "minItems": 1,
              "description": "枚举值列表，至少需要一个枚举值"
            },
            "default": {
              "type": "string",
              "description": "默认的枚举值"
            }
          },
          "required": ["symbols"]
        }
      ]
    },
    
    "Dictionary": {
      "allOf": [
        { "$ref": "#/definitions/BaseType" },
        {
          "properties": {
            "type": { "enum": ["Dictionary"] },
            "id": {
              "type": "integer",
              "description": "字典的唯一标识符"
            },
            "indexType": {
              "$ref": "#/definitions/DataType",
              "description": "字典索引的类型定义"
            },
            "valueType": {
              "$ref": "#/definitions/DataType",
              "description": "字典值的类型定义"
            },
            "isOrdered": {
              "type": "boolean",
              "default": false,
              "description": "字典是否已排序，默认为false"
            }
          },
          "required": ["id", "indexType", "valueType"]
        }
      ]
    }
  },
  
  "oneOf": [
    { "$ref": "#/definitions/Boolean" },
    { "$ref": "#/definitions/Int" },
    { "$ref": "#/definitions/Float" },
    { "$ref": "#/definitions/String" },
    { "$ref": "#/definitions/Binary" },
    { "$ref": "#/definitions/FixedSizeBinary" },
    { "$ref": "#/definitions/Decimal" },
    { "$ref": "#/definitions/Date" },
    { "$ref": "#/definitions/Time" },
    { "$ref": "#/definitions/Timestamp" },
    { "$ref": "#/definitions/Interval" },
    { "$ref": "#/definitions/Duration" },
    { "$ref": "#/definitions/List" },
    { "$ref": "#/definitions/LargeList" },
    { "$ref": "#/definitions/FixedSizeList" },
    { "$ref": "#/definitions/Map" },
    { "$ref": "#/definitions/Struct" },
    { "$ref": "#/definitions/Union" },
    { "$ref": "#/definitions/Enum" },
    { "$ref": "#/definitions/Dictionary" }
  ]
}
```


##### 2.4.6.4 支持的数据类型

下表列出了所有支持的数据类型及其属性：

| 类型名称            | 描述        | 必需属性                     | 可选属性       | 示例                                                                                                                                                     |
| --------------- | --------- | ------------------------ | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Boolean         | 布尔类型      | -                        | -          | `{"type": "Boolean", "nullable": false}`                                                                                                               |
| Int             | 整数类型      | bitWidth                 | isSigned   | `{"type": "Int", "bitWidth": 32, "nullable": false}`                                                                                                   |
| Float           | 浮点数类型     | precision                | -          | `{"type": "Float", "precision": "DOUBLE", "nullable": false}`                                                                                          |
| String          | 字符串类型     | -                        | -          | `{"type": "String", "nullable": true}`                                                                                                                 |
| Binary          | 二进制类型     | -                        | -          | `{"type": "Binary", "nullable": false}`                                                                                                                |
| FixedSizeBinary | 固定大小二进制类型 | byteWidth                | -          | `{"type": "FixedSizeBinary", "byteWidth": 16, "nullable": false}`                                                                                      |
| Decimal         | 十进制数类型    | precision                | scale      | `{"type": "Decimal", "precision": 10, "scale": 2, "nullable": false}`                                                                                  |
| Date            | 日期类型      | unit                     | -          | `{"type": "Date", "unit": "DAY", "nullable": false}`                                                                                                   |
| Time            | 时间类型      | unit, bitWidth           | -          | `{"type": "Time", "unit": "MILLISECOND", "bitWidth": 32, "nullable": false}`                                                                           |
| Timestamp       | 时间戳类型     | unit                     | timezone   | `{"type": "Timestamp", "unit": "SECOND", "timezone": "UTC", "nullable": false}`                                                                        |
| Interval        | 时间间隔类型    | unit                     | -          | `{"type": "Interval", "unit": "DAY_TIME", "nullable": false}`                                                                                          |
| Duration        | 持续时间类型    | unit                     | -          | `{"type": "Duration", "unit": "SECOND", "nullable": false}`                                                                                            |
| List            | 列表类型      | itemType                 | -          | `{"type": "List", "itemType": {"type": "Int", "bitWidth": 32, "nullable": false}, "nullable": false}`                                                  |
| LargeList       | 大型列表类型    | itemType                 | -          | `{"type": "LargeList", "itemType": {"type": "String", "nullable": true}, "nullable": false}`                                                           |
| FixedSizeList   | 固定大小列表类型  | itemType, size           | -          | `{"type": "FixedSizeList", "itemType": {"type": "Float", "precision": "DOUBLE", "nullable": false}, "size": 3, "nullable": false}`                     |
| Map             | 映射类型      | keyType, valueType       | keysSorted | `{"type": "Map", "keyType": {"type": "String", "nullable": false}, "valueType": {"type": "Int", "bitWidth": 64, "nullable": true}, "nullable": false}` |
| Struct          | 结构体类型     | fields                   | -          | `{"type": "Struct", "fields": [...], "nullable": false}`                                                                                               |
| Union           | 联合类型      | mode, types              | typeIds    | `{"type": "Union", "mode": "DENSE", "types": [...], "nullable": false}`                                                                                |
| Enum            | 枚举类型      | symbols                  | default    | `{"type": "Enum", "symbols": ["RED", "GREEN", "BLUE"], "nullable": false}`                                                                             |
| Dictionary      | 字典类型      | id, indexType, valueType | isOrdered  | `{"type": "Dictionary", "id": 1, "indexType": {...}, "valueType": {...}, "nullable": false}`                                                           |
| Any             | 任意类型      |                          |            | `{"type": "Any"}`                                                                                                                                      |


类型系统采用层次化结构，基本类型作为原子类型，复合类型可以嵌套包含其他类型，形成树状结构。

#### 2.4.7 表达式语言设计

基于JSON的AST表达式语言设计是一套用于描述复杂表达式的规范，它使用JSON格式进行表达，可以覆盖SQL-99支持的除子查询外的表达式语法，并支持通过类型系统进行跨语言的类型推断和校验。

##### 2.4.7.1 设计目标

- 提供简洁统一的表达式描述方式
- 支持SQL-99级别的表达式能力（不包括子查询、聚合函数、开窗函数）
- 通过类型系统实现跨语言的类型推断和校验
- 便于与现有数据处理框架集成

##### 2.4.7.2 基本结构

表达式语言借鉴go-template的设计，简化为以下三种核心节点类型：

1. **Literal**：字面量值
   - `val`: 使用JSON表示的字面量值
   - `struct`: 字面量的类型描述（使用2.4.6中定义的数据类型系统）(可选，如果可以推断出类型则可以省略)

2. **FuncCall**：函数调用（也用于表示运算符）
   - `name`: 函数名称
   - `args`: 参数列表，每个参数都是一个表达式节点
   - `options`: 可选的函数配置选项

3. **Identifier**：标识符（变量引用）
   - `name`: 标识符名称
   - `attrPath`: 可选的属性访问路径（如`user.address.city`）

所有SQL中的运算符（如`+`, `-`, `*`, `/`, `=`, `AND`等）都被映射为函数调用，使表达式结构保持简洁统一。

##### ******* 上下文结构

为支持类型推断和校验，表达式系统定义了统一的上下文结构：

```yaml
# 标识符表，包含所有可用的标识符及其类型
identifier_table:
- name: 标识符名称
  struct: 类型结构（使用2.4.6中定义的数据类型系统）
  
# 函数表，包含所有可用的函数、运算符及其签名
function_table: 
- name: 函数名称
  description: 函数描述
  # 固定参数列表
  args_list:
  - name: 参数名称
    description: 参数描述 
    # 可以接受的类型
    acceptable_struct: 类型结构或类型结构数组
  # 变长参数定义（可选）
  var_arg:
    description: 变长参数描述
    # 可以接受的类型
    acceptable_struct: 类型结构或类型结构数组

  # 函数的返回类型结构
  return_struct: 返回类型结构

  # 函数选项的JSON Schema（可选）
  options_json_schema: 选项的JSON Schema
```

##### ******* JSON Schema 定义

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "JSON表达式AST和上下文",
  "description": "定义基于JSON的表达式AST节点（Literal、FuncCall、Identifier）和执行上下文（标识符表和函数表）的结构，使用自定义的DataType系统。",

  "definitions": {
    // ExpressionNode: 表达式节点基类型
    "ExpressionNode": {
      "description": "表达式AST中任何节点的基类型。",
      "oneOf": [
        { "$ref": "#/definitions/Literal" },
        { "$ref": "#/definitions/FuncCall" },
        { "$ref": "#/definitions/Identifier" }
      ]
    },

    // Literal: 字面量节点
    "Literal": {
      "type": "object",
      "description": "表示字面量值。",
      "required": ["type", "val"],
      "properties": {
        "type": {
          "const": "Literal"
        },
        "val": {
          "description": "以标准JSON格式表示的字面量值（字符串、数字、布尔值、数组、对象、null）。"
        },
        "struct": {
          "$ref": "#/definitions/DataType",
          "description": "字面量值的数据类型结构。"
        }
      },
      "additionalProperties": false
    },

    // Identifier: 标识符节点
    "Identifier": {
      "type": "object",
      "description": "表示标识符，可能包含属性访问。",
      "required": ["type", "name"],
      "properties": {
        "type": {
          "const": "Identifier"
        },
        "name": {
          "type": "string",
          "description": "标识符的基本名称（如变量名）。"
        },
        "attrPath": {
          "type": "array",
          "items": { "type": "string" },
          "description": "可选的属性访问部分列表（如['address', 'city']表示'user.address.city'）。"
        }
      },
      "additionalProperties": false
    },

    // FuncCall: 函数调用节点
    "FuncCall": {
      "type": "object",
      "description": "表示函数或运算符调用。",
      "required": ["type", "name", "args"],
      "properties": {
        "type": {
          "const": "FuncCall"
        },
        "name": {
          "type": "string",
          "description": "函数或运算符的名称（如'add'、'concat'、'in'、'case'）。"
        },
        "args": {
          "type": "array",
          "items": { "$ref": "#/definitions/ExpressionNode" },
          "description": "函数调用的参数列表，每个参数都是一个表达式节点。"
        },
        "options": {
          "type": "object",
          "description": "特定于函数的可选键值选项（结构由函数的'options_json_schema'定义）。"
        }
      },
      "additionalProperties": false
    },

    // 上下文结构

    // IdentifierInfo: 标识符信息
    "IdentifierInfo": {
      "type": "object",
      "required": ["name", "struct"],
      "properties": {
        "name": {
          "type": "string",
          "description": "标识符名称。"
        },
        "struct": {
          "$ref": "#/definitions/DataType",
          "description": "标识符的数据类型结构。"
        }
      },
      "additionalProperties": false
    },

    // FunctionArgInfo: 函数参数信息
    "FunctionArgInfo": {
      "type": "object",
      "required": ["name", "acceptable_struct"],
      "properties": {
        "name": {
          "type": "string",
          "description": "参数名称。"
        },
        "description": {
          "type": "string",
          "description": "参数描述。"
        },
        "acceptable_struct": {
          "oneOf": [
            { "$ref": "#/definitions/DataType" },
            {
              "type": "array",
              "items": { "$ref": "#/definitions/DataType" }
            }
          ],
          "description": "此参数所需的数据类型结构。"
        }
      },
      "additionalProperties": false
    },

    // FunctionVarArgInfo: 函数变长参数信息
    "FunctionVarArgInfo": {
      "type": "object",
      "required": ["acceptable_struct"],
      "properties": {
        "description": {
          "type": "string",
          "description": "变长参数描述。"
        },
        "acceptable_struct": {
          "oneOf": [
            { "$ref": "#/definitions/DataType" },
            {
              "type": "array",
              "items": { "$ref": "#/definitions/DataType" }
            }
          ],
          "description": "变长参数所需的数据类型结构。"
        }
      },
      "additionalProperties": false
    },

    // FunctionInfo: 函数信息
    "FunctionInfo": {
      "type": "object",
      "required": ["name", "return_struct"],
      "properties": {
        "name": {
          "type": "string",
          "description": "函数/运算符名称。"
        },
        "description": {
          "type": "string",
          "description": "函数描述。"
        },
        "args_list": {
          "type": "array",
          "items": { "$ref": "#/definitions/FunctionArgInfo" },
          "description": "固定参数列表。"
        },
        "var_arg": {
          "$ref": "#/definitions/FunctionVarArgInfo",
          "description": "变长参数定义（如果函数支持的话）。"
        },
        "return_struct": {
          "$ref": "#/definitions/DataType",
          "description": "函数返回值的数据类型结构。"
        },
        "options_json_schema": {
          "type": "object",
          "description": "定义此函数调用的'options'属性结构的可选JSON Schema。"
        }
      },
      "additionalProperties": false
    },

    // Context: 上下文对象
    "Context": {
      "type": "object",
      "properties": {
        "identifier_table": {
          "type": "array",
          "items": { "$ref": "#/definitions/IdentifierInfo" },
          "description": "已知标识符及其类型的表格。"
        },
        "function_table": {
          "type": "array",
          "items": { "$ref": "#/definitions/FunctionInfo" },
          "description": "可用函数/运算符及其签名的表格。"
        }
      },
      "additionalProperties": false
    }
  },

  // 顶层对象可以是表达式节点或上下文
  "oneOf": [
    { "$ref": "#/definitions/ExpressionNode" },
    { "$ref": "#/definitions/Context" }
  ]
}
```

##### ******* SQL复杂结构映射示例

通过将所有运算符和SQL特性映射为函数调用，本设计可以覆盖SQL-99的表达式功能。以下是一些常见SQL结构的映射示例：

###### 1. IN操作

SQL: `age IN (18, 30)`

JSON AST:
```json
{
  "type": "FuncCall",
  "name": "in",
  "args": [
    {"type": "Identifier", "name": "age"},
    {
      "type": "Literal", 
      "val": [18, 30], 
      "struct": {
        "type": "List", 
        "nullable": false, 
        "itemType": {
          "type": "Int", 
          "bitWidth": 32, 
          "nullable": false
        }
      }
    }
  ]
}
```

对应函数定义:
```json
{
  "name": "in",
  "description": "检查值是否存在于列表中。",
  "args_list": [
    {
      "name": "value", 
      "description": "要检查的值。", 
      "acceptable_struct": {"type": "Any"}
    },
    {
      "name": "list", 
      "description": "要检查的列表。", 
      "acceptable_struct": {"type": "List"}
    }
  ],
  "return_struct": {"type": "Boolean", "nullable": false}
}
```

###### 2. LIKE操作

SQL: `name LIKE 'A%'`

JSON AST:
```json
{
  "type": "FuncCall",
  "name": "like",
  "args": [
    {"type": "Identifier", "name": "name"},
    {"type": "Literal", "val": "A%", "struct": {"type": "String", "nullable": false}}
  ]
}
```

对应函数定义:
```json
{
  "name": "like",
  "description": "检查字符串是否匹配模式。",
  "args_list": [
    {
      "name": "value", 
      "description": "要检查的字符串。", 
      "acceptable_struct": {"type": "String"}
    },
    {
      "name": "pattern", 
      "description": "模式字符串（如使用%和_）。", 
      "acceptable_struct": {"type": "String"}
    }
  ],
  "return_struct": {"type": "Boolean", "nullable": false}
}
```

###### 3. CASE WHEN操作

SQL: `CASE WHEN status = 1 THEN 'Active' WHEN status = 2 THEN 'Inactive' ELSE 'Unknown' END`

JSON AST:
```json
{
  "type": "FuncCall",
  "name": "case",
  "args": [
    // WHEN status = 1
    {
      "type": "FuncCall",
      "name": "equal",
      "args": [
        {"type": "Identifier", "name": "status"},
        {"type": "Literal", "val": 1, "struct": {"type": "Int", "bitWidth": 32, "nullable": false}}
      ]
    },
    // THEN 'Active'
    {"type": "Literal", "val": "Active", "struct": {"type": "String", "nullable": false}},
    // WHEN status = 2
    {
      "type": "FuncCall",
      "name": "equal",
      "args": [
        {"type": "Identifier", "name": "status"},
        {"type": "Literal", "val": 2, "struct": {"type": "Int", "bitWidth": 32, "nullable": false}}
      ]
    },
    // THEN 'Inactive'
    {"type": "Literal", "val": "Inactive", "struct": {"type": "String", "nullable": false}},
    // ELSE 'Unknown'
    {"type": "Literal", "val": "Unknown", "struct": {"type": "String", "nullable": false}}
  ]
}
```

对应函数定义:
```json
{
  "name": "case",
  "description": "评估条件并返回相应的结果。",
  "var_arg": {
    "description": "交替的条件（Boolean）和结果（Any），以可选的最终结果（else）结束。结果类型必须兼容。",
    "acceptable_struct": {"type": "Any"}
  },
  "return_struct": {"type": "Any", "nullable": true}
}
```

##### ******* 实现注意事项

1. **类型兼容性检查**：实现表达式引擎时，需要特别处理类型兼容性检查，如确保函数参数类型与预期类型匹配。

2. **类型推断**：复杂表达式的类型推断应该自底向上进行，先推断叶节点类型，然后根据函数签名推断父节点类型。

3. **空值处理**：类型系统需要考虑NULL值的处理，包括nullable标志的传播和NULL值参与运算的规则。

4. **扩展性**：设计允许通过扩展function_table来添加新的函数和运算符，使系统可以适应各种特定领域的需求。

5. **性能考虑**：在解析和评估表达式时，可以考虑使用预编译和缓存技术来提高性能。

通过这种设计，我们可以使用简洁的JSON结构表示复杂的SQL表达式，并结合强大的类型系统进行跨语言的类型推断和校验，为数据处理提供统一、安全的表达式层。
