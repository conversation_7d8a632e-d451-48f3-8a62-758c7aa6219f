# 数据处理引擎 - DuckDB 后端实现设计文档

## 1. 引言

本文档详细描述了数据处理引擎（DataProcessEngine）的 DuckDB 后端实现方案。设计目标是提供一个高效、可靠的 DuckDB 执行后端，使其能够无缝集成到数据处理引擎的整体架构中，并与核心设计文档 [数据处理引擎详细设计.md](./数据处理引擎详细设计.md) 保持兼容。

本文档将重点关注以下几个核心组件的设计：

-   `DuckDBBackend`: DuckDB 后端的整体管理者。
-   `DuckDBSession`: 具体的 DuckDB 执行会话。
-   `OperatorHandlerContext`: 为算子处理器提供上下文信息和会话交互能力的接口。
-   `OperatorHandler`: 具体数据处理算子的执行逻辑实现接口。
-   `HandlerManager`: 管理算子类型与其对应处理器的映射关系，并分发执行任务。

这些组件将协同工作，以实现在 DuckDB 中高效执行数据处理任务。

## 2. 项目结构

建议的 `dpe_backend_duckdb` 模块的项目结构如下：

```
src/
  dpe_backend_duckdb/
    __init__.py
    backend.py          # DuckDBBackend 实现
    session.py          # DuckDBSession 实现
    handler.py          # OperatorHandlerContext 和 OperatorHandler 接口定义
    handlers/
        __init__.py
        base_handler.py    # OperatorHandler 接口和可能的基类
        manager.py          # HandlerManager 实现
        read_handler.py    # ReadStructuredDataResource 的 Handler 实现示例
        filter_handler.py  # FilterStructuredData 的 Handler 实现示例
        # ... 其他具体算子的处理器
    task.py             # DuckDBTask 实现 (如果需要特定于DuckDB的Task逻辑)
    resource.py         # DuckDBDataResource 实现 (如需要)
    utils.py            # DuckDB 相关的辅助函数
```

## 3. 核心组件设计

### 3.1. 核心对象关系图

```mermaid
classDiagram
    direction TD

    class DataProcessBackend {
        <<Interface>>
        +backend_type: str
        +run_op~T_Result, T_Resource~(Operator~T_Result, T_Resource~) Task~T_Result, T_Resource~
        +create_session() DataProcessSession
        +get_session(str) DataProcessSession
        +list_sessions() List[str]
        +close_session(str) bool
        +get_data_resource(ResourceId) ResourceAccessor~DataResource~
        +listen_session_lifecycle(SessionLifecycleListener) None
        +unlisten_session_lifecycle(SessionLifecycleListener) None
        +close() None
    }

    class SessionLifecycleListener {
        <<Protocol>>
        +on_session_created(backend: DataProcessBackend, session_id: str) None
        +on_session_state_changed(backend: DataProcessBackend, session_id: str, old_state: SessionState, new_state: SessionState) None
        +on_session_closed(backend: DataProcessBackend, session_id: str) None
    }

    class DataProcessSession {
        <<Interface>>
        +session_id: str
        +state: SessionState
        +run_op~T_Result, T_Resource~(Operator~T_Result, T_Resource~) Task~T_Result, T_Resource~
        +get_task_output_resource(str) ResourceAccessor~DataResource~
        +close() None
    }

    class DuckDBBackend {
        - Dict~str, DuckDBSession~ _sessions
        - List~SessionLifecycleListener~ _lifecycle_listeners
        - Dict~str, duckdb.DuckDBPyConnection~ _connections
        - DuckDBConnectionConfig _config
        + backend_type: str
        + run_op~T_Result, T_Resource~(Operator~T_Result, T_Resource~) async Task~T_Result, T_Resource~
        + create_session(**kwargs) async DuckDBSession
        + get_session(str) async DuckDBSession
        + list_sessions() List[str]
        + close_session(str) async bool
        + get_data_resource(ResourceId) async ResourceAccessor~DataResource~
        + listen_session_lifecycle(SessionLifecycleListener) None
        + unlisten_session_lifecycle(SessionLifecycleListener) None
        + close() async None
        + get_connection(str) duckdb.DuckDBPyConnection
        + create_connection() duckdb.DuckDBPyConnection
        + release_connection(str) None
        + register_session(DuckDBSession) None
        + unregister_session(str) None
        - _notify_session_created(str) async None
        - _notify_session_state_changed(str, SessionState, SessionState) async None
        - _notify_session_closed(str) async None
    }

    class DuckDBSession {
        - str _session_id
        - duckdb.DuckDBPyConnection _connection
        - HandlerManager _handler_manager
        - Dict~str, str~ _task_output_tables_map  // taskId -> tableName
        - SessionState _state
        + session_id: str
        + state: SessionState
        + run_op~T_Result, T_Resource~(Operator~T_Result, T_Resource~) async Task~T_Result, T_Resource~
        + get_task_output_resource(str) async ResourceAccessor~DataResource~
        + close() async None
        + get_db_connection() duckdb.DuckDBPyConnection
        + map_resource_id_to_table_name(ResourceId) str
        + register_task_output(str, str) ResourceId
        - _set_state(SessionState) None
    }

    class OperatorHandlerContext {
        <<Interface>>
        + session_id: str
        + task_id: str
        + duckdb_conn: duckdb.DuckDBPyConnection
        + resolve_input_as_relation(ResourceId) async DuckDBPyRelation
        + register_relation_as_output(DuckDBPyRelation) async ResourceId
    }

    class OperatorHandler~T_Result, T_Resource~ {
        <<Interface, Generic>>
        + handle(Operator~T_Result, T_Resource~, OperatorHandlerContext) async T_Result
    }

    class HandlerManager {
        - Dict~Type[Operator], Type[OperatorHandler]~ _handlers_map
        + register_handler(Type[Operator], Type[OperatorHandler]) None
        + get_handler(Operator) OperatorHandler
        + handle_operator~T_Result, T_Resource~(OperatorHandlerContext, Operator~T_Result, T_Resource~) async T_Result
    }
    
    class Operator~T_Result, T_Resource~ {
        <<Abstract, Generic>>
        +type: str
        +session_id: str
        +task_id: Optional[str]
        +result_type() Type[T_Result]
        +output_resource_type() Type[T_Resource]
        +validate_op() None
    }
    
    class OperatorResult {
        <<BaseModel>>
        +session_id: str
        +task_id: str
        +resource_id: Optional[ResourceId]
    }

    class Task~T_Result, T_Resource~ {
        <<Interface, Generic>>
        +task_id: str
        +session_id: str
        +status() TaskStatus
        +cancel() bool
        +result(timeout: Optional[float]) async T_Result
        +exception(timeout: Optional[float]) async Optional[Exception]
        +get_resource_accessor() ResourceAccessor~T_Resource~
    }
    
    class ResourceAccessor~T_Resource~ {
        <<Interface, Generic>>
        +get_resource(timeout: Optional[float]) async T_Resource
    }
    
    class ResourceId {
      <<BaseModel>>
    }

    DataProcessBackend <|-- DuckDBBackend
    DataProcessSession <|-- DuckDBSession
    DataProcessBackend ..> SessionLifecycleListener : notifies

    DuckDBBackend o-- DuckDBSession : "manages"
    DuckDBBackend --> duckdb.DuckDBPyConnection : "creates & manages"
    DuckDBSession ..> duckdb.DuckDBPyConnection : "uses"
    DuckDBSession o-- HandlerManager : "uses"
    DuckDBSession ..> OperatorHandlerContext : "creates & provides"
    HandlerManager ..> OperatorHandler : "manages & dispatches to"
    OperatorHandler o-- OperatorHandlerContext : "uses"
    
    DuckDBSession --> Task : "creates via run_op"
    Task o-- OperatorResult : "returns via result()"
    OperatorResult o-- ResourceId : "contains optional"
    Task --> ResourceAccessor : "provides via get_resource_accessor()"
    
    OperatorHandler ..> Operator : "processes"
    OperatorHandler ..> OperatorResult : "produces"

```

### 3.2. 核心对象交互流程

以下两个时序图分别展示了"运行算子"和"资源内容查看"两个关键流程中核心对象的交互过程，以帮助理解组件间如何协同工作。

#### 3.2.1. 运行算子流程

```mermaid
sequenceDiagram
    actor User
    participant Backend as DuckDBBackend
    participant Session as DuckDBSession
    participant Task as DuckDBTask
    participant ExecManager as HandlerManager
    participant OpContext as OperatorHandlerContext
    participant Executor as OperatorHandler
    participant DuckDB

    User->>Backend: create_session("s1")
    activate Backend
    Backend->>Backend: get_connection("s1")
    Note right of Backend: 创建或获取会话连接
    Backend->>Session: new DuckDBSession("s1", connection, callback)
    activate Session
    Session-->>Backend: session_instance
    Backend->>Backend: register_session(session_instance)
    deactivate Session
    Backend-->>User: session_instance
    deactivate Backend

    User->>Session: run_op(MyOperator)
    activate Session
    Session->>MyOperator: validate_op()
    Session->>Session: new DuckDBOperatorHandlerContext(session, "t1")
    Session->>Task: new DuckDBTask(op, exec_manager, context)
    activate Task
    Note right of Session: MyOperator.session_id = "s1"<br/>MyOperator.task_id = "t1" (auto-gen)
    Session->>Task: start_execution() # (async, non-blocking)
    Task-->>Session: task_instance
    Session-->>User: task_instance
    deactivate Session

    Note over Task, Executor: Task's background thread starts
    Task->>ExecManager: handle_operator(context, MyOperator)
    activate ExecManager
    ExecManager->>MyOperator: validate_op()
    ExecManager->>ExecManager: get_handler(MyOperator)
    
    ExecManager->>Executor: handle(operator, context)
    activate Executor
    Executor->>OpContext: duckdb_conn
    activate OpContext
    OpContext-->>Executor: duckdb_conn
    deactivate OpContext
    
    Executor->>OpContext: resolve_input_as_relation(input_res_id)
    activate OpContext
    OpContext->>Session: map_resource_id_to_table_name(input_res_id)
    Session-->>OpContext: "input_table_A"
    OpContext->>DuckDB: conn.table("input_table_A")
    DuckDB-->>OpContext: relation
    OpContext-->>Executor: relation
    deactivate OpContext

    Executor->>DuckDB: relation.filter("条件表达式")
    activate DuckDB
    DuckDB-->>Executor: filtered_relation (DuckDBPyRelation)
    deactivate DuckDB

    Executor->>OpContext: register_relation_as_output(filtered_relation)
    activate OpContext
    Note right of OpContext: 内部自动生成唯一表名<br/>并将relation物化为表
    OpContext->>Session: register_task_output("t1", "auto_generated_name")
    Note right of Session: Session stores mapping: <br/>"t1" -> "auto_generated_name"<br/>"t1" -> SessionResourceId("s1", "data-resource", "t1")
    Session-->>OpContext: output_resource_id
    OpContext-->>Executor: output_resource_id
    deactivate OpContext

    Executor-->>ExecManager: operator_result (with output_resource_id)
    deactivate Executor
    ExecManager-->>Task: operator_result
    deactivate ExecManager
    
    Task->>Task: Set internal Future result, update status to SUCCESS
    deactivate Task
    Note over Task, Executor: Task's background thread ends

    User->>Task: result(timeout=...)
    activate Task
    Note left of Task: Blocks until Future is done or timeout
    Task-->>User: operator_result
    deactivate Task

    User->>Task: get_resource_accessor()
    activate Task
    Task-->>User: resource_accessor
    deactivate Task
    
    User->>ResourceAccessor: get_resource(timeout=...)
    activate ResourceAccessor
    Note left of ResourceAccessor: Blocks until resource is available
    ResourceAccessor-->>User: data_resource
    deactivate ResourceAccessor

    User->>Session: close()
    activate Session
    Session->>Session: 清理内部资源
    Session->>Session: state = CLOSED
    Session->>Backend: on_close_callback()
    activate Backend
    Backend->>Backend: unregister_session("s1")
    Backend->>Backend: release_connection("s1")
    deactivate Backend
    Session-->>User: (void)
    deactivate Session
```

#### 3.2.2. 资源内容查看流程

```mermaid
sequenceDiagram
    title 资源内容查看流程
    actor User
    participant Backend as DuckDBBackend
    participant Session as DuckDBSession
    participant ResAccessor as DuckDBResourceAccessor
    participant DataResource as DuckDBDataResource
    participant DuckDB

    User->>Backend: get_data_resource("session://session1/data-resource/task1")
    activate Backend
    
    Note right of Backend: 解析ResourceId为SessionResourceId
    
    Backend->>Backend: get_session("session1")
    Note right of Backend: 查找会话实例
    
    Backend->>Session: get_task_output_resource("task1")
    activate Session
    
    Session->>ResAccessor: new DuckDBSessionResourceAccessor(session, "task1", resource_id)
    activate ResAccessor
    ResAccessor-->>Session: resource_accessor
    
    Session-->>Backend: resource_accessor
    deactivate Session
    
    Backend-->>User: resource_accessor
    deactivate Backend
    
    User->>ResAccessor: get_resource(timeout=60)
    activate ResAccessor
    Note right of ResAccessor: 等待任务完成并获取资源
    
    ResAccessor->>Session: map_resource_id_to_table_name(resource_id)
    activate Session
    Session-->>ResAccessor: "output_table_name"
    deactivate Session
    
    ResAccessor->>Session: duckdb_conn
    activate Session
    Session-->>ResAccessor: duckdb_connection
    deactivate Session
    
    ResAccessor->>DuckDB: PRAGMA table_info('output_table_name')
    activate DuckDB
    DuckDB-->>ResAccessor: schema_info
    deactivate DuckDB
    
    ResAccessor->>DataResource: new DuckDBDataResource(conn, table_name, schema_info, resource_id)
    activate DataResource
    DataResource-->>ResAccessor: data_resource
    deactivate DataResource
    
    ResAccessor-->>User: data_resource (可查询的数据资源)
    deactivate ResAccessor
    
    User->>DataResource: get_data(limit=10)
    activate DataResource
    
    DataResource->>DuckDB: SELECT * FROM output_table_name LIMIT 10
    activate DuckDB
    DuckDB-->>DataResource: result_rows
    deactivate DuckDB
    
    DataResource-->>User: row_data (例如 pandas DataFrame)
    deactivate DataResource
```

### 3.3. DuckDBBackend

`DuckDBBackend` 实现了 `DataProcessBackend` 接口，主要负责 DuckDB 连接的生命周期管理以及 `DuckDBSession` 实例的创建和维护。

**职责:**

1.  **连接管理**：创建、获取和释放 DuckDB 连接实例，是 DuckDB 资源管理的核心。
2.  **会话管理**：创建、注册、获取和关闭 `DuckDBSession` 实例，维护会话映射关系。
3.  **会话生命周期监听**：提供会话生命周期事件的监听机制，支持外部组件监听会话的创建、状态变更和关闭事件。
4.  **生命周期管理**：管理后端自身的生命周期，确保资源的正确初始化和清理。

其他更具体的业务功能，如任务运行、资源访问等，都委托给对应的 `DuckDBSession` 实例处理。

**接口定义 (Python 示例):**

```python
# src/dpe_backend_duckdb/backend.py
from typing import Dict, Optional, TypeVar, List, Type, Generic, Union, Protocol
import duckdb
import uuid
import threading
import asyncio
from pydantic import BaseModel

from dpe_core import (
    DataProcessBackend, DataProcessSession, Task, ResourceAccessor, DataResource,
    Operator, OperatorResult,
    SessionState, ResourceId, SessionResourceId, ResourceType, 
)
from .session import DuckDBSession, DuckDBSessionConfig

# region 类型定义
T_Result = TypeVar('T_Result', bound=OperatorResult)
T_Resource = TypeVar('T_Resource', bound=Union[DataResource, Optional[DataResource], None])

class DuckDBConnectionConfig(BaseModel):
    """DuckDB连接配置"""
    database_path: str = ":memory:"
    read_only: bool = False
# endregion

class DuckDBBackend(DataProcessBackend):
    """DuckDB后端实现，管理DuckDB连接生命周期和会话管理"""
    
    # region 初始化
    def __init__(self, config: Optional[DuckDBConnectionConfig] = None):
        """初始化DuckDB后端"""
        self._backend_type = "duckdb"
        self._config = config or DuckDBConnectionConfig()
        self._sessions: Dict[str, 'DuckDBSession'] = {}
        self._connections: Dict[str, duckdb.DuckDBPyConnection] = {}
        self._lifecycle_listeners: Set[SessionLifecycleListener] = set()
        self._listeners_lock = threading.RLock()
        self._db_path = self._config.database_path
    # endregion
    
    # region 核心属性与连接管理
    @property
    def backend_type(self) -> str:
        """获取后端类型标识"""
        return self._backend_type
    
    def create_connection(self) -> duckdb.DuckDBPyConnection:
        """创建新的DuckDB连接实例"""
        # 直接连接到配置的数据库文件或内存数据库
        return duckdb.connect(database=self._db_path, read_only=self._config.read_only)
    
    def get_connection(self, session_id: str) -> duckdb.DuckDBPyConnection:
        """获取或创建与会话关联的DuckDB连接"""
        if session_id not in self._connections:
            self._connections[session_id] = self.create_connection()
        return self._connections[session_id]
    
    def release_connection(self, session_id: str) -> None:
        """释放会话关联的DuckDB连接资源"""
        if session_id in self._connections:
            conn = self._connections.pop(session_id)
            conn.close()
    # endregion
    
    # region 核心接口方法
    async def run_op(self, operator: Operator[T_Result, T_Resource]) -> Task[T_Result, T_Resource]:
        """将算子执行请求委托给对应的会话处理"""
        # 委托给相应会话处理
        session = await self.get_session(operator.session_id)
        return await session.run_op(operator)
    
    async def create_session(self, **kwargs) -> 'DuckDBSession':
        """异步创建新的DuckDBSession实例"""
        from .session import DuckDBSession, DuckDBSessionConfig
        
        # 创建会话ID并获取或创建连接
        session_id = kwargs.get("session_id") or str(uuid.uuid4())
        connection = self.get_connection(session_id)
        session_config = DuckDBSessionConfig(**kwargs)
        
        # 创建会话实例，注册关闭和状态变更回调
        new_session = DuckDBSession(
            session_id=session_id,
            connection=connection,
            config=session_config,
            on_close_callback=lambda: self._handle_session_close_sync(session_id),
            on_state_change_callback=lambda old, new: 
                self._notify_session_state_changed_sync(session_id, old, new)
        )
        
        # 注册并通知创建事件
        self._sessions[session_id] = new_session
        await self._notify_session_created(session_id)
        
        return new_session
    
    async def get_session(self, session_id: str) -> 'DuckDBSession':
        """异步获取指定ID的会话实例"""
        session = self._sessions.get(session_id)
        if not session:
            raise ValueError(f"会话 {session_id} 未找到")
        return session
    
    def list_sessions(self) -> List[str]:
        """列出所有当前活跃的会话ID"""
        return list(self._sessions.keys())
    
    async def close_session(self, session_id: str) -> bool:
        """异步关闭指定ID的会话"""
        session = await self.get_session(session_id)
        if not session:
            return False
        await session.close()  # 会触发会话关闭回调
        return True
    
    async def get_data_resource(self, resource_id: ResourceId) -> ResourceAccessor[DataResource]:
        """异步获取指定资源ID对应的资源访问器"""
        # 当前仅支持会话资源
        if isinstance(resource_id, SessionResourceId):
            session = await self.get_session(resource_id.session_id)
            return await session.get_task_output_resource(resource_id.sub_id)
        raise NotImplementedError(f"DuckDB后端暂不支持此类型资源: {type(resource_id)}")
    
    def listen_session_lifecycle(self, listener: SessionLifecycleListener) -> None:
        """注册会话生命周期监听器"""
        with self._listeners_lock:
            if listener not in self._lifecycle_listeners:
                self._lifecycle_listeners.append(listener)
    
    def unlisten_session_lifecycle(self, listener: SessionLifecycleListener) -> None:
        """注销会话生命周期监听器"""
        with self._listeners_lock:
            if listener in self._lifecycle_listeners:
                self._lifecycle_listeners.remove(listener)
    
    async def close(self) -> None:
        """异步关闭后端，释放所有资源"""
        # 关闭所有会话
        for session_id in list(self._sessions.keys()):
            await self.close_session(session_id)
        
        # 确保所有连接都已释放
        for conn in list(self._connections.values()):
            conn.close()
        
        self._sessions.clear()
        self._connections.clear()
        
        # 清空监听器列表
        with self._listeners_lock:
            self._lifecycle_listeners.clear()
    # endregion
    
    # region 私有方法
    async def _handle_session_close_sync(self, session_id: str) -> None:
        """会话关闭时的同步回调处理方法 - 触发异步通知并清理资源"""
        ...
    
    async def _notify_session_state_changed_sync(self, session_id: str, 
                                         old_state: SessionState, 
                                         new_state: SessionState) -> None:
        """同步方法，触发异步的状态变更通知"""
        ...
    
    async def _trigger_async_notification(self, async_method, *args):
        """触发异步通知的工具方法"""
        # 实现代码
        pass
        
    async def _notify_session_created(self, session_id: str) -> None:
        """异步通知所有监听器会话创建事件"""
        # 方法签名
        pass
    
    async def _notify_session_state_changed(self, session_id: str, 
                                         old_state: SessionState, 
                                         new_state: SessionState) -> None:
        """异步通知所有监听器会话状态变更事件"""
        # 方法签名
        pass
    
    async def _notify_session_closed(self, session_id: str) -> None:
        """异步通知所有监听器会话关闭事件"""
        # 方法签名
        pass
    # endregion
```

### 3.4 DuckDBSession

`DuckDBSession` 实现了 `DataProcessSession` 接口，负责具体会话级别的数据处理和任务管理，是算子执行的主要环境。

**职责：**

1. **算子执行管理**：接收算子执行请求，创建任务，委托给执行器处理
2. **资源管理**：维护任务输出与表名的映射关系，提供资源访问能力
3. **执行环境提供**：为算子执行器提供必要的执行上下文
4. **生命周期管理**：管理会话自身的生命周期

**接口定义 (Python 示例):**

```python
# src/dpe_backend_duckdb/session.py
from typing import Dict, Callable, Optional, TypeVar, Any, List, Generic, Union
import duckdb
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor

from dpe_core import (
    DataProcessSession, Task, ResourceAccessor, DataResource,
    Operator, OperatorResult,
    SessionState, ResourceId, SessionResourceId, ResourceType, 
    SessionError,
)

# region 类型定义
T_Result = TypeVar('T_Result', bound=OperatorResult)
T_Resource = TypeVar('T_Resource', bound=Union[DataResource, Optional[DataResource], None])

class DuckDBSessionConfig:
    """DuckDB会话配置"""
    max_concurrent_tasks: int = 4
    # 其他配置选项

# endregion

class DuckDBSession(DataProcessSession):
    """DuckDB会话实现，管理会话级别的任务执行和资源访问"""
    
    # region 初始化
    def __init__(
        self, 
        session_id: str, 
        connection: duckdb.DuckDBPyConnection,
        config: Optional['DuckDBSessionConfig'] = None,
        on_close_callback: Optional[Callable[[], None]] = None,
        on_state_change_callback: Optional[Callable[[SessionState, SessionState], None]] = None
    ):
        """初始化DuckDB会话"""
        self._session_id = session_id
        self._connection = connection
        self._config = config or DuckDBSessionConfig()
        self._on_close_callback = on_close_callback
        self._on_state_change_callback = on_state_change_callback
        
        # 创建处理器管理器和任务线程池
        from .handlers.manager import HandlerManager
        self._handler_manager = HandlerManager()
        self._task_output_tables_map: Dict[str, str] = {}  # task_id -> table_name
        self._tasks: Dict[str, 'DuckDBTask'] = {}  # 任务ID到任务实例的映射
        self._thread_pool = ThreadPoolExecutor(
            max_workers=self._config.max_concurrent_tasks or 4,
            thread_name_prefix=f"duckdb-session-{session_id}"
        )
        self._lock = threading.RLock()
        self._state = SessionState.OPEN
    # endregion
    
    # region 核心属性和状态管理
    @property
    def session_id(self) -> str:
        """获取会话ID"""
        return self._session_id
    
    @property
    def state(self) -> SessionState:
        """获取会话状态"""
        return self._state
    
    def _set_state(self, new_state: SessionState) -> None:
        """更新会话状态并触发状态变更回调"""
        with self._lock:
            old_state = self._state
            if old_state != new_state:
                self._state = new_state
                
                # 调用状态变更回调
                if self._on_state_change_callback:
                    self._on_state_change_callback(old_state, new_state)
    
    def get_db_connection(self) -> duckdb.DuckDBPyConnection:
        """获取DuckDB连接"""
        return self._connection
    # endregion
    
    # region 核心接口方法
    async def run_op(self, operator: Operator[T_Result, T_Resource]) -> Task[T_Result, T_Resource]:
        """异步执行算子，创建任务并启动执行"""
        # 检查会话状态
        if self._state != SessionState.OPEN:
            raise SessionError(f"会话 {self.session_id} 状态为 {self._state}，无法执行操作")
        
        # 生成任务ID并创建执行上下文
        task_id = str(uuid.uuid4())
        operator.session_id = self.session_id  # 确保session_id一致
        
        # 创建处理上下文和任务
        from .handler import DuckDBOperatorHandlerContext
        from .task import DuckDBTask
        
        context = DuckDBOperatorHandlerContext(self, task_id)
        task = DuckDBTask(
            task_id=task_id, 
            session_id=self.session_id, 
            handler_manager=self._handler_manager, 
            operator=operator, 
            execution_context=context,
            executor=self._thread_pool,
            resource_resolver=self._build_resource_resolver(operator)
        )
        
        # 注册任务到会话并启动执行
        with self._lock:
            self._tasks[task_id] = task
        task.start_execution()
        
        return task
    
    def map_resource_id_to_table_name(self, resource_id: ResourceId) -> str:
        """将资源ID映射为数据库表名"""
        # 会话资源 (通常是任务输出)
        if isinstance(resource_id, SessionResourceId) and resource_id.session_id == self.session_id:
            if resource_id.sub_id in self._task_output_tables_map:
                return self._task_output_tables_map[resource_id.sub_id]
        
        # 不支持其他类型资源
        raise ResourceNotFoundError(f"无法映射资源ID: {resource_id}")
    
    def register_task_output(self, task_id: str, table_name: str) -> ResourceId:
        """注册任务输出表，建立task_id与表名的映射"""
        # 记录任务输出表名映射
        with self._lock:
            self._task_output_tables_map[task_id] = table_name
        
        # 创建并返回会话资源ID
        return SessionResourceId(
            session_id=self.session_id,
            resource_type=ResourceType.DATA_RESOURCE,
            sub_id=task_id
        )
    
    async def get_task_output_resource(self, task_id: str) -> ResourceAccessor[DataResource]:
        """获取任务输出资源访问器"""
        # 验证任务输出是否存在
        with self._lock:
            if task_id not in self._task_output_tables_map:
                raise ResourceNotFoundError(f"任务输出不存在: {task_id}")
        
        # 根据任务状态决定返回何种资源访问器
        if task.status() in (TaskStatus.PENDING, TaskStatus.RUNNING):
            # 任务尚未完成，返回FutureResourceAccessor
            from .resource import FutureResourceAccessor
            return FutureResourceAccessor(task._future, resource_resolver)
        
        try:
            # 任务已完成，尝试直接获取结果并解析资源
            result = await task.result(timeout=0)
            resource = await resource_resolver.resolve_resource(result)
            
            # 返回已完成的资源访问器
            from .resource import CompletedResourceAccessor
            return CompletedResourceAccessor(resource)
        except Exception as e:
            # 获取结果失败，仍然返回FutureResourceAccessor
            from .resource import FutureResourceAccessor
            return FutureResourceAccessor(task._future, resource_resolver)
    
    async def close(self) -> None:
        """异步关闭会话，清理资源"""
        if self._state == SessionState.CLOSED:
            return
        
        # 更新状态为正在关闭
        self._set_state(SessionState.CLOSING)
        
        # 关闭任务、清理表和资源
        await self._cancel_all_tasks()
        await self._cleanup_tables()
        self._thread_pool.shutdown(wait=False)
        
        # 更新状态为已关闭并调用回调
        self._set_state(SessionState.CLOSED)
        if self._on_close_callback:
            self._on_close_callback()
    # endregion
    
    # region 私有方法 

    def _build_resource_resolver(self, operator: Operator[T_Result, T_Resource]) -> ResourceResolver[T_Resource]:
        """构建资源解析器"""
        ...

    async def _cancel_all_tasks(self) -> None:
        """取消所有正在执行的任务"""
        # 方法签名
        pass
    
    async def _cleanup_tables(self) -> None:
        """清理会话创建的临时表"""
        # 方法签名
        pass
    # endregion

```


### 3.5 DuckDBTask

`DuckDBTask` 实现了 `Task` 接口，负责封装算子执行过程，提供异步执行能力和结果访问。

**职责：**

1. **异步执行**: 将算子执行委托给线程池异步执行
2. **状态管理**: 维护任务状态并在不同阶段进行转换
3. **结果处理**: 保存和提供算子执行结果
4. **错误处理**: 捕获并处理执行过程中的异常
5. **资源访问**: 提供获取资源访问器的能力

**接口定义 (Python 示例):**

```python
# src/dpe_backend_duckdb/task.py
from typing import TypeVar, Generic, Optional, Type, Any, Union
from concurrent.futures import ThreadPoolExecutor, Future
import threading
import time

from dpe_core import (
    DataProcessSession, Task, ResourceAccessor, DataResource,
    Operator, OperatorResult,
    SessionState, ResourceId, SessionResourceId, ResourceType, 
    TaskExecutionError,
)

# region 类型定义
T_Result = TypeVar('T_Result', bound=OperatorResult)
T_Resource = TypeVar('T_Resource', bound=Union[DataResource, Optional[DataResource], None])
# endregion

class DuckDBTask(Task[T_Result, T_Resource]):
    """DuckDB任务实现，封装算子执行过程"""
    
    # region 初始化
    def __init__(
        self,
        task_id: str,
        session_id: str,
        handler_manager: HandlerManager,
        handler_context: OperatorHandlerContext,
        operator: Operator[T_Result, T_Resource],
        executor: Executor,
        resource_resolver: ResourceResolver[T_Resource]
    ):
        """初始化任务"""
        self._task_id = task_id
        self._session_id = session_id
        self._handler_manager = handler_manager
        self._handler_context = handler_context
        self._operator = operator
        self._executor = executor
        self._resource_resolver = resource_resolver
        self._state = TaskStatus.PENDING
        self._state_lock = threading.Lock()
        self._result: Optional[T_Result] = None
        self._error: Optional[Exception] = None
        self._future: Optional[Future] = None
        self._start_time: Optional[float] = None
        self._end_time: Optional[float] = None
    # endregion
    
    # region 核心属性
    @property
    def task_id(self) -> str:
        """获取任务ID"""
        return self._task_id
    
    @property
    def session_id(self) -> str:
        """获取会话ID"""
        return self._session_id
    
    def status(self) -> TaskStatus:
        """获取任务状态"""
        with self._state_lock:
            return self._state
    # endregion
    
    # region 核心接口方法
    def start_execution(self) -> None:
        """启动任务异步执行"""
        with self._state_lock:
            if self._state != TaskStatus.PENDING:
                return  # 已经启动或完成，忽略
            
            self._state = TaskStatus.RUNNING
            self._start_time = time.time()
        
        # 提交到线程池异步执行
        self._future = self._executor.submit(self._execute)
    
    async def result(self, timeout: Optional[float] = None) -> T_Result:
        """获取任务执行结果，可能会阻塞等待"""
        if self._future is None:
            raise RuntimeError("任务尚未启动")
        
        try:
            # 等待Future完成
            import asyncio
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None, 
                lambda: self._future.result(timeout=timeout)
            )
            
            # Future成功完成，返回结果
            return self._result
        except Exception as e:
            # 转换异常
            raise TaskExecutionError(
                f"任务执行失败: {e}",
                original_error=self._error
            ) from e
    
    async def exception(self, timeout: Optional[float] = None) -> Optional[Exception]:
        """获取任务执行过程中抛出的异常（如果有）"""
        if self._future is None:
            return None
        
        # 等待任务完成或超时
        await self._wait_async(timeout)
        
        # 返回存储的异常
        return self._error
    
    def get_resource_accessor(self) -> ResourceAccessor[T_Resource]:
        """获取与此任务关联的资源访问器"""
        from .resource import FutureResourceAccessor
        return FutureResourceAccessor(self._future, self._resource_resolver)
    
    def cancel(self) -> bool:
        """尝试取消任务执行"""
        if self._future is None:
            return False
        
        # 尝试取消Future
        cancelled = self._future.cancel()
        
        if cancelled:
            with self._state_lock:
                if self._state in (TaskStatus.PENDING, TaskStatus.RUNNING):
                    self._state = TaskStatus.CANCELLED
                    self._end_time = time.time()
        
        return cancelled
    # endregion
    
    # region 私有方法
    def _execute(self) -> T_Result:
        """任务实际执行逻辑，在线程池中运行"""
        ...
    
    async def _wait_async(self, timeout: Optional[float] = None) -> bool:
        """异步等待任务完成"""
        ...
    # endregion
```

### 3.6 OperatorHandler 和 HandlerManager

`OperatorHandler` 是算子处理器的接口定义，所有具体算子处理器都应实现该接口：

```python
# src/dpe_backend_duckdb/handler.py
from typing import TypeVar, Generic, Optional, Union
from abc import ABC, abstractmethod

from dpe_core.operator.base import Operator, OperatorResult
from dpe_core.models import DataResource
from ..handler import DuckDBOperatorHandlerContext

# 泛型定义，用于表示算子结果类型
T_Result = TypeVar('T_Result', bound=OperatorResult)
# 泛型定义，用于表示算子的输出资源类型
T_Resource = TypeVar('T_Resource', bound=Union[DataResource, Optional[DataResource], None])
T_Operator = TypeVar('T_Operator', bound=Operator)
T_Handler = TypeVar('T_Handler', bound=OperatorHandler[T_Operator])


class OperatorHandlerContext(ABC):
    """
    算子处理上下文接口，为处理器提供会话交互能力
    """
    
    @property
    @abstractmethod
    def session_id(self) -> str:
        """获取会话ID"""
        pass
    
    @property
    @abstractmethod
    def task_id(self) -> str:
        """获取任务ID"""
        pass
    
    @abstractmethod
    async def resolve_input_as_relation(self, resource_id: ResourceId) -> Any:
        """
        异步将输入资源ID解析为对应后端的关系对象
        
        Args:
            resource_id: 资源ID
            
        Returns:
            后端特定的关系对象
            
        Raises:
            ResourceNotFoundError: 如果资源不存在或无法解析
        """
        pass
    
    @abstractmethod
    async def register_relation_as_output(self, relation: Any) -> ResourceId:
        """
        异步将关系对象注册为任务输出
        
        Args:
            relation: 后端特定的关系对象
            
        Returns:
            ResourceId: 注册后的资源ID
        """
        pass

class OperatorHandler(Generic[T_Operator], ABC):
    """
    算子处理器接口，实现算子的具体执行逻辑
    """
    
    @classmethod
    def operator_type() -> T_Operator:
        """
        处理器对应的算子类型
        """
        ...

    @classmethod
    @abstractmethod
    def handle(
        self, 
        operator: T_Operator, 
        context: OperatorHandlerContext 
    ) -> OperatorResult:
        """
        处理算子，返回结果
        
        Args:
            operator: 要处理的算子实例
            context: 处理上下文
            
        Returns:
            算子处理结果，结果类型必须与 T_Operator 对应的泛型类型对应
            
        Raises:
            OperatorHandlingError: 如果处理过程中出现错误
        """
        pass
```

`HandlerManager` 负责管理不同类型算子的处理器实现，并为会话提供处理算子的统一入口。

**接口定义 (Python 示例):**

```python
# src/dpe_backend_duckdb/handlers/manager.py
from typing import Dict, Type, TypeVar, Generic, Any, Optional, Union

from dpe_core import (
    DataProcessSession, Task, ResourceAccessor, DataResource,
    Operator, OperatorResult,
    SessionState, ResourceId, SessionResourceId, ResourceType, 
    TaskExecutionError,
)

from .handler import OperatorHandler, OperatorHandlingError

# region 类型定义
T_Result = TypeVar('T_Result', bound=OperatorResult)
T_Resource = TypeVar('T_Resource', bound=Union[DataResource, Optional[DataResource], None])
# endregion

class HandlerManager:
    """负责管理和分发算子处理任务到对应的处理器实现"""
    
    # region 初始化
    def __init__(self):
        """初始化处理器管理器"""
        # 映射 算子类型 -> 处理器类型
        self._handlers_map: Dict[Type[Operator], Type[OperatorHandler]] = {}
        self._register_builtin_handlers()
    # endregion
    
    # region 核心方法

    def register_handler(
        self, 
        operator_type: Type[Operator[T_Result, T_Resource]], 
        handler_type: Type[OperatorHandler[T_Result, T_Resource]]
    ) -> None:
        """注册算子类型与处理器类型的映射关系"""
        self._handlers_map[operator_type] = handler_type
    
    def handler(self) -> Callable[[Type[T_Handler]], Type[T_Handler]]:
        """
        装饰器方法，用于将算子处理器实现类注册到管理器
        
        用法:
        @manager_instance.handler()
        class AnHandler(OperatorHandler[AnOperator]):
            @property
            @classmethod
            def operator_type() -> Type[AnOperator]:
                return AnOperator
                
            @classmethod
            async def handle(self, operator: AnOperator, context: OperatorHandlerContext) -> Result:
                # 实现算子处理逻辑
                pass
        
        装饰器会自动从类的 operator_type 属性中推断对应的算子类型，
        并将该处理器类注册到管理器中
        
        Returns:
            接受处理器类并返回原类的装饰器函数
        """
        def decorator(handler_cls: Type[T_Handler]) -> Type[T_Handler]:
            # 获取处理器类对应的算子类型
            op_type = handler_cls.operator_type
            if not op_type:
                raise ValueError(f"处理器类 {handler_cls.__name__} 未定义 operator_type 类属性")
            
            # 注册到管理器
            self.register_handler(op_type, handler_cls)
            
            # 返回原类
            return handler_cls
        
        return decorator
    
    def get_handler(
        self, 
        operator: Operator[T_Result, T_Resource]
    ) -> OperatorHandler[T_Result, T_Resource]:
        """获取可处理指定算子的处理器实例"""
        # 根据算子类型查找处理器类型
        operator_type = type(operator)
        handler_type = self._find_handler_type(operator_type)
        
        if handler_type is None:
            raise HandlerNotFoundError(f"找不到处理{operator_type}类型算子的处理器")
        
        # 创建并返回处理器实例
        return handler_type()
    
    async def handle_operator(
        self,
        operator: Operator[T_Result, T_Resource],
        context: OperatorHandlerContext
    ) -> T_Result:
        """异步执行算子，返回结果"""
        # 参数验证
        operator.validate_op()
        
        # 获取对应的处理器并执行
        handler = self.get_handler(operator)
        
        try:
            # 执行算子并返回结果
            return await handler.handle(operator, context)
        except Exception as e:
            # 封装并抛出处理错误
            raise OperatorHandlingError(
                f"处理算子{type(operator).__name__}失败: {str(e)}", 
                original_error=e
            )
    # endregion
    
    # region 私有方法
    def _find_handler_type(self, operator_type: Type[Operator]) -> Optional[Type[OperatorHandler]]:
        """根据算子类型的映射关系查找合适的处理器类型"""
        # 方法签名
        pass
    # endregion
```

特定算子处理器的实现示例：

```python
# src/dpe_backend_duckdb/handlers/read_handler.py
import duckdb
from typing import Dict, Any, List, Optional

from dpe_core.operator import SchemaField
from dpe_core.operator.reader import ReadStructuredDataResource, StructuredDataResourceResult

from ..handler import OperatorHandler, OperatorHandlerContext


class ReadStructuredDataResourceHandler(OperatorHandler[ReadStructuredDataResource]):
    """
    读取结构化数据资源的处理器
    """
    
    async def handle(
        self, 
        operator: ReadStructuredDataResource, 
        context: OperatorHandlerContext
    ) -> StructuredDataResourceResult:
        """
        处理读取结构化数据资源的算子
        
        Args:
            operator: 读取算子
            context: 处理上下文
            
        Returns:
            StructuredDataResourceResult: 读取结果
        """
        # 获取DuckDB连接
        conn = context.duckdb_conn
        
        # 根据数据源和格式读取数据
        if operator.format == "csv":
            # 读取CSV文件
            relation = conn.read_csv(operator.source, **operator.options)
        elif operator.format == "parquet":
            # 读取Parquet文件
            relation = conn.read_parquet(operator.source, **operator.options)
        elif operator.format == "json":
            # 读取JSON文件
            relation = conn.read_json(operator.source, **operator.options)
        else:
            raise ValueError(f"不支持的数据格式: {operator.format}")
        
        # 注册为输出表
        resource_id = context.register_relation_as_output(relation)
        
        # 获取表结构
        schema_info = self._get_relation_schema(relation)
        
        # 获取表行数
        row_count = self._get_relation_row_count(relation)
        
        # 创建并返回结果
        return StructuredDataResourceResult(
            session_id=context.session_id,
            task_id=context.task_id,
            resource_id=resource_id,
            schema=schema_info,
            row_count=row_count
        )
    
    def _get_relation_schema(self, relation: duckdb.DuckDBPyRelation) -> List[SchemaField]:
        """
        获取关系的Schema信息
        
        Args:
            relation: DuckDB关系对象
            
        Returns:
            List[SchemaField]: Schema字段列表
        """
        # 获取关系的Schema
        schema = relation.describe()
        
        # 转换为SchemaField列表
        schema_fields = []
        for column in schema.fetchall():
            schema_fields.append(SchemaField(
                name=column[0],
                type=column[1],
                nullable=True  # DuckDB默认允许NULL
            ))
        
        return schema_fields
    
    def _get_relation_row_count(self, relation: duckdb.DuckDBPyRelation) -> int:
        """
        获取关系的行数
        
        Args:
            relation: DuckDB关系对象
            
        Returns:
            int: 行数
        """
        # 执行COUNT查询
        count_relation = relation.aggregate("COUNT(*) as row_count")
        count_result = count_relation.fetchone()
        
        return count_result[0] if count_result else 0
```

### 3.7 ResourceAccessor 相关实现设计

在DuckDB后端实现中，为了分离任务状态管理与资源访问的职责，我们引入了ResourceResolver和各种ResourceAccessor实现。这些组件遵循以下设计原则：

- **关注点分离**：任务(Task)专注于任务执行状态管理，资源访问由会话(Session)统一管理
- **延迟解析**：资源只在实际需要时才被解析，避免不必要的资源消耗
- **统一接口**：所有资源访问都通过ResourceAccessor接口，隐藏实现细节
- **类型安全**：利用泛型保证资源类型在编译时的一致性

#### 3.7.1 核心组件关系

```mermaid
classDiagram
    class ResourceAccessor~T_Resource~ {
        <<Interface>>
        +get_resource(timeout) async T_Resource
    }
    
    class ResourceResolver~T_Resource~ {
        <<Protocol>>
        +resolve_resource(task_result) async T_Resource
    }
    
    class FutureResourceAccessor~T_Resource~ {
        -Future _future
        -ResourceResolver~T_Resource~ _resource_resolver
        +get_resource(timeout) async T_Resource
    }
    
    class CompletedResourceAccessor~T_Resource~ {
        -T_Resource _resource
        +get_resource(timeout) async T_Resource
    }
    
    
    class DuckDBSession {
        +get_task_output_resource(task_id) async ResourceAccessor[DataResource]
    }
    
    class Task~T_Result, T_Resource~ {
        +get_resource_accessor() ResourceAccessor[T_Resource]
    }
    
    ResourceAccessor <|.. FutureResourceAccessor : implements
    ResourceAccessor <|.. CompletedResourceAccessor : implements
    
    FutureResourceAccessor ..> ResourceResolver : uses
    Task --> FutureResourceAccessor : creates
    DuckDBSession --> ResourceResolver : creates
    DuckDBSession ..> FutureResourceAccessor : creates
    DuckDBSession ..> CompletedResourceAccessor : creates
```


#### 3.7.2 ResourceResolver 接口

ResourceResolver 是一个协议接口，定义了如何从任务结果解析出具体的资源对象：

```python
# src/dpe_backend_duckdb/resource.py
from typing import TypeVar, Generic, Optional, Protocol, Any, Union, Callable
from abc import ABC, abstractmethod
from concurrent.futures import Future

from dpe_core import (
    ResourceAccessor, DataResource, OperatorResult,
    ResourceId, ResourceNotFoundError
)

# region 类型定义
# 协变泛型类型变量
T_Resource_co = TypeVar('T_Resource_co', bound=Union[DataResource, Optional[DataResource], None], covariant=True)
# endregion

class ResourceResolver(Generic[T_Resource_co], Protocol):
    """
    资源解析器协议，用于从任务结果解析出实际资源
    
    不同类型的后端和资源可以实现此协议，以提供统一的资源解析机制
    """
    
    # region 核心方法
    async def resolve_resource(self, task_result: OperatorResult) -> T_Resource_co:
        """
        从任务结果解析出实际的资源
        
        Args:
            task_result: 任务执行的结果
            
        Returns:
            T_Resource_co: 解析出的资源
            
        Raises:
            ResourceNotFoundError: 如果无法解析资源
        """
        ...
    # endregion
```

#### 3.7.3 ResourceAccessor 实现


```python
# src/dpe_backend_duckdb/resource.py
class FutureResourceAccessor(ResourceAccessor[T_Resource_co]):
    """
    基于 Future 的资源访问器，在 Future 完成后使用 ResourceResolver 解析资源
    
    主要用于异步等待任务完成并获取其输出资源
    """
    
    # region 初始化
    def __init__(
        self, 
        future: Future, 
        resource_resolver: ResourceResolver[T_Resource_co]
    ):
        """
        初始化资源访问器
        
        Args:
            future: 任务执行的 Future 对象
            resource_resolver: 资源解析器，用于从任务结果解析资源
        """
        self._future = future
        self._resource_resolver = resource_resolver
    # endregion
    
    # region 核心接口方法
    async def get_resource(self, timeout: Optional[float] = None) -> T_Resource_co:
        """
        异步等待 Future 完成并解析资源
        
        等待关联的 Future 完成，然后使用 ResourceResolver 从结果中解析出资源
        
        Args:
            timeout: 最长等待时间（秒），None表示无限等待
            
        Returns:
            T_Resource_co: 解析出的资源
            
        Raises:
            TimeoutError: 如果在超时前未完成
            CancelledError: 如果任务被取消
            ResourceNotFoundError: 如果无法从结果解析资源
            Exception: 任务执行过程中抛出的原始异常
        """
        try:
            # 等待 Future 完成
            import asyncio
            loop = asyncio.get_event_loop()
            task_result = await loop.run_in_executor(
                None, 
                lambda: self._future.result(timeout=timeout)
            )
            
            # 使用 ResourceResolver 解析资源
            return await self._resource_resolver.resolve_resource(task_result)
            
        except Exception as e:
            # 转换异常
            if isinstance(e, TimeoutError):
                raise
            elif hasattr(self._future, 'cancelled') and self._future.cancelled():
                raise CancelledError("任务已被取消")
            else:
                raise ResourceNotFoundError(f"无法获取资源: {str(e)}") from e
    # endregion

class CompletedResourceAccessor(ResourceAccessor[T_Resource_co]):
    """
    已完成的资源访问器，直接返回已有的资源
    
    用于包装已经可用的资源，提供统一的访问接口
    """
    
    # region 初始化
    def __init__(self, resource: T_Resource_co):
        """
        初始化资源访问器
        
        Args:
            resource: 已有的资源
        """
        self._resource = resource
    # endregion
    
    # region 核心接口方法
    async def get_resource(self, timeout: Optional[float] = None) -> T_Resource_co:
        """
        直接返回已有的资源
        
        由于资源已经可用，此方法会立即返回，不会阻塞
        
        Args:
            timeout: 忽略，因为资源已经可用
            
        Returns:
            T_Resource_co: 已有的资源
        """
        return self._resource
    # endregion
```



### 3.8 DuckDBOperatorHandlerContext

`DuckDBOperatorHandlerContext` 为算子处理器提供处理上下文，是连接算子处理器与会话的桥梁。

**接口定义 (Python 示例):**

```python
# src/dpe_backend_duckdb/handler.py
from typing import Optional
import duckdb
import uuid
import asyncio # Added for run_in_executor

from dpe_core.models import ResourceId
# from .session import DuckDBSession # Forward declaration or conditional import if needed for type hint
# Forward declare DuckDBSession for type hint to avoid circular dependency if executor is imported by session
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .session import DuckDBSession


class DuckDBOperatorHandlerContext:
    """
    DuckDB算子处理上下文，为处理器提供会话交互能力
    """
    
    def __init__(self, session: 'DuckDBSession', task_id: str): # Use string literal for DuckDBSession
        """
        初始化处理上下文
        
        Args:
            session: DuckDBSession实例
            task_id: 任务ID
        """
        self._session = session
        self._task_id = task_id
    
    @property
    def session_id(self) -> str:
        """获取会话ID"""
        return self._session.session_id
    
    @property
    def task_id(self) -> str:
        """获取任务ID"""
        return self._task_id
    
    @property
    def duckdb_conn(self) -> duckdb.DuckDBPyConnection:
        """获取DuckDB连接"""
        return self._session.get_db_connection()
    
    async def resolve_input_as_relation(self, resource_id: ResourceId) -> duckdb.DuckDBPyRelation:
        """
        异步将输入资源ID解析为DuckDB关系对象
        
        Args:
            resource_id: 资源ID
            
        Returns:
            duckdb.DuckDBPyRelation: DuckDB关系对象
            
        Raises:
            ResourceNotFoundError: 如果资源不存在或无法解析
        """
        # 将资源ID映射为表名 (sync call)
        table_name = self._session.map_resource_id_to_table_name(resource_id)
        
        # 获取DuckDB连接 (sync call)
        conn = self.duckdb_conn
        
        # 获取表对应的关系对象 (sync call, wrap in executor for async)
        try:
            loop = asyncio.get_running_loop()
            relation = loop.run_in_executor(None, lambda: conn.table(table_name))
            return relation
        except Exception as e:
            # Consider specific DuckDB exceptions
            raise ResourceNotFoundError(f"无法解析资源为关系对象: {resource_id}, 原因: {str(e)}")
    
    async def register_relation_as_output(self, relation: duckdb.DuckDBPyRelation) -> ResourceId:
        """
        异步将DuckDB关系对象注册为任务输出
        
        Args:
            relation: DuckDB关系对象
            
        Returns:
            ResourceId: 注册后的资源ID
        """
        # 生成唯一表名
        table_name = f"task_output_{self.task_id.replace('-', '_')}"
        
        # 获取DuckDB连接 (sync call)
        conn = self.duckdb_conn
        
        # 注册为物理表 (sync call, wrap in executor for async)
        loop = asyncio.get_running_loop()
        loop.run_in_executor(None, lambda: conn.execute(f"CREATE OR REPLACE TABLE {table_name} AS SELECT * FROM relation"))

        # 注册到会话 (sync call)
        resource_id = self._session.register_task_output(self.task_id, table_name)
        
        return resource_id
```

### 3.9 会话生命周期监听示例

以下是会话生命周期监听的简单使用示例：

```python
import asyncio
from datetime import datetime
# Assuming DataProcessBackend, SessionState, DataProcessEngine are imported correctly
# from dpe_core import DataProcessBackend, SessionState, DataProcessEngine 
# (adjust imports based on actual project structure)

# 定义一个自定义的会话生命周期监听器
class SessionActivityLogger: # Ensure it matches SessionLifecycleListener protocol if using strict type checking
    """
    记录会话活动的监听器示例
    """
    def __init__(self, log_file_path: str = "session_activity.log"):
        """初始化日志记录器"""
        self.log_file_path = log_file_path
    
    def _write_log(self, message: str):
        """写入日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(self.log_file_path, "a") as f:
            f.write(f"{timestamp} - {message}\n")
    
    async def on_session_created(self, backend: DataProcessBackend, session_id: str) -> None: # Made async
        """当会话被创建时记录日志"""
        self._write_log(f"会话已创建: {session_id} (后端: {backend.backend_type})")
    
    async def on_session_state_changed(self, backend: DataProcessBackend, session_id: str, old_state: SessionState, new_state: SessionState) -> None: # Made async
        """当会话状态变更时记录日志"""
        self._write_log(f"会话状态变更: {session_id} 从 {old_state} 变为 {new_state}")
    
    async def on_session_closed(self, backend: DataProcessBackend, session_id: str) -> None: # Made async
        """当会话关闭时记录日志"""
        self._write_log(f"会话已关闭: {session_id}")


# 使用示例
async def monitoring_example(): # Made async
    # 获取或创建 DuckDB 后端
    backend = DataProcessEngine.backend("duckdb") # Assuming DataProcessEngine.backend is sync
    
    # 创建并注册监听器
    logger = SessionActivityLogger()
    backend.listen_session_lifecycle(logger) # listen_session_lifecycle is sync
    
    # 创建会话 (将触发 on_session_created)
    session1 = await backend.create_session() # create_session is async
    print(f"已创建会话: {session1.session_id}")
    
    # 执行一些操作...
    # Example: op = YourOperator(session_id=session1.session_id, ...)
    # task = await session1.run_op(op)
    # await task.result()
    
    # 关闭会话 (将触发 on_session_state_changed 和 on_session_closed)
    await session1.close() # close is async
    
    # 如果不再需要监听，可以注销监听器
    backend.unlisten_session_lifecycle(logger) # unlisten_session_lifecycle is sync
    
    # 创建另一个会话 (不会被记录，因为监听器已注销)
    session2 = await backend.create_session()
    await session2.close()

```
