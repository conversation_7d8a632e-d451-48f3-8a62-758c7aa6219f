`# 数据处理引擎详细设计-DAG调度相关

## 简介

数据处理引擎的DAG调度扩展是在现有DataProcessEngine框架基础上，新增对有向无环图(DAG)形式的数据处理工作流调度能力。该扩展通过引入 `DataProcessDagSession` 类来支持复杂的多算子编排、依赖管理和批量执行能力。

### 核心价值

- **DAG工作流支持**：支持将多个算子组织成有向无环图，自动处理算子间的依赖关系
- **批量任务管理**：提供创建、运行、删除多个相关任务的批量操作能力
- **智能重跑策略**：支持多种重跑模式（NEVER、FORCE、FAILED_ONLY），优化任务执行效率
- **执行图导入导出**：支持执行图的持久化和复用，便于工作流的版本管理和分享
- **任务状态传播**：自动处理上游任务状态变化对下游任务的影响
- **资源生命周期管理**：统一管理DAG中所有任务产生的数据资源

### 设计目标

DAG调度扩展的设计遵循以下目标：

1. **核心设计目标**：**向下兼容并支持DAG形式调度**，完全兼容现有的 `DataProcessSession` 接口，不影响单任务执行模式。**以ResourceId为核心的DAG自动维护**，通过ResourceId类型系统实现完全自动化的DAG依赖管理
2. **类型安全**：保持泛型系统的完整性，确保编译时类型检查
3. **异步友好**：所有DAG操作都支持异步执行，避免阻塞主线程
4. **容错设计**：提供完善的错误处理和恢复机制
5. **可扩展性**：预留接口扩展点，支持未来更复杂的调度策略
6. **智能依赖推断**：通过算子输入字段的`resource_id`自动推断和维护任务间的依赖关系
7. 

#### 以算子的 ResourceId 类型字段为核心的进行自动依赖管理策略

系统通过识别使用`input_fields`的`ClassVar`声明的并且是以下类型的字段作为任务上游输入：
- `ResourceId`：单个资源引用
- `Optional[ResourceId]`：可选的单个资源引用  
- `List[ResourceId]`：多个资源引用列表
- `Optional[List[ResourceId]]`：可选的多个资源引用列表


**SessionResourceId处理（直接依赖）**：
```python
# 当字段值为SessionResourceId时，直接建立依赖关系
session_resource_id = "session://session-123/data-resource/task-456"
# 系统自动识别：此任务依赖于session-123中的task-456
```

**GlobalResourceId处理（适配转换）**：
```python
# 当字段值为GlobalResourceId时，自动创建适配任务
global_resource_id = "global://data-catalog/sales-2023"

# 系统自动创建 ReadByResourceId 算子任务：
adapter_task = ReadByResourceId(
    resource_id=global_resource_id,
    task_id="auto-adapter-{hash}"
)

# 并替换原字段为对应的SessionResourceId：
session_resource_id = f"session://{session_id}/data-resource/{adapter_task.task_id}"
```

#### 使用示例：多种ResourceId类型组合

```python
# 定义包含多种ResourceId类型的算子
class DataJoinOperator(StructuredDataOperator):
    input_fields: ClassVar[Sequence[str]] = ("left_input", "right_input","reference_data","optional_enrichment")
    left_input: ResourceId = Field(..., description="左侧数据源")
    right_input: Optional[ResourceId] = Field(None, description="右侧数据源")
    reference_data: List[ResourceId] = Field(default_factory=list, description="参考数据列表")
    optional_enrichment: Optional[List[ResourceId]] = Field(None, description="可选的数据补充")
    non_input_field: ResourceId = Field(..., description="非上游输入字段")

# 使用示例
join_op = DataJoinOperator(
    left_input="session://session-123/data-resource/task-001",  # SessionResourceId：直接依赖
    right_input="global://data-catalog/customer-info",          # GlobalResourceId：自动创建适配任务
    reference_data=[
        "session://session-123/data-resource/task-002",        # SessionResourceId：直接依赖
        "global://data-catalog/product-catalog"                # GlobalResourceId：自动创建适配任务
    ]
)

# 系统自动处理：
# 1. 识别task-001和task-002为直接上游依赖
# 2. 为customer-info和product-catalog自动创建ReadByResourceId适配任务
# 3. 建立完整的依赖关系图
# 4. 替换GlobalResourceId为对应的SessionResourceId

dag_task = dag_session.create_task(join_op)
# 此时dag_task自动依赖于4个上游任务：task-001, task-002, 以及两个自动创建的适配任务
```

#### 核心优势

1. **透明的资源访问**：开发者可以无缝使用全局资源和会话资源，系统自动处理转换
2. **自动化依赖管理**：基于类型系统自动识别和建立依赖关系，无需手动配置
3. **资源复用优化**：相同的GlobalResourceId只创建一个适配任务，避免重复计算
4. **类型安全保障**：通过静态类型检查确保ResourceId使用的正确性
5. **灵活的资源组合**：支持单个、可选、列表等多种ResourceId组合方式

### 核心业务对象及关系

```mermaid
classDiagram

    class SupportDagSessionBackend {
        +create_dag_session(session_conf, initial_graph) DataProcessDagSession
    }

    class DataProcessSession {
        <<Interface>>
        +session_id: str
        +run_op(operator) Task
        +list_tasks() Dict[str, Task]
        +get_task_output_resource(task_id) ResourceAccessor[IResource]
        +close() None
    }
    
    class DataProcessDagSession {
        <<Interface>>
        +run_task(task_ids: Sequence[str], rerun_mode: RerunMode) Dict[str, Task]
        +delete_task(task_ids: Sequence[str]) Sequence[str]
        +export_graph() ExecutionGraph
        +run_all_tasks(rerun_mode: RerunMode) Dict[str, Task]
        +open_graph_client() ExecutionGraphClient
    }
    
    class ExecutionGraphClient {
        <<Interface>>
        +add_nodes(nodes: Sequence[GraphNode]) Sequence[str]
        +update_nodes(id_ref_configs: Dict[str, UpdateNodeParam]) Sequence[str]
        +delete_nodes(node_ids: Sequence[str]) Sequence[str]
        +add_edges(edges: Sequence[GraphEdge]) Sequence[str]
        +delete_edges(edges: Sequence[GraphEdge]) Sequence[str]
        +add_op(op: T) T
        +load_graph(graph: ExecutionGraph, merge_mode: MergeMode) Sequence[str]
        +verify() VerifyResult
        +save() Sequence[str]
        +export_graph() ExecutionGraph
        +resolve_task(op: Operator) DagTask
        +current_edit_status() ClientEditStatus
        +close() None
    }
    
    class Task {
        <<Interface>>
        +task_id: str
        +session_id: str
        +status() TaskStatus
        +cancel() bool
        +result(timeout: Optional[float]) OperatorResult
        +exception(timeout: Optional[float]) Optional[Exception]
        +get_resource_accessor() ResourceAccessor[IResource]
    }
    
    class DagTask {
        <<Interface>>
        # 新增属性和方法（继承Task的所有基础功能）
        +node_id: Optional[str]
        +operator: Operator
        +dependencies: Set[str]
        +dependents: Set[str]
        +upstream_changed: bool
    }
    
    class ExecutionGraph {
        +id: str
        +metadata: ObjectMetadata
        +nodes: Dict[str, GraphNode]
        +edges: List[GraphEdge]
    }
    
    class GraphNode {
        +id: str
        +metadata: ObjectMetadata
        +op_type: str
        +op_configs: Dict[str, Any]
    }
    
    class GraphEdge {
        +source: str
        +target: str
        +target_config: str
    }
    
    class RerunMode {
        <<Enumeration>>
        NEVER
        FORCE  
        FAILED_ONLY
    }
    
    class MergeMode {
        <<Enumeration>>
        FAILED_ON_CONFLICT
        OVERWRITE_EXISTING
        MERGE_AND_UPDATE
    }
    
    DataProcessBackend <|-- SupportDagSessionBackend : extends
    DataProcessSession <|-- DataProcessDagSession : extends
    Task <|-- DagTask : extends
    DataProcessDagSession --> DagTask : manages
    DataProcessDagSession --> ExecutionGraph : imports/exports
    DataProcessDagSession --> ExecutionGraphClient : creates
    ExecutionGraphClient --> ExecutionGraph : edits/operates
    ExecutionGraphClient --> DagTask : resolves
    ExecutionGraph *-- GraphNode : contains
    ExecutionGraph *-- GraphEdge : contains
    DagTask --> RerunMode : uses for rerun strategy
    ExecutionGraphClient --> MergeMode : uses for graph loading
```

### 核心对象说明

#### DataProcessDagSession
扩展的DAG会话接口，在标准会话基础上增加了DAG调度能力，包括：
- 批量任务管理（运行、删除）
- 执行图导出
- 智能重跑策略
- 图编辑客户端访问

#### DagTask  
DAG任务接口，扩展了标准Task，增加了：
- 依赖关系信息（dependencies、dependents）
- 上游变化标记（upstream_changed）
- 节点ID关联（node_id）

#### ExecutionGraph
执行图数据结构，定义了：
- 图的元数据信息
- 节点集合（GraphNode）
- 边集合（GraphEdge）

#### ExecutionGraphClient
图编辑客户端接口，提供更灵活的执行图编辑能力：
- 节点和边的批量操作（添加、更新、删除）
- 基于算子的高级图编辑模式
- 执行图加载和合并策略
- 图结构验证和保存机制
- 算子到任务的解析能力
- 获取客户端当前编辑状态（`current_edit_status`）：查询是否有未提交变更、当前生效图、客户端当前图及待提交变更详情。

## 具体核心业务对象设计

### DataProcessDagSession 详细设计

```mermaid
classDiagram
    class DataProcessDagSession {
        <<Interface>>
        # 继承的基础方法
        +session_id: str
        +run_op(operator: Operator) Task
        +list_tasks() Dict[str, Task]
        +get_task_output_resource(task_id: str) ResourceAccessor[IResource]
        +close() None
        
        # DAG扩展方法
        +run_task(task_ids: Sequence[str], rerun_mode: RerunMode) Dict[str, Task]
        +delete_task(task_ids: Sequence[str]) Sequence[str]
        +export_graph() ExecutionGraph
        +run_all_tasks(rerun_mode: RerunMode) Dict[str, Task]
        +open_graph_client() ExecutionGraphClient
    }
    
    class RerunMode {
        <<Enumeration>>
        NEVER        # 从不重跑已成功的任务
        FORCE        # 强制重跑所有指定任务
        FAILED_ONLY  # 仅重跑失败的任务
    }
    
```

### Python 接口定义

```python
from __future__ import annotations
from typing import Dict, List, Optional, Set, Union, Any
from abc import ABC, abstractmethod
from enum import Enum, auto
from dataclasses import dataclass
from pydantic import BaseModel, Field

# 导入基础类型
from .session import DataProcessSession, Task
from .operator import Operator, OperatorResult
from .resource import DataResource, ResourceAccessor

class RerunMode(Enum):
    """重跑模式枚举"""
    NEVER = "never"           # 从不重跑已成功的任务
    FORCE = "force"           # 强制重跑所有指定任务  
    FAILED_ONLY = "failed_only"  # 仅重跑失败的任务

class MergeMode(Enum):
    """图合并模式枚举"""
    FAILED_ON_CONFLICT = "failed_on_conflict"     # 如果存在冲突则失败
    OVERWRITE_EXISTING = "overwrite_existing"     # 覆盖已存在的节点
    MERGE_AND_UPDATE = "merge_and_update"         # 合并并更新冲突节点

@dataclass
class ConflictInfo:
    """执行冲突信息"""
    conflict_type: str  # "dependency_cycle", "resource_conflict", etc.
    message: str
    involved_tasks: List[str]
    
class VerifyResult(BaseModel):
    """验证结果信息"""
    valid: bool = Field(..., description="整体是否合法")
    failed_details: List[VerifyFailureDetail] = Field(default_factory=list, description="失败的验证详情列表")

class VerifyFailureDetail(BaseModel):
    """验证失败详情"""
    rule_type: str = Field(..., description="失败的规则类型代码")
    message: str = Field(..., description="详细描述信息")
    extra_info: Dict[str, Any] = Field(default_factory=dict, description="半结构化的额外信息")

class PendingChanges(BaseModel):
    """
    待提交的图变更集合
    
    记录客户端待提交的图变更操作，类似于 GraphChanges 但不包含算子添加信息。
    用于在客户端编辑状态中表示未提交的变更。
    """
    nodes_to_add: Dict[str, GraphNode] = Field(default_factory=dict, description="要添加的节点映射：节点ID -> GraphNode")
    nodes_to_update: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="要更新的节点配置映射：节点ID -> 配置更新字典")
    nodes_to_delete: Set[str] = Field(default_factory=set, description="要删除的节点ID集合")
    edges_to_add: List[GraphEdge] = Field(default_factory=list, description="要添加的边列表")
    edges_to_delete: List[GraphEdge] = Field(default_factory=list, description="要删除的边列表")


class ClientEditStatus(BaseModel):
    """
    客户端编辑状态信息
    
    包含客户端的完整编辑状态，包括是否有未提交变更、
    当前生效的执行图、客户端当前图状态以及待提交的变更详情。
    """
    has_pending_changes: bool = Field(..., description="是否有未提交的变更")
    current_effective_graph: ExecutionGraph = Field(..., description="当前会话实际生效的执行图")
    current_graph: ExecutionGraph = Field(..., description="当前客户端对应的执行图（包含未提交的变更）")
    pending_changes: PendingChanges = Field(..., description="未提交的执行图变更详情")

class UpdateNodeParam(BaseModel):
    """节点更新参数，用于更新节点的元数据和配置"""
    metadata: Optional[ObjectMetadata] = Field(default=None, description="要更新的节点元数据")
    op_config: Optional[Dict[str, Any]] = Field(default=None, description="要更新的节点配置参数")


class DagTask(Task[T_Result, T_Resource]):
    """DAG任务接口，扩展标准Task增加依赖关系管理"""
    
    @property
    @abstractmethod
    def node_id(self) -> Optional[str]:
        """获取关联的图节点ID"""
        pass
    
    @property  
    @abstractmethod
    def operator(self) -> Operator:
        """获取关联的算子实例"""
        pass
    
    @property
    @abstractmethod
    def dependencies(self) -> Set[str]:
        """获取直接依赖的任务ID集合"""
        pass
    
    @property
    @abstractmethod 
    def dependents(self) -> Set[str]:
        """获取直接依赖此任务的任务ID集合"""
        pass
    
    @property
    @abstractmethod
    def upstream_changed(self) -> bool:
        """标识上游任务是否发生了变化（用于判断是否需要重跑）"""
        pass


class ExecutionGraphClient(ABC):
    """
    执行图编辑客户端接口，提供面向图编辑的高级API
    支持两种编辑模式：基于节点边的低级编辑和基于算子的高级编辑
    """
    
    @abstractmethod
    async def add_nodes(self, nodes: Sequence[GraphNode]) -> Sequence[str]:
        """
        批量添加图节点
        
        Args:
            nodes: 要添加的节点列表
            
        Returns:
            成功添加的节点ID列表
            
        Raises:
            GraphValidationError: 如果节点验证失败
            ConflictError: 如果存在ID冲突
        """
        pass
    
    @abstractmethod
    async def update_nodes(self, id_ref_configs: Dict[str, UpdateNodeParam]) -> List[str]:
        """
        批量更新节点配置
        
        Args:
            id_ref_configs: 节点ID到UpdateNodeParam的映射
            
        Returns:
            成功更新的节点ID列表
            
        Raises:
            NodeNotFoundError: 如果指定的节点不存在
            GraphValidationError: 如果更新后的配置验证失败
        """
        pass
    
    @abstractmethod
    async def delete_nodes(self, node_ids: Sequence[str]) -> Sequence[str]:
        """
        批量删除图节点
        
        Args:
            node_ids: 要删除的节点ID列表
            
        Returns:
            成功删除的节点ID列表
            
        Raises:
            NodeNotFoundError: 如果指定的节点不存在
            DependencyError: 如果存在其他节点依赖要删除的节点
        """
        pass
    
    @abstractmethod
    async def add_edges(self, edges: Sequence[GraphEdge]) -> Sequence[str]:
        """
        批量添加图边
        
        Args:
            edges: 要添加的边列表
            
        Returns:
            成功添加的边标识列表（通常是source-target格式）
            
        Raises:
            GraphValidationError: 如果边验证失败
            NodeNotFoundError: 如果边引用的节点不存在
        """
        pass
    
    @abstractmethod
    async def delete_edges(self, edges: Sequence[GraphEdge]) -> Sequence[str]:
        """
        批量删除图边
        
        Args:
            edges: 要删除的边列表
            
        Returns:
            成功删除的边标识列表
            
        Raises:
            EdgeNotFoundError: 如果指定的边不存在
        """
        pass
    
    @abstractmethod
    async def add_op(self, op: T_Operator) -> T_Operator:
        """
        添加算子到执行图中，自动生成节点和推断边关系
        
        Args:
            op: 算子实例
            
        Returns:
            添加了task_id或其他标识信息的算子实例
            
        Raises:
            OperatorValidationError: 如果算子验证失败
            GraphValidationError: 如果添加后图结构无效
        """
        pass
    
    @abstractmethod
    async def load_graph(self, graph: ExecutionGraph, merge_mode: MergeMode = MergeMode.FAILED_ON_CONFLICT) -> Sequence[str]:
        """
        加载执行图到当前客户端中
        
        Args:
            graph: 要加载的执行图
            merge_mode: 合并模式，控制如何处理冲突
            
        Returns:
            受影响的节点ID列表
            
        Raises:
            GraphValidationError: 如果执行图验证失败
            ConflictError: 如果存在冲突且合并模式为FAILED_ON_CONFLICT
        """
        pass
    
    @abstractmethod
    async def verify(self) -> VerifyResult:
        """
        异步验证当前图结构的有效性
        
        Returns:
            验证结果，包含整体状态和详细的失败信息
            
        Raises:
            SessionError: 如果会话状态不允许验证
        """
        pass
    
    @abstractmethod
    async def save(self) -> Sequence[str]:
        """
        异步保存当前编辑状态，使配置生效
        内部会调用verify()确保保存的正确性
        
        Returns:
            变更影响的节点ID列表
            
        Raises:
            GraphValidationError: 如果保存前验证失败
            SessionError: 如果会话状态不允许保存
        """
        pass
    
    @abstractmethod
    async def export_graph(self) -> ExecutionGraph:
        """
        异步导出当前生效的执行图
        只能在save()之后调用
        
        Returns:
            当前的执行图对象
            
        Raises:
            ClientStateError: 如果客户端状态不允许导出
        """
        pass
    
    @abstractmethod
    async def resolve_task(self, op: Operator) -> DagTask:
        """
        异步解析算子对应的任务实例
        只能在save()之后调用
        
        Args:
            op: 要解析的算子实例
            
        Returns:
            对应的DAG任务实例
            
        Raises:
            OperatorNotFoundError: 如果算子对应的任务不存在
            ClientStateError: 如果客户端状态不允许解析
        """
        pass

    @abstractmethod
    async def current_edit_status(self) -> ClientEditStatus:
        """
        获取客户端当前编辑状态
        
        Returns:
            客户端当前编辑状态信息
            
        Raises:
            ClientStateError: 如果客户端状态不允许获取编辑状态
        """
        pass

    @abstractmethod
    async def close(self) -> None:
        """
        异步关闭客户端并释放相关资源
        
        如果有未保存的变更，会被丢弃。客户端关闭后不应再被使用。
        
        Raises:
            ClientStateError: 如果客户端状态不允许关闭
        """
        pass


class DataProcessDagSession(DataProcessSession):
    """
    支持DAG调度的数据处理会话接口
    扩展标准会话，增加批量任务管理和执行图操作能力
    """
    
    @abstractmethod
    async def run_task(self, task_ids: Sequence[str], rerun_mode: RerunMode = RerunMode.NEVER) -> Sequence[DagTask]:
        """
        批量运行指定的任务，自动处理依赖关系和执行顺序
        
        Args:
            task_ids: 要运行的任务ID列表
            rerun_mode: 重跑模式，控制如何处理已完成的任务
            
        Returns:
            实际执行的任务列表（可能少于输入的任务数，取决于rerun_mode）
            
        Raises:
            TaskNotFoundError: 如果指定的任务ID不存在
            DagCycleError: 如果检测到循环依赖
            SessionError: 如果会话状态不允许运行任务
        """
        pass
    
    @abstractmethod
    async def delete_task(self, task_ids: Sequence[str]) -> Sequence[str]:
        """
        批量删除指定任务并清理对应的数据资源
        
        Args:
            task_ids: 要删除的任务ID列表
            
        Returns:
            实际删除的任务ID列表（可能少于输入列表，如果某些任务不存在或无法删除）
            
        Raises:
            SessionError: 如果会话状态不允许删除任务
            DependencyError: 如果存在其他任务依赖要删除的任务
        """
        pass
    
    @abstractmethod
    async def export_graph(self) -> ExecutionGraph:
        """
        导出当前会话对应的执行图
        
        Returns:
            包含所有任务和依赖关系的执行图对象
            
        Raises:
            SessionError: 如果会话状态不允许导出
        """
        pass
    
    @abstractmethod
    async def run_all_tasks(self, rerun_mode: RerunMode = RerunMode.NEVER) -> Sequence[DagTask]:
        """
        运行当前会话中的所有任务
        
        Args:
            rerun_mode: 重跑模式
            
        Returns:
            实际执行的任务列表
            
        Raises:
            DagCycleError: 如果检测到循环依赖
            SessionError: 如果会话状态不允许运行任务
        """
        pass

    @abstractmethod
    async def open_graph_client(self) -> ExecutionGraphClient:
        """
        获取执行图客户端接口
        
        Returns:
            执行图客户端接口实例
        """
        pass

# 异常类定义
class DagCycleError(SessionError):
    """DAG中存在循环依赖时抛出"""
    pass

class TaskNotFoundError(SessionError):
    """指定的任务ID不存在时抛出"""
    pass

class DependencyError(SessionError):
    """依赖关系错误时抛出"""
    pass

class GraphValidationError(DataProcessEngineError):
    """执行图验证失败时抛出"""
    pass

class ConflictError(DataProcessEngineError):
    """图编辑操作冲突时抛出"""
    pass

class NodeNotFoundError(DataProcessEngineError):
    """指定的节点不存在时抛出"""
    pass

class EdgeNotFoundError(DataProcessEngineError):
    """指定的边不存在时抛出"""
    pass

class OperatorNotFoundError(BackendError):
    """指定的算子不存在时抛出"""
    pass

class ClientStateError(SessionError):
    """客户端状态不正确时抛出"""
    pass
```

### ExecutionGraph 相关数据结构

```python
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

class ObjectMetadata(BaseModel):
    """对象元数据"""
    display_name: str = Field(default="", description="显示名称")
    description: str = Field(default="", description="描述信息")
    labels: Dict[str, str] = Field(default_factory=dict, description="标签")
    annotations: Dict[str, str] = Field(default_factory=dict, description="注解")

class GraphNode(BaseModel):
    """执行图节点，表示一个算子实例"""
    id: str = Field(..., description="节点唯一标识")
    metadata: ObjectMetadata = Field(default_factory=ObjectMetadata, description="节点元数据")
    op_type: str = Field(..., description="算子类型")
    op_config: Dict[str, Any] = Field(default_factory=dict, description="算子配置参数")

class GraphEdge(BaseModel):
    """执行图边，表示节点间的依赖关系"""
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    target_config: str = Field(default="", description="目标节点的输入配置字段名")

class ExecutionGraph(BaseModel):
    """执行图，定义算子节点及其依赖关系"""
    id: str = Field(..., description="执行图唯一标识")
    metadata: ObjectMetadata = Field(default_factory=ObjectMetadata, description="执行图元数据")
    nodes: Dict[str, GraphNode] = Field(default_factory=dict, description="节点集合")
    edges: List[GraphEdge] = Field(default_factory=list, description="边集合")
    
    def validate_structure(self) -> None:
        """验证图结构的有效性"""
        ...
        
    
    def _has_cycle(self) -> bool:
        """检查图中是否存在循环"""
        ...
```

### 使用示例

#### 兼容原始设计的 run_op 使用示例

```python
import asyncio
from data_process_engine import DataProcessEngine, RowEval
from data_process_operators import ReadStructuredDataResource, FilterStructuredData, TransformStructuredData

async def dag_run_op_example():
    """
    展示DAG会话中使用run_op方法的示例，完全兼容原始设计
    通过ResourceId自动建立任务依赖关系，无需手动管理依赖
    """
    
    # 获取支持DAG的后端和会话 - 兼容原始设计
    backend = DataProcessEngine.backend("duckdb")
    dag_session = await backend.create_dag_session()  # 使用DAG会话替代普通会话
    
    try:
        # 1. 读取全局数据资源 - GlobalResourceId自动适配
        read_op = ReadStructuredDataResource(
            source="global://data-catalog/sales-2023",  # GlobalResourceId
            format="csv",
            options={"header": True}
        )
        
        read_task = await dag_session.run_op(read_op)  # 完全兼容原始run_op接口
        print(f"提交读取任务: {read_task.task_id}")
        
        # 系统自动处理：为GlobalResourceId创建ReadByResourceId适配任务
        # 并替换为SessionResourceId: "session://{session_id}/data-resource/{adapter_task_id}"
        
        # 2. 过滤数据 - SessionResourceId自动依赖
        read_result = await read_task.result(timeout=30)
        filter_op = FilterStructuredData(
            resourceId=read_result.resource_id,  # SessionResourceId - 自动建立对read_task的依赖
            condition=RowEval(expression={
                "type": "FuncCall", 
                "name": "gt", 
                "args": [
                    {"type": "Identifier", "name": "amount"},
                    {"type": "Literal", "val": 1000}
                ]
            })
        )
        
        filter_task = await dag_session.run_op(filter_op)  # 自动识别依赖关系
        print(f"提交过滤任务: {filter_task.task_id} (依赖: {read_task.task_id})")
        
        # 3. 数据转换 - 链式依赖自动建立
        filter_result = await filter_task.result(timeout=30)
        transform_op = TransformStructuredData(
            resourceId=filter_result.resource_id,  # 自动建立对filter_task的依赖
            outputColumns=[
                {
                    "key": "customer_id",
                    "source": "customer_id"
                },
                {
                    "key": "total_amount", 
                    "eval": {
                        "type": "FuncCall",
                        "name": "multiply",
                        "args": [
                            {"type": "Identifier", "name": "amount"},
                            {"type": "Identifier", "name": "quantity"}
                        ]
                    }
                }
            ]
        )
        
        transform_task = await dag_session.run_op(transform_op)
        print(f"提交转换任务: {transform_task.task_id} (依赖: {filter_task.task_id})")
        
        # 4. 混合使用GlobalResourceId和SessionResourceId
        # 假设需要与参考数据进行关联
        join_op = JoinStructuredData(
            leftResourceId=transform_result.resource_id,  # SessionResourceId - 依赖transform_task
            rightResourceId="global://data-catalog/customer-master",  # GlobalResourceId - 自动适配
            joinType="left",
            joinCondition={
                "type": "FuncCall",
                "name": "eq",
                "args": [
                    {"type": "Identifier", "name": "customer_id", "table": "left"},
                    {"type": "Identifier", "name": "id", "table": "right"}
                ]
            }
        )
        
        join_task = await dag_session.run_op(join_op)
        print(f"提交关联任务: {join_task.task_id}")
        
        # 系统自动处理：
        # 1. 识别transform_task为直接上游依赖
        # 2. 为customer-master创建ReadByResourceId适配任务
        # 3. 建立对两个上游任务的依赖关系
        
        # 5. 等待最终结果
        final_result = await join_task.result(timeout=60)
        print(f"数据处理完成，最终资源ID: {final_result.resource_id}")
        
        # 6. 获取处理后的数据资源 - 兼容原始设计
        final_resource_accessor = join_task.get_resource_accessor()
        final_data_resource = await final_resource_accessor.get_resource(timeout=30)
        
        if hasattr(final_data_resource, 'as_arrow_ds'):
            dataset = final_data_resource.as_arrow_ds()
            print(f"处理完成，共 {dataset.count_rows()} 行数据")
        
    except Exception as e:
        print(f"数据处理失败: {e}")
    finally:
        await dag_session.close()
        print("DAG会话已关闭")

# 运行示例
if __name__ == "__main__":
    asyncio.run(dag_run_op_example())
```

**核心特点说明：**

1. **完全兼容原始设计**：
   - 使用相同的`run_op()`方法接口
   - 相同的算子定义和参数结构
   - 相同的任务状态查询和结果获取方式

2. **自动依赖管理**：
   - **GlobalResourceId** (`global://data-catalog/sales-2023`) 自动创建适配任务
   - **SessionResourceId** (`session://session-id/data-resource/task-id`) 自动建立任务依赖
   - 无需手动调用任何依赖管理API

3. **透明的资源转换**：
   - 系统自动为GlobalResourceId创建`ReadByResourceId`适配任务
   - 开发者无感知地使用全局资源和会话资源
   - 保持统一的ResourceId使用体验

4. **零学习成本**：
   - 现有使用`DataProcessSession`的代码只需替换为`DataProcessDagSession`
   - 算子定义、参数配置、结果处理完全不变
   - 自动获得DAG调度和依赖管理能力

#### 传统DAG工作流使用示例

```python
import asyncio
from data_process_engine import DataProcessEngine, RerunMode

async def dag_workflow_example():
    """传统DAG工作流使用示例"""
    
    # 获取支持DAG的后端和会话
    backend = DataProcessEngine.backend("duckdb")
    dag_session = await backend.create_dag_session(
      initial_graph=graph_to_run
    )
    
    try:
        # 运行所有任务
        executed_tasks = dag_session.run_all_tasks(
            rerun_mode=RerunMode.NEVER
        )
        print(f"执行了 {len(executed_tasks)} 个任务")
        
        # 等待所有任务完成
        for task in executed_tasks:
            result = await task.result(timeout=60)
            print(f"任务 {task.task_id} 完成: {result.resource_id}")
        
    finally:
        await dag_session.close()
```
#### ExecutionGraphClient 使用示例

##### 方式1：通过节点和边的API编辑图（通常对应前端或者弱类型的后端调用）

```python
async def graph_edit_example_low_level():
    """使用节点和边API编辑图的示例"""
    
    backend = DataProcessEngine.backend("duckdb")
    dag_session = await backend.create_dag_session()
    
    try:
        async with dag_session.open_graph_client() as client:

            client.add_nodes([
                GraphNode(
                    id="node1",
                    op_type="Read",
                    op_config={"path": "data/sales.csv"}
                )
            ])
            
            client.add_edges([
                GraphEdge(
                    source="node1",
                    target="node2", 
                    target_config="input_left"
                )
            ])

            changed_node_ids = await client.save()
            
            executed_tasks = dag_session.run_task(changed_node_ids)
            
            for task in executed_tasks:
                result = await task.result(timeout=60)
                print(f"任务 {task.task_id} 完成")
            
    finally:
        await dag_session.close()
```

##### 方式2：使用算子编辑图（通常对应后端需要强类型的批量调用场景）

```python
async def graph_edit_example_high_level():
    """使用算子API编辑图的示例"""
    
    backend = DataProcessEngine.backend("duckdb") 
    dag_session = await backend.create_dag_session()
    
    try:
        async with dag_session.open_graph_client() as client:

            read_op = client.add_op(
                ReadOperator(
                    path="data/sales.csv"
                )
            )

            filter_op = client.add_op(
                FilterOperator(
                    input_data=read_op.build_output_resource_id(),
                    condition="amount > 1000"
                )
            )

            changed_node_ids = await client.save()

            executed_tasks = dag_session.run_task(changed_node_ids)

            filter_op_task = await client.resolve_task(filter_op)

            result = await filter_op_task.result(timeout=60)

            output_resource = await filter_op_task.get_resource_accessor().get_data_resource(timeout=60)
            
            print(f"过滤后数据: {output_resource}")
        
    finally:
        await dag_session.close()
```

### 与界面设计的对接

该DAG调度设计完全支持界面设计文档中描述的功能：

1. **执行图管理**：通过 `export_graph()` 和 `load_graph()` 支持执行图的保存和导入
2. **任务创建**：通过 `add_nodes()` 和 `add_edges()` 支持基于算子配置创建任务节点
3. **批量运行**：通过 `run_task()` 和 `run_all_tasks()` 支持批量任务执行
4. **依赖管理**：自动处理任务间的依赖关系和执行顺序
5. **状态监控**：继承现有的任务状态监控能力，支持实时状态更新
6. **重跑策略**：提供灵活的重跑模式，优化执行效率

## 设计总结

### 核心创新：以ResourceId为中心的DAG自动维护

本设计的最大创新是**以ResourceId为核心的DAG自动维护机制**，这一设计理念彻底改变了传统DAG系统需要手动管理依赖关系的复杂性：

#### 🎯 **零配置依赖管理**
- 开发者只需要在算子字段中使用ResourceId类型，系统自动识别并建立依赖关系
- 支持4种ResourceId类型的自动识别：`ResourceId`、`Optional[ResourceId]`、`List[ResourceId]`、`Optional[List[ResourceId]]`
- 完全基于类型系统，提供编译时安全保障

#### 🔄 **智能资源适配**
- **SessionResourceId**：直接建立任务间依赖，零额外开销
- **GlobalResourceId**：自动创建`ReadByResourceId`适配任务，实现全局到会话资源的透明转换
- **资源复用**：相同的GlobalResourceId只创建一个适配任务，优化执行效率

#### 🚀 **开发体验革命**
- **声明式编程**：开发者只需声明需要什么资源，无需关心如何获取
- **类型驱动**：通过类型系统确保ResourceId使用的正确性
- **自动优化**：系统自动处理资源复用和依赖优化

### 企业级能力

#### 📊 **完整的DAG调度功能**
- **DataProcessDagSession**：扩展现有会话，提供完整的DAG调度能力
- **ExecutionGraphClient**：双层API设计，支持低级图编辑和高级算子操作
- **批量任务管理**：支持批量运行、删除和状态管理

#### 🔧 **灵活的重跑策略**
- **NEVER**：从不重跑已成功的任务，优化执行效率
- **FORCE**：强制重跑所有指定任务，支持完整重新计算
- **FAILED_ONLY**：仅重跑失败的任务，智能故障恢复

#### 🏗️ **可扩展架构**
- **向下兼容**：完全兼容现有`DataProcessSession`接口
- **类型安全**：保持泛型系统的完整性
- **异步友好**：所有DAG操作都支持异步执行

### 与现有框架的完美融合

本设计在保持与现有数据处理引擎完全兼容的同时，为其注入了强大的DAG调度能力。通过ResourceId为核心的设计理念，我们实现了：

- **无缝升级**：现有单任务代码无需修改即可获得DAG能力
- **渐进式采用**：可以逐步将单任务工作流升级为DAG工作流
- **统一体验**：DAG和单任务使用相同的算子和资源模型

这个设计不仅解决了传统DAG系统的复杂性问题，更为数据处理领域带来了全新的开发范式，让复杂的数据处理工作流变得简单、直观、可维护。