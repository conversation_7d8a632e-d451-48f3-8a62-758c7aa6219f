# 数据处理引擎详细设计-DAG调度相关-设计实现

## 1. 设计概述

本文档详细描述了DAG调度器(DagScheduler)的设计实现方案，包括其核心接口、组件交互以及关键实现细节。DagScheduler作为一个独立组件，专注于管理DAG的状态和调度逻辑，不直接涉及具体的任务执行实现。

### 1.1 设计原则

- **关注点分离**: DagScheduler专注于DAG结构管理和调度逻辑，不涉及具体任务执行
- **松耦合**: 通过Protocol接口与Session交互，避免直接依赖
- **线程安全**: 使用读写锁(RWLock)确保对DAG的并发操作是安全的，支持多线程并发读取DAG状态，同时保证写操作的互斥性
- **可扩展性**: 设计灵活的接口，支持不同的执行引擎和调度策略

### 1.2 核心职责

- 维护DAG的结构（节点和边）
- 管理任务依赖关系
- 控制任务的执行顺序
- 处理任务状态变化和传播
- 实现不同的重跑策略
- 提供ExecutionGraphClient接口

## 2. 核心组件与交互

### 2.1 核心组件关系图

```mermaid
classDiagram
    class DagScheduler {
        -_dag: DirectedGraph
        -_tasks: Dict[str, DagTaskInfo]
        -_session_interact: SessionInteractProtocol
        -_lock: RWLock
        +run_op(operator: Operator): DagTask
        +run_tasks(task_ids: Seq[str], rerun_mode: RerunMode): Seq[DagTask]
        +delete_tasks(task_ids: Seq[str]): Seq[str]
        +get_execution_graph(): ExecutionGraph
        +open_graph_client(): ExecutionGraphClient
        +apply_graph_changes(changes: GraphChanges): Seq[str]
    }

    class SessionInteractProtocol {
        <<Protocol>>
        +convert_operator_to_runnable(op: Operator): Callable[[], OperatorResult]
        +submit_runnable(runnable: Callable[[], OperatorResult]): Future[OperatorResult]
        +resolve_resource_for_result(result: OperatorResult): IResource
    }

    class GraphExecutionClientImpl {
        -_scheduler: DagScheduler
        -_pending_changes: GraphChanges
        +add_nodes(nodes: Seq[GraphNode]): Seq[str]
        +update_nodes(id_configs: Dict): Seq[str]
        +delete_nodes(node_ids: Seq[str]): Seq[str]
        +add_edges(edges: Seq[GraphEdge]): Seq[str]
        +delete_edges(edges: Seq[GraphEdge]): Seq[str]
        +add_op(op: Operator): Operator
        +load_graph(graph: ExecutionGraph): Seq[str]
        +verify(): VerifyResult
        +save(): Seq[str]
        +resolve_task(op: Operator) -> DagTask
        +current_edit_status(): ClientEditStatus
        +close() -> None
    }

    class GraphChanges {
        +nodes_to_add: Dict[str, GraphNode]
        +nodes_to_update: Dict[str, Dict]
        +nodes_to_delete: Set[str]
        +edges_to_add: List[GraphEdge]
        +edges_to_delete: List[GraphEdge]
        +operators_to_add: Dict[str, Operator]
    }

    class TaskDependencyManager {
        -_graph: DirectedGraph
        +add_dependency(source: str, target: str): void
        +remove_dependency(source: str, target: str): void
        +get_dependencies(task_id: str): Set[str]
        +get_dependents(task_id: str): Set[str]
        +has_cycle(): bool
        +topological_sort(): List[str]
    }

    DagScheduler --> SessionInteractProtocol: uses
    DagScheduler --> TaskDependencyManager: contains
    DagScheduler ..> GraphExecutionClientImpl: provides
    GraphExecutionClientImpl --> DagScheduler: submits changes to
    GraphExecutionClientImpl --> GraphChanges: manages
    DagScheduler --> GraphChanges: applies
```

### 2.2 交互流程图

```mermaid
sequenceDiagram
    participant Client
    participant DagScheduler
    participant GraphClient as GraphExecutionClientImpl
    participant SessionInteract
    participant TaskDependencyManager

    %% 路径1：直接run_op执行单个任务
    Client->>DagScheduler: run_op(operator)
    DagScheduler->>DagScheduler: _extract_dependencies(operator)
    DagScheduler->>DagScheduler: _create_task_internal(task_id, operator, dependencies)
    DagScheduler->>TaskDependencyManager: register dependencies
    DagScheduler->>DagScheduler: _run_task(task_id)
    DagScheduler->>SessionInteract: convert_operator_to_runnable(task.operator)
    SessionInteract-->>DagScheduler: runnable
    DagScheduler->>SessionInteract: submit_runnable(runnable)
    SessionInteract-->>DagScheduler: future
    DagScheduler->>DagScheduler: handle_future_completion(future, task_id)
    DagScheduler-->>Client: task
    
    %% 路径2：通过客户端编辑图并执行
    Client->>GraphClient: add_op(operator)
    GraphClient->>GraphClient: store in pending_changes
    GraphClient-->>Client: Updated Operator (with task_id)
    
    Client->>GraphClient: add_edge(edge)
    GraphClient->>GraphClient: store in pending_changes
    GraphClient-->>Client: edge_id
    
    Client->>GraphClient: save()
    GraphClient->>DagScheduler: apply_graph_changes(pending_changes)
    
    DagScheduler->>TaskDependencyManager: verify_no_cycles()
    TaskDependencyManager-->>DagScheduler: verified
    
    DagScheduler->>DagScheduler: for each op in operators_to_add
    DagScheduler->>DagScheduler: _extract_dependencies(operator)
    DagScheduler->>DagScheduler: _create_task_internal(task_id, operator, dependencies)
    DagScheduler->>TaskDependencyManager: register dependencies
    
    DagScheduler-->>GraphClient: affected_node_ids
    GraphClient->>GraphClient: clear pending_changes
    GraphClient-->>Client: affected_node_ids
    
    Client->>DagScheduler: run_tasks(task_ids, rerun_mode)
    DagScheduler->>TaskDependencyManager: get_execution_order(task_ids)
    TaskDependencyManager-->>DagScheduler: ordered_tasks
    
    loop For each ready task
        DagScheduler->>DagScheduler: _should_rerun_task(task_id, rerun_mode)
        alt Task should be run
            DagScheduler->>SessionInteract: convert_operator_to_runnable(task.operator)
            SessionInteract-->>DagScheduler: runnable
            DagScheduler->>SessionInteract: submit_runnable(runnable)
            SessionInteract-->>DagScheduler: future
            DagScheduler->>DagScheduler: update_task_status(task_id, RUNNING)
            DagScheduler->>DagScheduler: handle_future_completion(future, task_id)
        end
    end
    
    DagScheduler-->>Client: executed_tasks
```

## 3. 核心实现接口

### 3.1 Operator 静态方法

#### Operator.get_type_by_code

```python
@staticmethod
def get_type_by_code(type_code: str) -> Type['Operator']:
    """
    根据算子类型代码获取对应的Operator类型
    
    这是一个静态方法，可以在整个系统中使用，无需依赖具体的会话实例。
    该方法通过递归搜索 Operator 的所有子类来查找匹配的算子类型。
    
    Args:
        type_code: 算子类型代码（通常是算子类名）
        
    Returns:
        对应的算子类型
        
    Raises:
        ValueError: 如果找不到对应的算子类型
        
    Usage:
        # 查找算子类型
        op_type = Operator.get_type_by_code("ReadStructuredDataResource")
        
        # 创建算子实例
        instance = op_type(**operator_config)
    """
```

### 3.2. SessionInteractProtocol

```python
class SessionInteractProtocol(Protocol):
    """Session交互协议，定义DagScheduler与Session的交互方式"""
    
    def get_operator_type_by_type_code(self, operator_type: str) -> Type['Operator']:
        """
        根据算子类型代码获取对应的Operator类型
        
        Note: 此方法已废弃，建议直接使用 Operator.get_type_by_code 静态方法
        """
        ...
        
    def convert_operator_to_runnable(self, op: 'Operator') -> Callable[[], 'OperatorResult']:
        """将算子转换为可执行的函数"""
        ...
        
    def submit_runnable(self, runnable: Callable[[], 'OperatorResult']) -> Future['OperatorResult']:
        """提交可执行函数到执行环境，返回Future对象"""
        ...
        
    def resolve_resource_for_result(self, result: 'OperatorResult') -> 'IResource':
        """从任务结果解析资源对象"""
        ...
```

### 3.3 DagScheduler

```python
@dataclass 
class GraphChanges:
    """图变更集合，记录未提交的图变更"""
    nodes_to_add: Dict[str, GraphNode] = field(default_factory=dict)
    nodes_to_update: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    nodes_to_delete: Set[str] = field(default_factory=set)
    edges_to_add: List[GraphEdge] = field(default_factory=list)
    edges_to_delete: List[GraphEdge] = field(default_factory=list)
    operators_to_add: Dict[str, Operator] = field(default_factory=dict)

class DagScheduler:
    """DAG调度器，负责管理DAG的状态和任务调度"""
    
    def __init__(self, session_interact: SessionInteractProtocol, session_id: str):
        """
        初始化DAG调度器
        
        Args:
            session_interact: Session交互接口
            session_id: 会话ID
        """
        self._session_interact = session_interact
        self._session_id = session_id
        self._tasks: Dict[str, DagTaskInfo] = {}
        self._dependency_manager = TaskDependencyManager()
        self._lock = threading.RWLock()  # 使用读写锁，保证读取DAG状态时也是线程安全的
        
    async def run_op(self, operator: 'Operator') -> 'DagTask':
        """
        运行单个算子，创建并执行对应的任务
        
        完全兼容DataProcessSession.run_op接口，为单个算子创建任务并立即执行。
        同时会自动处理算子的依赖关系。
        
        Args:
            operator: 算子实例
            
        Returns:
            创建并执行的DAG任务实例
            
        Raises:
            ValidationError: 如果算子验证失败
        """
        # 创建并执行任务的逻辑
        # 1. 分析算子依赖
        dependencies = self._extract_dependencies_from_operator(operator)
        
        # 2. 创建任务
        task_id = operator.task_id or str(uuid.uuid4())
        task = self._create_task_internal(task_id, operator, dependencies)
        
        # 3. 立即执行任务
        await self._run_task(task_id)
        
        return task
        
    async def run_tasks(self, task_ids: Sequence[str], 
                       rerun_mode: RerunMode = RerunMode.NEVER) -> Sequence['DagTask']:
        """
        运行指定的任务
        
        Args:
            task_ids: 要运行的任务ID列表
            rerun_mode: 重跑模式
            
        Returns:
            实际执行的任务列表
            
        Raises:
            TaskNotFoundError: 如果指定的任务不存在
            DagCycleError: 如果检测到循环依赖
        """
        ...
        
    async def delete_tasks(self, task_ids: Sequence[str]) -> Sequence[str]:
        """
        删除指定的任务
        
        Args:
            task_ids: 要删除的任务ID列表
            
        Returns:
            成功删除的任务ID列表
            
        Raises:
            TaskNotFoundError: 如果指定的任务不存在
            DependencyError: 如果存在其他任务依赖要删除的任务
        """
        ...
        
    async def apply_graph_changes(self, changes: GraphChanges) -> Sequence[str]:
        """
        应用图变更
        
        此方法是线程安全的，会验证图结构并应用变更。
        所有对DAG结构的变更都应该通过此方法进行。
        
        Args:
            changes: 图变更集合
            
        Returns:
            受影响的节点ID列表
            
        Raises:
            GraphValidationError: 如果图结构验证失败
            OperatorTypeNotFoundError: 如果找不到算子类型
        """
        with self._lock.write_lock():  # 使用写锁进行互斥访问
            # 验证图结构
            self._verify_graph_changes(changes)
            
            # 应用节点变更
            affected_nodes = []
            
            # 处理添加的节点
            for node_id, node in changes.nodes_to_add.items():
                # ...处理节点添加逻辑
                affected_nodes.append(node_id)
                
            # 处理添加的算子
            for op_id, op in changes.operators_to_add.items():
                # 分析算子依赖
                dependencies = self._extract_dependencies_from_operator(op)
                
                # 内部创建任务
                self._create_task_internal(op_id, op, dependencies)
                affected_nodes.append(op_id)
                
            # 处理更新的节点配置
            for node_id, config in changes.nodes_to_update.items():
                # ...处理节点配置更新逻辑
                affected_nodes.append(node_id)
                
            # 处理删除的节点
            for node_id in changes.nodes_to_delete:
                await self.delete_tasks([node_id])
                affected_nodes.append(node_id)
                
            # 处理添加的边
            for edge in changes.edges_to_add:
                self._dependency_manager.add_dependency(edge.source, edge.target)
                
            # 处理删除的边
            for edge in changes.edges_to_delete:
                self._dependency_manager.remove_dependency(edge.source, edge.target)
                
            return affected_nodes
            
    def _verify_graph_changes(self, changes: GraphChanges) -> None:
        """
        验证图变更是否有效
        
        对传入的图变更进行验证，确保应用变更后的图结构仍然有效。
        
        Args:
            changes: 图变更集合
            
        Raises:
            GraphValidationError: 如果图结构验证失败
        """
        # 复制当前图进行验证
        temp_dependency_manager = copy.deepcopy(self._dependency_manager)
        
        # 应用所有边的变更
        for edge in changes.edges_to_add:
            temp_dependency_manager.add_dependency(edge.source, edge.target)
            
        # 检查是否有循环
        if temp_dependency_manager.has_cycle():
            raise GraphValidationError("应用变更后的图结构存在循环")
        
        # 可以添加更多的验证逻辑
        
    def get_task(self, task_id: str) -> Optional['DagTask']:
        """
        获取指定的任务实例
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务实例，如果不存在则返回None
        """
        with self._lock.read_lock():  # 获取读锁，允许并发读取
            task_info = self._tasks.get(task_id)
            return task_info.task if task_info else None
        
    async def get_execution_graph(self) -> 'ExecutionGraph':
        """
        获取当前执行图
        
        Returns:
            当前的执行图对象
        """
        ...
        
    def open_graph_client(self) -> 'ExecutionGraphClient':
        """
        获取执行图客户端
        
        每次调用都创建一个新的客户端实例，不同客户端之间互不影响，
        拥有独立的pending_changes状态。
        
        Returns:
            执行图客户端实例
        """
        return GraphExecutionClientImpl(self)

    def _create_task_internal(self, task_id: str, operator: 'Operator', dependencies: Set[str]) -> 'DagTask':
        """
        内部方法：创建任务实例
        
        Args:
            task_id: 任务ID
            operator: 算子实例
            dependencies: 依赖的任务ID集合
            
        Returns:
            创建的任务实例
        """
        with self._lock.write_lock():  # 获取写锁，互斥访问
            # 创建任务并更新DAG状态
            task = DagTaskImpl(task_id, self._session_id, operator, self)
            self._tasks[task_id] = DagTaskInfo(task=task, status=TaskStatus.PENDING)
            
            # 建立依赖关系
            for dep_id in dependencies:
                self._dependency_manager.add_dependency(dep_id, task_id)
                
            return task

    async def _run_task(self, task_id: str) -> 'DagTask':
        """
        运行单个任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务实例
            
        Raises:
            TaskNotFoundError: 如果任务不存在
        """
        ...
        
    def _extract_dependencies_from_operator(self, operator: 'Operator') -> Set[str]:
        """
        从算子中提取依赖关系
        
        基于ResourceId类型字段分析算子的上游依赖，是自动依赖管理的核心实现。
        该方法内部使用ResourceIdUtils工具类进行ResourceId分析和处理。
        
        Args:
            operator: 算子实例
            
        Returns:
            依赖的任务ID集合
        """
        # 实现已移至5.3节 "DagScheduler与ResourceIdUtils的交互"

    def _create_adapter_task_for_global_resource(self, resource_id: str) -> str:
        """
        为全局资源创建适配任务
        
        当算子引用 GlobalResourceId 时，自动创建一个适配任务将ResourceId对应的资源加载到会话中，如果 GlobalResourceId 的 resource_type 是 DATA_RESOURCE、则创建 LoadDataResourceByResourceId。
        将全局资源转换为会话内可访问的资源。
        
        Args:
            resource_id: 全局资源ID
            
        Returns:
            创建的适配任务ID
            
        Notes:
            此方法会检查缓存，避免为同一全局资源重复创建适配任务。
        """

    def _replace_global_resource_with_session_resource(self, 
                                                 operator: 'Operator', 
                                                 field_name: str, 
                                                 adapter_task_id: str) -> None:
        """
        替换算子中的全局资源引用为会话资源引用
        
        当为GlobalResourceId创建适配任务后，需要修改原算子的对应字段，
        将其替换为指向适配任务输出资源的SessionResourceId。
        
        Args:
            operator: 要修改的算子实例
            field_name: 要替换的字段名
            adapter_task_id: 适配任务ID
        """

    def _should_rerun_task(self, task_id: str, rerun_mode: RerunMode, upstream_changed: bool = False) -> bool:
        """
        根据重跑模式判断任务是否需要重新运行
        
        基于当前任务状态、重跑模式和上游状态变化，判断任务是否需要重新执行。
        
        Args:
            task_id: 任务ID
            rerun_mode: 重跑模式
            upstream_changed: 上游任务是否有变化
            
        Returns:
            是否需要重新运行任务
        """

    def _mark_downstream_as_upstream_changed(self, task_id: str) -> None:
        """
        标记下游任务的上游已变更
        
        当任务重新执行或状态变更时，需要将其所有下游任务标记为上游已变更，
        用于后续判断是否需要重新运行。
        
        Args:
            task_id: 发生变更的任务ID
        """

    def _handle_task_completion(self, future: Future['OperatorResult'], task_id: str) -> None:
        """
        处理任务完成事件
        
        当任务执行完成后（无论成功或失败）的回调处理，更新任务状态、
        处理结果或异常，并触发下游任务状态更新。
        
        Args:
            future: 包含任务结果的Future对象
            task_id: 任务ID
        """

    def _get_execution_order(self, task_ids: Sequence[str]) -> List[str]:
        """
        获取任务的执行顺序
        
        根据任务依赖关系，计算一组任务的拓扑排序顺序，并包含所有必要的上游任务。
        
        Args:
            task_ids: 目标任务ID列表
        
        Returns:
            按执行顺序排列的任务ID列表，包括所有必要的上游任务
            
        Raises:
            DagCycleError: 如果检测到循环依赖
        """
```

### 3.3 GraphExecutionClientImpl

```python
class GraphExecutionClientImpl:
    """执行图客户端实现"""
    
    def __init__(self, scheduler: DagScheduler):
        """
        初始化执行图客户端
        
        Args:
            scheduler: DAG调度器实例
        """
        self._scheduler = scheduler
        self._pending_changes = GraphChanges()
        
    async def add_nodes(self, nodes: Sequence['GraphNode']) -> Sequence[str]:
        """
        批量添加图节点（仅存储在待提交变更中）
        
        Args:
            nodes: 要添加的节点列表
            
        Returns:
            添加的节点ID列表
        """
        node_ids = []
        for node in nodes:
            self._pending_changes.nodes_to_add[node.id] = node.copy(deep=True)
            node_ids.append(node.id)
        return node_ids
            
    async def update_nodes(self, id_ref_configs: Dict[str, UpdateNodeParam]) -> List[str]:
        """
        批量更新节点配置（仅存储在待提交变更中）
        
        Args:
            id_ref_configs: 节点ID到UpdateNodeParam的映射
            
        Returns:
            更新的节点ID列表
        """
        for node_id, update_param in id_ref_configs.items():
            self._pending_changes.nodes_to_update[node_id] = update_param
        return list(id_ref_configs.keys())
            
    async def delete_nodes(self, node_ids: Sequence[str]) -> Sequence[str]:
        """
        批量删除图节点（仅存储在待提交变更中）
        
        Args:
            node_ids: 要删除的节点ID列表
            
        Returns:
            删除的节点ID列表
        """
        for node_id in node_ids:
            self._pending_changes.nodes_to_delete.add(node_id)
            # 如果节点在待添加列表中，直接移除
            self._pending_changes.nodes_to_add.pop(node_id, None)
            self._pending_changes.nodes_to_update.pop(node_id, None)
        return list(node_ids)
            
    async def add_edges(self, edges: Sequence['GraphEdge']) -> Sequence[str]:
        """
        批量添加图边（仅存储在待提交变更中）
        
        Args:
            edges: 要添加的边列表
            
        Returns:
            添加的边标识列表
        """
        edge_ids = []
        for edge in edges:
            self._pending_changes.edges_to_add.append(edge.copy(deep=True))
            edge_ids.append(f"{edge.source}-{edge.target}")
        return edge_ids
            
    async def delete_edges(self, edges: Sequence['GraphEdge']) -> Sequence[str]:
        """
        批量删除图边（仅存储在待提交变更中）
        
        Args:
            edges: 要删除的边列表
            
        Returns:
            删除的边标识列表
        """
        edge_ids = []
        for edge in edges:
            self._pending_changes.edges_to_delete.append(edge.copy(deep=True))
            edge_ids.append(f"{edge.source}-{edge.target}")
        return edge_ids
            
    async def add_op(self, op: 'Operator') -> 'Operator':
        """
        添加算子到执行图中（仅存储在待提交变更中）
        
        Args:
            op: 算子实例
            
        Returns:
            添加了task_id或其他标识信息的算子实例
        """
        # 生成任务ID（如果未设置）
        if not hasattr(op, 'task_id') or not op.task_id:
            op.task_id = str(uuid.uuid4())
            
        # 存储到待添加算子中
        self._pending_changes.operators_to_add[op.task_id] = op.copy(deep=True)
        return op
            
    async def load_graph(self, graph: 'ExecutionGraph', 
                       merge_mode: MergeMode = MergeMode.FAILED_ON_CONFLICT) -> Sequence[str]:
        """
        加载执行图（仅存储在待提交变更中）
        
        Args:
            graph: 要加载的执行图
            merge_mode: 合并模式
            
        Returns:
            影响的节点ID列表
        """
        affected_nodes = []
        
        # 根据合并模式处理节点
        for node_id, node in graph.nodes.items():
            if node_id in self._pending_changes.nodes_to_add:
                if merge_mode == MergeMode.FAILED_ON_CONFLICT:
                    raise ConflictError(f"节点ID冲突: {node_id}")
                elif merge_mode == MergeMode.OVERWRITE_EXISTING:
                    self._pending_changes.nodes_to_add[node_id] = node.copy(deep=True)
            else:
                self._pending_changes.nodes_to_add[node_id] = node.copy(deep=True)
                
            affected_nodes.append(node_id)
            
        # 处理边
        for edge in graph.edges:
            self._pending_changes.edges_to_add.append(edge.copy(deep=True))
            
        return affected_nodes
            
    async def verify(self) -> 'VerifyResult':
        """
        验证当前待提交变更的有效性
        
        Returns:
            验证结果
        """
        try:
            # 创建临时的GraphChanges进行验证
            temp_changes = copy.deepcopy(self._pending_changes)
            
            # 调用调度器的验证方法（内部不会实际应用变更）
            self._scheduler._verify_graph_changes(temp_changes)
            
            # 验证通过
            return VerifyResult(valid=True)
        except Exception as e:
            # 验证失败
            return VerifyResult(
                valid=False,
                failed_details=[
                    VerifyFailureDetail(
                        rule_type="graph_validation",
                        message=str(e),
                        extra_info={}
                    )
                ]
            )
            
    async def save(self) -> Sequence[str]:
        """
        保存当前编辑状态，将变更提交到调度器
        
        Returns:
            变更影响的节点ID列表
        """
        # 复制当前的待提交变更
        changes_to_apply = copy.deepcopy(self._pending_changes)
        
        # 提交变更到调度器
        affected_nodes = await self._scheduler.apply_graph_changes(changes_to_apply)
        
        # 清空待处理变更
        self._pending_changes = GraphChanges()
        
        return affected_nodes
            
    async def export_graph(self) -> 'ExecutionGraph':
        """
        导出当前生效的执行图
        
        Returns:
            当前执行图对象
        """
        return await self._scheduler.get_execution_graph()
            
    async def resolve_task(self, op: 'Operator') -> 'DagTask':
        """
        解析算子对应的任务实例
        
        Args:
            op: 要解析的算子实例
            
        Returns:
            对应的DAG任务实例
        """
        if not hasattr(op, 'task_id') or not op.task_id:
            raise ValueError("算子没有task_id")
            
        task = self._scheduler.get_task(op.task_id)
        if not task:
            raise OperatorNotFoundError(f"找不到算子对应的任务: {op.task_id}")
            
        return task
            
    async def close(self) -> None:
        """
        关闭客户端并释放相关资源
        
        如果有未保存的变更，会被丢弃。客户端关闭后不应再被使用。
        
        Returns:
            None
        """
        # 清空待处理变更
        self._pending_changes = GraphChanges()
        # 清除其他可能持有的资源引用
        self._scheduler = None

    def current_edit_status(self) -> 'ClientEditStatus':
        """
        获取当前客户端的编辑状态
        
        Returns:
            客户端编辑状态信息
        """
        return ClientEditStatus(
            has_pending_changes=bool(self._pending_changes.nodes_to_add or self._pending_changes.nodes_to_update or self._pending_changes.nodes_to_delete or self._pending_changes.edges_to_add or self._pending_changes.edges_to_delete or self._pending_changes.operators_to_add),
            current_effective_graph=await self.export_graph(),
            current_graph=await self.export_graph(),
            pending_changes=self._pending_changes
        )
```

## 4. 关键实现方法

本节描述其他关键组件的实现方法，重点关注依赖分析和任务管理相关的核心逻辑。

### 4.1 TaskDependencyManager关键方法

TaskDependencyManager负责高效地管理任务间的依赖关系，支持快速的依赖查询和图分析操作。

```python
class TaskDependencyManager:
    """任务依赖关系管理器"""
    
    def add_dependency(self, source: str, target: str) -> None:
        """
        添加依赖关系：target依赖于source
        
        Args:
            source: 源任务ID（上游）
            target: 目标任务ID（下游）
            
        Raises:
            ValueError: 如果添加此依赖会导致循环
        """
    
    def remove_dependency(self, source: str, target: str) -> None:
        """
        移除依赖关系
        
        Args:
            source: 源任务ID（上游）
            target: 目标任务ID（下游）
            
        Returns:
            bool: 是否成功移除
        """
    
    def get_dependencies(self, task_id: str) -> Set[str]:
        """
        获取任务的直接依赖（上游任务）
        
        Args:
            task_id: 任务ID
            
        Returns:
            依赖任务ID集合
        """
    
    def get_dependents(self, task_id: str) -> Set[str]:
        """
        获取依赖于该任务的任务（下游任务）
        
        Args:
            task_id: 任务ID
            
        Returns:
            下游任务ID集合
        """
    
    def get_all_dependencies(self, task_id: str) -> Set[str]:
        """
        获取任务的所有依赖（包括间接依赖）
        
        Args:
            task_id: 任务ID
            
        Returns:
            所有上游任务ID集合（包括直接和间接依赖）
        """
    
    def has_cycle(self) -> bool:
        """
        检查图中是否存在环
        
        Returns:
            是否存在环
        """
    
    def topological_sort(self, start_nodes: Optional[Sequence[str]] = None) -> List[str]:
        """
        对图进行拓扑排序
        
        Args:
            start_nodes: 起始节点列表，如果提供，则返回包含这些节点及其所有依赖的排序
            
        Returns:
            拓扑排序结果
            
        Raises:
            DagCycleError: 如果图中存在环
        """
```

### 4.2 ResourceId分析与处理

ResourceId分析与处理是实现自动依赖管理的核心机制。在之前的设计中，这些功能直接实现在DagScheduler类中，现在已经被抽取到独立的ResourceIdUtils工具类中，实现关注点分离，同时复用了Operator类中的功能。详细实现请参考第5章"ResourceId工具模块设计"。

DagScheduler中以下方法：

```python
def _extract_dependencies_from_operator(self, operator: 'Operator') -> Set[str]:
    """
    从算子中提取依赖关系
    
    基于ResourceId类型字段分析算子的上游依赖，是自动依赖管理的核心实现。
    该方法内部使用ResourceIdUtils工具类进行ResourceId分析和处理。
    
    Args:
        operator: 算子实例
        
    Returns:
        依赖的任务ID集合
    """
    # 实现已移至5.3节 "DagScheduler与ResourceIdUtils的交互"
```

## 5. ResourceId工具模块设计

为了提高代码复用性并保持关注点分离，将与ResourceId解析和处理相关的功能从DagScheduler中抽离为独立的工具模块。这些功能与DAG调度关联较弱，但对于整个数据处理引擎是通用且基础的能力。

### 5.1 ResourceIdUtils模块

```python
class ResourceIdFieldType(Enum):
    """ResourceId字段类型枚举"""
    RESOURCE_ID = "resource_id"
    OPTIONAL_RESOURCE_ID = "optional_resource_id"
    LIST_RESOURCE_ID = "list_resource_id"
    OPTIONAL_LIST_RESOURCE_ID = "optional_list_resource_id"


class ResourceIdUtils:
    """ResourceId工具类，提供对资源ID的解析和处理能力"""
    
    @staticmethod
    def analyze_field_type(field: Field) -> Optional[ResourceIdFieldType]:
        """
        分析字段类型，确定ResourceId字段类型
        
        通过反射和类型检查确定字段是否是以下支持的类型之一：
        - ResourceId
        - Optional[ResourceId]
        - List[ResourceId]
        - Optional[List[ResourceId]]
        
        复用Operator元类中的类型检查逻辑，基于Python类型注解系统识别ResourceId字段。
        
        Args:
            field: 字段定义对象
                
        Returns:
            字段的ResourceId类型，如果不是ResourceId相关类型则返回None
        """
        ...
        
    @staticmethod
    def extract_resource_ids_from_field(
        obj: Any,
        field_name: str,
        field_type: ResourceIdFieldType
    ) -> List[str]:
        """
        从对象字段中提取所有的ResourceId值
        
        根据字段类型，提取字段值中的所有资源ID，包括处理列表和可选类型。
        复用Operator的model_dump方法中处理ResourceId的逻辑，确保统一的字符串表示。
        
        Args:
            obj: 对象实例
            field_name: 字段名称
            field_type: 字段的ResourceId类型
                
        Returns:
            提取的资源ID列表
        """
        ...
        
    @staticmethod
    def parse_resource_id(resource_id: str) -> Tuple[str, str, str]:
        """
        解析资源ID格式，提取关键组成部分
        
        解析格式如：protocol://location/type/identifier
        
        Args:
            resource_id: 资源ID字符串
                
        Returns:
            元组(protocol, location, identifier)
                
        Raises:
            ValueError: 如果资源ID格式不正确
        """
        ...
        
    @staticmethod
    def is_session_resource_id(resource_id: str) -> bool:
        """
        判断资源ID是否为会话资源ID
        
        会话资源ID格式为：session://session-id/resource-type/task-id
        
        Args:
            resource_id: 资源ID
                
        Returns:
            是否为会话资源ID
        """
        ...
        
    @staticmethod
    def extract_task_id_from_session_resource_id(resource_id: str) -> str:
        """
        从会话资源ID中提取任务ID
        
        从格式为session://session-id/resource-type/task-id的资源ID中提取task-id部分
        
        Args:
            resource_id: 会话资源ID
                
        Returns:
            任务ID
                
        Raises:
            ValueError: 如果资源ID不是有效的会话资源ID
        """
        ...

    @staticmethod
    def build_session_resource_id(session_id: str, resource_type: ResourceType, task_id: str) -> SessionResourceId:
        """
        构建会话资源ID
        
        根据会话ID、资源类型和任务ID构建标准格式的SessionResourceId
        复用Operator的build_output_resource_id方法中的逻辑
        
        Args:
            session_id: 会话ID
            resource_type: 资源类型
            task_id: 任务ID
            
        Returns:
            构建的SessionResourceId对象
            
        Raises:
            ValueError: 如果参数无效
        """
        ...
```

### 5.2 与Operator类的关系

ResourceIdUtils工具类与Operator类有紧密的协作关系：

1. **复用类型检查逻辑**：
   - `analyze_field_type` 方法复用了 Operator 元类中的类型检查逻辑
   - 利用 Python 类型注解系统识别 ResourceId 相关字段类型

2. **一致的资源ID表示**：
   - 复用 Operator 的 `model_dump` 方法中处理 ResourceId 的逻辑
   - 确保 ResourceId 在整个系统中有统一的字符串表示

3. **构建资源ID**：
   - 提供 `build_session_resource_id` 方法，与 Operator 的 `build_output_resource_id` 方法保持一致
   - 确保资源ID的构建逻辑统一

### 5.3 DagScheduler与ResourceIdUtils的交互

DagScheduler在处理任务依赖分析时，使用ResourceIdUtils工具类进行ResourceId的解析和处理：

```python
def _extract_dependencies_from_operator(self, operator: 'Operator') -> Set[str]:
    """
    从算子中提取依赖关系
    
    基于ResourceId类型字段分析算子的上游依赖，是自动依赖管理的核心实现。
    该方法使用ResourceIdUtils工具类处理ResourceId，并负责依赖关系的建立。
    
    Args:
        operator: 算子实例
        
    Returns:
        依赖的任务ID集合
    """
    # 1. 初始化依赖集合
    dependencies = set()
    
    # 2. 获取算子类型定义的input_fields
    op_class = type(operator)
    if not hasattr(op_class, 'input_fields'):
        return dependencies
    
    # 3. 遍历每个input字段
    for field_name in op_class.input_fields:
        # 使用ResourceIdUtils分析字段类型
        field = op_class.__fields__.get(field_name)
        if not field:
            continue
            
        field_type = ResourceIdUtils.analyze_field_type(field)
        if not field_type:
            continue
            
        # 提取字段中的所有ResourceId
        resource_ids = ResourceIdUtils.extract_resource_ids_from_field(
            operator, field_name, field_type
        )
        
        # 处理每个ResourceId
        for resource_id in resource_ids:
            if ResourceIdUtils.is_session_resource_id(resource_id):
                # 会话资源ID直接建立依赖
                task_id = ResourceIdUtils.extract_task_id_from_session_resource_id(resource_id)
                dependencies.add(task_id)
            else:
                # 全局资源ID创建适配任务
                adapter_task_id = self._create_adapter_task_for_global_resource(resource_id)
                dependencies.add(adapter_task_id)
                
    return dependencies
```

### 5.4 使用ResourceIdUtils的优势与设计考量

将ResourceId相关功能从DagScheduler迁移到独立的ResourceIdUtils工具类有以下几个优势：

1. **关注点分离**：
   - DagScheduler专注于DAG调度和任务管理
   - ResourceIdUtils专注于资源ID解析和处理
   - 降低了DagScheduler的复杂度和耦合度

2. **代码复用**：
   - 多个组件可以共享相同的ResourceId处理逻辑
   - 避免在不同模块中重复实现相似功能
   - 确保系统中ResourceId处理逻辑的一致性

3. **提升可测试性**：
   - 独立的工具类更容易进行单元测试
   - 可以独立于DagScheduler测试ResourceId相关逻辑
   - 更容易模拟各种边界情况和异常场景

4. **Operator能力复用**：
   - 复用了Operator类中的ResourceId相关能力
   - 利用现有的类型检查和序列化逻辑
   - 确保整个系统中ResourceId处理的一致性

5. **可扩展性提升**：
   - 将来可以更容易添加新的ResourceId相关功能
   - 可以独立演进ResourceId处理逻辑，不影响DagScheduler
   - 为未来支持新的资源ID类型和格式提供了灵活性

## 6. 实现优势与关键特性

### 6.1 自动依赖分析的核心优势

DagScheduler的设计实现了基于ResourceId的自动依赖分析，其核心优势包括：

1. **零配置依赖管理**：
   - 通过分析算子字段中的ResourceId自动建立依赖关系
   - 无需开发者手动声明任务间依赖
   - 与业务逻辑自然结合，减少额外配置

2. **类型系统驱动**：
   - 基于Python类型注解识别ResourceId字段
   - 提供编译时类型检查，减少运行时错误
   - 支持IDE智能提示，改善开发体验

3. **灵活的资源引用处理**：
   - SessionResourceId直接转换为任务依赖
   - GlobalResourceId自动创建适配任务
   - 支持复杂的资源组合（列表、可选类型）

### 6.2 实现的关键特性

1. **依赖变更传播**：
   - 当任务状态变化时自动标记下游任务
   - 支持智能重跑策略（NEVER/FORCE/FAILED_ONLY）
   - 避免不必要的重复计算

2. **执行效率优化**：
   - 基于拓扑排序的最小化任务执行集
   - 全局资源复用优化，相同GlobalResourceId只创建一个适配任务
   - 惰性资源加载，避免不必要的数据传输

3. **线程安全设计**：
   - 使用读写锁机制保护并发访问
   - 原子操作处理批量变更
   - 事务性的图结构修改

   ```python
   # 读取DAG状态的示例（使用读锁）
   def get_task(self, task_id: str) -> Optional['DagTask']:
       with self._lock.read_lock():  # 获取读锁，允许并发读取
           task_info = self._tasks.get(task_id)
           return task_info.task if task_info else None
   
   # 修改DAG状态的示例（使用写锁）
   def _create_task_internal(self, task_id: str, operator: 'Operator', dependencies: Set[str]) -> 'DagTask':
       with self._lock.write_lock():  # 获取写锁，互斥访问
           # 创建任务并更新DAG状态
           task = DagTaskImpl(task_id, self._session_id, operator, self)
           self._tasks[task_id] = DagTaskInfo(task=task, status=TaskStatus.PENDING)
           # ...建立依赖关系等
           return task
   ```

4. **错误处理机制**：
   - 精细的异常分类与处理
   - DAG结构验证与循环检测
   - 任务失败的可控传播与恢复

### 6.3 模块化与关注点分离 

本设计在系统架构上采用了模块化和关注点分离的原则，通过将功能分解到专注于特定职责的组件中，提高了整体系统的可维护性和可扩展性：

1. **ResourceIdUtils工具模块**：
   - 将ResourceId处理功能从DagScheduler中抽取为独立模块
   - 复用Operator类中已有的类型检查和序列化逻辑
   - 提供清晰的API供其他组件使用，确保系统各部分对ResourceId处理的一致性
   
2. **TaskDependencyManager组件**：
   - 专注于图结构管理和依赖分析
   - 与DagScheduler松耦合，使依赖管理逻辑可以单独测试和维护
   
3. **GraphExecutionClient接口**：
   - 提供面向用户的图编辑功能
   - 将图编辑操作与实际执行分离，使API更加清晰
   
这种模块化设计不仅提高了代码复用度，降低了各组件间的耦合度，同时也为未来系统演进提供了良好的基础。通过明确定义组件边界和接口，系统可以在保持稳定性的同时逐步增强功能。

## 7. 单元测试用例设计

本节提供DAG调度器核心组件的单元测试用例设计，通过全面的测试覆盖保障系统稳定性和正确性。测试用例主要涵盖DagScheduler、TaskDependencyManager、GraphExecutionClient以及ResourceId处理相关功能。

### 7.1 DagScheduler测试用例

| 测试方法 | 测试场景描述 | 示例调用方式 |
|---------|------------|------------|
| `run_op` | 运行单个算子，验证任务创建和依赖提取 | `await scheduler.run_op(read_op)` |
| `run_op` | 运行引用会话资源的算子，验证依赖自动建立 | `await scheduler.run_op(filter_op_with_session_resource_id)` |
| `run_op` | 运行引用全局资源的算子，验证适配任务自动创建 | `await scheduler.run_op(filter_op_with_global_resource_id)` |
| `run_op` | 运行包含复杂ResourceId组合的算子（List, Optional） | `await scheduler.run_op(complex_resource_op)` |
| `run_tasks` | 运行多个任务，验证正确的执行顺序 | `await scheduler.run_tasks([task1_id, task2_id])` |
| `run_tasks` | 使用NEVER重跑模式，验证成功的任务不被重跑 | `await scheduler.run_tasks([task1_id], RerunMode.NEVER)` |
| `run_tasks` | 使用FORCE重跑模式，验证所有任务都被重跑 | `await scheduler.run_tasks([task1_id], RerunMode.FORCE)` |
| `run_tasks` | 使用FAILED_ONLY重跑模式，验证只有失败任务被重跑 | `await scheduler.run_tasks([task1_id], RerunMode.FAILED_ONLY)` |
| `delete_tasks` | 删除一个没有依赖它的任务 | `await scheduler.delete_tasks([task1_id])` |
| `delete_tasks` | 尝试删除有依赖它的任务，验证异常抛出 | `await scheduler.delete_tasks([task1_id]) # 应抛出DependencyError` |
| `apply_graph_changes` | 应用正确的图变更，验证变更成功 | `await scheduler.apply_graph_changes(valid_changes)` |
| `apply_graph_changes` | 应用带有循环依赖的图变更，验证异常抛出 | `await scheduler.apply_graph_changes(cyclic_changes) # 应抛出GraphValidationError` |
| `_extract_dependencies_from_operator` | 提取不同类型的ResourceId依赖 | `dependencies = scheduler._extract_dependencies_from_operator(op)` |
| `_create_adapter_task_for_global_resource` | 为GlobalResourceId创建适配任务 | `adapter_id = scheduler._create_adapter_task_for_global_resource("global://catalog/resource")` |
| `_create_adapter_task_for_global_resource` | 为相同的GlobalResourceId重复创建，验证复用机制 | `id1 = scheduler._create_adapter_task_for_global_resource("global://catalog/resource")`<br>`id2 = scheduler._create_adapter_task_for_global_resource("global://catalog/resource")`<br>`# 验证id1 == id2` |
| `_should_rerun_task` | 不同的重跑模式和任务状态组合 | `should_rerun = scheduler._should_rerun_task(task_id, RerunMode.NEVER, False)` |

### 7.2 TaskDependencyManager测试用例

| 测试方法 | 测试场景描述 | 示例调用方式 |
|---------|------------|------------|
| `add_dependency` | 添加正常依赖关系 | `manager.add_dependency("task1", "task2")` |
| `add_dependency` | 添加会形成循环依赖的关系，验证异常抛出 | `manager.add_dependency("task2", "task1") # 应抛出ValueError` |
| `remove_dependency` | 移除已存在的依赖关系 | `manager.remove_dependency("task1", "task2")` |
| `remove_dependency` | 尝试移除不存在的依赖关系 | `manager.remove_dependency("task1", "task3") # 返回False` |
| `get_dependencies` | 获取任务的直接依赖 | `deps = manager.get_dependencies("task2")` |
| `get_dependents` | 获取依赖于任务的下游任务 | `deps = manager.get_dependents("task1")` |
| `get_all_dependencies` | 获取任务的所有依赖（包括间接依赖） | `all_deps = manager.get_all_dependencies("task3")` |
| `has_cycle` | 检查无环图 | `has_cycle = manager.has_cycle() # 应返回False` |
| `has_cycle` | 检查有环图 | `# 添加形成环的边后: has_cycle = manager.has_cycle() # 应返回True` |
| `topological_sort` | 对简单图进行拓扑排序 | `order = manager.topological_sort()` |
| `topological_sort` | 指定起始节点进行拓扑排序 | `order = manager.topological_sort(["task3"])` |
| `topological_sort` | 尝试对有环图进行拓扑排序，验证异常抛出 | `# 添加形成环的边后: order = manager.topological_sort() # 应抛出DagCycleError` |

### 7.3 GraphExecutionClientImpl测试用例

| 测试方法                  | 测试场景描述                              | 示例调用方式                                                                                     |
| --------------------- | ----------------------------------- | ------------------------------------------------------------------------------------------ |
| `add_nodes`           | 添加一组新节点                             | `await client.add_nodes([node1, node2])`                                                   |
| `update_nodes`        | 更新节点配置                              | `await client.update_nodes({"node1": {"param": "value"}})`                          |
| `delete_nodes`        | 删除一组节点                              | `await client.delete_nodes(["node1", "node2"])`                                            |
| `add_edges`           | 添加一组边                               | `await client.add_edges([edge1, edge2])`                                                   |
| `delete_edges`        | 删除一组边                               | `await client.delete_edges([edge1, edge2])`                                                |
| `add_op`              | 添加算子并自动生成task_id                    | `op = await client.add_op(op_without_id)`                                                  |
| `add_op`              | 添加已有task_id的算子                      | `op = await client.add_op(op_with_id)`                                                     |
| `load_graph`          | 使用FAILED_ON_CONFLICT模式加载无冲突图        | `await client.load_graph(graph, MergeMode.FAILED_ON_CONFLICT)`                             |
| `load_graph`          | 使用FAILED_ON_CONFLICT模式加载有冲突图，验证异常抛出 | `await client.load_graph(conflict_graph, MergeMode.FAILED_ON_CONFLICT) # 应抛出ConflictError` |
| `load_graph`          | 使用OVERWRITE_EXISTING模式加载有冲突图        | `await client.load_graph(conflict_graph, MergeMode.OVERWRITE_EXISTING)`                    |
| `load_graph`          | 使用MERGE_AND_UPDATE模式加载有冲突图          | `await client.load_graph(conflict_graph, MergeMode.MERGE_AND_UPDATE)`                      |
| `verify`              | 验证有效的图变更                            | `result = await client.verify() # 应返回valid=True`                                           |
| `verify`              | 验证无效的图变更                            | `result = await client.verify() # 应返回valid=False和失败详情`                                     |
| `save`                | 保存有效的图变更                            | `affected_nodes = await client.save()`                                                     |
| `save`                | 尝试保存无效的图变更，验证异常抛出                   | `await client.save() # 应抛出GraphValidationError`                                            |
| `export_graph`        | 导出当前图状态                             | `graph = await client.export_graph()`                                                      |
| `resolve_task`        | 解析已存在的算子任务                          | `task = await client.resolve_task(existing_op)`                                            |
| `resolve_task`        | 尝试解析不存在的算子任务，验证异常抛出                 | `await client.resolve_task(non_existing_op) # 应抛出OperatorNotFoundError`                    |

### 7.4 ResourceIdUtils测试用例

| 测试方法                                        | 测试场景描述                            | 示例调用方式                                                                                                                          |
| ------------------------------------------- | --------------------------------- | ------------------------------------------------------------------------------------------------------------------------------- |
| `analyze_field_type`                       | 分析ResourceId类型字段                  | `field_type = ResourceIdUtils.analyze_field_type(simple_resource_field)`                                                             |
| `analyze_field_type`                       | 分析Optional[ResourceId]类型字段        | `field_type = ResourceIdUtils.analyze_field_type(optional_resource_field)`                                                           |
| `analyze_field_type`                       | 分析List[ResourceId]类型字段            | `field_type = ResourceIdUtils.analyze_field_type(list_resource_field)`                                                               |
| `analyze_field_type`                       | 分析Optional[List[ResourceId]]类型字段  | `field_type = ResourceIdUtils.analyze_field_type(optional_list_resource_field)`                                                      |
| `analyze_field_type`                       | 分析非ResourceId类型字段                 | `field_type = ResourceIdUtils.analyze_field_type(non_resource_field) # 应返回None`                                                      |
| `extract_resource_ids_from_field`          | 从简单ResourceId字段提取资源ID             | `ids = ResourceIdUtils.extract_resource_ids_from_field(op, "simple_field", ResourceIdFieldType.RESOURCE_ID)`                         |
| `extract_resource_ids_from_field`          | 从Optional[ResourceId]字段提取资源ID（有值） | `ids = ResourceIdUtils.extract_resource_ids_from_field(op, "optional_field", ResourceIdFieldType.OPTIONAL_RESOURCE_ID)`              |
| `extract_resource_ids_from_field`          | 从Optional[ResourceId]字段提取资源ID（无值） | `ids = ResourceIdUtils.extract_resource_ids_from_field(op, "null_field", ResourceIdFieldType.OPTIONAL_RESOURCE_ID)`                  |
| `extract_resource_ids_from_field`          | 从List[ResourceId]字段提取资源ID         | `ids = ResourceIdUtils.extract_resource_ids_from_field(op, "list_field", ResourceIdFieldType.LIST_RESOURCE_ID)`                      |
| `parse_resource_id`                        | 解析有效的会话资源ID                       | `protocol, location, identifier = ResourceIdUtils.parse_resource_id("session://session-123/data-resource/task-456")`                 |
| `parse_resource_id`                        | 解析有效的全局资源ID                       | `protocol, location, identifier = ResourceIdUtils.parse_resource_id("global://catalog/sales-data")`                                  |
| `parse_resource_id`                        | 解析无效的资源ID，验证异常抛出                  | `ResourceIdUtils.parse_resource_id("invalid-resource-id") # 应抛出ValueError`                                                           |
| `is_session_resource_id`                   | 检测会话资源ID                          | `is_session = ResourceIdUtils.is_session_resource_id("session://session-123/data-resource/task-456") # 应返回True`                      |
| `is_session_resource_id`                   | 检测非会话资源ID                         | `is_session = ResourceIdUtils.is_session_resource_id("global://catalog/sales-data") # 应返回False`                                      |
| `extract_task_id_from_session_resource_id` | 从会话资源ID提取任务ID                     | `task_id = ResourceIdUtils.extract_task_id_from_session_resource_id("session://session-123/data-resource/task-456") # 应返回"task-456"` |
| `extract_task_id_from_session_resource_id` | 尝试从非会话资源ID提取任务ID，验证异常抛出           | `ResourceIdUtils.extract_task_id_from_session_resource_id("global://catalog/sales-data") # 应抛出ValueError`                            |
| `build_session_resource_id`                | 构建标准格式的会话资源ID                     | `resource_id = ResourceIdUtils.build_session_resource_id("session-123", ResourceType.DATA_RESOURCE, "task-456")`                      |
| `build_session_resource_id`                | 使用无效参数构建会话资源ID，验证异常抛出            | `ResourceIdUtils.build_session_resource_id("", ResourceType.DATA_RESOURCE, "task-456") # 应抛出ValueError`                              |