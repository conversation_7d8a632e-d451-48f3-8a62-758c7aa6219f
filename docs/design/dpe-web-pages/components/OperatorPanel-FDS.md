# OperatorPanel 组件使用说明

## 简介

`OperatorPanel` 是一个用于展示算子库的 Vue 组件。它从传入的算子列表中渲染可用的算子项，支持按关键词搜索和按分类筛选。用户可以通过点击或拖拽算子项将其添加到工作流画布中。组件还提供了一个添加数据集的入口。

## 组件位置

`src/pages/dpe-web/components/OperatorPanel`

## 安装

这是一个内部组件，无需单独安装。直接在需要使用的 Vue 文件中导入即可。

## 使用示例

```vue
<template>
  <div class="my-page">
    <h2>算子面板示例</h2>
    <!-- 使用 OperatorPanel 组件 -->
    <OperatorPanel
      :operators="availableOperators"
      :selected-category="currentCategory"
      :search-keyword="searchString"
      @search="handlePanelSearch"
      @category-change="handlePanelCategoryChange"
      @operator-select="handleOperatorSelect"
      @operator-drag-start="handleOperatorDragStart"
      @operator-drag-end="handleOperatorDragEnd"
      @add-dataset="handleAddNewDataset"
    />

    <!-- 示例：显示当前选中的算子信息 -->
    <div v-if="selectedOperator">
      <h3>当前选中的算子:</h3>
      <p>类型: {{ selectedOperator.type }}</p>
      <p>名称: {{ selectedOperator.metadata.displayName }}</p>
      <p>描述: {{ selectedOperator.metadata.description }}</p>
    </div>
  </div>
</template>

<script>
import OperatorPanel from '@/pages/dpe-web/components/OperatorPanel';
// 确保 OperatorInfo 模型的引入路径正确，这里仅为示例
import OperatorInfo from '@/pages/dpe-web/models/OperatorInfo.js'; 

export default {
  components: {
    OperatorPanel
  },
  data() {
    return {
      // 示例用的算子列表
      availableOperators: [
        new OperatorInfo({ type: 'read-csv', metadata: { displayName: '读取CSV', description: '从CSV文件读取数据', labels: { category: 'data-source' } } }),
        new OperatorInfo({ type: 'filter-data', metadata: { displayName: '过滤数据', description: '根据条件过滤数据集', labels: { category: 'data-process' } } }),
        new OperatorInfo({ type: 'train-model', metadata: { displayName: '训练模型', description: '使用数据集训练机器学习模型', labels: { category: 'machine-learning' } } }),
        // ... 更多算子
      ],
      currentCategory: 'all', // 当前选中的分类
      searchString: '',       // 当前搜索关键词
      selectedOperator: null  // 示例：保存当前选中的算子
    };
  },
  methods: {
    /**
     * 处理面板搜索事件
     * @param {string} keyword - 搜索关键词
     */
    handlePanelSearch(keyword) {
      this.searchString = keyword;
      console.log('搜索关键词变化:', keyword);
      // 根据关键词和分类更新 displayedOperators (实际应用中可能在父组件中处理过滤逻辑)
    },

    /**
     * 处理面板分类变化事件
     * @param {string} category - 选中的分类 key ('all' 或其他分类 key)
     */
    handlePanelCategoryChange(category) {
      this.currentCategory = category;
      console.log('分类变化:', category);
       // 根据关键词和分类更新 displayedOperators (实际应用中可能在父组件中处理过滤逻辑)
    },

    /**
     * 处理算子项点击选择事件
     * @param {OperatorInfo} operator - 被选中的算子信息对象
     */
    handleOperatorSelect(operator) {
      this.selectedOperator = operator;
      console.log('算子被选中:', operator);
      // 可以在这里执行选中算子后的逻辑，如在详情面板显示信息
    },

    /**
     * 处理算子项拖拽开始事件
     * @param {Object} payload - 包含 event 和 operator 的对象
     * @param {DragEvent} payload.event - 原生 DragEvent
     * @param {OperatorInfo} payload.operator - 被拖拽的算子信息对象
     */
    handleOperatorDragStart({ event, operator }) {
      console.log('开始拖拽算子:', operator.type);
      // 在这里处理拖拽开始的逻辑，通常用于设置 DataTransfer 对象和记录正在拖拽的算子信息
      // event.dataTransfer.setData(...) 已经在组件内部完成基本设置
    },

    /**
     * 处理算子项拖拽结束事件
     * @param {DragEvent} event - 原生 DragEvent
     */
    handleOperatorDragEnd(event) {
      console.log('拖拽算子结束.');
      // 在这里处理拖拽结束的逻辑，如清理拖拽状态
    },
    
    /**
     * 处理点击添加数据集按钮事件
     */
    handleAddNewDataset() {
      console.log('点击了添加数据集按钮');
      // 在这里处理添加数据集的逻辑，如打开一个模态框
    }
  }
};
</script>

<style scoped>
.my-page {
  display: flex;
  /* 根据实际布局调整 */
}
</style>
```

## Props

| Prop 名称          | 类型          | 默认值  | 是否必须 | 说明                                           |
| :----------------- | :------------ | :------ | :------- | :--------------------------------------------- |
| `operators`        | `Array<OperatorInfo>` | `[]`    | 是       | 定义算子库中所有可用算子的列表，每个元素为 `OperatorInfo` 实例。 |
| `selectedCategory` | `string`      | `'all'` | 否       | 初始化或控制当前选中的算子分类。                |
| `searchKeyword`    | `string`      | `''`    | 否       | 初始化或控制当前的搜索关键词。                  |

## Events

| Event 名称            | 说明                                   | 回调参数                                          |
| :-------------------- | :------------------------------------- | :------------------------------------------------ |
| `search`              | 搜索关键词变化时触发                   | `(keyword: string)`: 最新的搜索关键词。           |
| `category-change`     | 算子分类选择变化时触发                 | `(category: string)`: 被选中的分类 key。          |
| `operator-select`     | 点击算子项时触发                       | `(operator: OperatorInfo)`: 被点击的算子信息对象。 |
| `operator-drag-start` | 开始拖拽算子项时触发                   | `({ event: DragEvent, operator: OperatorInfo })`: 拖拽事件对象和被拖拽的算子信息。 |
| `operator-drag-end`   | 结束拖拽算子项时触发                   | `(event: DragEvent)`: 拖拽结束事件对象。         |
| `add-dataset`         | 点击"数据集"按钮时触发                 | 无                                                |

## Methods

该组件没有提供外部直接调用的公共方法（如通过 `ref` 调用），其主要交互通过 Props 和 Events 进行。

## 依赖

*   Vue 2.x
*   Ant Design Vue 1.7.x
*   项目内部定义的 `OperatorInfo` 模型类。

## 开发注意事项

*   确保传入 `operators` 数组中的每个元素都是 `OperatorInfo` 的正确实例，并且包含 `type` 和 `metadata` 属性，`metadata` 中应包含 `displayName`、`description` 和 `labels.category` 用于展示和分类。
*   组件的搜索和分类筛选是基于内部状态进行的，但它会通过 `search` 和 `category-change` 事件通知父组件当前的筛选条件。父组件可以监听这些事件，并在父组件中维护和更新 `searchKeyword` 和 `selectedCategory` prop，从而实现双向控制和数据过滤逻辑（例如，在父组件的计算属性中根据这两个值过滤传入的 `operators` 列表）。
*   `operator-drag-start` 事件提供了拖拽的 `event` 对象和 `operator` 信息，父组件可以利用这些信息在拖拽目标（如画布）的 `drop` 事件中处理算子的添加。
