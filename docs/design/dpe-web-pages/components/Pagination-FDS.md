# Pagination 组件使用说明

## 简介

`Pagination` 是一个通用的分页组件，专门用于与数据处理引擎的后端分页接口进行对接。它支持分页导航、页面大小调整、排序控制以及与后端筛选条件的集成。该组件遵循后端驱动的分页模式，所有的数据筛选、排序和分页逻辑都在服务端实现。

## 组件位置

`src/components/Pagination`

## 核心特性

- **后端驱动分页**：与数据处理引擎的统一分页接口完全兼容
- **灵活排序支持**：支持单字段和多字段排序
- **筛选条件集成**：seamless 与业务筛选条件结合
- **页面大小可调**：用户可动态调整每页显示条目数
- **加载状态管理**：内置加载状态指示器
- **响应式设计**：适配不同屏幕尺寸

## 使用示例

```vue
<template>
  <div>
    <!-- 业务筛选条件区域 -->
    <div class="filters-section">
      <a-form layout="inline">
        <a-form-item label="后端类型">
          <a-select v-model="filters.backend_types" mode="multiple" placeholder="选择后端类型">
            <a-select-option value="duckdb">DuckDB</a-select-option>
            <a-select-option value="spark">Spark</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="会话状态">
          <a-select v-model="filters.states" mode="multiple" placeholder="选择状态">
            <a-select-option value="RUNNING">运行中</a-select-option>
            <a-select-option value="CREATED">已创建</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="会话ID">
          <a-input v-model="filters.session_id_pattern" placeholder="输入会话ID关键词" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-table 
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        :loading="loading"
        :row-key="record => record.session_id"
      />
    </div>

    <!-- 分页组件 -->
    <Pagination
      :page-param="pageParam"
      :filters="filters"
      :loading="loading"
      :show-size-changer="true"
      :show-quick-jumper="true"
      :show-sort-options="true"
      :available-sort-fields="availableSortFields"
      @page-change="handlePageChange"
      @size-change="handleSizeChange" 
      @sort-change="handleSortChange"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import { DataProcessService } from '@/services';

export default {
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      tableData: [],
      // 分页参数，与后端 PageParam 完全对应
      pageParam: {
        pageIndex: 0,
        limit: 10,
        pageTotal: 0,
        recordTotal: 0,
        sortField: 'created_at',
        sortType: 'desc',
        sortList: [
          { field: 'created_at', type: 'desc' },
          { field: 'session_id', type: 'asc' }
        ],
        isTop: false
      },
      // 业务筛选条件
      filters: {
        backend_types: [],
        states: [],
        session_id_pattern: '',
        has_tasks: null,
        task_count_min: null,
        task_count_max: null
      },
      // 可用的排序字段配置
      availableSortFields: [
        { value: 'created_at', label: '创建时间' },
        { value: 'updated_at', label: '更新时间' },
        { value: 'session_id', label: '会话ID' },
        { value: 'backend_name', label: '后端类型' },
        { value: 'task_count', label: '任务数量' }
      ],
      columns: [
        { title: '会话ID', dataIndex: 'session_id', key: 'session_id' },
        { title: '后端类型', dataIndex: 'backend_name', key: 'backend_name' },
        { title: '状态', dataIndex: 'state', key: 'state' },
        { title: '创建时间', dataIndex: 'created_at', key: 'created_at' },
        { title: '任务数量', dataIndex: 'task_count', key: 'task_count' }
      ]
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    /**
     * 加载数据的核心方法
     */
    async loadData() {
      this.loading = true;
      try {
        // 调用后端分页接口
        const response = await DataProcessService.listSessions({
          pageParam: this.pageParam,
          filters: this.filters
        });
        
        this.tableData = response.data;
        this.pageParam = response.pageParam; // 更新分页信息
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 处理分页变化
     * @param {Object} newPageParam - 新的分页参数
     */
    handlePageChange(newPageParam) {
      this.pageParam = { ...this.pageParam, ...newPageParam };
      this.loadData();
    },

    /**
     * 处理页面大小变化
     * @param {number} pageSize - 新的页面大小
     */
    handleSizeChange(pageSize) {
      this.pageParam = {
        ...this.pageParam,
        limit: pageSize,
        pageIndex: 0 // 重置到第一页
      };
      this.loadData();
    },

    /**
     * 处理排序变化
     * @param {Object} sortConfig - 排序配置
     */
    handleSortChange(sortConfig) {
      this.pageParam = {
        ...this.pageParam,
        ...sortConfig,
        pageIndex: 0 // 重置到第一页
      };
      this.loadData();
    },

    /**
     * 处理刷新
     */
    handleRefresh() {
      this.loadData();
    },

    /**
     * 处理搜索
     */
    handleSearch() {
      this.pageParam.pageIndex = 0; // 重置到第一页
      this.loadData();
    },

    /**
     * 处理重置
     */
    handleReset() {
      this.filters = {
        backend_types: [],
        states: [],
        session_id_pattern: '',
        has_tasks: null,
        task_count_min: null,
        task_count_max: null
      };
      this.pageParam.pageIndex = 0;
      this.loadData();
    }
  }
};
</script>

<style scoped>
.filters-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.table-section {
  margin-bottom: 16px;
}
</style>
```

## Props

| Prop 名称                | 类型      | 默认值    | 是否必须 | 说明                                           |
| :----------------------- | :-------- | :-------- | :------- | :--------------------------------------------- |
| `pageParam`              | `Object`  | `{}`      | 是       | 分页参数对象，与后端 PageParam 结构完全对应      |
| `filters`                | `Object`  | `{}`      | 否       | 业务筛选条件对象，会传递给后端接口               |
| `loading`                | `Boolean` | `false`   | 否       | 加载状态，控制分页组件的禁用状态                 |
| `showSizeChanger`        | `Boolean` | `true`    | 否       | 是否显示页面大小选择器                         |
| `showQuickJumper`        | `Boolean` | `true`    | 否       | 是否显示快速跳转输入框                         |
| `showSortOptions`        | `Boolean` | `false`   | 否       | 是否显示排序选项                               |
| `availableSortFields`    | `Array`   | `[]`      | 否       | 可用的排序字段配置，当 showSortOptions 为 true 时有效 |
| `pageSizeOptions`        | `Array`   | `[10, 20, 50, 100]` | 否 | 页面大小选项                         |
| `showTotal`              | `Boolean` | `true`    | 否       | 是否显示总数信息                               |
| `showRefresh`            | `Boolean` | `true`    | 否       | 是否显示刷新按钮                               |

### pageParam 结构说明

```javascript
{
  pageIndex: 0,        // 当前页码，从0开始
  limit: 10,           // 每页记录数
  pageTotal: 5,        // 总页数（由后端返回）
  recordTotal: 50,     // 总记录数（由后端返回）
  sortField: 'created_at', // 主排序字段
  sortType: 'desc',    // 主排序类型（asc/desc）
  sortList: [          // 多字段排序配置
    { field: 'created_at', type: 'desc' },
    { field: 'session_id', type: 'asc' }
  ],
  isTop: false         // 是否置顶
}
```

### availableSortFields 结构说明

```javascript
[
  { value: 'created_at', label: '创建时间' },
  { value: 'updated_at', label: '更新时间' },
  { value: 'session_id', label: '会话ID' },
  { value: 'metadata.display_name', label: '显示名称' } // 支持嵌套字段
]
```

## Events

| Event 名称      | 说明                     | 回调参数                                          |
| :-------------- | :----------------------- | :------------------------------------------------ |
| `page-change`   | 页码变化时触发           | `(pageParam: Object)`: 包含 pageIndex 的分页参数对象 |
| `size-change`   | 页面大小变化时触发       | `(pageSize: number)`: 新的页面大小                |
| `sort-change`   | 排序变化时触发           | `(sortConfig: Object)`: 包含 sortField, sortType, sortList 的排序配置 |
| `refresh`       | 点击刷新按钮时触发       | 无                                                |

## Methods

通过 `ref` 获取组件实例后，可以调用以下方法：

| 方法名称           | 说明                       | 参数                    | 返回值  |
| :----------------- | :------------------------- | :---------------------- | :------ |
| `goToPage`         | 跳转到指定页面             | `pageIndex: number`     | `void`  |
| `changePageSize`   | 更改页面大小               | `pageSize: number`      | `void`  |
| `resetToFirstPage` | 重置到第一页               | 无                      | `void`  |
| `updateSortConfig` | 更新排序配置               | `sortConfig: Object`    | `void`  |

## 与后端接口对接

### API 接口规范

Pagination 组件设计为与数据处理引擎的统一分页接口完全兼容，支持以下接口：

- `/api/sessions/query` - 会话列表分页
- `/api/backends/query` - 后端列表分页  
- `/api/backends/{backend_type}/operators/query` - 算子列表分页
- `/api/sessions/{session_id}/tasks/query` - 任务列表分页

### 请求格式

```javascript
// 发送给后端的请求体格式
{
  pageParam: {
    pageIndex: 0,
    limit: 10,
    sortField: 'created_at',
    sortType: 'desc',
    sortList: [
      { field: 'created_at', type: 'desc' },
      { field: 'session_id', type: 'asc' }
    ],
    isTop: false
  },
  filters: {
    // 业务特定的筛选条件
    backend_types: ['duckdb'],
    states: ['RUNNING'],
    session_id_pattern: 'test-'
  }
}
```

### 响应格式

```javascript
// 后端返回的响应格式
{
  data: [
    // 数据列表
  ],
  pageParam: {
    pageIndex: 0,
    limit: 10,
    pageTotal: 5,      // 由后端计算
    recordTotal: 50,   // 由后端计算
    sortField: 'created_at',
    sortType: 'desc',
    sortList: [...],
    isTop: false
  }
}
```

## 业务集成示例

### 会话列表页面集成

```javascript
// SessionListPage.vue 中的使用示例
import { DataProcessService } from '@/services';

export default {
  data() {
    return {
      sessionFilters: {
        backend_types: [],
        states: [],
        created_after: null,
        created_before: null,
        session_id_pattern: '',
        has_tasks: null
      }
    };
  },
  methods: {
    async loadSessions() {
      const response = await DataProcessService.listSessions({
        pageParam: this.pageParam,
        filters: this.sessionFilters
      });
      // 处理响应...
    }
  }
};
```

### 执行图列表页面集成

```javascript
// ExecutionGraphListPage.vue 中的使用示例
import { ExecutionGraphRepositoryService } from '@/services';

export default {
  data() {
    return {
      graphFilters: {
        display_name_pattern: '',
        labels: {},
        created_after: null
      }
    };
  },
  methods: {
    async loadGraphs() {
      const response = await ExecutionGraphRepositoryService.listGraphs({
        pageParam: this.pageParam,
        filters: this.graphFilters
      });
      // 处理响应...
    }
  }
};
```

## 性能优化建议

1. **防抖处理**：对于用户输入的筛选条件，建议添加防抖处理
2. **缓存策略**：可以实现简单的页面缓存，避免重复请求
3. **加载优化**：支持skeleton loading 提升用户体验
4. **响应式适配**：在移动端隐藏部分非必要功能

## 依赖

- Vue 2.x
- Ant Design Vue 1.7.x
- 项目内部的服务层接口 (`@/services`)

## 开发注意事项

1. **后端驱动**：所有的分页、排序、筛选逻辑都在后端实现，前端组件只负责参数传递和状态展示
2. **状态同步**：确保 pageParam 与后端返回的分页信息保持同步
3. **错误处理**：妥善处理网络异常和数据加载失败的情况
4. **用户体验**：在数据加载期间禁用分页操作，避免重复请求
5. **参数验证**：确保传递给后端的参数格式正确，特别是日期时间格式 