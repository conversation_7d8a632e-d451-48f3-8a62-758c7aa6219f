# NodeDetailPanel 组件详细设计文档

## 1. 引言

`NodeDetailPanel` 是数据处理引擎前端界面中用于展示和管理执行图节点详细信息的核心组件。它的主要目标是为用户提供一个集中的视图，以便他们能够：

- 查看和编辑节点的配置参数。
- 监控节点关联任务的实时状态。
- 预览节点任务执行后的输出数据。

本组件设计旨在实现职责分离、高内聚低耦合，并通过明确的接口与父组件及子组件进行数据和行为的交互。

## 2. 职责划分

根据系统设计文档和组件的定位，`NodeDetailPanel` 及其子组件的职责划分如下：

**组件内部目录结构**：

由于 `NodeDetailPanel` 是一个复杂的组件，其内部将遵循以下目录结构，以确保代码的组织性和可维护性：

```
src/pages/dpe-web/components/NodeDetailPanel/
├── index.vue             # NodeDetailPanel 组件入口文件
├── components/           # 存放 NodeDetailPanel 的子组件
│   ├── NodeConfigPanel.vue
│   ├── NodeTaskInfoPanel.vue
│   ├── NodeDataPanel/    # NodeDataPanel 目录
│   │   ├── index.vue     # NodeDataPanel 组件入口文件
│   │   └── contents/     # 存放各种数据内容预览组件
│   │       ├── mixin.js
│   │       ├── NodeStructuredDataContent.vue
│   │       └── ...       # 其他数据内容预览组件
│   └── ...               # 其他子组件，例如可能存在的 NodeOperatorPanel 等
├── models.js             # 存放组件内部使用的数据模型定义，如 NodeFormData、NodeDetailPanelCallbacks 等
├── utils/                # 存放组件内部使用的工具函数 (如果存在)
│   └── index.js
└── ...                   # 其他目录，如 styles/
```

```mermaid
classDiagram
    class NodeDetailPanel {
        <<Component>>
        + sessionId
        + nodeId
        + operatorInfo
        + editable
        + nodeFormData (v-model)
        + callbacks
        + latestTaskInfo
        + handleTabChange()
    }

    class NodeConfigPanel {
        <<Component>>
        + sessionId
        + nodeId
        + operatorInfo
        + editable
        + formData (v-model)
        + validate()
        + save()
        + @input(formData)
    }

    class NodeTaskInfoPanel {
        <<Component>>
        + sessionId
        + nodeId
        + taskInfo
        + callbacks
    }

    class NodeDataPanel {
        <<Component>>
        + sessionId
        + nodeId
        + taskInfo
        + displayData(resourceId)
    }

    class NodeStructuredDataContent {
        <<Component>>
        + resourceId
        + previewData(page, pageSize)
        + getSchema()
    }

    NodeDetailPanel --> NodeConfigPanel : 包含/协调
    NodeDetailPanel o-- NodeTaskInfoPanel : 提供数据
    NodeDetailPanel --> NodeDataPanel : 包含/协调
    NodeDataPanel --> NodeStructuredDataContent : 包含/协调 (基于类型)

```

- **`NodeDetailPanel` (当前组件)**：
  - **核心职责**：聚合和协调 `NodeConfigPanel`、`NodeTaskInfoPanel` 和 `NodeDataPanel` 的功能。
  - **数据管理**：接收父组件传入的节点静态信息和 `v-model` 绑定的表单数据，并将表单数据的修改回传给父组件。
  - **任务信息管理**：负责查询和维护当前节点的最新任务信息 (`latestTaskInfo`)，并将其传递给 `NodeTaskInfoPanel` 和 `NodeDataPanel`。
  - **事件传递**：将父组件传入的回调函数（如保存、运行、取消任务）传递给相应的子组件或在自身内部处理。
  - **布局协调**：负责内部各功能区域（配置、任务、数据）的整体布局和切换逻辑。
  - **不负责**：它仅作为节点相关详情内容的展示，在对节点详情内容的布局由使用该组件的父级组件负责（如侧边栏展示）。

### NodeConfigPanel

**职责**：专注于展示和编辑节点配置。

**Props**：

-   `session-id` (`String`)：当前会话的唯一标识符。
-   `node-id` (`String`)：当前节点的唯一标识符。
-   `operator-info` (`Object`)：`OperatorInfo` 实例，提供算子的元数据和配置元数据（`opConfigMetadata`），用于动态表单的渲染。
-   `editable` (`Boolean`)：控制配置表单是否可编辑。当为 `false` 时，表单将只读。
-   `value` (`Object` - `v-model`)：双向绑定的表单数据，其结构与 `NodeDetailPanel` 的 `node-form-data` 相同，包含 `op_config` 和 `op_inputs`。
-   `available-upstream-nodes` (`Array<GraphNode>`)：当前会话中所有可以作为上游节点的 GraphNode 列表，用于在 UI 中展示可供选择的输入。
-   `callbacks` (`NodeDetailPanelCallbacks`)：包含 `validate` 和 `save` 异步回调函数。具体结构与 `NodeDetailPanelCallbacks` 中的 `validate` 和 `save` 字段一致。

**Events**：

-   `input` (`formData: Object`)：当表单数据发生变化时触发，回传更新后的表单数据。

**Public Methods (通过 `ref` 调用)**：

-   `validate(): Promise<boolean>`：手动触发内部表单的验证，返回验证是否通过。这个方法会内部表单校验确保输入正确，然后调用 `props.callbacks.validate`确保满足所有校验。
-   `save(): Promise<boolean>`：手动触发保存操作，返回保存是否成功。这个方法会内部调用 `props.callbacks.save`。

### NodeTaskInfoPanel

**职责**：专注于渲染由 `NodeDetailPanel` 提供的任务状态和相关信息，不直接负责任务的查询和管理。

**Props**：

-   `task-info` (`Object`)：`TaskInfo` 实例，包含任务的当前状态、进度、消息和结果资源ID等信息。此 `prop` 由 `NodeDetailPanel` 负责提供和更新。
-   `session-id` (`String`)：当前会话的唯一标识符，用于传递给 `cancelTask` 回调函数。
-   `node-id` (`String`)：当前节点的唯一标识符，可能用于日志或特定任务操作。
-   `callbacks` (`NodeDetailPanelCallbacks`)：包含 `cancelTask` 异步回调函数。具体结构与 `NodeDetailPanelCallbacks` 中的 `cancelTask` 字段一致。

**Events**：

-   无特定事件（任务状态变更事件由 `NodeDetailPanel` 监听并更新 `task-info` prop）。

**Public Methods (通过 `ref` 调用)**：

-   无特定公共方法（数据由 `props` 传入，不提供内部查询方法）。

### NodeDataPanel

**职责**：提供展示任务输出数据内容的能力，并根据资源类型协调不同的数据预览子组件。`NodeDataPanel` 将作为数据预览组件的容器，其内部的 `contents` 目录中将存放具体的数据内容预览组件，例如 `NodeStructuredDataContent.vue`。

**Props**：

-   `session-id` (`String`)：当前会话的唯一标识符。
-   `node-id` (`String`)：当前节点的唯一标识符。
-   `task-info` (`Object`)：`TaskInfo` 实例，包含 `resultResourceId` 等信息，用于决定展示何种类型的数据以及获取数据。

**Events**：

-   无特定事件。

**Public Methods (通过 `ref` 调用)**：

-   `loadData(): Promise<void>`：根据 `task-info` 中的 `resultResourceId` 加载并展示数据。

### NodeStructuredDataContent

**职责**：专注于结构化数据资源的预览和展示。其底层数据访问依赖于 `@数据处理引擎界面设计.md` 中定义的 `StructuredDataResourceClient` 接口，通过该接口调用后端 `/api/resources/data-resource/structured/preview` 和 `/api/resources/data-resource/structured/schema` 等接口。

**Props**：

-   `resource-id` (`String`)：结构化数据资源的唯一标识符。

**Events**：

-   无特定事件。

**Public Methods (通过 `ref` 调用)**：

-   `previewData(page: number, pageSize: number): Promise<{total: number, schema: Array<SchemaField>, data: Array<Object>}>`：根据页码和每页条数预览数据内容。
-   `getSchema(): Promise<Array<SchemaField>>`：获取结构化数据的数据结构。


## 3. 组件结构

`NodeDetailPanel` 的内部结构将围绕其三大核心功能区域进行组织：

```html
<template>
  <div class="node-detail-panel">
    <!-- 顶部操作区域：例如保存、运行、取消按钮，根据 editable 状态和任务状态控制显示 -->
    <div class="actions-bar">
      <!-- ... 按钮 ... -->
    </div>

    <!-- 主内容区域：配置、任务、数据面板的切换视图 -->
    <a-tabs default-active-key="config" @change="handleTabChange">
      <a-tab-pane key="config" tab="节点配置">
        <NodeConfigPanel
          ref="nodeConfigPanel"
          :session-id="sessionId"
          :node-id="nodeId"
          :operator-info="operatorInfo"
          :editable="editable"
          v-model="internalNodeFormData"
          :callbacks="callbacks"
        />
      </a-tab-pane>
      <a-tab-pane key="task" tab="任务信息">
        <NodeTaskInfoPanel
          ref="nodeTaskInfoPanel"
          :session-id="sessionId"
          :node-id="nodeId"
          :task-info="latestTaskInfo"
          :callbacks="callbacks"
        />
      </a-tab-pane>
      <a-tab-pane key="data" tab="数据预览">
        <NodeDataPanel
          ref="nodeDataPanel"
          :session-id="sessionId"
          :node-id="nodeId"
          :task-info="latestTaskInfo"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
```

**注意**：`NodeDataPanel` 将根据 `latestTaskInfo` 中包含的 `resultResourceId` 来决定要展示的数据，并内部实例化 `StructuredDataResourceClient` 来获取和预览数据。`latestTaskInfo` 应该由 `NodeDetailPanel` 通过监听任务生命周期或轮询来维护。

## 4. 数据流与 Props 交互

### 4.1. Props 定义与说明

`NodeDetailPanel` 组件的 props 设计如下：

-   `session-id` (`String`)：当前会话的唯一标识。用于任务和数据服务的 API 调用。
-   `node-id` (`String`)：当前节点的唯一标识。用于任务和数据服务的 API 调用。
-   `operator-info` (`Object`)：`OperatorInfo` 实例，提供算子的元数据和配置元数据（`opConfigMetadata`），这些信息将传递给 `NodeConfigPanel` 用于动态表单的渲染。
-   `editable` (`Boolean`)：控制整个面板中的配置部分是否可编辑。当为 `false` 时，`NodeConfigPanel` 应呈现只读状态。
-   `node-form-data` (`Object` - `v-model`)：双向绑定的表单数据，其结构严格遵循 `NodeFormData` 定义。
-   `callbacks` (`NodeDetailPanelCallbacks`)：`NodeDetailPanelCallbacks` 接口的实例，包含了父组件提供的异步回调函数集合。内部通过 `props.callbacks.validate()` 等方式调用。
-   `latestTaskInfo` (`Object`)：`TaskInfo` 实例，表示当前节点的最新任务信息。该属性由 `NodeDetailPanel` 内部维护，并传递给子组件。

### 4.2. 内部数据管理

`NodeDetailPanel` 将使用内部 `data` 属性 `internalNodeFormData` 来管理 `v-model` 绑定的数据，并通过 `watch` 监听 `node-form-data` 的变化并同步，同时在 `internalNodeFormData` 变化时通过 `$emit('input', this.internalNodeFormData)` 将更新回传给父组件。

任务状态 (`latestTaskInfo`) 的获取和维护将通过 `DataProcessService.listenTaskLifecycle` 进行监听，并在组件挂载时启动，卸载时取消订阅。如果无法监听，则会通过轮询 `DataProcessService.getTaskInfo` 来获取任务信息。

## 5. 交互设计

- **配置编辑**：用户可以在“节点配置”标签页中看到由 `NodeConfigPanel` 渲染的动态表单。如果 `editable` 为 `true`，用户可以修改表单数据。修改后的数据会通过 `v-model` 实时同步到 `node-form-data`。
- **保存操作**：用户点击保存按钮时，`NodeDetailPanel` 会通过 `ref` 调用 `NodeConfigPanel` 的 `validate()` 方法进行数据校验。校验通过后，再调用 `NodeConfigPanel` 的 `save()` 方法将数据提交给父组件进行持久化。
- **运行任务**：用户点击运行按钮时，`NodeDetailPanel` 会调用 `props.callbacks.runTask` 提交当前节点的任务。任务提交后，会自动切换到“任务信息”标签页，并开始监听任务状态。
- **任务状态监控**：在“任务信息”标签页，`NodeTaskInfoPanel` 会实时显示任务的当前状态（CREATED, PENDING, RUNNING, SUCCESS, ERROR, CANCELLED, UNKNOWN）、进度、消息和日志。
- **取消任务**：如果任务处于运行中，用户可以点击取消按钮，`NodeDetailPanel` 会调用 `props.callbacks.cancelTask` 取消任务。
- **数据预览**：当任务成功完成后，`NodeDataPanel` 会在“数据预览”标签页根据 `latestTaskInfo` 中的 `resultResourceId` 展示相应的数据。对于结构化数据，`NodeStructuredDataPreviewPanel` 将负责以表格形式进行展示，并支持分页和查看数据结构。

## 6. 错误处理与状态管理

- **表单校验错误**：`NodeConfigPanel` 的 `validate()` 方法返回是否校验通过，其中错误信息会界面提示用户而不是返回值。
- **API 调用错误**：保存、运行、取消任务等操作中，如果父组件的回调函数抛出错误，`NodeDetailPanel` 应该捕获并向用户显示错误通知。
- **任务状态异常**：`NodeTaskInfoPanel` 需要处理任务状态为 `ERROR` 的情况，并显示详细的错误信息。
- **加载状态**：在数据加载、任务提交等耗时操作期间，应显示加载指示器，提升用户体验。

## 7. 未来扩展

`NodeDataPanel` 被设计为一个可扩展的数据预览组件。未来可以根据需要，增加对其他数据资源类型（如非结构化文本、图片、图数据等）的 `NodeDataPreviewPanel` 子组件实现，以满足更广泛的数据预览需求。 