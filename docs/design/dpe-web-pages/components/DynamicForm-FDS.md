# DynamicForm 组件使用说明

## 简介

`DynamicForm` 是一个基于 JSON Schema 动态生成表单的 Vue 组件。它根据传入的配置字段 (`configFields`) 自动渲染不同类型的表单控件，并处理数据绑定、验证和数据获取等功能。组件支持基本数据类型（字符串、数字、布尔值）以及复杂类型（数组、对象）的嵌套渲染。

## 组件位置

`src/pages/dpe-web/components/DynamicForm`

## 安装

这是一个内部组件，无需单独安装。直接在需要使用的 Vue 文件中导入即可。

## 使用示例

```vue
<template>
  <div>
    <h2>动态表单示例</h2>
    <!-- 使用 DynamicForm 组件 -->
    <DynamicForm
      ref="myDynamicForm"
      :config-fields="formConfig"
      :value="formData"
      :readonly="isReadonly"
      @input="handleInput"
      @change="handleChange"
    />

    <h3>当前表单数据:</h3>
    <pre>{{ JSON.stringify(formData, null, 2) }}</pre>

    <a-space>
      <a-button type="primary" @click="validateAndGetData">验证并获取数据</a-button>
      <a-button @click="resetFormData">重置</a-button>
      <a-button @click="toggleReadonly">{{ isReadonly ? '切换到编辑模式' : '切换到只读模式' }}</a-button>
    </a-space>
  </div>
</template>

<script>
import DynamicForm from '@/pages/dpe-web/components/DynamicForm';
import { ConfigField } from '@/pages/dpe-web/models'; // 确保 ConfigField 模型的引入路径正确

export default {
  components: {
    DynamicForm
  },
  data() {
    return {
      isReadonly: false,
      // 表单数据对象，建议初始化为空对象 {} 或包含默认值的对象
      formData: {},
      // 表单配置字段数组，每个元素都是 ConfigField 的实例
      formConfig: [
        new ConfigField({
          key: 'username',
          schema: {
            type: 'string',
            title: '用户名',
            placeholder: '请输入用户名',
            required: true,
            minLength: 4,
            description: '唯一的用户名，至少4个字符'
          }
        }),
        new ConfigField({
          key: 'age',
          schema: {
            type: 'integer',
            title: '年龄',
            minimum: 18,
            maximum: 60,
            description: '用户年龄，需在18到60之间'
          }
        }),
        new ConfigField({
          key: 'isActive',
          schema: {
            type: 'boolean',
            title: '是否活跃',
            default: true,
            description: '标记用户是否处于活跃状态'
          }
        }),
        new ConfigField({
          key: 'role',
          schema: {
            type: 'string',
            title: '角色',
            enum: ['admin', 'user', 'guest'],
            description: '用户所属的角色'
          }
        }),
        new ConfigField({
          key: 'notes',
          schema: {
            type: 'string',
            title: '备注',
            format: 'textarea',
            placeholder: '输入备注信息',
            description: '用户的补充说明'
          }
        }),
        new ConfigField({
          key: 'contact',
          schema: {
            type: 'object',
            title: '联系方式',
            properties: {
              email: { type: 'string', title: '邮箱' },
              phone: { type: 'string', title: '手机' }
            },
            required: ['email'],
            description: '用户的联系方式，邮箱必填'
          }
        }),
        new ConfigField({
          key: 'interests',
          schema: {
            type: 'array',
            title: '兴趣',
            items: { type: 'string', placeholder: '输入兴趣' },
            description: '用户的兴趣列表'
          }
        })
      ]
    };
  },
  methods: {
    /**
     * 处理表单整体输入变化
     * @param {Object} value - 整个表单的最新数据
     */
    handleInput(value) {
      this.formData = value;
      console.log('Form Input:', value);
    },

    /**
     * 处理单个字段变化
     * @param {Object} change - 包含 key, value, formData 的对象
     */
    handleChange(change) {
      console.log('Field Changed:', change);
    },

    /**
     * 验证表单并获取数据
     */
    async validateAndGetData() {
      const valid = await this.$refs.myDynamicForm.validateForm();
      if (valid) {
        const data = this.$refs.myDynamicForm.getFormData();
        console.log('Form is valid. Data:', data);
        alert('表单验证通过，数据已获取！');
      } else {
        console.log('Form validation failed.');
        alert('表单验证失败，请检查输入！');
      }
    },

    /**
     * 重置表单数据和验证状态
     */
    resetFormData() {
      this.$refs.myDynamicForm.resetForm();
    },

    /**
     * 切换只读模式
     */
    toggleReadonly() {
      this.isReadonly = !this.isReadonly;
    }
  }
};
</script>

<style scoped>
/* Add necessary styles */
pre {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 5px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
```

## Props

| Prop 名称      | 类型    | 默认值  | 是否必须 | 说明                                           |
| :------------- | :------ | :------ | :------- | :--------------------------------------------- |
| `configFields` | `Array` | `[]`    | 是       | 定义表单结构的配置字段数组，每个元素为 `ConfigField` 实例。 |
| `value`        | `Object`| `{}`    | 否       | 表单的初始数据或当前值。组件内部会维护一份 `formData` 副本并同步。 |
| `readonly`     | `Boolean`| `false` | 否       | 是否启用只读模式。启用后所有表单项将被禁用。           |
| `layout`       | `Object`| `{ labelCol: { span: 6 }, wrapperCol: { span: 18 } }` | 否 | Ant Design Form 的布局配置。                   |

## Events

| Event 名称 | 说明                                   | 回调参数                                          |
| :--------- | :------------------------------------- | :------------------------------------------------ |
| `input`    | 表单数据变化时触发，用于实现 `v-model` | `(value: Object)`: 整个表单的最新数据对象。       |
| `change`   | 任一字段值变化时触发                 | `(change: { key: string, value: any, formData: Object })`: 变化详情，包含字段 key、新值和完整的表单数据。 |

## Methods

通过 `ref` 获取组件实例后，可以调用以下方法：

| 方法名称        | 说明                                   | 参数   | 返回值         |
| :-------------- | :------------------------------------- | :----- | :------------- |
| `validateForm`  | 触发表单验证                           | 无     | `Promise<boolean>`: 验证通过返回 `true`，否则返回 `false`。 |
| `getFormData`   | 获取当前表单的数据                     | 无     | `Object`: 当前表单数据对象。                     |
| `resetForm`     | 重置表单到初始值并清除验证状态         | 无     | `void`         |
| `clearValidate` | 清除表单的验证错误提示                 | 无     | `void`         |

## `ConfigField` 结构说明

`configFields` 数组的每个元素是一个 `ConfigField` 类的实例。`ConfigField` 主要包含 `key` 和 `schema` 属性。`schema` 遵循 JSON Schema 规范，定义了字段的类型、约束和 UI 提示信息。

以下是 `schema` 中常用的属性和对应的字段类型：

| Schema 属性     | 类型        | 描述                                              | 对应控件 / 影响             | 示例值                                                                                           |
| :------------ | :-------- | :---------------------------------------------- | :-------------------- | :-------------------------------------------------------------------------------------------- |
| `type`        | `string`  | 字段的数据类型。必填。                                     | 决定渲染的控件               | `'string'`, `'number'`, `'integer'`, `'boolean'`, `'array'`, `'object'`                       |
| `title`       | `string`  | 字段的显示标签文本。                                      | Form Item Label       | `'用户名'`                                                                                       |
| `placeholder` | `string`  | 输入框的占位符文本。                                      | Input/Select          | `'请输入用户名'`                                                                                    |
| `required`    | `boolean` | 是否必填。                                           | 验证规则                  | `true`                                                                                        |
| `description` | `string`  | 字段的说明或提示信息。                                     | 文本提示                  | `'用户的真实姓名'`                                                                                   |
| `minLength`   | `number`  | 字符串最小长度。                                        | 验证规则 (string)         | `4`                                                                                           |
| `maxLength`   | `number`  | 字符串最大长度。                                        | 验证规则 (string)         | `50`                                                                                          |
| `pattern`     | `string`  | 字符串正则表达式。                                       | 验证规则 (string)         | `'^[a-zA-Z]+$'`                                                                               |
| `minimum`     | `number`  | 数字最小值。                                          | 验证规则 (number/integer) | `0`                                                                                           |
| `maximum`     | `number`  | 数字最大值。                                          | 验证规则 (number/integer) | `120`                                                                                         |
| `enum`        | `Array`   | 枚举值列表。用于 `string` 类型。                           | Select 选项             | `['option1', 'option2']`                                                                      |
| `format`      | `string`  | 字符串的格式，如 `textarea`, `password`。                | 决定渲染的控件               | `'textarea'`, `'password'`                                                                    |
| `items`       | `Object`  | 数组 (`type: 'array'`) 中每个元素的 schema。             | ArrayInput 子项         | `{ type: 'string', placeholder: '输入标签名' }`, `{ type: 'object', properties: { name: {...} } }` |
| `properties`  | `Object`  | 对象 (`type: 'object'`) 的属性定义。键为属性名，值为对应的 schema。 | ObjectInput 子项        | `{ email: { type: 'string' }, phone: { type: 'string' } }`                                    |
| `minItems`    | `number`  | 数组 (`type: 'array'`) 最小项数。                      | 验证规则/操作限制             | `1`                                                                                           |
| `maxItems`    | `number`  | 数组 (`type: 'array'`) 最大项数。                      | 验证规则/操作限制             | `10`                                                                                          |
| `default`     | `any`     | 字段的默认值。                                         | 初始化数据                 | `true`, `0`, `'默认文本'`                                                                         |

**注意**:

*   `type: 'array'` 字段会渲染 `DynamicArrayInput` 组件。`items` 属性用于定义数组中每个元素的类型。
*   `type: 'object'` 字段会渲染 `DynamicObjectInput` 组件。`properties` 属性用于定义对象的结构化属性。如果没有 `properties`，组件会默认提供 JSON 编辑模式。
*   `required` 属性在 Ant Design Form 中通常需要配合 `rules` 属性一起使用才能触发验证提示。`DynamicForm` 组件内部会根据 `required` 属性自动生成基本的必填规则。

## 依赖

*   Vue 2.x
*   Ant Design Vue 1.7.x
*   项目内部定义的 `ConfigField` 模型类。

## 开发注意事项

*   确保传入 `configFields` 数组中的每个元素都是 `ConfigField` 的正确实例。
*   `value` prop 使用 `v-model` 的方式双向绑定，父组件应监听 `@input` 事件更新数据。
*   对于复杂的对象或数组结构，请确保 `items` 或 `properties` 中的 schema 定义正确，以便 `DynamicArrayInput` 和 `DynamicObjectInput` 可以正确渲染子控件。
