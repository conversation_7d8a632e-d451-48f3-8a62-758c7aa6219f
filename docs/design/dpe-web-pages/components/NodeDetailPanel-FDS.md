# NodeDetailPanel 组件使用文档

## 简介

`NodeDetailPanel` 组件是数据处理引擎中用于展示和编辑执行图节点详细信息的核心组件。它整合了节点配置、任务状态展示和任务输出数据预览等功能，为用户提供了一个统一的界面来管理和监控单个节点的行为。

该组件不包含侧边栏的布局逻辑，只专注于节点详情内容的展示。
## 组件位置

`src/pages/dpe-web/components/NodeDetailPanel`
## 功能特性

- **节点配置编辑**：通过集成 `NodeConfigPanel` 子组件，允许用户对节点的参数进行配置。
- **任务状态展示**：通过集成 `NodeTaskInfoPanel` 子组件，实时显示当前节点的任务运行状态、进度和相关日志。
- **任务数据预览**：通过集成 `NodeDataPanel` 子组件（目前主要支持 `NodeStructuredDataPreviewPanel`），展示任务执行后的输出数据。

## Props

以下是 `NodeDetailPanel` 组件的 props 及其详细说明：

| Prop 名称                   | 类型         | 默认值    | 描述                                                                                                                                                                |
| ------------------------- | ---------- | ------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `session-id`              | `String`   | -      | 当前会话的唯一标识符，用于与后端服务交互。                                                                                                                                             |
| `node-id`                 | `String`   | -      | 当前节点的唯一标识符，用于获取节点相关的任务和数据信息。                                                                                                                                      |
| `operator-info`           | `Object`   | -      | 算子信息对象，包含算子类型、元数据和配置元数据等，用于动态渲染节点配置表单。                                                                                                                            |
| `editable`                | `Boolean`  | `true` | 控制节点配置是否可编辑。当为 `false` 时，配置表单将只读。                                                                                                                                 |
| `node-form-data`          | `Object`   | -      | 使用 `v-model` 绑定的节点表单数据。数据结构示例如下：<br>```yaml\nopConfigs: { key: value }\nopInputs:\n  input_field_1: \n  - up_stream_1\n  - up_stream_2\n  input_field_2: []\n``` |
| `available-upstream-nodes`| `Array`    | `[]`   | 当前会话中所有可以作为上游节点的 GraphNode 列表，用于在 UI 中展示可供选择的输入。类型是 `GraphNode[]`。 |
| `callbacks`               | `Object`   | -      | 父组件传入的回调函数集合，包含表单验证、保存、运行任务和取消任务等异步方法。具体结构参见下方 `NodeDetailPanelCallbacks` 接口定义。                                                                                                                                                                                                                                                                                                 |

## 用法示例

```html
<template>
  <NodeDetailPanel
    :session-id="currentSessionId"
    :node-id="currentNodeId"
    :operator-info="currentOperatorInfo"
    :editable="isEditable"
    v-model="nodeFormData"
    :callbacks="nodeDetailPanelCallbacks"
  />
</template>

<script>
import NodeDetailPanel,{ Session, TaskInfo, OperatorInfo } from '@/pages/dpe-web/components/NodeDetailPanel'; 

export default {
  components: {
    NodeDetailPanel,
  },
  data() {
    return {
      currentSessionId: 'session-123',
      currentNodeId: 'node-abc',
      currentOperatorInfo: new OperatorInfo({
        type: 'example_op',
        metadata: { displayName: '示例算子', description: '这是一个示例算子' },
        opConfigMetadata: [
          { key: 'param1', schema: { type: 'string', title: '参数1' } },
          { key: 'param2', schema: { type: 'number', title: '参数2' } },
        ],
        inputFields: ['input_field_1', 'input_field_2'],
      }),
      isEditable: true,
      nodeFormData: {
        opConfigs: {
          param1: 'test',
          param2: 123,
        },
        opInputs: {
          input_field_1: ['upstream-a'],
          input_field_2: [],
        },
        metadata: {
          displayName: '示例节点',
          description: '这是一个示例节点的元数据',
          labels: {},
          annotations: {}
        }
      },
      availableUpstreamNodes: [ // 新增的示例数据
        { id: 'upstream-a', metadata: { displayName: '上游节点 A' } },
        { id: 'upstream-b', metadata: { displayName: '上游节点 B' } },
      ],
      nodeDetailPanelCallbacks: {
        async validate(formData) {
          console.log('校验节点配置:', formData);
          // 实际的校验逻辑
          return { valid: true, message: '' };
        },
        async save(formData) {
          console.log('保存节点配置:', formData);
          // 实际的保存逻辑，例如调用后端API
          return true; // 返回保存结果
        },
        async runTask(formData) {
          console.log('运行节点任务:', formData);
          // 实际的运行任务逻辑，例如调用后端API
          const taskInfo = new TaskInfo({ id: 'task-123', status: 'RUNNING' });
          return taskInfo; // 返回任务信息
        },
        async cancelTask(taskId) {
          console.log('取消节点任务:', taskId);
          // 实际的取消任务逻辑
          return true; // 返回取消结果
        },
      },
    };
  },
};
</script>
```

## 接口定义

### NodeFormData

`NodeFormData` 是 `NodeDetailPanel` 组件通过 `v-model` 绑定的数据结构，用于表示节点的配置、输入连接和元数据。它主要用于在前端表单中收集和展示节点的相关可编辑信息。

**与富执行图模型的关系**

`NodeFormData` 中的各个字段与 `RichGraphNode` （及其继承的 `GraphNode`）和 `RichGraphEdge` 等富执行图数据模型之间存在直接的映射和转换关系。`NodeDetailPanel` 组件内部负责处理这些数据转换，以确保前端表单数据与后端业务模型以及 `@antv/x6` 的显示属性保持同步。

-   **`metadata`** 字段：直接映射到 `RichGraphNode` 实例的 `metadata` 属性。此字段包含节点的显示名称、描述、标签和注解等信息，与 `ObjectMetadata` 类结构一致。与 `opConfigs` 类似，`metadata` 的更新也是双向同步的。
-   **`opConfigs`** 字段：直接映射到 `RichGraphNode` 实例的 `opConfig` 属性。当 `NodeDetailPanel` 初始化时，`RichGraphNode.opConfig` 的值会填充到 `nodeFormData.opConfigs`。用户在表单中对 `opConfigs` 的修改会通过 `v-model` 实时更新，并最终同步回 `RichGraphNode.opConfig`，用于后续的保存和任务运行。
-   **`opInputs`** 字段：描述了算子输入字段与上游节点ID的映射关系。这部分数据在逻辑上表示了当前节点输入端口的连接情况，对应于 `GraphEdge` 中 `targetConfig`（目标节点输入字段）和 `source`（源节点ID）的概念。`NodeDetailPanel` 会根据这些信息来理解和展示节点间的连线关系，尽管这不是 `RichGraphEdge` 的直接实例，但其数据可以用于构建或验证 `RichGraphEdge`。例如，当一个 `RichGraphEdge` 的 `target` 是当前节点，`targetConfig` 是 `input_field_1`，而 `source` 是 `upstream_node_id_1` 时，`nodeFormData.opInputs.input_field_1` 中会包含 `upstream_node_id_1`。


```javascript
/**
 * 节点表单数据定义
 * @typedef {Object} NodeFormData
 * @property {Object.<string, any>} opConfigs - 算子配置的具体键值对，键为配置项名称，值为配置数据。
 * @property {Object.<string, string[]>} opInputs - 算子输入字段与上游节点ID的映射，键为输入字段名称，值为一个包含上游节点ID的字符串数组。
 * @property {ObjectMetadata} metadata - 节点的元数据，对应 GraphNode 的 metadata 属性，包含显示名称、描述等信息。
 */
// 示例
const nodeFormDataExample = {
  // 节点元数据
  metadata: {
    displayName: '示例节点',
    description: '这是一个示例节点的元数据',
    labels: {},
    annotations: {}
  },
  opConfigs: {
    param1: 'value1',
    param2: 123,
    // ... 其他算子配置
  },
  opInputs: {
    input_field_1: ['upstream_node_id_1', 'upstream_node_id_2'],
    input_field_2: [],
    // ... 其他输入字段
  },
};
```

### NodeDetailPanelCallbacks

`NodeDetailPanelCallbacks` 定义了 `NodeDetailPanel` 组件需要父组件提供的异步回调函数集合。这些回调函数用于处理表单的验证、保存以及任务的运行和取消等核心业务逻辑。为了遵循更规范的 JavaScript 接口定义，我们使用 `class` 形式来描述。

```javascript
/**
 * NodeDetailPanel 的回调函数接口定义。
 * 该类作为一个抽象接口，定义了 NodeDetailPanel 与父组件交互的异步方法。
 */
class NodeDetailPanelCallbacks {
  /**
   * 异步验证回调函数，用于在保存配置前对表单数据进行校验。
   * @param {NodeFormData} formData - 当前节点的表单数据。
   * @returns {Promise<{valid: boolean, message: string}>} 一个 Promise，resolve 一个包含 valid (布尔值) 和 message (字符串) 的对象。
   */
  async validate(formData) {
    throw new Error('Method 'validate' must be implemented.');
  }

  /**
   * 异步保存回调函数，当用户点击保存按钮时触发，用于将配置数据持久化到后端。
   * @param {NodeFormData} formData - 当前节点的表单数据。
   * @returns {Promise<boolean>} 一个 Promise，resolve 一个布尔值表示保存是否成功。
   */
  async save(formData) {
    throw new Error('Method 'save' must be implemented.');
  }

  /**
   * 异步运行任务回调函数，当用户点击运行任务按钮时触发，用于向后端提交运行当前节点的任务。
   * @param {NodeFormData} formData - 当前节点的表单数据。
   * @returns {Promise<TaskInfo>} 一个 Promise，resolve 一个 TaskInfo 实例。
   */
  async runTask(formData) {
    throw new Error('Method 'runTask' must be implemented.');
  }

  /**
   * 异步取消任务回调函数，当用户点击取消任务按钮时触发，用于向后端发送取消当前任务的请求。
   * @param {string} taskId - 需要取消的任务ID。
   * @returns {Promise<boolean>} 一个 Promise，resolve 一个布尔值表示取消是否成功。
   */
  async cancelTask(taskId) {
    throw new Error('Method 'cancelTask' must be implemented.');
  }
}
``` 