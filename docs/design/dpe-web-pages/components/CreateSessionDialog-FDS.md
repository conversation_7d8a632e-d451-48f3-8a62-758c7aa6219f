# CreateSessionDialog 组件使用说明

## 简介

`CreateSessionDialog` 是一个用于创建新会话的对话框组件。它提供了一个表单界面，允许用户输入会话名称、描述，选择执行图和后端类型，并支持动态会话配置。组件内置了表单验证和错误提示功能。

## 组件位置

`src/pages/dpe-web/components/CreateSessionDialog`

## 使用示例

```vue
<template>
  <div>
    <a-button type="primary" @click="showCreateSessionDialog">创建会话</a-button>

    <CreateSessionDialog
      :visible="isDialogVisible"
      :execution-graph-options="myExecutionGraphs"
      :session-configs="initialSessionConfigs"
      @submit="handleSessionSubmit"
      @cancel="handleDialogCancel"
      @visibility-change="handleVisibilityChange"
    />
  </div>
</template>

<script>
import CreateSessionDialog from '@/pages/dpe-web/components/CreateSessionDialog';
// 假设您有ExecutionGraphService和SessionConfigService来获取数据
// import { ExecutionGraphService } from '@/services'; 
// import { SessionConfigService } from '@/services'; 

export default {
  components: {
    CreateSessionDialog
  },
  data() {
    return {
      isDialogVisible: false,
      myExecutionGraphs: [
        // 示例数据，实际应从后端获取
        { id: 'graph1', metadata: { displayName: '图表A' } },
        { id: 'graph2', metadata: { displayName: '图表B' } },
      ],
      initialSessionConfigs: {
        // 示例初始配置
        someDefaultKey: 'defaultValue'
      }
    };
  },
  methods: {
    /**
     * 显示创建会话对话框
     */
    showCreateSessionDialog() {
      this.isDialogVisible = true;
      // 如果需要预填充执行图信息，可以在这里传入
      // this.$refs.createSessionDialog.show({ id: 'prefilledGraphId' });
    },

    /**
     * 处理会话提交
     * @param {Object} data - 提交的数据，包含 session 和 formData
     */
    handleSessionSubmit(data) {
      console.log('会话创建成功:', data.session);
      console.log('表单数据:', data.formData);
      this.isDialogVisible = false;
      // 可以在这里刷新会话列表等
    },

    /**
     * 处理对话框取消
     */
    handleDialogCancel() {
      console.log('创建会话已取消');
      this.isDialogVisible = false;
    },

    /**
     * 处理对话框可见性变化
     * @param {boolean} visible - 对话框当前可见状态
     */
    handleVisibilityChange(visible) {
      console.log('对话框可见性变化:', visible);
      this.isDialogVisible = visible;
    }
  }
};
</script>

<style scoped>
/* 示例样式 */
</style>
```

## Props

| Prop 名称             | 类型      | 默认值  | 是否必须 | 说明                                       |
| :-------------------- | :-------- | :------ | :------- | :----------------------------------------- |
| `visible`             | `Boolean` | `false` | `否`     | 对话框是否可见。                           |
| `executionGraphOptions` | `Array`   | `[]`    | `否`     | 执行图选项列表，每个元素应包含 `id` 和 `metadata.displayName`。 |
| `sessionConfigs`      | `Object`  | `{}`    | `否`     | 预设的会话配置对象，会作为 `formData.sessionConfig` 的初始值。 |

## Events

| Event 名称        | 说明                           | 回调参数                                   |
| :---------------- | :----------------------------- | :----------------------------------------- |
| `submit`          | 会话创建成功时触发。           | `(data: { session: Object, formData: Object })`: 包含创建的会话对象和表单数据。 |
| `cancel`          | 对话框取消时触发。             | `无`                                       |
| `visibility-change` | 对话框可见性变化时触发。       | `(visible: Boolean)`: 对话框的当前可见状态。 |

## Methods

通过 `ref` 获取组件实例后，可以调用以下方法：

| 方法名称 | 说明             | 参数                                   | 返回值 |
| :------- | :--------------- | :------------------------------------- | :----- |
| `show`   | 显示对话框。     | `(graphInfo: Object)`: 可选的执行图信息，用于预填充表单。 | `void` |
| `hide`   | 隐藏对话框。     | `无`                                   | `void` |

## 依赖

*   Vue 2.x
*   Ant Design Vue 1.7.x
*   `DynamicForm` 组件 (`@/pages/dpe-web/components/dynamic-form`)
*   项目内部定义的 `DataProcessService` 服务 (`@/services`)
*   项目内部定义的 `BackendInfo` 模型类 (`@/models`)

## 开发注意事项

*   `visible` prop 使用 `v-model` 的方式双向绑定，父组件应监听 `@visibility-change` 事件来同步状态。
*   `executionGraphOptions` 数组中的每个对象应包含 `id` 和 `metadata.displayName` 属性，以便正确显示。
*   `sessionConfigs` 用于初始化动态表单的会话配置，其结构应与后端期望的会话配置一致。 