# GraphEditor 组件使用说明

## 简介

`GraphEditor` 是一个基于 [@antv/x6](https://x6.antv.antgroup.com/) 实现的通用图形编辑器组件。它提供了一个可交互的画布，用于可视化和编辑图形数据（节点和边）。组件内部使用自定义的**富执行图模型 (RichExecutionGraph, RichGraphNode, RichGraphEdge)** 来管理数据，该模型在基础业务数据 (`ExecutionGraph`) 的基础上增加了与 `@antv/x6` 相关的显示属性 (`displayAttr`)。组件支持基本的图形操作（如缩放、平移）、节点和边的选择与删除，并提供了加载和导出富执行图数据的功能。

## 组件位置

`src/pages/dpe-web/components/GraphEditor`

## 安装

这是一个内部组件，无需单独安装。直接在需要使用的 Vue 文件中导入即可。

## 富执行图数据模型

组件内部使用 `src/pages/dpe-web/components/GraphEditor/models.js` 中定义的富执行图数据模型：

-   `RichGraphNode`: 继承自 `src/pages/dpe-web/models/ExecutionGraph.js` 中的 `GraphNode`，增加 `displayAttr` 属性用于存储 `@antv/x6` 节点的显示配置（位置、尺寸、样式等）。
-   `RichGraphEdge`: 继承自 `src/pages/dpe-web/models/ExecutionGraph.js` 中的 `GraphEdge`，增加 `displayAttr` 属性用于存储 `@antv/x6` 边的显示配置（样式等）。
-   `RichExecutionGraph`: 继承自 `src/pages/dpe-web/models/ExecutionGraph.js` 中的 `ExecutionGraph`，其内部的节点和边集合分别存储 `RichGraphNode` 和 `RichGraphEdge` 实例。该类提供了以下重要的转换方法：
    -   `toX6Format()`: 将富执行图数据转换为 `@antv/x6` 库可直接加载的格式 (`{ cells: [...] }`)，包含了所有必要的显示和业务数据。
    -   `fromX6Format(x6Data, id, metadata)`: 静态方法，从 `@antv/x6` 导出的格式数据创建 `RichExecutionGraph` 实例。
    -   `toExecutionGraph()`: 将富执行图数据转换为基础的 `ExecutionGraph` 格式，仅包含业务相关的节点和边信息（不含显示属性）。
    -   `fromExecutionGraph(executionGraph)`: 静态方法，从基础的 `ExecutionGraph` 格式创建 `RichExecutionGraph` 实例，并为节点和边生成默认的 `displayAttr`。

组件主要通过处理 `RichExecutionGraph` 实例来进行渲染和交互。

## 使用示例

```vue
<template>
  <div style="width: 800px; height: 600px; border: 1px solid #eee;">
    <!-- 使用 GraphEditor 组件，绑定 RichExecutionGraph 实例到 graphData，并使用 .sync 实现双向绑定 -->
    <GraphEditor
      ref="graphEditor"
      :graph-data.sync="richGraphData"
      :readonly="isReadonly"
      :show-minimap="true"
      :show-toolbar="true"
      @node-click="handleNodeClick"
      @edge-click="handleEdgeClick"
      @blank-click="handleBlankClick"
      @selection-change="handleSelectionChange"
      @init-error="handleInitError"
      @graph-data-change="handleGraphDataChange"
    />
  </div>
  <a-space style="margin-top: 16px;">
    <a-button @click="loadExampleData">加载示例数据 (ExecutionGraph)</a-button>
    <a-button @click="fitGraph">适应画布</a-button>
    <a-button @click="exportCurrentData">导出当前数据 (RichExecutionGraph)</a-button>
    <a-button @click="toggleReadonly">{{ isReadonly ? '切换到编辑模式' : '切换到只读模式' }}</a-button>
  </a-space>
  <div>
    <h4>当前选中元素:</h4>
    <pre>{{ selectedCell ? JSON.stringify(selectedCell.toJSON(), null, 2) : '无' }}</pre>
  </div>
</template>

<script>
import GraphEditor,{ RichExecutionGraph, RichGraphNode, RichGraphEdge } from '@/pages/dpe-web/components/GraphEditor';
import ExecutionGraph, { GraphNode, GraphEdge } from '@/pages/dpe-web/models/ExecutionGraph.js'; // 导入基础模型

export default {
  components: {
    GraphEditor
  },
  data() {
    return {
      isReadonly: false,
      // 组件现在主要接收 RichExecutionGraph 实例
      richGraphData: null,
      selectedCell: null
    };
  },
  methods: {
    loadExampleData() {
      // 示例 ExecutionGraph 数据 (业务数据结构)
      const exampleExecutionGraph = new ExecutionGraph({
        id: 'example-graph-1',
        metadata: {
          displayName: '简单示例图',
          description: '这是一个包含两个节点和一条边的示例图'
        },
        nodes: {
          'node-1': new GraphNode({
            id: 'node-1',
            metadata: { displayName: '开始节点' },
            opType: 'Start',
            opConfig: {}
          }),
          'node-2': new GraphNode({
            id: 'node-2',
            metadata: { displayName: '结束节点' },
            opType: 'End',
            opConfig: {}
          })
        },
        edges: [
          new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'input' })
        ]
      });

      // 从基础 ExecutionGraph 创建 RichExecutionGraph (会自动生成默认显示属性)
      this.richGraphData = RichExecutionGraph.fromExecutionGraph(exampleExecutionGraph);
      console.log('加载示例 RichExecutionGraph 数据:', this.richGraphData);
    },
    handleGraphDataChange(updatedGraph) {
      console.log('Graph data changed:', updatedGraph);
      // 更新父组件的数据
      this.richGraphData = updatedGraph;
    },
    handleNodeClick({ node, event }) {
      console.log('节点被点击:', node.id, node.toJSON());
      this.selectedCell = node;
    },
    handleEdgeClick({ edge, event }) {
      console.log('边被点击:', edge.id, edge.toJSON());
      this.selectedCell = edge;
    },
    handleBlankClick({ event }) {
      console.log('画布空白处被点击');
      this.selectedCell = null;
    },
    handleSelectionChange({ added, removed, selected }) {
       console.log('选中变化:', { added: added.map(c => c.id), removed: removed.map(c => c.id), selected: selected.map(c => c.id) });
       // 可以在这里更新 selectedCell 状态，或者通过 getSelectedCell 方法获取
    },
     handleInitError(error) {
      console.error('GraphEditor 初始化错误:', error);
      alert('图形编辑器初始化失败，请检查控制台日志。');
    },
    fitGraph() {
      this.$refs.graphEditor.fitView();
    },
    exportCurrentData() {
      // exportData 方法现在返回 RichExecutionGraph 实例
      const richGraphData = this.$refs.graphEditor.exportData();
      console.log('导出的富执行图数据 (RichExecutionGraph):', richGraphData);
      // 如果需要基础的 ExecutionGraph 数据，可以调用其 toExecutionGraph() 方法
      const executionGraphData = richGraphData.toExecutionGraph();
      console.log('导出的执行图数据 (ExecutionGraph):', executionGraphData);
      alert('数据已导出到控制台！');
    },
    toggleReadonly() {
      this.isReadonly = !this.isReadonly;
    }
  }
};
</script>

<style scoped>
pre {
  background-color: #f4f4f4;
  padding: 10px;
  border-radius: 5px;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px; /* 限制高度 */
  overflow-y: auto; /* 允许滚动 */
}
</style>
```

## Props

| Prop 名称     | 类型               | 默认值   | 是否必须 | 说明                                                               |
| :------------ | :----------------- | :----- | :--- | :----------------------------------------------------------------- |
| `graphData`   | `RichExecutionGraph` | `null` | 否    | 富执行图数据，必须为 `RichExecutionGraph` 类型实例。组件将加载此数据并渲染。支持 `.sync` 修饰符或 `v-model` 实现双向绑定。 |
| `initialGraphData`   | `RichExecutionGraph` | `null` | 否    | 初始的执行图数据，用于指定"重置操作"时回滚到的状态。 |
| `readonly`    | `Boolean`          | `false`| 否    | 是否启用只读模式。只读模式下，禁止添加、删除、移动节点/边，禁用框选。                     |
| `showMinimap` | `Boolean`          | `true` | 否    | 是否显示小地图插件。                                                       |
| `showToolbar` | `Boolean`          | `true` | 否    | 是否显示内置工具栏（包含缩放和适应画布按钮）。                                      |
| `config`      | `Object`           | `{}`   | 否    | 自定义 AntV X6 Graph 实例的配置选项，会与默认配置合并。                            |

## Events
| Event 名称            | 说明                                                                                                               | 回调参数                                                                                                                                                                            |
| :------------------ | :--------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `node-click`        | 节点被点击时触发。                                                                                                        | `({ node: RichGraphNode, event: MouseEvent })`: 被点击的富节点对象和原生事件。                                                                                                                 |
| `node-select`       | 节点被选中时触发 (通过 `cell:click` 触发)。                                                                                   | `({ node: RichGraphNode, event: MouseEvent })`: 被选中的富节点对象和原生事件。                                                                                                                 |
| `edge-click`        | 边被点击时触发。                                                                                                         | `({ edge: RichGraphEdge, event: MouseEvent })`: 被点击的富边对象和原生事件。                                                                                                                  |
| `edge-select`       | 边被选中时触发 (通过 `edge:click` 触发)。                                                                                    | `({ edge: RichGraphEdge, event: MouseEvent })`: 被选中的富边对象和原生事件。                                                                                                                  |
| `blank-click`       | 画布空白处被点击时触发。                                                                                                     | `({ event: MouseEvent })`: 原生事件。                                                                                                                                                |
| `graph-data-change` | 富执行图数据发生变化时触发，用于配合 `graphData` prop 实现 `.sync` 或 `v-model` 双向绑定。此外，组件内部在进行添加/删除节点或边、更新节点配置等细粒度操作后，会额外触发对应的细粒度事件。 | `(updatedGraph: RichExecutionGraph)`: 更新后的富执行图实例。                                                                                                                               |
| `selection-change`  | 选中状态发生变化时触发。                                                                                                     | `({ added: Array<RichGraphNode\|RichGraphEdge>, removed: Array<RichGraphNode\|RichGraphEdge>, selected: Array<RichGraphNode\|RichGraphEdge> })`: 新增选中、移除选中和当前所有选中的富单元格（节点或边）数组。 |
| `nodes-deselect`    | 当画布上所有节点和边都被取消选中时触发（例如点击画布空白处）。                                                                                  | 无                                                                                                                                                                               |
| `node-add`          | 通过组件方法 `addNode` 添加节点成功后触发。                                                                                      | `({ node: RichGraphNode })`: 添加的富节点对象。                                                                                                                                          |
| `node-remove`       | 通过组件方法 `removeNode` 或 `removeCell` 删除节点成功后触发。                                                                    | `({ nodeId: string })`: 被删除的节点 ID。                                                                                                                                              |
| `node-connect`      | 通过组件方法 `addEdge` 添加边成功后触发（通常表示节点之间连接）。                                                                           | `({ edge: RichGraphEdge, source: string, target: string })`: 添加的富边对象及其源/目标节点 ID。                                                                                                |
| `init-error`        | 图形编辑器初始化失败时触发。                                                                                                   | `(error: Error)`: 初始化失败的错误对象。                                                                                                                                                   |

## Methods

通过 `ref` 获取组件实例后，可以调用以下方法：

| 方法名称                | 说明                             | 参数                                                                 | 返回值                                        |
| :------------------ | :----------------------------- | :----------------------------------------------------------------- | :----------------------------------------- |
| `addNode`           | 向画布中添加一个新富节点。                  | `richNode: RichGraphNode` - 要添加的富节点实例。                             | `string` - 添加成功的节点 ID。                  |
| `removeNode`        | 根据富节点对象或 ID 删除画布中的节点。          | `node: RichGraphNode \| String` - 富节点实例或节点 ID。                     | `void`                                     |
| `addEdge`           | 向画布中添加一条新富边。                   | `richEdge: RichGraphEdge` - 要添加的富边实例。                              | `string` - 添加成功的边 ID。                   |
| `removeEdge`        | 根据富边对象或 ID 删除画布中的边。          | `edge: RichGraphEdge \| String` - 富边实例或边 ID。                         | `void`                                     |
| `reset`             | 重置执行图为初始化状态                    | 无                                                                  | `void`                                     |
| `undo`              | 撤销                             | steps - 撤销的步数，默认一步                                                 | `void`                                     |
| `redo`              | 重做                             | steps - 重做的步数，默认一步                                                 | `void`                                     |
| `zoomIn`            | 放大画布视图。                        | 无                                                                  | `void`                                     |
| `zoomOut`           | 缩小画布视图。                        | 无                                                                  | `void`                                     |
| `fitView`           | 使整个图适应画布可见区域。                  | 无                                                                  | `void`                                     |
| `setZoom`           | 设置画布的缩放级别。                     | `scale: Number` - 目标缩放比例。                                          | `void`                                     |
| `exportData`        | 导出当前画布中的所有图形数据为富执行图格式。         | 无                                                                  | `RichExecutionGraph` - 当前的富执行图实例。          |
| `getExecutionGraph` | 导出当前画布中的数据为基础的执行图格式。           | 无                                                                  | `ExecutionGraph` - 转换后的基础执行图对象。            |
| `selectNodeById`    | 根据节点ID选中画布中的节点。                  | `nodeId: string` - 要选中的节点ID。                                      | `void`                                     |
| `clearGraphSelection`| 清除画布中所有节点和边的选中状态。             | 无                                                                  | `void`                                     |

## 数据格式说明

`GraphEditor` 组件现在主要使用 **富执行图格式 (`RichExecutionGraph`)** 作为内部和外部交互的数据模型。

-   **富执行图格式 (`RichExecutionGraph`):** 这是组件推荐和主要处理的数据格式。它是一个 `RichExecutionGraph` 类的实例，包含了业务数据（节点ID、类型、配置等）和与 `@antv/x6` 相关的显示属性 (`displayAttr`)。通过 `graphData` prop 并结合 `.sync` 修饰符（或使用 `v-model`）来实现与父组件的双向绑定。组件内部的数据变化会触发 `graph-data-change` 事件，并将最新的 `RichExecutionGraph` 实例作为参数传递，父组件可以通过监听此事件来同步数据。

## 依赖

*   Vue 2.x
*   Ant Design Vue 1.7.x (用于工具栏按钮样式)
*   [@antv/x6](https://x6.antv.antgroup.com/) (核心图形引擎)
*   [@antv/x6-plugin-minimap](https://x6.antv.antgroup.com/api/plugins/minimap) (小地图插件)
*   [@antv/x6-plugin-selection](https://x6.antv.antgroup.com/api/plugins/selection) (选择插件)
*   [@antv/x6-plugin-keyboard](https://x6.antv.antgroup.com/api/plugins/keyboard) (键盘事件插件)

## 开发注意事项

*   **容器尺寸**: 组件初始化时会尝试获取容器 (`$refs.graphContainer`) 的实际宽高。如果容器初始渲染时宽高为 0，组件会进行重试。请确保在使用组件时，其父容器具有明确的尺寸，或者等待 DOM 渲染完毕后再加载数据。
*   **数据加载**: 推荐通过 `graphData` prop 绑定 `RichExecutionGraph` 实例来加载数据。如果只有基础业务数据 (`ExecutionGraph`)，务必使用 `RichExecutionGraph.fromExecutionGraph()` 方法先转换为富执行图实例再绑定。
*   **只读模式**: `readonly` prop 会禁用大部分交互操作（拖拽、框选、删除）。
*   **事件处理**: 组件提供了丰富的事件 (`node-click`, `edge-click`, `blank-click`, `graph-data-change`, `selection-change`, `nodes-deselect` 等)。`node-click` 和 `edge-click` 事件的 `payload` 现在直接包含业务模型对象 (`RichGraphNode` 或 `RichGraphEdge`)，可以直接使用。`graph-data-change` 事件用于整体数据同步，而 `node-add`、`node-remove`、`node-connect` 等细粒度事件可用于精确响应业务操作。父组件可以通过监听这些事件来实现外部逻辑（如显示属性面板、保存图形变化等）。
*   **方法参数类型**: 调用组件对外暴露的方法时，请注意参数的类型要求，特别是 `addNode` 期望 `RichGraphNode` 实例，`addEdge` 期望 `RichGraphEdge` 实例，而 `removeNode`, `removeEdge`, `selectCell` 可以接收富单元格实例或 ID。新增的 `selectNodeById` 接收节点ID，`clearGraphSelection` 无参数。
*   **插件**: 小地图、选择、键盘等功能是通过 X6 插件实现的。如果在使用中发现相关功能不工作，请检查对应的插件是否成功加载（组件内部会尝试动态导入并有警告提示）。
*   **错误处理**: 组件初始化失败会触发 `init-error` 事件，建议在父组件中监听此事件并给出适当的用户提示。