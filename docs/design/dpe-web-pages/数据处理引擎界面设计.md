# 数据处理引擎界面设计

## 简介

本文档描述了数据处理引擎的前端界面设计，包括核心业务流程、页面组件关系以及业务对象接口定义。设计基于Vue 2.7.16和Ant-Design-Vue 1.7.8实现。

## 核心业务流程

数据处理引擎的核心业务流程如下：

```mermaid
flowchart TD
    UserStart["用户"] -->|访问| GraphList["执行图列表"]
    UserStart -->|访问| SessionList["会话列表"]
    
    GraphList -->|选择| OpenGraph["导入执行图"]
    GraphList -->|根据图| CreateSession["创建会话"]
    
    SessionList -->|创建| CreateSession
    SessionList -->|选择| SessionDetail["打开会话界面"]
    CreateSession -->|完成创建| SessionDetail
    
    SessionDetail -->|设计流程 & 打开图编辑客户端| EditGraph["编辑执行图"]
    SessionDetail -->|配置元数据| MetadataConfig["配置执行图元数据"]
    SessionDetail -->|保存| SaveGraph["保存执行图"]
    SessionDetail -->|运行| RunGraph["运行执行图"]
    SessionDetail -->|查看| TaskList["查看任务列表"]

    EditGraph -->|查看| ListOperators["列出算子"]
    EditGraph -->|添加| AddNode["添加节点"]
    EditGraph -->|删除| DeleteNode["删除节点"]
    EditGraph -->|连接| ConnectNode["建立节点连接"]
    EditGraph -->|配置| NodeDetail["节点详情"]
    EditGraph -->|获取编辑状态| GetEditStatus["获取编辑状态"]
    GetEditStatus -- 如果有未提交变更 --> ShowPendingChangesDialog["显示未提交变更对话框"]
    ShowPendingChangesDialog -- 用户确认继续 / 取消 --> SaveGraph
    SaveGraph -->|保存成功| RunGraph
    
    NodeDetail -->|保存/重置| SaveConfig["保存/重置配置"]
    NodeDetail -->|运行| RunTask["提交运行任务"]
    NodeDetail -->|查看| TaskDetail["获取任务详情"]
    TaskDetail -->|预览| PreviewData["预览数据"]
    
    RunTask -->|更新状态| TaskList
```

## 核心页面和组件关系

数据处理引擎的界面由以下核心页面和组件组成：

```mermaid
classDiagram
    class DataProcessEnginePage {
        <<Page>>
        +渲染主布局()
        +切换视图()
    }
    
    class ExecutionGraphListPage {
        <<Page>>
        +分页列出执行图()
        +获取执行图信息()
        +根据图创建会话()
        +导入执行图()
    }
    
    class SessionListPage {
        <<Page>>
        +分页列出会话()
        +打开会话()
        +创建会话()
    }
    
    class SessionDetailPage {
        <<Page>>
        +保存执行图()
        +运行执行图()
        +导出执行图()
        +配置执行图元数据()
        +查看算子列表()
        +添加/删除算子()
        +建立算子连接()
        +获取当前编辑状态(): ClientEditStatus
    }
    
    class CreateSessionDialog {
        <<Component>>
        +选择后端类型()
        +设置会话参数()
        +提交创建()
        +根据后端类型动态渲染表单()
    }
    
    class NodeDetailPanel {
        <<Component>>
        +显示节点详情()
        +保存/重置配置()
        +提交运行任务()
        +显示任务状态()
        +监听任务变化()
    }
    
    class NodeConfigPanel {
        <<Component>>
        +编辑节点元数据()
        +配置算子参数()
        +保存配置()
    }
    
    class NodeTaskInfoPanel {
        <<Component>>
        +显示任务状态()
        +显示任务日志()
    }
    
    class NodeDataPanel {
        <<Component>>
        +根据数据类型选择预览组件()
        +加载数据内容()
    }
    
    class NodeDataContent {
        <<Component interface>>
    }
    
    class NodeStructuredDataContent {
        <<Component>>
        +预览结构化数据()
        +分页浏览()
        +查看数据结构()
    }
    
    class OperatorPanel {
        <<Component>>
        +显示可用算子()
        +拖拽添加算子()
    }
    
    class GraphEditor {
        <<Component>>
        +显示执行图()
        +编辑节点连接()
        +选择节点()
        +获取当前编辑状态(): ClientEditStatus
    }

    class DynamicForm {
        <<Component>>
        +渲染表单()
        +编辑表单()
        +提交表单并获取表单数据()
    }
    
    DataProcessEnginePage --> ExecutionGraphListPage : 包含
    DataProcessEnginePage --> SessionListPage : 包含
    DataProcessEnginePage --> SessionDetailPage : 包含
    
    SessionListPage --> CreateSessionDialog : 使用
    SessionDetailPage --> NodeDetailPanel : 使用
    SessionDetailPage --> OperatorPanel : 使用
    SessionDetailPage --> GraphEditor : 使用
    SessionDetailPage --> ExecutionGraphClient : 创建/使用
    GraphEditor --> ExecutionGraphClient : 使用
    
    NodeDetailPanel *-- NodeConfigPanel : 包含
    NodeDetailPanel *-- NodeTaskInfoPanel : 包含
    NodeDetailPanel *-- NodeDataPanel : 包含
    
    NodeDataPanel --> NodeDataContent : 使用
    NodeDataContent <|-- NodeStructuredDataContent : 继承

    CreateSessionDialog --> DynamicForm : 使用
    NodeConfigPanel --> DynamicForm : 使用
```

## 核心业务对象和依赖服务JavaScript接口定义

### 核心业务对象

以下是核心业务对象之间的关系图：

```mermaid
classDiagram
    class ObjectMetadata {
        +string displayName
        +string description
        +Object labels
        +Object annotations
    }
    
    class BackendInfo {
        +string type
        +ObjectMetadata metadata
        +Array~ConfigField~ sessionConfigs
        +constructor(data)
    }
    
    class ExecutionGraph {
        +string id
        +ObjectMetadata metadata
        +Object~string,GraphNode~ nodes
        +Array~GraphEdge~ edges
        +constructor(data)
    }
    
    class GraphNode {
        +string id
        +ObjectMetadata metadata
        +string opType
        +Object opConfig
        +constructor(data)
    }
    
    class GraphEdge {
        +string source
        +string target
        +constructor(data)
    }

    class PendingChanges {
        +Object~string,GraphNode~ nodesToAdd
        +Object~string,Object~ nodesToUpdate
        +Array~string~ nodesToDelete
        +Array~GraphEdge~ edgesToAdd
        +Array~GraphEdge~ edgesToDelete
        +constructor(data)
    }

    class ClientEditStatus {
        +boolean hasPendingChanges
        +ExecutionGraph currentEffectiveGraph
        +ExecutionGraph currentGraph
        +PendingChanges pendingChanges
        +constructor(data)
    }
    
    class TaskInfo {
        +string id
        +string sessionId
        +string status
        +string message
        +Date createdAt
        +Date updatedAt
        +string resultResourceId
        +Object result
        +boolean isUpstreamChanged
        +constructor(data)
    }
    
    class OperatorInfo {
        +string type
        +ObjectMetadata metadata
        +Array~ConfigField~ opConfigMetadata
        +Array~string~ inputFields
        +constructor(data)
    }
    
    class ConfigField {
        +string key
        +Object schema # 字段的 json-schema 
        +constructor(data)
    }
    
    ExecutionGraph "1" *-- "1" ObjectMetadata : 包含
    BackendInfo "1" *-- "1" ObjectMetadata : 包含
    BackendInfo "1" *-- "多" ConfigField : 包含
    ExecutionGraph "1" *-- "多" GraphNode : 包含
    ExecutionGraph "1" *-- "多" GraphEdge : 包含
    GraphNode "1" *-- "1" ObjectMetadata : 包含
    OperatorInfo "1" *-- "1" ObjectMetadata : 包含
    OperatorInfo "1" *-- "多" ConfigField : 包含
    TaskInfo "1" --> "0..1" GraphNode : 关联
    ClientEditStatus "1" *-- "1" PendingChanges : 包含
    ClientEditStatus "1" *-- "1" ExecutionGraph : 包含 (currentEffectiveGraph)
    ClientEditStatus "1" *-- "1" ExecutionGraph : 包含 (currentGraph)


```

#### BackendInfo（后端信息）
```javascript
/**
 * 后端信息类，描述数据处理后端的信息和配置
 */
class BackendInfo {
  /**
   * 后端类型
   * @type {string}
   */
  type;

  /**
   * 后端元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 会话配置字段，用于创建会话时动态渲染表单
   * @type {Array<ConfigField>}
   */
  sessionConfigs;

  constructor(data) {
    this.type = data.type;
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.sessionConfigs = (data.sessionConfigs || []).map(field => new ConfigField(field));
  }
}

/**
 * 待提交的图变更集合
 */
class PendingChanges {
  /**
   * 要添加的节点映射：节点ID -> GraphNode
   * @type {Object<string, GraphNode>}
   */
  nodesToAdd;

  /**
   * 要更新的节点配置映射：节点ID -> 配置更新字典
   * @type {Object<string, Object>}
   */
  nodesToUpdate;

  /**
   * 要删除的节点ID集合
   * @type {Array<string>}
   */
  nodesToDelete;

  /**
   * 要添加的边列表
   * @type {Array<GraphEdge>}
   */
  edgesToAdd;

  /**
   * 要删除的边列表
   * @type {Array<GraphEdge>}
   */
  edgesToDelete;

  constructor(data) {
    this.nodesToAdd = {};
    if (data.nodesToAdd) {
      Object.keys(data.nodesToAdd).forEach(nodeId => {
        this.nodesToAdd[nodeId] = new GraphNode(data.nodesToAdd[nodeId]);
      });
    }
    this.nodesToUpdate = data.nodesToUpdate || {};
    this.nodesToDelete = data.nodesToDelete || [];
    this.edgesToAdd = (data.edgesToAdd || []).map(edge => new GraphEdge(edge));
    this.edgesToDelete = (data.edgesToDelete || []).map(edge => new GraphEdge(edge));
  }
}

/**
 * 客户端编辑状态信息
 */
class ClientEditStatus {
  /**
   * 是否有未提交的变更
   * @type {boolean}
   */
  hasPendingChanges;

  /**
   * 当前会话实际生效的执行图
   * @type {ExecutionGraph}
   */
  currentEffectiveGraph;

  /**
   * 当前客户端对应的执行图（包含未提交的变更）
   * @type {ExecutionGraph}
   */
  currentGraph;

  /**
   * 未提交的执行图变更详情
   * @type {PendingChanges}
   */
  pendingChanges;

  constructor(data) {
    this.hasPendingChanges = data.hasPendingChanges;
    this.currentEffectiveGraph = new ExecutionGraph(data.currentEffectiveGraph || {});
    this.currentGraph = new ExecutionGraph(data.currentGraph || {});
    this.pendingChanges = new PendingChanges(data.pendingChanges || {});
  }
}

#### ExecutionGraph（执行图）

```javascript
/**
 * 执行图类，表示一个数据处理工作流
 */
class ExecutionGraph {
  /**
   * 执行图ID
   * @type {string}
   */
  id;

  /**
   * 图的元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 图的节点集合，键为节点ID，值为节点对象
   * @type {Object<string, GraphNode>}
   */
  nodes;

  /**
   * 图的边集合
   * @type {Array<GraphEdge>}
   */
  edges;

  constructor(data) {
    this.id = data.id;
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.nodes = {};
    this.edges = data.edges || [];
    
    // 初始化节点
    if (data.nodes) {
      Object.keys(data.nodes).forEach(nodeId => {
        this.nodes[nodeId] = new GraphNode(data.nodes[nodeId]);
      });
    }
  }
}

/**
 * 对象元数据类
 */
class ObjectMetadata {
  /**
   * 显示名称
   * @type {string}
   */
  displayName;

  /**
   * 描述
   * @type {string}
   */
  description;

  /**
   * 标签
   * @type {Object<string, string>}
   */
  labels;

  /**
   * 注解
   * @type {Object<string, string>}
   */
  annotations;

  constructor(data) {
    this.displayName = data.displayName || '';
    this.description = data.description || '';
    this.labels = data.labels || {};
    this.annotations = data.annotations || {};
  }
}

/**
 * 图节点类，表示一个算子实例
 */
class GraphNode {
  /**
   * 节点ID
   * @type {string}
   */
  id;

  /**
   * 节点元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 算子类型
   * @type {string}
   */
  opType;

  /**
   * 算子配置
   * @type {Object}
   */
  opConfig;

  constructor(data) {
    this.id = data.id;
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.opType = data.opType;
    this.opConfig = data.opConfig || {};
  }
}

/**
 * 图边类，表示节点之间的连接
 */
class GraphEdge {
  /**
   * 源节点ID
   * @type {string}
   */
  source;

  /**
   * 目标节点ID
   * @type {string}
   */
  target;

  /**
   * 目标节点的目标配置属性
   * @type {string}
   */
  targetConfig;

  constructor(data) {
    this.source = data.source;
    this.target = data.target;
    this.targetConfig = data.targetConfig;
  }
}
```

#### TaskInfo（任务信息）

```javascript
/**
 * 任务状态常量定义
 */
const TaskStatus = {
  CREATED: 'CREATED',     // 任务已创建但未开始执行
  PENDING: 'PENDING',     // 任务已提交执行但存在需要等待满足的条件
  RUNNING: 'RUNNING',     // 任务正在执行
  SUCCESS: 'SUCCESS',     // 任务成功完成
  ERROR: 'ERROR',         // 任务执行失败
  CANCELLED: 'CANCELLED', // 任务被取消
  UNKNOWN: 'UNKNOWN'      // 状态未知或无法确定
};

/**
 * 任务信息类，表示一个算子的执行任务
 */
class TaskInfo {
  /**
   * 任务ID
   * @type {string}
   */
  id;

  /**
   * 会话ID
   * @type {string}
   */
  sessionId;

  /**
   * 任务状态，使用TaskStatus常量
   * @type {string}
   */
  status;

  /**
   * 任务消息
   * @type {string}
   */
  message;

  /**
   * 创建时间
   * @type {Date}
   */
  createdAt;

  /**
   * 更新时间
   * @type {Date}
   */
  updatedAt;

  /**
   * 结果资源ID（ResourceId在JavaScript中表示为字符串）
   * @type {string}
   */
  resultResourceId;

  /**
   * 结果数据
   * @type {Object}
   */
  result;

  /**
   * 上游是否发生变更
   * @type {boolean}
   */
  isUpstreamChanged;

  constructor(data) {
    this.id = data.id;
    this.sessionId = data.sessionId;
    this.status = data.status || TaskStatus.PENDING;
    this.message = data.message || '';
    this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
    this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
    this.resultResourceId = data.resultResourceId;
    this.result = data.result;
    this.isUpstreamChanged = data.isUpstreamChanged || false;
  }
}
```

#### OperatorInfo（算子配置）

```javascript
/**
 * 算子配置类
 */
class OperatorInfo {
  /**
   * 算子类型
   * @type {string}
   */
  type;

  /**
   * 元数据
   * @type {ObjectMetadata}
   */
  metadata;

  /**
   * 配置元数据，定义算子的配置项
   * @type {Array<ConfigField>}
   */
  opConfigMetadata;

  /**
   * 输入字段列表，作为上游输入的字段
   * @type {Array<string>}
   */
  inputFields;

  constructor(data) {
    this.type = data.type;
    this.metadata = new ObjectMetadata(data.metadata || {});
    this.opConfigMetadata = (data.opConfigMetadata || []).map(field => new ConfigField(field));
    this.inputFields = data.inputFields || [];
  }
}

/**
 * 配置字段定义
 */
class ConfigField {
  /**
   * 字段键
   * @type {string}
   */
  key;

  /**
   * 字段的json-schema模式定义
   * @type {Object}
   */
  schema;

  constructor(data) {
    this.key = data.key;
    this.schema = data.schema || {};
  }
}
```

### 依赖服务接口

以下是依赖服务接口之间的关系图：

```mermaid
classDiagram 
    direction LR
    class PageParam {
        +number pageIndex
        +number limit
        +number pageTotal
        +number recordTotal
        +string sortField
        +string sortType
        +Array sortList
        +boolean isTop
    }

    class BackendInfo {
        +string type
        +ObjectMetadata metadata
        +Array~ConfigField~ sessionConfigs
    }

    class Session {
        +string id
        +string backendType
        +string state
        +Date createdAt
        +Date updatedAt
        +openGraphClient(): ExecutionGraphClient
    }
    
    class SessionLifecycleListener {
        <<interface>>
        +onSessionCreated(session) void
        +onSessionStateChanged(session, oldState, newState) void
        +onSessionClosed(session) void
    }
    
    class TaskLifecycleListener {
        <<interface>>
        +onTaskStatusChanged(task, oldStatus, newStatus) void
        +onTaskCompleted(task) void
        +onTaskError(task, error) void
    }
    
    class Subscription {
        +unsubscribe() void
    }
    
    class DataProcessService {
        +async getBackendTypes() Array~BackendInfo~
        +async createSession() Session
        +async listSessions() Object
        +async getSession() Session
        +async closeSession() boolean
        +listenSessionLifecycle(listener) Subscription
        +async listOperatorTypes() Object
        +async getOperatorInfo(backendType, operatorType) OperatorInfo
        +async runOperator() TaskInfo
        +async listTasks() Object
        +async getTaskInfo() TaskInfo
        +async cancelTask() boolean
        +async runGraph() Array~TaskInfo~
        +async openGraphClient(sessionId) ExecutionGraphClient
        +async listenTaskLifecycle(sessionId, listener) Subscription
    }
    
    class MergeMode {
        <<Enumeration>>
        FAILED_ON_CONFLICT
        OVERWRITE_EXISTING
        MERGE_AND_UPDATE
    }

    class VerifyResult {
        +boolean valid
        +Array~VerifyFailureDetail~ failed_details
    }

    class VerifyFailureDetail {
        +string rule_type
        +string message
        +Object extra_info
    }

    class DagTaskInfo {
        <<Interface>>
        +string node_id
        +OperatorInfo operator
        +Array~string~ dependencies
        +Array~string~ dependents
        +boolean upstream_changed
    }

    class ExecutionGraphClient {
        <<interface>>
        +get_edit_status() ClientEditStatus
        +add_nodes(nodes: Array~GraphNode~) Array~string~
        +update_nodes_config(id_ref_configs: Object~string,Object~) Array~string~
        +delete_nodes(node_ids: Array~string~) Array~string~
        +add_edges(edges: Array~GraphEdge~) Array~string~
        +delete_edges(edges: Array~GraphEdge~) Array~string~
        +add_op(op: OperatorInfo) OperatorInfo
        +load_graph(graph: ExecutionGraph, merge_mode: MergeMode) Array~string~
        +verify() VerifyResult
        +save() Array~string~
        +export_graph() ExecutionGraph
        +resolve_task(op: OperatorInfo) DagTaskInfo
        +close() void
    }

    class ExecutionGraphRepositoryService {
        +async listGraphs() Object
        +async getGraph() ExecutionGraph
        +async saveGraph() ExecutionGraph
        +async createSessionFromGraph() Session
        +async importGraph() ExecutionGraph
    }
    
    class ResourceService {
        +async getDataResource(resourceId) DataResource
        +async openStructuredDataResourceClient(resourceId) StructuredDataResourceClient
    }
    
    class DataResourceClient {
        <<interface>>
        +async close() void
    }
    
    class StructuredDataResourceClient {
        <<interface>>
        +async previewData(page, pageSize) Object
        +async getSchema() Array~SchemaField~
    }
    
    class DataResource {
        +string resourceId
        +string resourceType
        +ObjectMetadata metadata
    }
    
    class StructuredDataResource {
        +string resourceId
        +Array~SchemaField~ schema
    }
    
    class ListSessionParams {
        +PageParam pageParam
        +string backendType
        +Object filters
    }
    
    class ListGraphParams {
        +PageParam pageParam
        +Object filters
    }
    
    class ListOperatorTypesParams {
        +PageParam pageParam
        +string category
    }
    
    class ListTasksParams {
        +PageParam pageParam
        +string sessionId
        +string status
        +boolean withDetails
    }

    class SchemaField {
        +string name
        +string type
        +boolean nullable
        +Object metadata
    }


    DataProcessService ..> BackendInfo : returns
    DataProcessService ..> PageParam : uses
    ExecutionGraphRepositoryService ..> PageParam : uses
    DataProcessService ..> ListSessionParams : accepts
    ExecutionGraphRepositoryService ..> ListGraphParams : accepts
    DataProcessService ..> ListOperatorTypesParams : accepts
    DataProcessService ..> ListTasksParams : accepts
    
    DataResource <|-- StructuredDataResource : extends
    ResourceService ..> DataResource : returns
    DataResourceClient <|-- StructuredDataResourceClient : extends
    ResourceService --> DataResourceClient : creates
    ResourceService ..> SchemaField : uses
    
    DataProcessService ..> SessionLifecycleListener : notifies
    DataProcessService ..> TaskLifecycleListener : notifies
    
    DataProcessService --> ExecutionGraphClient : returns
    DataProcessService --> Subscription : returns

    TaskInfo <|-- DagTaskInfo : extends
    ExecutionGraphClient --> ExecutionGraph : manages
    ExecutionGraphClient --> DagTaskInfo : resolves
    ExecutionGraphClient --> MergeMode : uses
    VerifyResult *-- VerifyFailureDetail : contains
```

#### 分页参数定义

```javascript
/**
 * 分页参数类
 */
class PageParam {
  /**
   * 当前页码，从0开始
   * @type {number}
   */
  pageIndex;

  /**
   * 每页记录数
   * @type {number}
   */
  limit;

  /**
   * 总页数
   * @type {number}
   */
  pageTotal;

  /**
   * 总记录数
   * @type {number}
   */
  recordTotal;

  /**
   * 排序字段
   * @type {string}
   */
  sortField;

  /**
   * 排序类型（asc/desc）
   * @type {string}
   */
  sortType;

  /**
   * 排序列表
   * @type {Array}
   */
  sortList;

  /**
   * 是否置顶
   * @type {boolean}
   */
  isTop;
}
```

#### 会话和资源模型定义

```javascript
/**
 * 会话状态常量定义
 */
const SessionState = {
  CREATED: 'CREATED',     // 会话已创建但未使用
  RUNNING: 'RUNNING',     // 会话正在运行
  CLOSING: 'CLOSING',     // 会话正在关闭
  CLOSED: 'CLOSED'        // 会话已关闭
};

/**
 * 会话类
 */
class Session {
  /**
   * 会话ID
   * @type {string}
   */
  id;

  /**
   * 后端类型
   * @type {string}
   */
  backendType;

  /**
   * 会话状态，使用SessionState常量
   * @type {string}
   */
  state;

  /**
   * 创建时间
   * @type {Date}
   */
  createdAt;

  /**
   * 更新时间
   * @type {Date}
   */
  updatedAt;

  constructor(data) {
    this.id = data.id;
    this.backendType = data.backendType;
    this.state = data.state || SessionState.CREATED;
    this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
    this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
  }
}

/**
 * 数据资源基类
 */
class DataResource {
  /**
   * 资源ID
   * @type {string}
   */
  resourceId;

  /**
   * 资源类型
   * @type {string}
   */
  resourceType;

  /**
   * 资源元数据
   * @type {ObjectMetadata}
   */
  metadata;
}

/**
 * 结构化数据资源
 */
class StructuredDataResource extends DataResource {
  /**
   * 数据结构定义
   * @type {Array<SchemaField>}
   */
  schema;
}

/**
 * 数据结构字段定义
 */
class SchemaField {
  /**
   * 字段名称
   * @type {string}
   */
  name;

  /**
   * 字段类型
   * @type {string}
   */
  type;

  /**
   * 是否可为空
   * @type {boolean}
   */
  nullable;

  /**
   * 字段元数据
   * @type {Object}
   */
  metadata;
}
```

#### 会话和任务状态监听

```javascript
/**
 * 订阅对象，用于取消监听
 */
class Subscription {
  /**
   * 取消订阅
   */
  unsubscribe() {
    // 执行取消订阅的逻辑
  }
}

/**
 * 会话生命周期监听器接口
 */
class SessionLifecycleListener {
  /**
   * 当会话被创建时调用
   * @param {Session} session - 创建的会话
   */
  onSessionCreated(session) {}
  
  /**
   * 当会话状态发生变更时调用
   * @param {Session} session - 会话
   * @param {string} oldState - 变更前的会话状态
   * @param {string} newState - 变更后的会话状态
   */
  onSessionStateChanged(session, oldState, newState) {}
  
  /**
   * 当会话被关闭时调用
   * @param {Session} session - 关闭的会话
   */
  onSessionClosed(session) {}
}

/**
 * 任务生命周期监听器接口
 */
class TaskLifecycleListener {
  /**
   * 当任务状态发生变更时调用
   * @param {TaskInfo} task - 任务
   * @param {string} oldStatus - 变更前的任务状态
   * @param {string} newStatus - 变更后的任务状态
   */
  onTaskStatusChanged(task, oldStatus, newStatus) {}
  
  /**
   * 当任务完成时调用
   * @param {TaskInfo} task - 完成的任务
   */
  onTaskCompleted(task) {}
  
  /**
   * 当任务出错时调用
   * @param {TaskInfo} task - 出错的任务
   * @param {Error} error - 错误信息
   */
  onTaskError(task, error) {}
}
```

#### DataProcessService（数据处理服务）

```javascript
/**
 * 数据处理服务参数定义
 */
class ListSessionParams {
  /**
   * 分页参数
   * @type {PageParam}
   */
  pageParam;

  /**
   * 后端类型过滤
   * @type {string}
   */
  backendType;

  /**
   * 其它过滤条件
   * @type {Object}
   */
  filters;
}

/**
 * 算子类型查询参数
 */
class ListOperatorTypesParams {
  /**
   * 分页参数
   * @type {PageParam}
   */
  pageParam;

  /**
   * 算子类别
   * @type {string}
   */
  category;
}

/**
 * 任务查询参数
 */
class ListTasksParams {
  /**
   * 分页参数
   * @type {PageParam}
   */
  pageParam;

  /**
   * 会话ID
   * @type {string}
   */
  sessionId;

  /**
   * 任务状态
   * @type {string}
   */
  status;

  /**
   * 是否包含详情
   * @type {boolean}
   */
  withDetails;
}

/**
 * 待提交的图变更集合
 */
class PendingChanges {
  /**
   * 要添加的节点映射：节点ID -> GraphNode
   * @type {Object<string, GraphNode>}
   */
  nodesToAdd;

  /**
   * 要更新的节点配置映射：节点ID -> 配置更新字典
   * @type {Object<string, Object>}
   */
  nodesToUpdate;

  /**
   * 要删除的节点ID集合
   * @type {Array<string>}
   */
  nodesToDelete;

  /**
   * 要添加的边列表
   * @type {Array<GraphEdge>}
   */
  edgesToAdd;

  /**
   * 要删除的边列表
   * @type {Array<GraphEdge>}
   */
  edgesToDelete;

  constructor(data) {
    this.nodesToAdd = {};
    if (data.nodesToAdd) {
      Object.keys(data.nodesToAdd).forEach(nodeId => {
        this.nodesToAdd[nodeId] = new GraphNode(data.nodesToAdd[nodeId]);
      });
    }
    this.nodesToUpdate = data.nodesToUpdate || {};
    this.nodesToDelete = data.nodesToDelete || [];
    this.edgesToAdd = (data.edgesToAdd || []).map(edge => new GraphEdge(edge));
    this.edgesToDelete = (data.edgesToDelete || []).map(edge => new GraphEdge(edge));
  }
}

/**
 * 客户端编辑状态信息
 */
class ClientEditStatus {
  /**
   * 是否有未提交的变更
   * @type {boolean}
   */
  hasPendingChanges;

  /**
   * 当前会话实际生效的执行图
   * @type {ExecutionGraph}
   */
  currentEffectiveGraph;

  /**
   * 当前客户端对应的执行图（包含未提交的变更）
   * @type {ExecutionGraph}
   */
  currentGraph;

  /**
   * 未提交的执行图变更详情
   * @type {PendingChanges}
   */
  pendingChanges;

  constructor(data) {
    this.hasPendingChanges = data.hasPendingChanges;
    this.currentEffectiveGraph = new ExecutionGraph(data.currentEffectiveGraph || {});
    this.currentGraph = new ExecutionGraph(data.currentGraph || {});
    this.pendingChanges = new PendingChanges(data.pendingChanges || {});
  }
}

/**
 * 执行图客户端接口，提供图的编辑和管理功能
 */
class ExecutionGraphClient {

  /**
   * 获取当前编辑状态
   * @returns {Promise<ClientEditStatus>} 当前编辑状态
   */
  async get_edit_status() {
    // TODO: 实现获取当前编辑状态
    return Promise.resolve(new ClientEditStatus({}));
  }

  /**
   * 添加节点
   * @param {Array<GraphNode>} nodes - 要添加的节点列表
   * @returns {Promise<Array<string>>} 添加成功的节点ID列表
   */
  async add_nodes(nodes) {
    // TODO: 实现添加节点
    return Promise.resolve([]);
  }

  /**
   * 更新节点的配置
   * @param {Object<string, Object<string,Any>>} id_ref_configs - 键为节点ID，值为新配置的对象
   * @returns {Promise<Array<string>>} 更新成功的节点ID列表
   */
  async update_nodes_config(id_ref_configs) {
    // TODO: 实现更新节点配置
    return Promise.resolve([]);
  }

  /**
   * 删除节点
   * @param {Array<string>} node_ids - 要删除的节点ID列表
   * @returns {Promise<Array<string>>} 删除成功的节点ID列表
   */
  async delete_nodes(node_ids) {
    // TODO: 实现删除节点
    return Promise.resolve([]);
  }

  /**
   * 添加边
   * @param {Array<GraphEdge>} edges - 要添加的边列表
   * @returns {Promise<Array<string>>} 添加成功的边ID列表
   */
  async add_edges(edges) {
    // TODO: 实现添加边
    return Promise.resolve([]);
  }

  /**
   * 删除边
   * @param {Array<GraphEdge>} edges - 要删除的边列表
   * @returns {Promise<Array<string>>} 删除成功的边ID列表
   */
  async delete_edges(edges) {
    // TODO: 实现删除边
    return Promise.resolve([]);
  }

  /**
   * 添加算子
   * @param {OperatorInfo} op - 要添加的算子信息
   * @returns {Promise<OperatorInfo>} 添加的算子信息
   */
  async add_op(op) {
    // TODO: 实现添加算子
    return Promise.resolve(new OperatorInfo({}));
  }

  /**
   * 加载执行图
   * @param {ExecutionGraph} graph - 要加载的执行图
   * @param {MergeMode} merge_mode - 合并模式
   * @returns {Promise<Array<string>>} 加载结果（例如：冲突列表）
   */
  async load_graph(graph, merge_mode) {
    // TODO: 实现加载执行图
    return Promise.resolve([]);
  }

  /**
   * 验证执行图
   * @returns {Promise<VerifyResult>} 验证结果
   */
  async verify() {
    // TODO: 实现验证执行图
    return Promise.resolve({});
  }

  /**
   * 保存执行图
   * @returns {Promise<Array<string>>} 保存结果（例如：保存成功的节点ID列表）
   */
  async save() {
    // TODO: 实现保存执行图
    return Promise.resolve([]);
  }

  /**
   * 导出执行图
   * @returns {Promise<ExecutionGraph>} 导出的执行图
   */
  async export_graph() {
    // TODO: 实现导出执行图
    return Promise.resolve(new ExecutionGraph({}));
  }

  /**
   * 解析任务
   * @param {OperatorInfo} op - 算子信息
   * @returns {Promise<DagTaskInfo>} DAG任务信息
   */
  async resolve_task(op) {
    // TODO: 实现解析任务
    return Promise.resolve({});
  }

  /**
   * 关闭客户端
   * @returns {Promise<void>}
   */
  async close() {
    // TODO: 实现关闭客户端
    return Promise.resolve();
  }
}

/**
 * 数据处理服务，提供与后端DataProcessEngine交互的方法
 * 整合了后端管理、会话管理、算子管理、任务管理等功能
 */
const DataProcessService = {
  /**
   * 获取所有可用的后端类型列表
   * @returns {Promise<Array<BackendInfo>>} 后端类型信息列表，包含类型、元数据和会话配置
   */
  async getBackendTypes() {
    // TODO: 实现获取后端类型列表
    return Promise.resolve([]);
  },
  
  /**
   * 创建新的数据处理会话
   * @param {string} backendType - 后端类型
   * @param {Object} options - 会话选项
   * @returns {Promise<Session>} 创建的会话信息
   */
  async createSession(backendType, options) {
    // TODO: 实现创建会话
    return Promise.resolve(new Session());
  },
  
  /**
   * 获取会话列表
   * @param {ListSessionParams} params - 查询参数
   * @returns {Promise<{data: Array<Session>, pageParam: PageParam}>} 分页的会话列表
   */
  async listSessions(params) {
    // TODO: 实现获取会话列表
    return Promise.resolve({ data: [], pageParam: new PageParam() });
  },
  
  /**
   * 获取会话详情
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Session>} 会话详情
   */
  async getSession(sessionId) {
    // TODO: 实现获取会话详情
    return Promise.resolve(new Session());
  },
  
  /**
   * 关闭会话
   * @param {string} sessionId - 会话ID
   * @returns {Promise<boolean>} 是否成功
   */
  async closeSession(sessionId) {
    // TODO: 实现关闭会话
    return Promise.resolve(true);
  },
  
  /**
   * 注册会话生命周期监听器
   * @param {SessionLifecycleListener} listener - 生命周期监听器
   * @returns {Subscription} 订阅对象，用于取消监听
   */
  listenSessionLifecycle(listener) {
    // TODO: 实现注册会话生命周期监听器
    return new Subscription();
  },
  
  /**
   * 获取所有可用的算子类型
   * @param {ListOperatorTypesParams} params - 查询参数
   * @returns {Promise<{data: Array<OperatorInfo>, pageParam: PageParam}>} 算子类型列表
   */
  async listOperatorTypes(params) {
    // TODO: 实现获取算子类型列表
    return Promise.resolve({ data: [], pageParam: new PageParam() });
  },
  
  /**
   * 获取算子详情配置
   * @param {string} backendType - 后端类型
   * @param {string} operatorType - 算子类型
   * @returns {Promise<OperatorInfo>} 算子配置
   */
  async getOperatorInfo(backendType, operatorType) {
    // TODO: 实现获取算子配置
    return Promise.resolve(new OperatorInfo({}));
  },
  
  /**
   * 提交运行算子任务
   * @param {string} sessionId - 会话ID
   * @param {string} nodeId - 节点ID
   * @param {Object} config - 算子配置
   * @returns {Promise<TaskInfo>} 任务信息
   */
  async runOperator(sessionId, nodeId, config) {
    // TODO: 实现提交运行算子任务
    return Promise.resolve(new TaskInfo({}));
  },
  
  /**
   * 获取任务列表
   * @param {ListTasksParams} params - 查询参数
   * @returns {Promise<{data: Array<TaskInfo>, pageParam: PageParam}>} 任务列表
   */
  async listTasks(params) {
    // TODO: 实现获取任务列表
    return Promise.resolve({ data: [], pageParam: new PageParam() });
  },
  
  /**
   * 获取任务详情
   * @param {string} sessionId - 会话ID
   * @param {string} taskId - 任务ID
   * @returns {Promise<TaskInfo>} 任务详情
   */
  async getTaskInfo(sessionId, taskId) {
    // TODO: 实现获取任务详情
    return Promise.resolve(new TaskInfo({}));
  },
  
  /**
   * 取消任务
   * @param {string} sessionId - 会话ID
   * @param {string} taskId - 任务ID
   * @returns {Promise<boolean>} 是否成功
   */
  async cancelTask(sessionId, taskId) {
    // TODO: 实现取消任务
    return Promise.resolve(true);
  },
  
  /**
   * 运行完整执行图
   * @param {string} sessionId - 会话ID
   * @returns {Promise<Array<TaskInfo>>} 任务列表
   */
  async runGraph(sessionId) {
    // TODO: 实现运行完整执行图
    return Promise.resolve([]);
  },

  /**
   * 获取执行图客户端接口
   * @param {string} sessionId - 会话ID
   * @returns {Promise<ExecutionGraphClient>} 执行图客户端接口实例
   */
  async openGraphClient(sessionId) {
    // TODO: 实现获取执行图客户端接口
    // 实际会建立 WebSocket 连接并返回一个包装了 WebSocket 交互的客户端实例
    return new ExecutionGraphClient(sessionId); // 示例返回
  },
  
  /**
   * 根据执行图创建会话
   * @param {string} graphId - 执行图ID
   * @param {string} backendType - 后端类型
   * @param {Object} options - 会话选项
   * @returns {Promise<Session>} 创建的会话信息
   */
  async createSessionFromGraph(graphId, backendType, options) {
    // TODO: 实现根据执行图创建会话
    return Promise.resolve(new Session());
  },
  
  /**
   * 注册任务生命周期监听器
   * @param {string} sessionId - 会话ID，如果提供则只监听该会话的任务
   * @param {TaskLifecycleListener} listener - 生命周期监听器
   * @returns {Subscription} 订阅对象，用于取消监听
   */
  listenTaskLifecycle(sessionId, listener) {
    // TODO: 实现注册任务生命周期监听器
    return new Subscription();
  }
};
```

#### ExecutionGraphRepositoryService（执行图服务）

```javascript
/**
 * 执行图查询参数
 */
class ListGraphParams {
  /**
   * 分页参数
   * @type {PageParam}
   */
  pageParam;

  /**
   * 过滤条件
   * @type {Object}
   */
  filters;
}

/**
 * 执行图服务，提供执行图的管理功能
 */
const ExecutionGraphRepositoryService = {
  /**
   * 获取执行图列表
   * @param {ListGraphParams} params - 查询参数
   * @returns {Promise<{data: Array<ExecutionGraph>, pageParam: PageParam}>} 分页的执行图列表
   */
  async listGraphs(params) {
    // TODO: 实现获取执行图列表
    return Promise.resolve({ data: [], pageParam: new PageParam() });
  },
  
  /**
   * 获取执行图详情
   * @param {string} graphId - 执行图ID
   * @returns {Promise<ExecutionGraph>} 执行图详情
   */
  async getGraph(graphId) {
    // TODO: 实现获取执行图详情
    return Promise.resolve(new ExecutionGraph({}));
  },
  
  /**
   * 保存执行图
   * @param {ExecutionGraph} graph - 执行图对象
   * @returns {Promise<ExecutionGraph>} 保存后的执行图
   */
  async saveGraph(graph) {
    // TODO: 实现保存执行图
    return Promise.resolve(graph);
  },
  
  
  /**
   * 导入执行图
   * @param {File|Object} graphData - 执行图数据或文件
   * @returns {Promise<ExecutionGraph>} 导入的执行图
   */
  async importGraph(graphData) {
    // TODO: 实现导入执行图
    return Promise.resolve(new ExecutionGraph({}));
  }
};
```

#### ResourceService（资源服务）

```javascript
/**
 * 资源服务，提供数据资源的访问功能
 * 
 * 【重要说明】此服务是通用资源访问的入口，通过 openStructuredDataResourceClient 
 * 获取的 StructuredDataResourceClient 才是对应API设计中的 /api/resources/data-resource/ 相关接口。
 * ResourceService 本身主要负责资源发现和客户端创建，具体的数据操作由对应的客户端完成。
 */
const ResourceService = {
  
  
  /**
   * 打开结构化数据资源客户端
   * 【重要】此方法返回的客户端对应API设计中的 /api/resources/data-resource/ 接口
   * @param {string} resourceId - 资源ID
   * @returns {Promise<StructuredDataResourceClient>} 结构化数据资源客户端
   * @throws {Error} 如果资源不是结构化数据或不存在
   */
  async openStructuredDataResourceClient(resourceId) {
    // TODO: 实现打开结构化数据资源客户端
    return Promise.resolve(new StructuredDataResourceClient());
  }
};
```

## 业务对象与后端接口对应关系

前端业务对象与后端数据处理引擎接口的对应关系如下：

1. **DataProcessService** - 对应后端的 **DataProcessEngine** 和 **DataProcessBackend**，提供后端类型管理、会话创建、算子管理、任务管理等统一功能。
2. **ExecutionGraphRepositoryService** - 管理执行图模板，**注意：这是前端特有的外部持久化服务，不是数据处理引擎的职责**，负责图模板的保存、导入和导出。
3. **ResourceService** - 对应API设计中的 `/api/resources/` 接口，专门提供资源访问能力。

## 约束和限制

在实现过程中需要注意以下约束和限制：

1. **任务运行约束**：
   - 任务提交运行后，直到任务结束运行前都不能修改当前节点的配置
   - 任务提交运行后，直到任务结束运行前都不能修改或运行节点的直接上游
   - 任务提交运行后，直到任务结束运行前都不能增删节点的直接上游

2. **资源访问约束**：
   - 数据资源访问需要等待任务完成后才能获取
   - 资源预览时需要适当分页，避免大量数据传输

3. **性能约束**：
   - 图编辑器需要处理大量节点和边的情况
   - 数据预览需要支持大数据量的高效展示

4. **兼容性约束**：
   - 界面设计需要符合Vue 2.7.16和Ant-Design-Vue 1.7.8的特性和限制

5. **架构职责分离约束**：
   - ExecutionGraphRepositoryService 是外部持久化服务，不依赖数据处理引擎内部实现
   - ResourceService 只负责资源展示相关功能，不涉及数据处理逻辑

#### 数据资源客户端接口

```javascript
/**
 * 数据资源客户端接口，提供数据资源访问和清理能力
 */
class ResourceClient {

	/**
	 * 获取资源ID
	 * @returns {string} 资源ID
	 */
	getResourceId() {
		// 实现获取资源ID
	}

  /**
   * 关闭客户端并释放资源
   * @returns {Promise<void>}
   */
  async close() {
    // 实现资源清理逻辑
  }
}

/**
 * 结构化数据资源客户端接口，提供结构化数据访问能力
 * 
 * 【重要说明】此客户端对应API设计中的 /api/resources/data-resource/ 相关接口，
 * 具体包括：
 * - previewData() 对应 POST /api/resources/data-resource/structured/preview
 * - getSchema() 对应 POST /api/resources/data-resource/structured/schema
 */
class StructuredDataResourceClient extends ResourceClient {
  /**
   * 预览数据资源内容
   * 对应API: POST /api/resources/data-resource/structured/preview
   * @param {number} page - 页码
   * @param {number} pageSize - 每页条数
   * @returns {Promise<{total: number, schema: Array<SchemaField>, data: Array<Object>}>} 数据预览
   */
  async previewData(page, pageSize) {
    // TODO: 实现预览数据
    return Promise.resolve({ total: 0, schema: [], data: [] });
  }
  
  /**
   * 获取数据结构
   * 对应API: POST /api/resources/data-resource/structured/schema
   * @returns {Promise<Array<SchemaField>>} 数据结构
   */
  async getSchema() {
    // TODO: 实现获取数据结构
    return Promise.resolve([]);
  }
}
```