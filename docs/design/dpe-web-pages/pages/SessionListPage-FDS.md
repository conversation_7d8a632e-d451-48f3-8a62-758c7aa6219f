# SessionListPage 页面使用说明

## 简介

`SessionListPage` 是用于展示和管理所有"会话"的页面。用户可以在此页面上浏览、搜索、筛选所有历史和当前的会话，并可以创建新的会话或打开一个已存在的会话进入详情页。

## 路由说明

-   **路由路径**: `/dpe-web/sessions`
-   **可用路由参数**: 无

## 相关业务流程

此页面的核心业务流程包括浏览会话、创建新会话以及进入已有的会话。

```mermaid
graph TD
    subgraph SessionListPage
        A[开始] --> B[列出并分页展示会话]
        B --> C[用户按条件搜索筛选]
        C -->|找到| B
        B --> D[用户点击创建会话]
        D --> E[打开 CreateSessionDialog 组件]
        E -->|填写信息并提交| F[创建新会话]
        F -->|成功| G[跳转到 SessionDetailPage]
        B --> H[用户点击某会话的打开会话或详情链接]
        H --> G
    end
```

## 用例场景

1.  **浏览和搜索会话**
    -   **UC-SLP-1**: 页面加载时，应调用 `DataProcessService.listSessions` 方法，获取第一页的会话数据并渲染成列表。
    -   **UC-SLP-2**: 当会话数量超过一页时，应显示分页控件，用户可以点击分页控件浏览所有会话。
    -   **UC-SLP-3**: 用户在顶部的搜索框中输入关键词，或使用筛选器（如按后端类型、状态筛选），点击"搜索"按钮，页面应根据条件过滤并显示匹配的会话。
    -   **UC-SLP-4**: 用户点击"重置"按钮，清除所有搜索和筛选条件，并重新加载所有会话。

2.  **创建新会话**
    -   **UC-SLP-5**: 用户点击页面右上角的"创建会话"按钮。
    -   **UC-SLP-6**: 系统应弹出 `CreateSessionDialog` 组件，允许用户从零开始创建一个新会话。
    -   **UC-SLP-7**: 用户在对话框中完成会话配置并提交后，系统应成功创建会话，并自动跳转到该新会话的 `SessionDetailPage` 页面（例如 `/dpe-web/sessions/new-session-id`）。

3.  **打开已有会话**
    -   **UC-SLP-8**: 用户在会话列表中找到一个状态为"已创建"或"已关闭"的会话，点击其对应的"打开会话"按钮。
    -   **UC-SLP-9**: 系统应跳转到该会话的 `SessionDetailPage` 页面，路由为 `/dpe-web/sessions/detail/{sessionId}`，其中 `{sessionId}` 是被点击会话的ID。

## 布局设计

页面主要由搜索/筛选区、列表区和操作区组成。

-   **整体布局**:
    ![会话列表页面布局](../entry_session-list.png)
