# SessionDetailPage 页面使用说明

## 简介

`SessionDetailPage` 是数据处理引擎的核心工作区。它提供了一个集成的环境，用户可以在此页面上进行执行图的可视化设计、配置节点的详细参数、运行图或单个节点，并查看任务状态和结果数据。

## 路由说明

-   **路由路径**: `/dpe-web/sessions/detail/:sessionId`
-   **可用路由参数**:
    -   `sessionId` (必选): 要打开的会话的唯一标识符。

## 相关业务流程

此页面是整个数据处理流程的核心，涉及执行图编辑、节点配置、任务执行和数据预览等多个复杂流程。

```mermaid
flowchart TD
    A[进入 SessionDetailPage] --> B[加载会话数据和执行图]
    B --> C[渲染 GraphEditor, OperatorPanel, NodeDetailPanel]
    
    subgraph "执行图编辑"
        C --> D[从 OperatorPanel 拖拽算子]
        D --> E[在 GraphEditor 中添加新节点]
        E --> F[在 GraphEditor 中连接删除节点和边]
        F --> G[点击 GraphEditor 中的节点]
        G --> H[在 NodeDetailPanel 中显示节点详情]
    end

    subgraph "节点配置与运行"
        H --> I[在 NodeDetailPanel 中编辑配置]
        I --> J[点击保存]
        J --> K[保存执行图]
        I --> L[点击运行]
        L --> M[提交单个节点的运行任务]
        M --> N[在 NodeDetailPanel 中展示任务状态]
    end

    subgraph "整图操作"
        C --> O[点击顶部运行按钮]
        O --> P[运行整个执行图]
        C --> Q[点击顶部保存按钮]
        Q --> K
    end
    
    subgraph "数据预览"
        N -->|任务成功| R[在 NodeDetailPanel 的数据Tab中]
        R --> S[加载并预览结果数据]
    end
```

## 用例场景

1.  **页面加载与初始化**
    -   **UC-SDP-1**: 用户导航到 `/dpe-web/sessions/detail/:sessionId`，页面应根据 `sessionId` 调用 `DataProcessService.getSession` 和 `ExecutionGraphClient.export_graph` 加载会话和执行图数据。
    -   **UC-SDP-2**: 页面应正确初始化 `GraphEditor`、`OperatorPanel` 和 `NodeDetailPanel` 三个核心组件。`GraphEditor` 显示加载的执行图，`OperatorPanel` 显示可用的算子列表。

2.  **执行图编辑**
    -   **UC-SDP-3**: 用户从左侧 `OperatorPanel` 拖拽一个算子到中间的 `GraphEditor` 画布上，画布上应出现一个对应的新节点。并且右侧节点详情抽屉应该展开，方便用户进行节点详情配置。
    -   **UC-SDP-4**: 用户在 `GraphEditor` 中，通过拖拽节点上的端口可以创建一条连接到另一个节点的边。
    -   **UC-SDP-5**: 用户选中 `GraphEditor` 中的一个节点或一条边，然后按 `Delete` 键，可以将其从画布中删除。

3.  **节点配置**
    -   **UC-SDP-6**: 用户在 `GraphEditor` 中点击一个节点，右侧的 `NodeDetailPanel` 应更新并显示该节点的详细信息，包括"配置"、"任务"、"数据"三个标签页。
    -   **UC-SDP-7**: 在 `NodeDetailPanel` 的"配置"标签页，用户可以修改节点的名称、描述以及算子特有的配置参数，对于输入字段的算子还能选择输入字段对应的上游。
    -   **UC-SDP-8**: 用户修改配置后，点击右上角的"保存"按钮，页面应调用 `ExecutionGraphClient.save` 方法持久化整个执行图的变更。

4.  **任务执行与监控**
    -   **UC-SDP-9**: 用户在 `NodeDetailPanel` 中，点击"运行"按钮，应提交当前节点的运行任务。提交后，面板应自动切换到"任务信息"标签页，并实时更新任务状态。
    -   **UC-SDP-10**: 当一个节点的任务成功完成后，`NodeDetailPanel` 的"数据"标签页应变为可用。用户点击后，可以看到任务的输出数据预览。
    -   **UC-SDP-11**: 用户点击页面顶部的"运行"按钮，应触发整个执行图的运行。所有节点的任务状态应在 `NodeDetailPanel` 中可追溯。

5. **执行图元数据配置**
   - UC-SDP-12: 用户可以在页面顶部找到"图设置"按钮，点击后可以配置执行图的名称、描述等元数据。
   
6. **导出执行图**  
   - UC-SDP-13: 用户点击页面顶部的"导出"按钮，系统应将当前执行图导出为JSON/YAML文件供下载。
   
7. **查看任务列表**
   - UC-SDP-14: 用户可以在页面中查看当前会话的所有任务列表，包括任务状态、创建时间等信息。

## 布局设计

页面采用经典的三栏式布局，为图形化编辑提供了高效的操作空间。

-   **左侧**: `OperatorPanel`，提供可选的算子列表。
-   **中间**: `GraphEditor`，核心的画布区域，用于执行图的可视化编辑。
-   **右侧**: `NodeDetailPanel`，当选中画布中的节点时，右侧抽屉显示该节点的详细配置、任务和数据信息。

### 初始布局

![会话详情页初始布局](../session-detail-layout.png)

### 编辑后布局

![会话详情页编辑后布局](../session-detail-layout_after-edit.png)
