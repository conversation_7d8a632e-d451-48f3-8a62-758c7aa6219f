# DataProcessEnginePage 页面使用说明

## 简介

`DataProcessEnginePage` 是数据处理引擎的前端主入口页面。它作为一个顶层容器，集成了"执行图"和"会话"两个核心功能模块的列表视图。用户可以通过此页面在不同的功能模块之间进行切换，并访问相应的功能列表。

## 路由说明

-   **路由路径**: `/dpe-web`
-   **可用路由参数**: 无
-   **子路由**:
    -   `/dpe-web/graphs`: 默认路由，显示 `ExecutionGraphListPage`。
    -   `/dpe-web/sessions`: 显示 `SessionListPage`。

## 相关业务流程

`DataProcessEnginePage` 是用户与数据处理引擎交互的起点，其核心流程是导航至不同的功能列表。

```mermaid
graph TD
    A[用户访问 /dpe-web] --> B{DataProcessEnginePage}
    B --> C[默认显示执行图Tab]
    B -->|点击会话Tab| D[显示会话Tab]
    C --> E[执行图列表 ExecutionGraphListPage]
    D --> F[会话列表 SessionListPage]
```

## 用例场景

1.  **导航与视图切换**
    -   **UC-DPE-1**: 用户访问 `/dpe-web` 路径，系统应默认显示"执行图"标签页，并加载 `ExecutionGraphListPage` 组件。
    -   **UC-DPE-2**: 用户在"执行图"标签页，点击页面顶部的"会话"标签，系统应切换到"会话"视图，并加载 `SessionListPage` 组件。
    -   **UC-DPE-3**: 用户在"会话"标签页，点击页面顶部的"执行图"标签，系统应切换回"执行图"视图，并加载 `ExecutionGraphListPage` 组件。

## 布局设计

`DataProcessEnginePage` 采用标签页布局，在顶部提供"执行图"和"会话"两个切换入口，下方内容区域根据当前选中的标签页动态渲染对应的列表页面。

-   **执行图列表视图**:
    ![执行图列表视图](../entry_graph-list.png)

-   **会话列表视图**:
    ![会话列表视图](../entry_session-list.png)
