# SessionDetailPage 页面详细设计文档

## 1. 引言

`SessionDetailPage` 是数据处理引擎前端界面中的核心工作区页面，为用户提供了完整的数据处理工作流设计和管理环境。为了提升代码的可维护性、测试友好性和职责分离的清晰度，我们将该页面重新设计为**两层组件架构**：

- **SessionDetailIndex** (`pages/SessionDetailPage/index.vue`)：负责路由参数解析、数据初始化和错误边界处理
- **SessionDetailContent** (`pages/SessionDetailPage/content.vue`)：负责业务逻辑协调和UI交互管理
- **SessionToolbar** (`pages/SessionDetailPage/components/SessionToolbar.vue`)：负责顶部工具栏的UI和操作按钮状态管理

这种设计使得业务逻辑组件（Content）可以独立测试，通过 Mock Props 模拟各种状态，同时保持与现有组件和API的完全兼容性。

## 2. 组件架构设计

### 2.1 整体架构图

```mermaid
classDiagram
    class SessionDetailIndex {
        <<Page Component>>
        + sessionId string (prop)
        + initializationError Object (data)
        + isInitializing boolean (data)
        + sessionInfo Session (data)
        + executionGraphClient ExecutionGraphClient (data)
        + operators Array~OperatorInfo~ (data)
        + loadInitialData() Promise
        + loadSessionInfo() Promise
        + initializeGraphClient() Promise
        + loadOperators() Promise
        + handleInitializationError()
    }

    class SessionDetailContent {
        <<Business Component>>
        + sessionInfo Session (prop)
        + executionGraphClient ExecutionGraphClient (prop)
        + operators Array~OperatorInfo~ (prop)
        + canSave boolean (prop)
        + canRun boolean (prop)
        + @error()
        + graphData RichExecutionGraph (data)
        + currentNode RichGraphNode (data)
        + clientEditStatus ClientEditStatus (data)
        + errorState Object (data)
        + handleGraphChange()
        + handleNodeSelect()
        + handleSaveGraph()
        + handleRunGraph()
    }

    class SessionToolbar {
        <<UI Component>>
        + sessionInfo Session (prop)
        + graphData RichExecutionGraph (prop)
        + canSave boolean (prop)
        + canRun boolean (prop)
        + isSaving boolean (data)
        + isRunning boolean (data)
        + @save-graph()
        + @run-graph()
        + @export-graph()
        + @graph-settings()
        + @view-task-list()
    }

    class GraphEditor {
        <<UI Component>>
        + graphData RichExecutionGraph (prop)
        + initialGraphData RichExecutionGraph (prop)
        + readonly boolean (prop)
        + showMinimap boolean (prop)
        + showToolbar boolean (prop)
        + config Object (prop)
        + @node-click(RichGraphNode)
        + @node-select(RichGraphNode)
        + @edge-click(RichGraphEdge)
        + @edge-select(RichGraphEdge)
        + @blank-click()
        + @graph-change(ChangeEvent)
        + @node-connect(RichGraphEdge, sourceId, targetId)
        + @selection-change(added, removed, selected)
        + @node-add(RichGraphNode)
        + @node-remove(nodeId)
        + @init-error(Error)
    }

    class OperatorPanel {
        <<UI Component>>
        + operators Array~OperatorInfo~ (prop)
        + selectedCategory string (prop)
        + searchKeyword string (prop)
        + @search(keyword)
        + @category-change(category)
        + @operator-select(operator)
        + @operator-drag-start(event, operator)
        + @operator-drag-end(event)
        + @add-dataset()
    }

    class NodeDetailPanel {
        <<UI Component>>
        + sessionId string (prop)
        + nodeId string (prop)
        + operatorInfo OperatorInfo (prop)
        + editable boolean (prop)
        + nodeFormData Object (prop) // v-model
        + callbacks NodeDetailPanelCallbacks (prop)
        + availableUpstreamNodes Array~GraphNode~ (prop)
        + @input(formData) // v-model update
        + @visibility-change(visible)
        + @view-result(resourceId)
    }

    SessionDetailIndex --> SessionDetailContent : 包含
    SessionDetailContent --> SessionToolbar : 包含
    SessionDetailContent --> GraphEditor : 包含
    SessionDetailContent --> OperatorPanel : 包含
    SessionDetailContent --> NodeDetailPanel : 包含
    SessionDetailIndex --> ExecutionGraphClient : 使用
    SessionDetailIndex --> DataProcessService : 使用
```

### 2.2 职责分离原则

| 组件 | 主要职责 | 次要职责 |
|------|----------|----------|
| **SessionDetailIndex** | 路由参数解析、初始化请求、错误边界 | 渲染加载状态、错误状态 |
| **SessionDetailContent** | 业务逻辑协调、UI交互管理、状态同步 | 图数据管理、子组件事件处理 |
| **SessionToolbar** | 顶部操作栏UI、操作按钮状态管理 | 操作事件向上传递 |

**SessionDetailContent 设计要点补充**：
*   执行图的状态以后端为准，所以每次执行图的操作（添加、更新、删除节点或边）都应该通过 `ExecutionGraphClient.get_edit_status()` 获取最新的执行图状态，并以此更新前端的 `graphData`。
*   拖拽节点到执行图时，**不**需要立即调用 `ExecutionGraphClient` 的 `add_nodes` 接口。此时节点仅在前端作为占位符存在。只有在节点完成配置并保存时，才根据节点是否已存在于后端执行图中按需调用 `add_nodes` 或 `update_nodes` 接口。

### 2.3 组件间通信

#### 2.3.1 数据流向图

##### ******* 页面初始化数据流

```mermaid
sequenceDiagram
    participant Router
    participant SessionDetailIndex
    participant SessionDetailContent
    participant DataProcessService
    participant ExecutionGraphClient
    participant SubComponents

    Router->>SessionDetailIndex: 路由到 /sessions/detail/:sessionId
    SessionDetailIndex->>SessionDetailIndex: 解析sessionId
    SessionDetailIndex->>DataProcessService: getSession(sessionId)
    DataProcessService-->>SessionDetailIndex: sessionInfo
    SessionDetailIndex->>DataProcessService: openGraphClient(sessionId)
    DataProcessService-->>SessionDetailIndex: executionGraphClient
    SessionDetailIndex->>DataProcessService: queryOperatorTypes(backendType)
    DataProcessService-->>SessionDetailIndex: operators
    SessionDetailIndex->>SessionDetailContent: 传递props
    SessionDetailContent->>ExecutionGraphClient: get_edit_status() (获取 ClientEditStatus)
    ExecutionGraphClient-->>SessionDetailContent: clientEditStatus (包含 currentGraph: ExecutionGraph)
    SessionDetailContent->>SessionDetailContent: graphData = RichExecutionGraph.fromExecutionGraph(clientEditStatus.currentGraph)
    SessionDetailContent->>SubComponents: 渲染子组件
```

##### ******* 节点编辑交互数据流

```mermaid
sequenceDiagram
    participant User
    participant GraphEditor
    participant SessionDetailContent
    participant NodeDetailPanel
    participant ExecutionGraphClient
    participant Backend

    User->>GraphEditor: 点击节点选择
    GraphEditor->>SessionDetailContent: @node-click(node)
    SessionDetailContent->>SessionDetailContent: 更新currentNode (RichGraphNode)
    SessionDetailContent->>NodeDetailPanel: 传递nodeId props 和 v-model (nodeFormData)
    NodeDetailPanel->>NodeDetailPanel: 加载节点配置 (基于传入的nodeFormData)
    
    User->>NodeDetailPanel: 修改节点配置
    NodeDetailPanel->>NodeDetailPanel: 验证配置数据 (NodeConfigPanel内部验证)
    User->>NodeDetailPanel: 点击保存 (NodeDetailPanel的handleSave)
    NodeDetailPanel->>SessionDetailContent: callbacks.save(formData) (NodeDetailPanel触发，包含 metadata, opConfigs, opInputs)
    SessionDetailContent->>ExecutionGraphClient: 根据节点是否存在，调用 add_nodes([newNode]) 或 update_nodes({nodeId: {metadata, opConfig}})
    SessionDetailContent->>ExecutionGraphClient: 根据 opInputs 变化，add_edges([newEdge]) 和/或 delete_edges([oldEdge]) 
    ExecutionGraphClient->>Backend: WebSocket 更新请求 (节点和边)
    Backend-->>ExecutionGraphClient: 更新结果
    ExecutionGraphClient-->>SessionDetailContent: 操作完成
    SessionDetailContent->>ExecutionGraphClient: get_edit_status() (获取 ClientEditStatus)
    ExecutionGraphClient-->>SessionDetailContent: clientEditStatus (包含 currentGraph: ExecutionGraph)
    SessionDetailContent->>SessionDetailContent: graphData.mergeFromExecutionGraph(clientEditStatus.currentGraph)
    SessionDetailContent->>GraphEditor: 更新 graphData props
    GraphEditor->>GraphEditor: 重新渲染节点和边
```

##### ******* 节点添加交互数据流

```mermaid
sequenceDiagram
    participant User
    participant OperatorPanel
    participant GraphEditor
    participant SessionDetailContent
    participant NodeDetailPanel
    participant ExecutionGraphClient
    participant Backend

    User->>OperatorPanel: 选择算子并拖拽
    OperatorPanel->>OperatorPanel: @dragstart 设置拖拽数据 (type: 'operator', data: operator)
    OperatorPanel->>SessionDetailContent: @operator-drag-start(event, operator)
    Note over OperatorPanel,GraphEditor: 用户拖拽算子到画布
    User->>GraphEditor: 释放拖拽到画布
    OperatorPanel->>SessionDetailContent: @operator-drag-end(event)
    SessionDetailContent->>SessionDetailContent: 根据拖拽结束位置判断是否在GraphEditor区域内
    SessionDetailContent->>SessionDetailContent: 基于算子信息和位置构造新的RichGraphNode (**不调用后端add_nodes**)
    SessionDetailContent->>GraphEditor: 调用addNode方法添加新节点到画布
    SessionDetailContent->>SessionDetailContent: 选中新添加的节点，更新currentNode，显示NodeDetailPanel
    User->>NodeDetailPanel: 用户配置新节点，点击保存
    NodeDetailPanel->>SessionDetailContent: callbacks.save(formData)
    SessionDetailContent->>ExecutionGraphClient: 根据节点是否存在，调用 add_nodes([newNode]) 或 update_nodes({nodeId: {metadata, opConfig}})
    SessionDetailContent->>ExecutionGraphClient: 根据 opInputs 变化，add_edges([newEdge]) 和/或 delete_edges([oldEdge]) 
    ExecutionGraphClient->>Backend: WebSocket 更新请求 (节点和边)
    Backend-->>ExecutionGraphClient: 添加结果
    ExecutionGraphClient-->>SessionDetailContent: 操作完成
    SessionDetailContent->>ExecutionGraphClient: get_edit_status() (获取 ClientEditStatus)
    ExecutionGraphClient-->>SessionDetailContent: clientEditStatus (包含 currentGraph: ExecutionGraph)
    SessionDetailContent->>SessionDetailContent: graphData.mergeFromExecutionGraph(clientEditStatus.currentGraph)
    SessionDetailContent->>GraphEditor: 更新 graphData props
    GraphEditor->>GraphEditor: 重新渲染，显示新节点（已保存）
```


#### 2.3.2 Props 传递链

**Index → Content**:
```javascript
<SessionDetailContent
  :session-info="sessionInfo"
  :execution-graph-client="executionGraphClient"
  :operators="operators"
  :can-save="canSave"
  :can-run="canRun"
  @error="handleContentError"
/>
```

**Content → SessionToolbar**:
```javascript
<SessionToolbar
  :session-info="sessionInfo"
  :graph-data="graphData"
  :can-save="canSave"
  :can-run="canRun"
  @save-graph="handleSaveGraph"
  @run-graph="handleRunGraph"
  @export-graph="handleExportGraph"
  @graph-settings="handleGraphSettings"
  @view-task-list="handleViewTaskList"
/>
```

**Content → 其他子组件**:
```javascript
<GraphEditor
  :graph-data="graphData"
  :initial-graph-data="initialGraphData"
  :readonly="isGraphReadonly"
  :show-minimap="true"
  :show-toolbar="true"
  @node-click="handleNodeSelect"
  @nodes-deselect="handleNodesDeselect"
  @graph-data-change="handleGraphChange"
  @selection-change="handleGraphSelectionChange"
  @node-add="handleGraphNodeAdded"
  @node-remove="handleGraphNodeRemoved"
  @edge-connected="handleGraphEdgeConnected"
  @init-error="handleGraphInitError"
/>

<OperatorPanel
  :operators="operators"
  :selected-category="selectedCategory"
  :search-keyword="searchKeyword"
  @search="handlePanelSearch"
  @category-change="handlePanelCategoryChange"
  @operator-select="handleOperatorSelect"
  @operator-drag-start="handleOperatorDragStart"
  @operator-drag-end="handleOperatorDragEnd"
  @add-dataset="handleAddNewDataset"
/>

<NodeDetailPanel
  :session-id="sessionInfo.id"
  :node-id="currentNodeId"
  :operator-info="currentNodeOperatorInfo"
  :editable="isNodeDetailEditable"
  v-model="nodeFormData"
  :callbacks="nodeDetailCallbacks"
  :available-upstream-nodes="availableUpstreamNodes"
  @input="handleNodeFormDataChange"
  @visibility-change="handleNodeDetailVisibilityChange"
  @view-result="handleViewTaskResult"
/>
```

#### 2.3.3 事件传递机制

| 事件源 | 事件名 | 事件数据 | 处理组件 | 处理方法 |
|--------|--------|----------|----------|----------|
| GraphEditor | node-click | RichGraphNode | Content | handleNodeSelect |
| GraphEditor | node-select | {node: RichGraphNode, event: Event} | Content | handleNodeSelect |
| GraphEditor | edge-click | {edge: RichGraphEdge, event: Event} | Content | - |
| GraphEditor | edge-select | {edge: RichGraphEdge, event: Event} | Content | - |
| GraphEditor | blank-click | {event: Event} | Content | handleNodesDeselect |
| GraphEditor | graph-data-change | RichExecutionGraph | Content | handleGraphChange |
| GraphEditor | selection-change | {added: Array, removed: Array, selected: Array} | Content | handleGraphSelectionChange |
| GraphEditor | node-add | {node: RichGraphNode} | Content | handleGraphNodeAdded |
| GraphEditor | node-remove | {nodeId: string} | Content | handleGraphNodeRemoved |
| GraphEditor | node-connect | {edge: RichGraphEdge, source: string, target: string} | Content | handleGraphEdgeConnected |
| GraphEditor | init-error | Error | Content | handleGraphInitError |
| OperatorPanel | search | string | Content | handlePanelSearch |
| OperatorPanel | category-change | string | Content | handlePanelCategoryChange |
| OperatorPanel | operator-select | OperatorInfo | Content | handleOperatorSelect |
| OperatorPanel | operator-drag-start | {event: DragEvent, operator: OperatorInfo} | Content | handleOperatorDragStart |
| OperatorPanel | operator-drag-end | DragEvent | Content | handleOperatorDragEnd |
| OperatorPanel | add-dataset | - | Content | handleAddNewDataset |
| SessionToolbar | save-graph | - | Content | handleSaveGraph |
| SessionToolbar | run-graph | - | Content | handleRunGraph |
| SessionToolbar | export-graph | - | Content | handleExportGraph |
| SessionToolbar | graph-settings | - | Content | handleGraphSettings |
| SessionToolbar | view-task-list | - | Content | handleViewTaskList |
| NodeDetailPanel | input | formData: Object | Content | handleNodeFormDataChange |
| NodeDetailPanel | visibility-change | boolean | Content | handleNodeDetailVisibilityChange |
| NodeDetailPanel | view-result | resourceId: string | Content | handleViewTaskResult |

## 2.4 富执行图 (RichExecutionGraph) 与 执行图 (ExecutionGraph) 的转换关系

在数据处理引擎中，我们区分了两种图模型：

-   **`ExecutionGraph`**：这是后端服务和 `ExecutionGraphClient` 内部使用的核心图模型，它是一个扁平化的图结构，由 `GraphNode` 和 `GraphEdge` 组成。`GraphEdge` 直接表示了节点间的连接，其 `targetConfig` 字段对应于目标节点的输入端口。

-   **`RichExecutionGraph`**：这是一个在前端组件（例如 `GraphEditor`）中使用的“富”图模型，它通常会包含更多的UI相关信息和便捷的属性访问器。`RichExecutionGraph` 内部会包装 `ExecutionGraph`，并提供更符合前端需求的表示。

两者之间的关键转换关系如下：

-   **`RichGraphNode` 与 `GraphNode`**：这两者之间是**一一对应**的关系。每个 `GraphNode` 在 `RichExecutionGraph` 中都有一个对应的 `RichGraphNode` 实例，它可能额外包含了布局信息、选中状态、UI显示属性等。

-   **`RichGraphEdge` 与 `GraphEdge`**：这之间是**一对多**的关系，一条 `RichGraphEdge` 可能对应 **0 到多条** `GraphEdge`。这是因为 `RichGraphEdge` 在 UI 上可能只表示一个从源节点到目标节点的连接，但在 `ExecutionGraph` 层面，目标节点可能有多个命名输入端口 (`targetConfig`)，源节点的输出可能会连接到目标节点的多个输入端口。因此，一条逻辑上的 `RichGraphEdge` 可能需要分解为多条 `GraphEdge` 来精确表示所有输入连接。

-   **`GraphEdge` 的配置**：`GraphEdge` 的具体连接信息（特别是 `targetConfig`，即目标节点的输入端口）主要通过 `NodeDetailPanel` 中的 `opInputs` 进行配置。正如 `NodeDetailPanel-FDS.md` 对 `NodeFormData` 的描述，`opInputs` 字段(`Object.<string, string[]>` 结构，键为输入字段名称，值为上游节点ID数组) 直接反映了 `GraphEdge` 中 `source` 和 `targetConfig` 的关系。当用户在 `NodeDetailPanel` 中配置节点的输入时，实际上就是在定义或修改与该节点相关的 `GraphEdge`。

这种分离和转换的目的是为了在后端数据模型和前端UI展示之间提供一个灵活的中间层，既能保持后端数据模型的简洁性，又能满足前端复杂交互的需求。

## 3. SessionDetailIndex 组件设计

### 3.1 组件职责

`SessionDetailIndex` 组件主要负责页面的初始化和顶层状态管理。其核心职责包括：

-   **路由参数解析**：从路由中获取 `sessionId`。
-   **数据初始化**：协调所有必要数据的加载，包括会话信息 (`sessionInfo`)、执行图客户端 (`executionGraphClient`) 和算子列表 (`operators`)。
-   **错误边界处理**：捕获并处理在数据加载和初始化过程中发生的错误，向用户显示友好的错误信息。
-   **加载状态管理**：管理页面加载状态，在数据加载期间显示加载指示器。

### 3.2 状态管理 (Data)

以下是 `SessionDetailIndex` 组件中管理的核心数据属性：

| 属性名                 | 类型                     | 说明                                                                  |
|------------------------|--------------------------|-----------------------------------------------------------------------|
| `sessionId`            | `string`                 | 当前会话的唯一标识符，从路由参数中获取。                              |
| `initializationError`  | `Object` \| `null`       | 初始化过程中发生的错误对象，如果无错误则为 `null`。                   |
| `isInitializing`       | `boolean`                | 指示页面是否正在进行初始化数据加载。                                  |
| `sessionInfo`          | `Session` \| `null`      | 从后端服务加载的当前会话的详细信息。                                  |
| `executionGraphClient` | `ExecutionGraphClient` \| `null` | 与后端执行图服务进行交互的客户端实例。                        |
| `operators`            | `Array<OperatorInfo>`    | 可用的算子类型列表，用于 `OperatorPanel`。                            |
| `canSave`              | `boolean`                | 指示当前执行图是否可保存（例如，当有未保存的变更时）。此属性传递给 `SessionDetailContent`。|
| `canRun`               | `boolean`                | 指示当前执行图是否可运行。此属性传递给 `SessionDetailContent`。        |

### 3.3 计算属性 (Computed)

`SessionDetailIndex` 的计算属性主要用于派生状态或简化模板逻辑。

| 属性名          | 类型      | 说明                                        | 依赖                    |
|-----------------|-----------|---------------------------------------------|-------------------------|
| `isLoading`     | `boolean` | `isInitializing` 的别名，用于模板中的加载状态判断。 | `isInitializing`        |
| `hasError`      | `boolean` | 指示 `initializationError` 是否存在。       | `initializationError`   |

### 3.4 生命周期钩子

`SessionDetailIndex` 主要利用 `created` 钩子来触发初始数据加载：

| 钩子名  | 说明                                                    |
|---------|---------------------------------------------------------|
| `created` | 在组件实例创建后立即调用，在此钩子中调用 `loadInitialData()` 方法，启动数据加载流程。 |

### 3.5 核心方法

`SessionDetailIndex` 包含以下核心方法，用于管理数据加载和错误处理：

| 方法名                    | 参数     | 返回值   | 说明                                                                                              |
|---------------------------|----------|----------|---------------------------------------------------------------------------------------------------|
| `loadInitialData()`       | 无       | `Promise<void>` | 异步方法，编排所有初始化数据的加载过程。包括获取 `sessionId`，调用 `loadSessionInfo()`、`initializeGraphClient()` 和 `loadOperators()`。 |
| `loadSessionInfo()`       | 无       | `Promise<void>` | 根据 `sessionId` 从 `DataProcessService` 加载会话信息。                                       |
| `initializeGraphClient()` | 无       | `Promise<void>` | 初始化 `ExecutionGraphClient` 实例，并获取初始的执行图状态。                                   |
| `loadOperators()`         | 无       | `Promise<void>` | 从 `DataProcessService` 加载所有可用的算子类型列表。                                          |
| `handleInitializationError(error)` | `Error` | `void`   | 处理初始化过程中发生的错误，设置 `initializationError` 状态。                                 |
| `handleContentError(error)` | `Error` | `void`   | 处理从 `SessionDetailContent` 组件传递上来的错误，例如保存失败等，以便在顶层进行统一的错误展示。|

## 4. SessionDetailContent 组件设计

### 4.1 组件职责

`SessionDetailContent` 组件是 `SessionDetailPage` 页面的核心业务逻辑协调者和 UI 交互管理者。它承载了大部分的业务逻辑，并协调 `GraphEditor`、`OperatorPanel`、`NodeDetailPanel` 和 `SessionToolbar` 之间的交互。其主要职责包括：

-   **业务逻辑协调**：作为中心枢纽，处理来自各个子组件的事件，并调用相应的后端服务（通过 `executionGraphClient`）进行数据操作。
-   **UI 交互管理**：根据用户操作和后端状态，更新 UI 组件的展示状态和数据。
-   **状态同步**：确保前端的执行图数据 (`graphData`) 始终与后端 `ExecutionGraphClient` 中的最新状态保持同步。
-   **图数据管理**：管理 `RichExecutionGraph` 实例的生命周期和变更，包括节点的添加、删除、更新以及边的连接和断开。
-   **子组件事件处理**：响应 `GraphEditor`、`OperatorPanel`、`NodeDetailPanel` 和 `SessionToolbar` 发出的各类事件，并执行相应的业务逻辑。

### 4.2 Props 接口

`SessionDetailContent` 组件通过 `props` 接收来自 `SessionDetailIndex` 的初始化数据和客户端实例，以及一些状态控制参数：

| 属性名                   | 类型                     | 说明                                                           |
|--------------------------|--------------------------|----------------------------------------------------------------|
| `sessionInfo`            | `Session`                | 当前会话的详细信息。                                           |
| `executionGraphClient`   | `ExecutionGraphClient`   | 与后端执行图服务进行交互的客户端实例。                         |
| `operators`              | `Array<OperatorInfo>`    | 可用的算子类型列表。                                           |
| `canSave`                | `boolean`                | 指示当前执行图是否可保存，由父组件 `SessionDetailIndex` 传入。 |
| `canRun`                 | `boolean`                | 指示当前执行图是否可运行，由父组件 `SessionDetailIndex` 传入。 |

### 4.3 状态管理 (Data)

`SessionDetailContent` 组件内部管理以下核心数据属性，这些属性驱动着页面的业务逻辑和 UI 渲染：

| 属性名               | 类型                     | 说明                                                                  |
|----------------------|--------------------------|-----------------------------------------------------------------------|
| `graphData`          | `RichExecutionGraph`     | 当前会话的执行图数据，包含 UI 相关的“富”信息。这是页面展示的核心数据。|
| `currentNode`        | `RichGraphNode` \| `null` | 当前在 `GraphEditor` 中被选中或在 `NodeDetailPanel` 中展示的节点。    |
| `clientEditStatus`   | `ClientEditStatus` \| `null` | 从 `ExecutionGraphClient` 获取的最新编辑状态，包含后端当前的执行图。|
| `errorState`         | `Object` \| `null`       | 业务逻辑处理中发生的错误状态，用于错误提示。                          |
| `initialGraphData`   | `RichExecutionGraph`     | `GraphEditor` 初始加载的图数据，用于判断图是否发生变更。              |
| `isSaving`           | `boolean`                | 指示当前是否正在保存执行图。                                          |
| `isRunning`          | `boolean`                | 指示当前是否正在运行执行图或单个节点。                                |

### 4.4 计算属性 (Computed)

`SessionDetailContent` 的计算属性用于从现有数据派生出新的状态或便捷值，简化模板和方法中的逻辑：

| 属性名                   | 类型                     | 说明                                                                  | 依赖                                  |
|--------------------------|--------------------------|-----------------------------------------------------------------------|---------------------------------------|
| `currentNodeId`          | `string` \| `null`       | 当前选中节点的 ID，如果无选中节点则为 `null`。                        | `currentNode`                         |
| `currentNodeOperatorInfo` | `OperatorInfo` \| `null` | 当前选中节点的算子信息，通过 `currentNode` 的 `operatorType` 从 `operators` 列表中查找。 | `currentNode`, `operators`            |
| `isNodeDetailEditable`   | `boolean`                | 指示 `NodeDetailPanel` 是否可编辑。通常在选中节点且图非只读时为 `true`。 | `currentNode`, `isGraphReadonly`      |
| `nodeFormData`           | `Object`                 | 用于 `NodeDetailPanel` 的 `v-model`，包含当前选中节点的配置数据。     | `currentNode`                         |
| `nodeDetailCallbacks`    | `NodeDetailPanelCallbacks` | 传递给 `NodeDetailPanel` 的回调函数对象，例如 `save` 和 `run`。       | `handleSaveNodeConfig`, `handleRunNode` |
| `availableUpstreamNodes` | `Array<GraphNode>`       | 可供当前选中节点连接的上游节点列表。                                  | `graphData`, `currentNode`            |
| `isGraphReadonly`        | `boolean`                | 指示 `GraphEditor` 是否处于只读模式。                                 | `sessionInfo`                         |
| `canSaveGraph`           | `boolean`                | 指示是否可以保存图，考虑 `canSave` 和图是否有变更。                     | `canSave`, `graphData`, `initialGraphData` |
| `canRunGraph`            | `boolean`                | 指示是否可以运行图，考虑 `canRun` 和图是否有节点。                      | `canRun`, `graphData`                 |

### 4.5 生命周期钩子

`SessionDetailContent` 组件利用生命周期钩子来初始化数据、设置监听器以及在组件销毁前进行清理：

| 钩子名        | 说明                                                                                                                                                                             |
|---------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `created`     | 组件实例创建后调用。在此钩子中，应初始化 `graphData`，并**订阅 `executionGraphClient` 的状态变更事件**，以便在后端图数据更新时及时刷新前端 `graphData`。                       |
| `mounted`     | 组件挂载到 DOM 后调用。可以在此进行一些需要 DOM 访问的操作，例如获取初始的图编辑状态 (`get_edit_status`) 来同步 `graphData`。                                            |
| `beforeDestroy` | 组件销毁前调用。在此钩子中，应**取消订阅 `executionGraphClient` 的状态变更事件**，以防止内存泄漏和不必要的更新。如果存在其他定时器或事件监听，也应在此处清理。 |

## 4.6 核心方法
### 4.6.1 图数据管理

`SessionDetailContent` 组件负责管理执行图数据的生命周期和变更，确保前端展示与后端状态同步。

| 方法名 | 参数 | 返回值 | 说明 |
|---|---|---|---|
| `syncGraphDataFromClientEditStatus()` | 无 | `Promise<void>` | 从 `executionGraphClient` 获取最新的 `ClientEditStatus`，并将其中的 `currentGraph` 转换为 `RichExecutionGraph` 来更新 `graphData`。此方法是确保前端图数据与后端同步的核心机制，应在每次后端图数据发生变更后调用。 |
| `handleGraphDataChange(newGraphData)` | `newGraphData: RichExecutionGraph` | `void` | 响应 `GraphEditor` 的 `graph-data-change` 事件。当 `GraphEditor` 内部（例如用户拖拽节点、改变布局等）导致图数据发生变化时，此方法会更新 `graphData`，并根据变更情况决定是否标记为未保存状态。此变化仅反映在前端，不立即触发后端保存。 |
| `handleGraphNodeAdded({node})` | `node: RichGraphNode` | `void` | 响应 `GraphEditor` 的 `node-add` 事件。当GraphEditor内部添加节点时触发（例如通过编程方式调用addNode方法）。此方法在前端更新 `graphData`，但不会立即调用后端 `add_nodes` 接口。 |
| `handleGraphNodeRemoved({nodeId})` | `nodeId: string` | `void` | 响应 `GraphEditor` 的 `node-remove` 事件。当用户从画布上删除一个节点时触发。此方法在前端更新 `graphData`，同样不会立即调用后端 `delete_nodes` 接口。 |
| `handleGraphEdgeConnected({edge, source, target})` | `edge: RichGraphEdge`, `source: string`, `target: string` | `void` | 响应 `GraphEditor` 的 `edge-connected` 事件。当用户在画布上连接两个节点创建一条边时触发。此方法在前端更新 `graphData`，不会立即调用后端 `add_edges` 接口。 |

### 4.6.2 节点交互处理

这些方法负责响应用户在 `GraphEditor` 和 `OperatorPanel` 中的节点相关交互，并更新组件内部状态。

| 方法名                                                      | 参数                                                                                                                                                  | 返回值    | 说明                                                                                                                                                                              |
| -------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------- | ------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `handleNodeSelect({node})`                               | `node: RichGraphNode`                                                                                                                               | `void` | 响应 `GraphEditor` 的 `node-click` 或 `node-select` 事件。当用户点击或选中画布上的某个节点时触发。此方法会将传入的 `node` 设置为 `currentNode`，从而在 `NodeDetailPanel` 中显示该节点的详细信息。                                     |
| `handleNodesDeselect()`                                  | 无                                                                                                                                                   | `void` | 响应 `GraphEditor` 的 `blank-click` 或 `nodes-deselect` 事件。当用户点击画布空白区域或取消选中所有节点时触发。此方法会将 `currentNode` 设置为 `null`，从而关闭 `NodeDetailPanel`。                                           |
| `handleGraphSelectionChange({added, removed, selected})` | `added: Array<RichGraphNode \| RichGraphEdge>`, `removed: Array<RichGraphNode \| RichGraphEdge>`, `selected: Array<RichGraphNode \| RichGraphEdge>` | `void` | 响应 `GraphEditor` 的 `selection-change` 事件。当画布上的选中项发生变化时触发。此方法会根据 `selected` 数组的内容更新 `currentNode`（如果选中项中包含节点）。                                                                   |
| `handlePanelSearch(keyword)`                             | `keyword: string`                                                                                                                                   | `void` | 响应 `OperatorPanel` 的 `search` 事件。当用户在算子面板中输入搜索关键词时触发。此方法会更新搜索状态并过滤显示的算子列表。                                                                                                         |
| `handlePanelCategoryChange(category)`                    | `category: string`                                                                                                                                  | `void` | 响应 `OperatorPanel` 的 `category-change` 事件。当用户在算子面板中切换分类时触发。此方法会更新当前选中的分类并过滤显示的算子列表。                                                                                              |
| `handleOperatorSelect(operator)`                         | `operator: OperatorInfo`                                                                                                                            | `void` | 响应 `OperatorPanel` 的 `operator-select` 事件。当用户点击算子面板中的算子项时触发。此方法可以用于高亮显示选中的算子或进行其他UI反馈。                                                                                         |
| `handleOperatorDragStart({event, operator})`            | `event: DragEvent`, `operator: OperatorInfo`                                                                                                       | `void` | 响应 `OperatorPanel` 的 `operator-drag-start` 事件。当用户开始拖拽算子时触发。此方法可以用于设置拖拽状态、UI反馈或准备拖拽相关的数据。                                                                                        |
| `handleOperatorDragEnd(event)`                          | `event: DragEvent`                                                                                                                                  | `void` | 响应 `OperatorPanel` 的 `operator-drag-end` 事件。当用户结束拖拽操作时触发。此方法会根据拖拽结束位置判断是否在GraphEditor区域内，如果是则基于算子信息构造新节点并调用GraphEditor的addNode方法添加到画布。同时处理拖拽状态的清理。 |
| `handleAddNewDataset()`                                 | 无                                                                                                                                                   | `void` | 响应 `OperatorPanel` 的 `add-dataset` 事件。当用户点击"添加数据集"按钮时触发。此方法会打开数据集添加对话框或导航到数据集管理页面。                                                                                           |

### 4.6.3 NodeDetailPanel 回调实现

这些方法实现了传递给 `NodeDetailPanel` 的回调函数，用于处理节点配置的保存、运行以及任务结果的查看。

| 方法名 | 参数 | 返回值 | 说明 |
|---|---|---|---|
| `handleNodeFormDataChange(formData)` | `formData: Object` | `void` | 响应 `NodeDetailPanel` 的 `input` 事件，用于更新 `nodeFormData`。此方法通常与 `NodeDetailPanel` 的 `v-model` 双向绑定机制配合使用，确保父子组件间数据的同步。 |
| `handleNodeDetailVisibilityChange(visible)` | `visible: boolean` | `void` | 响应 `NodeDetailPanel` 的 `visibility-change` 事件。当 `NodeDetailPanel` 的显示状态发生变化时触发，可以用于同步 `currentNode` 的选中状态，例如当面板关闭时清除 `currentNode`。 |
| `handleSaveNodeConfig(formData)` | `formData: Object` | `Promise<void>` | **核心方法**。当用户在 `NodeDetailPanel` 中点击保存节点配置时触发。此方法会根据 `currentNode` 的状态（是新增节点还是已存在的节点）和 `formData` 中的 `metadata`、`opConfig` 以及 `opInputs`，调用 `executionGraphClient.add_nodes()` 或 `executionGraphClient.update_nodes()` 来持久化节点的配置。同时，它会分析 `opInputs` 的变化，按需调用 `executionGraphClient.add_edges()` 和/或 `executionGraphClient.delete_edges()` 来更新边。操作完成后，会调用 `syncGraphDataFromClientEditStatus()` 来确保前端 `graphData` 与后端同步，并更新 `isSaving` 状态。 |
| `handleRunNode()` | 无 | `Promise<void>` | 当用户在 `NodeDetailPanel` 中点击“运行”按钮时触发。此方法会调用 `executionGraphClient.run_node()` 接口来运行当前选中的单个节点。它会更新 `isRunning` 状态，并在任务提交后更新相关UI状态以显示任务进度。 |
| `handleViewTaskResult(resourceId)` | `resourceId: string` | `void` | 响应 `NodeDetailPanel` 的 `view-result` 事件。当用户在“任务信息”标签页点击查看任务结果时触发。此方法会根据 `resourceId` 导航到相应的页面或打开一个组件来预览任务的输出数据。 |

### 4.6.4 SessionToolbar 事件处理

这些方法负责响应 `SessionToolbar` 组件发出的各类事件，以触发整个执行图层面的操作。

| 方法名 | 参数 | 返回值 | 说明 |
|---|---|---|---|
| `handleSaveGraph()` | 无 | `Promise<void>` | 响应 `SessionToolbar` 的 `save-graph` 事件。此方法会调用 `executionGraphClient.save_graph()` 将整个执行图保存到后端服务。在保存过程中，`isSaving` 状态会被设置为 `true`，完成后恢复为 `false`。保存成功后，会调用 `syncGraphDataFromClientEditStatus()` 来确保前端图数据与后端同步。 |
| `handleRunGraph()` | 无 | `Promise<void>` | 响应 `SessionToolbar` 的 `run-graph` 事件。此方法会调用 `executionGraphClient.run_graph()` 接口来运行整个执行图。在运行过程中，`isRunning` 状态会被设置为 `true`，并在任务提交后恢复为 `false`。 |
| `handleExportGraph()` | 无 | `void` | 响应 `SessionToolbar` 的 `export-graph` 事件。此方法会触发当前执行图的导出操作，通常是通过调用服务方法将图数据下载为 JSON 或 YAML 文件。 |
| `handleGraphSettings()` | 无 | `void` | 响应 `SessionToolbar` 的 `graph-settings` 事件。此方法通常会打开一个模态对话框或导航到新页面，允许用户配置执行图的元数据，例如图名称、描述等。 |
| `handleViewTaskList()` | 无 | `void` | 响应 `SessionToolbar` 的 `view-task-list` 事件。此方法会导航到显示当前会话所有任务列表的页面或组件，以便用户查看任务状态和历史记录。 |

## 6. 错误处理分层设计

### 6.1 错误处理层级

```mermaid
graph TD
    A[路由错误] --> B[SessionDetailIndex]
    C[初始化错误] --> B
    D[客户端连接错误] --> B
    E[图操作错误] --> F[SessionDetailContent]
    G[UI交互错误] --> F
    H[组件内部错误] --> I[各子组件]
    
    B --> J[全页错误状态]
    F --> K[局部错误提示]
    I --> L[组件内错误处理]
```


为了提供健壮的用户体验，`SessionDetailPage` 实现了分层的错误处理机制，确保不同来源的错误都能被有效捕获和处理。

| 层级 | 职责 | 错误处理方式 |
|---|---|---|
| **顶层（SessionDetailIndex）** | 作为页面的入口和错误边界组件。负责捕获页面初始化阶段的全局错误以及 `SessionDetailContent` 抛出的业务错误。 | 通过 `data` 属性 `initializationError` 捕获并存储错误信息，并在模板中显示友好的错误提示界面。同时，它监听 `SessionDetailContent` 的 `@error` 事件，统一处理来自子组件的未捕获错误。 |
| **业务逻辑层（SessionDetailContent）** | 处理所有与业务逻辑相关的错误，例如保存执行图失败、运行节点失败、数据校验不通过等。 | 在执行异步操作（如调用后端服务）时使用 `try-catch` 块捕获错误，将后端返回的错误信息或自定义的业务错误转化为用户可理解的提示信息（例如通过 Ant Design Vue 的 `message` 或 `notification` 组件）。对于需要中断流程的严重错误，可以通过 `this.$emit('error', error)` 向上抛出给 `SessionDetailIndex` 处理。 |
| **服务层（ExecutionGraphClient, DataProcessService 等）** | 负责处理与后端 API 交互时发生的网络错误、HTTP 状态码错误、以及后端返回的业务错误。 | 封装 `Axios` 请求，统一处理网络连接问题、请求超时、HTTP 状态码（如 4xx, 5xx）等。将这些底层错误转换为统一的、具有业务含义的错误对象（例如，包含错误码和错误消息），并向上层（业务逻辑层）抛出。 |
| **UI 组件层（GraphEditor, NodeDetailPanel, OperatorPanel 等）** | 处理组件内部的数据校验错误、用户输入错误、以及一些特定的 UI 渲染或交互错误。 | 对于用户输入校验错误，通常直接在组件内部通过表单验证机制进行提示。对于一些非致命的内部错误，可以通过日志记录或局部 UI 提示（如 `tooltip`）进行处理。如果遇到无法自行恢复的严重错误，应通过组件事件（`$emit`）向上传递给 `SessionDetailContent` 或 `SessionDetailIndex`。 |

### 6.2 错误类型与处理策略

针对 `SessionDetailPage` 及其相关组件可能出现的错误，我们采取以下分类和处理策略：

| 错误类型 | 常见原因 | 处理策略 |
|---|---|---|
| **网络/API 错误** | 后端服务不可用、网络中断、HTTP 状态码（如 401 Unauthorized, 404 Not Found, 500 Internal Server Error）等。 | <ul><li>**统一拦截**: 在 `Axios` 拦截器中统一处理 HTTP 状态码错误，将原始错误信息转换为友好的提示。</li><li>**全局提示**: 通过 Ant Design Vue 的 `message` 或 `notification` 组件显示全局错误提示，告知用户操作失败及原因。</li><li>**重试机制**: 对于临时的网络问题或特定 API 错误，可以提供重试选项。</li><li>**错误页面**: 对于无法恢复的严重错误（如初始化失败），重定向到通用错误页面或在当前页面显示错误边界组件。</li></ul> |
| **业务逻辑错误** | 后端返回的业务校验失败（如节点配置参数不合法、图结构不符合规范）、操作冲突等。 | <ul><li>**精准提示**: 根据后端返回的错误码或错误消息，在 UI 界面提供精准的用户提示，指出具体是哪个字段或哪个操作存在问题。</li><li>**引导纠正**: 提示用户如何纠正错误，例如“节点名称已存在，请修改”。</li><li>**局部反馈**: 对于特定组件的业务错误，在组件内部显示错误状态（例如表单验证错误）。</li></ul> |
| **前端运行时错误** | JavaScript 运行时异常、Vue 组件生命周期错误、响应式数据更新问题、第三方库错误等。 | <ul><li>**错误边界**: `SessionDetailIndex` 作为错误边界，捕获其子组件树中所有的 JavaScript 错误，防止整个应用崩溃，并显示备用 UI。</li><li>**日志记录**: 将错误信息（包括堆栈跟踪）记录到控制台或通过监控服务上报，便于问题排查。</li><li>**用户友好**: 避免直接向用户展示技术性错误信息，而是提供简洁的“发生未知错误”提示。</li></ul> |
| **数据校验错误** | 用户在表单中输入的数据不符合预期格式、范围或业务规则（例如，节点名称为空、数字超出范围）。 | <ul><li>**实时校验**: 在用户输入过程中或失去焦点时进行实时数据校验。</li><li>**即时反馈**: 通过表单控件的错误状态（如红色边框、错误提示文本）即时向用户反馈校验结果。</li><li>**阻止提交**: 只有当所有数据通过校验后，才允许用户提交表单或保存操作。</li></ul> |

### 6.3 错误恢复机制

为了最大限度地减少错误对用户工作的影响并保障数据完整性，`SessionDetailPage` 实现了以下错误恢复机制：

| 恢复机制 | 说明 | 实施考量 |
|---|---|---|
| **操作撤销与重做（Undo/Redo）** | 对于用户在 `GraphEditor` 中对执行图结构（节点添加、删除、移动，边的连接、断开）的修改，提供撤销和重做功能。 | 通过维护一个操作历史栈来记录用户的图操作，允许用户回溯到之前的状态或重新应用已撤销的操作。这主要针对前端的图编辑操作，不直接影响后端持久化数据，直到用户明确保存。 |
| **自动保存与草稿管理** | 周期性地将用户未明确保存的图变更作为“草稿”状态进行保存，以防止意外关闭或浏览器崩溃导致数据丢失。 | 需要设计一个机制来识别和存储这些草稿，并在用户重新打开会话时，提示用户是否恢复上次未保存的更改。这可能涉及前端 LocalStorage 或后端临时存储。 |
| **错误重试机制** | 对于因暂时性网络波动或后端瞬时负载高导致的保存、运行等异步操作失败，提供用户友好的重试选项。 | 在错误提示中包含“重试”按钮，允许用户再次尝试执行操作。这通常在服务层进行封装。 |
| **前端状态回滚** | 当涉及后端持久化的操作（如保存图、运行节点）失败时，前端应将相关 UI 和数据回滚到操作之前的状态，避免显示不一致或错误的数据。 | 例如，如果保存操作失败，前端的“未保存变更”状态不应被清除，并且应提示用户保存失败。 |
| **明确的错误信息与引导** | 即使无法自动恢复，也应向用户提供清晰、易懂的错误信息，并引导用户采取下一步行动（例如，检查网络连接、联系管理员、查看错误日志）。 | 避免技术性错误信息，使用业务语言描述问题。 |
| **错误日志与报告** | 确保前端捕获到的错误被妥善记录，并提供用户主动报告错误的机制，以便开发团队能够及时发现和解决问题。 | 集成前端错误监控工具，将错误信息上报至日志系统。同时提供用户反馈入口。 |

## 7. 测试友好性设计

为了确保 `SessionDetailPage` 及其子组件的质量和可维护性，我们从设计之初就考虑了测试友好性，主要通过以下两点实现：

### 7.1 Mock 支持

`SessionDetailContent` 组件通过 `props` 接收其外部依赖（如 `executionGraphClient` 和 `operators`），这使得在进行单元测试时，这些依赖可以被“模拟”（Mock）对象替代。这种设计模式带来了显著的测试效益：

-   **隔离性**: 组件的测试不再依赖于真实的后端服务或复杂的外部状态，从而提高了测试的稳定性和可靠性。
-   **效率**: Mock 对象可以快速响应，避免了真实网络请求带来的延迟，大大加快了测试的运行速度。
-   **可控性**: 可以模拟各种边界条件和错误场景，例如模拟 `executionGraphClient` 返回不同的图状态或抛出特定错误，从而全面测试组件在各种情况下的行为。
-   **焦点集中**: 单元测试可以专注于组件自身的业务逻辑和 UI 交互，而不是外部依赖的行为。

**关键可 Mock 依赖**：
-   `executionGraphClient`: 在测试 `SessionDetailContent` 时，必须使用 `tests/unit/pages/dpe-web/models/MockExecutionGraphClient.js` 作为 `ExecutionGraphClient` 的完整模拟实现。该 Mock 实例应能够完全模拟后端 `ExecutionGraphClient` 的行为，包括其所有方法的调用和响应，以确保测试的真实性和完整性。
-   `operators`: 模拟的算子数据应至少包含 `tests/e2e/pages/dpe-web/pages/SessionDetailPage/SessionDetailContentTestData.js` 中的 `RealOperatorInfos`，以确保测试覆盖真实的算子场景。
-   **其他 Service**：对于 `DataProcessService`、`ResourceService` 等其他后端服务，其 Mock 处理通常采用 API 拦截的方式（例如，使用 `msw` 或类似的库），在网络请求层面进行模拟，而非直接 Mock 服务实例。

**Mock 策略示例**：
在 Jest 单元测试中，可以使用 `jest.fn()` 或 `jest.mock()` 来创建 Mock 函数或 Mock 模块，然后将这些 Mock 实例作为 `props` 传递给被测试的 `SessionDetailContent` 组件。

### 7.2 e2e测试覆盖

为了保证 `SessionDetailPage` 及其核心子组件的代码质量和功能正确性，我们将进行全面的端到端（e2e）测试。

-   **测试范围**：
    -   `SessionDetailIndex`：验证路由参数解析、数据初始化流程和错误边界处理。
    -   `SessionDetailContent`：作为页面的核心业务协调者，是 E2E 测试的重点，需要验证其数据流管理、节点和边操作、与子组件的交互逻辑（事件处理和回调）。
    -   `SessionToolbar`、`OperatorPanel`、`NodeDetailPanel`：作为通用 UI 组件，验证其 `props` 接收、内部状态管理和事件触发。

-   **测试工具**：
    -   **Playwright**：作为现代化的端到端测试框架，用于模拟实际用户在浏览器中的交互。
    -   **Jest**：作为 JavaScript 测试框架，可以与 Playwright 结合使用，用于运行测试和提供断言库。

-   **测试策略**：
    -   **UI 交互模拟**： 对于涉及节点操作的主流程（如拖拽、点击），**必须使用 Playwright 提供的实际浏览器操作进行模拟**，确保整个系统的端到端功能正确性。
    -   **数据准备**： 在测试场景的准备阶段，可以调用组件内部的方法或模拟 API 响应来准备测试数据。
    -   **场景验证**： 验证复杂用户场景下的完整业务流程，包括数据流转、UI 更新和后端交互。
    -   **错误场景覆盖**： 模拟各种错误情况，验证页面是否能正确地显示错误信息并提供适当的恢复机制。
    -   **页面导航与路由**： 验证路由跳转和参数传递的正确性。

-   **覆盖率目标**： 对关键的用户路径和业务流程，力求达到高覆盖率，确保端到端功能的稳定性。

-   **测试文件命名和位置**：e2e 测试文件将放置在 `tests/e2e` 目录下，并遵循 `*.e2e.spec.js` 的命名约定。

## 8. 布局与样式设计

### 8.1 组件布局结构

`SessionDetailPage` 采用经典的三栏式布局，为用户提供了高效、直观的操作界面。这种布局旨在最大化 `GraphEditor` 的工作空间，同时提供方便的侧边面板用于算子选择和节点配置。

-   **整体布局**： 页面整体采用 Ant Design Vue 的 `a-layout` 组件进行组织，包含 `a-layout-sider` 用于左侧面板和右侧抽屉，以及 `a-layout-content` 用于中心画布区域。
-   **左侧面板（OperatorPanel）**： 位于页面的最左侧，通过 `a-layout-sider` 实现。它展示了可供选择的算子列表，用户可以通过拖拽操作将算子添加到画布中。
-   **中间区域（GraphEditor）**： 占据页面的主要空间，通过 `a-layout-content` 实现。它是执行图的可视化编辑画布，用户可以在此添加、连接、配置和管理图节点。
-   **右侧抽屉（NodeDetailPanel）**： 当用户选中 `GraphEditor` 中的节点时，右侧会弹出 `NodeDetailPanel` 抽屉，通过 `a-drawer` 或类似的组件实现。它提供节点的详细配置、任务信息和数据预览功能。
-   **顶部工具栏（SessionToolbar）**： 页面顶部集成 `SessionToolbar`，提供全局操作按钮，如保存、运行、导出和图设置等。通常固定在页面顶部，不随滚动条滚动。
-   **尺寸分配**： `GraphEditor` 作为核心工作区，占据大部分可用空间。`OperatorPanel` 和 `NodeDetailPanel` 的宽度应适中，通常为固定宽度或根据内容自适应，并支持展开/收缩功能，以优化不同屏幕尺寸下的用户体验。
-   **响应式设计**： 考虑在不同屏幕尺寸下（例如，在较小的显示器上）对布局进行适当调整，例如隐藏或折叠侧边面板，或改变抽屉的展现方式，以确保页面的可用性和良好的视觉效果。

### 8.2 样式规范

为了保持页面样式的一致性、可维护性和扩展性，`SessionDetailPage` 及其相关组件的样式设计遵循以下规范：

-   **统一的样式标准**：
    -   严格遵循 Ant Design Vue 的设计体系和组件库提供的样式变量，确保与现有 UI 风格保持一致。
    -   遵循项目自身的通用样式约定，如颜色、字体、间距等。

-   **CSS 预处理器**：
    -   推荐使用 Less 或 Sass 作为 CSS 预处理器，以利用其提供的变量、混合（mixins）、嵌套（nesting）等功能，提高样式代码的复用性和可读性。
    -   通过 `@import` 引入公共样式文件和变量文件，避免重复定义。

-   **Scoped CSS**：
    -   Vue 组件的 `<style>` 标签应尽可能使用 `scoped` 属性，将样式限定在当前组件的作用域内，防止样式污染和冲突。
    -   对于需要穿透 `scoped` 样式修改子组件或第三方组件样式的场景，可以使用深度选择器（如 `/deep/` 或 `::v-deep`）。

-   **命名约定**：
    -   遵循 BEM（Block-Element-Modifier）命名约定，例如 `component-block__element--modifier`，以提高 CSS 类的可读性、可维护性和可扩展性。
    -   避免使用过于宽泛或通用的类名，防止意外覆盖。

-   **主题化支持**：
    -   如果项目支持主题化，应通过 Less/Sass 变量覆盖 Ant Design Vue 的默认主题变量，实现全局主题定制。
    -   自定义的样式中也应优先使用主题变量，以便在切换主题时保持一致。

-   **CSS 变量**：
    -   考虑在全局或特定组件中使用 CSS 变量（Custom Properties），以便更灵活地管理颜色、字体大小、间距等设计令牌。
    -   CSS 变量可以与 Less/Sass 变量结合使用，提供更强大的样式控制能力。

-   **图标库**：
    -   统一使用 Ant Design Vue 提供的图标库（或项目指定的主题图标库），确保图标风格的一致性。
    -   通过组件方式引入图标，避免直接使用图片资源。
