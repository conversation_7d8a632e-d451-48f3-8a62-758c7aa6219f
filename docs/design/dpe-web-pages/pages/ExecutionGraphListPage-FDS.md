# ExecutionGraphListPage 页面使用说明

## 简介

`ExecutionGraphListPage` 是用于展示和管理所有已保存的"执行图"的页面。用户可以在此页面上浏览、搜索、导入执行图，并基于选定的执行图创建新的数据处理会话。

## 路由说明

-   **路由路径**: `/dpe-web/graphs`
-   **可用路由参数**: 无

## 相关业务流程

此页面的核心业务流程包括浏览执行图、导入新图以及基于图创建会话。

```mermaid
graph TD
    subgraph ExecutionGraphListPage
        A[开始] --> B[列出并分页展示执行图]
        B --> C[用户搜索]
        C -->|找到| B
        B --> D[用户点击导入执行图]
        D --> E[弹出文件选择框]
        E -->|选择文件| F[调用服务导入图模板]
        F -->|成功| B
        B --> G[用户点击某执行图的创建会话]
        G --> H[打开 CreateSessionDialog 组件]
        H -->|填写信息并提交| I[创建新会话]
        I -->|成功| J[跳转到 SessionDetailPage]
    end
```

## 用例场景

1.  **浏览和搜索执行图**
    -   **UC-EGLP-1**: 页面加载时，应调用 `ExecutionGraphRepositoryService.listGraphs` 方法，获取第一页的执行图数据并渲染成列表。
    -   **UC-EGLP-2**: 当执行图数量超过一页时，应显示分页控件，用户可以点击分页控件浏览所有执行图。
    -   **UC-EGLP-3**: 用户在顶部的搜索框中输入关键词，点击"搜索"按钮，页面应根据关键词过滤并显示匹配的执行图。
    -   **UC-EGLP-4**: 用户点击"重置"按钮，清除搜索条件，并重新加载所有执行图。

2.  **导入执行图**
    -   **UC-EGLP-5**: 用户点击页面右上角的"导入执行图"按钮，应触发文件选择对话框。
    -   **UC-EGLP-6**: 用户选择一个合法的执行图文件（例如 JSON 格式）后，系统应调用 `ExecutionGraphRepositoryService.importGraph` 方法进行导入，并在成功后刷新列表。

3.  **基于执行图创建会话**
    -   **UC-EGLP-7**: 用户在列表中找到目标执行图，点击其对应的"创建会话"按钮。
    -   **UC-EGLP-8**: 系统应弹出 `CreateSessionDialog` 组件，并将所选执行图的信息预填充到对话框中。
    -   **UC-EGLP-9**: 用户在对话框中完成会话配置并提交后，系统应成功创建会话，并自动跳转到该新会话的 `SessionDetailPage` 页面（例如 `/dpe-web/sessions/new-session-id`）。

## 布局设计

页面主要由搜索区、列表区和操作区组成。

-   **整体布局**:
    ![执行图列表页面布局](../entry_graph-list.png)
