# 数据处理引擎 HTTP API 设计

## 接口概述

本文档定义了通过 FastAPI 框架调用 `DataProcessEngine` 进行数据处理的 HTTP 接口规范。
这些接口旨在提供一个标准化的方式来远程触发和管理数据处理任务。

### API 前缀说明

**所有 API 接口都使用 `/api` 作为统一前缀。**

例如，获取后端列表的完整URL为：`/api/backends`

### 接口目录

| API 路径                                              | HTTP 方法  | 描述                   | 示例 URL 参数                                                             | 请求体示例                                                                                         |
| --------------------------------------------------- | -------- | -------------------- | --------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- |
| `/api/backends`                                     | `GET`    | 获取可用后端列表（简化版本）       | -                                                                     | -                                                                                             |
| `/api/backends/query`                               | `POST`   | 获取可用后端列表（支持分页和过滤）    | -                                                                     | `{"pageParam": {...}, "filters": {...}}`                                                      |
| `/api/backends/default`                             | `PUT`    | 设置默认后端               | -                                                                     | `{"backend_type": "duckdb"}`                                                                  |
| `/api/backends/{backend_type}/supported_operators`  | `GET`    | 获取后端支持的算子列表（简化版本）    | `backend_type=duckdb`                                                 | -                                                                                             |
| `/api/backends/{backend_type}/operators/query`      | `POST`   | 获取后端支持的算子列表（支持分页和过滤） | `backend_type=duckdb`                                                 | `{"pageParam": {...}, "filters": {...}}`                                                      |
| `/api/sessions`                                     | `GET`    | 列出所有会话（简化版本）         | -                                                                     | -                                                                                             |
| `/api/sessions/query`                               | `POST`   | 列出所有会话（支持分页和过滤）      | -                                                                     | `{"pageParam": {...}, "filters": {...}}`                                                      |
| `/api/sessions`                                     | `POST`   | 创建新会话                | -                                                                     | `{"backend_type": "duckdb", "session_id": "my_session", "config": {...}}`                     |
| `/api/sessions/dag`                                 | `POST`   | 创建DAG会话              | -                                                                     | `{"backend_type": "duckdb", "session_id": "my_dag_session", "config": {...}, "graph": {...}}` |
| `/api/sessions/lifecycle-subscribe`                 | `GET`    | SSE监听会话生命周期          | `session_id=sess-123`, `event_types=session_created,task_completed`   | -                                                                                             |
| `/api/sessions/{session_id}`                        | `GET`    | 获取会话信息               | `session_id=sess-123`                                                 | -                                                                                             |
| `/api/sessions/{session_id}`                        | `DELETE` | 关闭会话                 | `session_id=sess-123`                                                 | -                                                                                             |
| `/api/sessions/{session_id}/tasks/run`              | `POST`   | 执行算子                 | `session_id=sess-123`, `wait=false`                                   | `{"type": "ReadStructuredDataResource", "source": "...", ...}`                                |
| `/api/sessions/{session_id}/tasks/{task_id}`        | `GET`    | 获取任务状态               | `session_id=sess-123`, `task_id=task-456`, `wait=false`, `timeout=60` | -                                                                                             |
| `/api/sessions/{session_id}/tasks/{task_id}/cancel` | `POST`   | 取消任务                 | `session_id=sess-123`, `task_id=task-456`                             | -                                                                                             |
| `/api/sessions/{session_id}/tasks/query`            | `POST`   | 查询会话内的任务列表（支持分页和过滤）  | `session_id=sess-123`                                                 | `{"pageParam": {...}, "filters": {...}}`                                                      |
| `/api/sessions/{session_id}/dag/run_task`           | `POST`   | 批量运行DAG任务            | `session_id=sess-123`                                                 | `{"task_ids": ["task-1", "task-2"], "rerun_mode": "NEVER"}`                                   |
| `/api/sessions/{session_id}/dag/tasks`              | `DELETE` | 批量删除DAG任务            | `session_id=sess-123`                                                 | `{"task_ids": ["task-1", "task-2"]}`                                                          |
| `/api/sessions/{session_id}/dag/export_graph`       | `GET`    | 导出执行图                | `session_id=sess-123`                                                 | -                                                                                             |
| `/api/sessions/{session_id}/dag/run_all_tasks`      | `POST`   | 运行所有DAG任务            | `session_id=sess-123`                                                 | `{"rerun_mode": "NEVER"}`                                                                     |
| `/api/sessions/{session_id}/dag/graph-client`       | `WS`     | 执行图编辑长连接             | `session_id=sess-123`                                                 | WebSocket消息格式见下文                                                                              |
| `/api/resources/data-resource/structured/preview`   | `POST`   | 获取数据资源预览             | -                                                                     | `{"resource_id": "session://sess-123/data-resource/task-456", "limit": 100}`                  |
| `/api/resources/data-resource/structured/schema`    | `POST`   | 获取数据资源Schema         | -                                                                     | `{"resource_id": "session://sess-123/data-resource/task-456"}`                                |

### 1. 后端管理接口

#### 1.1 列出可用后端（简化版本）

*   **URL**: `/api/backends`
*   **Method**: `GET`
*   **Description**: 获取当前注册的所有数据处理后端名称列表。这是简化版本，返回所有后端不支持分页。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "backends": [
            {
                "backend_type": "duckdb",
                "metadata": {
                    "display_name": "DuckDB后端",
                    "description": "基于DuckDB的数据处理后端",
                    "labels": {},
                    "annotations": {}
                },
                "session_configs": [
                    {
                        "key": "max_memory",
                        "schema": {
                            "type": "string",
                            "description": "最大内存限制",
                            "default": "4GB"
                        }
                    }
                ]
            }
        ]
    }
    ```
*   **Error Response**:
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取后端列表失败: <error_message>"}`

#### 1.1.1 列出可用后端（支持分页和过滤）

*   **URL**: `/api/backends/query`
*   **Method**: `POST`
*   **Description**: 获取当前注册的所有数据处理后端名称列表，支持分页、排序和过滤功能。
*   **Request Body**:
    ```json
    {
        "pageParam": {
            "pageIndex": 0,
            "limit": 10,
            "sortField": "backend_type",
            "sortType": "asc",
            "sortList": [
                {"field": "backend_type", "type": "asc"},
                {"field": "metadata.display_name", "type": "desc"}
            ],
            "isTop": false
        },
        "filters": {
            "backend_type_pattern": "duck",
            "display_name_pattern": "DuckDB",
            "has_session_configs": true,
            "labels": {
                "version": "0.9.2"
            }
        }
    }
    ```
    
    **参数说明**:
    *   `pageParam` (PageParam, optional): 分页参数
        - `pageIndex` (number): 当前页码，从0开始
        - `limit` (number): 每页记录数，默认10
        - `sortField` (string, optional): 主排序字段
        - `sortType` (string, optional): 主排序类型（asc/desc）
        - `sortList` (array, optional): 多字段排序配置
        - `isTop` (boolean, optional): 是否置顶显示
    *   `filters` (object, optional): 过滤条件
        - `backend_type_pattern` (string, optional): 后端类型模糊匹配
        - `display_name_pattern` (string, optional): 显示名称模糊匹配
        - `has_session_configs` (boolean, optional): 是否有会话配置
        - `labels` (object, optional): 按标签过滤

*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "data": [
            {
                "backend_type": "duckdb",
                "metadata": {
                    "display_name": "DuckDB后端",
                    "description": "基于DuckDB的数据处理后端",
                    "labels": {"version": "0.9.2"},
                    "annotations": {}
                },
                "session_configs": [
                    {
                        "key": "max_memory",
                        "schema": {
                            "type": "string",
                            "description": "最大内存限制",
                            "default": "4GB"
                        }
                    }
                ]
            }
        ],
        "pageParam": {
            "pageIndex": 0,
            "limit": 10,
            "pageTotal": 1,
            "recordTotal": 1,
            "sortField": "backend_type",
            "sortType": "asc",
            "sortList": [
                {"field": "backend_type", "type": "asc"}
            ],
            "isTop": false
        }
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，无效的分页参数或过滤条件)
    *   **Content**: `{"detail": "查询后端列表失败: <error_message>"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "查询后端列表失败: <error_message>"}`

#### 1.2 设置默认后端

*   **URL**: `/api/backends/default`
*   **Method**: `PUT`
*   **Description**: 设置默认的数据处理后端。
*   **Request Body**: `{"backend_type": "duckdb"}`
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: `{"message": "默认后端已设置为 'duckdb'"}`
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，后端名称无效)
    *   **Content**: `{"detail": "设置默认后端失败: 后端 'invalid_backend' 未注册"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "设置默认后端失败: <error_message>"}`

#### 1.3 获取后端支持的算子列表

*   **URL**: `/api/backends/{backend_type}/supported_operators`
*   **Method**: `GET`
*   **Description**: 获取指定后端支持的所有算子类型和配置信息。这是简化版本，返回所有算子不支持分页。
*   **Path Parameters**:
    *   `backend_type` (string, required): 后端类型名称，如 "duckdb"。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "operators": [
            {
                "type": "ReadStructuredDataResource",
                "metadata": {
                    "display_name": "读取结构化数据资源",
                    "description": "从数据源读取结构化数据",
                    "labels": {"category": "input"},
                    "annotations": {}
                },
                "op_config_metadata": [
                    {
                        "key": "source",
                        "schema": {
                            "type": "string",
                            "description": "数据源路径或资源ID",
                            "required": true
                        }
                    },
                    {
                        "key": "format",
                        "schema": {
                            "type": "string",
                            "enum": ["csv", "parquet", "json"],
                            "description": "数据格式",
                            "required": true
                        }
                    }
                ],
                "input_fields": []
            },
            {
                "type": "FilterStructuredData",
                "metadata": {
                    "display_name": "过滤结构化数据",
                    "description": "根据条件过滤结构化数据",
                    "labels": {"category": "transform"},
                    "annotations": {}
                },
                "op_config_metadata": [
                    {
                        "key": "resourceId",
                        "schema": {
                            "type": "string",
                            "description": "输入数据资源ID",
                            "required": true
                        }
                    },
                    {
                        "key": "condition",
                        "schema": {
                            "type": "object",
                            "description": "过滤条件表达式",
                            "required": true
                        }
                    }
                ],
                "input_fields": ["resourceId"]
            }
        ]
    }
    ```
*   **Error Response**:
    *   **Code**: `404 Not Found` (后端不存在)
    *   **Content**: `{"detail": "后端 'invalid_backend' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取算子列表失败: <error_message>"}`

#### 1.3.1 获取后端支持的算子列表（支持分页和过滤）

*   **URL**: `/api/backends/{backend_type}/operators/query`
*   **Method**: `POST`
*   **Description**: 获取指定后端支持的所有算子类型和配置信息，支持分页、排序和过滤功能。
*   **Path Parameters**:
    *   `backend_type` (string, required): 后端类型名称，如 "duckdb"。
*   **Request Body**:
    ```json
    {
        "pageParam": {
            "pageIndex": 0,
            "limit": 20,
            "sortField": "type",
            "sortType": "asc",
            "sortList": [
                {"field": "metadata.labels.category", "type": "asc"},
                {"field": "type", "type": "asc"}
            ],
            "isTop": false
        },
        "filters": {
            "category": "transform",
            "operator_type_pattern": "Filter",
            "display_name_pattern": "过滤",
            "has_input_fields": true,
            "labels": {
                "complexity": "simple"
            }
        }
    }
    ```
    
    **参数说明**:
    *   `pageParam` (PageParam, optional): 分页参数
    *   `filters` (object, optional): 过滤条件
        - `category` (string, optional): 算子类别过滤（从labels.category中获取）
        - `operator_type_pattern` (string, optional): 算子类型模糊匹配
        - `display_name_pattern` (string, optional): 显示名称模糊匹配
        - `has_input_fields` (boolean, optional): 是否有输入字段
        - `labels` (object, optional): 按标签过滤

*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "data": [
            {
                "type": "FilterStructuredData",
                "metadata": {
                    "display_name": "过滤结构化数据",
                    "description": "根据条件过滤结构化数据",
                    "labels": {"category": "transform", "complexity": "simple"},
                    "annotations": {}
                },
                "op_config_metadata": [
                    {
                        "key": "resourceId",
                        "schema": {
                            "type": "string",
                            "description": "输入数据资源ID",
                            "required": true
                        }
                    },
                    {
                        "key": "condition",
                        "schema": {
                            "type": "object",
                            "description": "过滤条件表达式",
                            "required": true
                        }
                    }
                ],
                "input_fields": ["resourceId"]
            }
        ],
        "pageParam": {
            "pageIndex": 0,
            "limit": 20,
            "pageTotal": 1,
            "recordTotal": 1,
            "sortField": "type",
            "sortType": "asc",
            "sortList": [
                {"field": "metadata.labels.category", "type": "asc"},
                {"field": "type", "type": "asc"}
            ],
            "isTop": false
        }
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，无效的分页参数或过滤条件)
    *   **Content**: `{"detail": "查询算子列表失败: <error_message>"}`
    *   **Code**: `404 Not Found` (后端不存在)
    *   **Content**: `{"detail": "后端 'invalid_backend' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "查询算子列表失败: <error_message>"}`

### 2. 会话管理接口

#### 2.1 列出所有会话

*   **URL**: `/api/sessions`
*   **Method**: `GET`
*   **Description**: 获取所有活跃会话的列表。这是简化版本，返回所有会话不支持分页。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: `{"sessions": [{"session_id": "sess-123", "backend_type": "duckdb", "state": "RUNNING", "created_at": "2025-05-14T12:00:00", "tasks": ["task-1", "task-2"]}, ...]}`
*   **Error Response**:
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取会话列表失败: <error_message>"}`

#### 2.1.1 列出所有会话（支持分页和过滤）

*   **URL**: `/api/sessions/query`
*   **Method**: `POST`
*   **Description**: 获取所有活跃会话的列表，支持分页、排序和过滤功能。
*   **Request Body**:
    ```json
    {
        "pageParam": {
            "pageIndex": 0,
            "limit": 15,
            "sortField": "created_at",
            "sortType": "desc",
            "sortList": [
                {"field": "created_at", "type": "desc"},
                {"field": "session_id", "type": "asc"}
            ],
            "isTop": false
        },
        "filters": {
            "backend_types": ["duckdb", "spark"],
            "states": ["RUNNING", "CREATED"],
            "created_after": "2025-01-01T00:00:00Z",
            "created_before": "2025-12-31T23:59:59Z",
            "session_id_pattern": "test-",
            "has_tasks": true,
            "task_count_min": 1,
            "task_count_max": 10
        }
    }
    ```
    
    **参数说明**:
    *   `pageParam` (PageParam, optional): 分页参数
    *   `filters` (object, optional): 过滤条件
        - `backend_types` (array, optional): 后端类型过滤（支持多选）
        - `states` (array, optional): 会话状态过滤（支持多选）
        - `created_after` (string, optional): 创建时间下限（ISO 8601格式）
        - `created_before` (string, optional): 创建时间上限（ISO 8601格式）
        - `session_id_pattern` (string, optional): 会话ID模糊匹配
        - `has_tasks` (boolean, optional): 是否有任务
        - `task_count_min` (number, optional): 最小任务数量
        - `task_count_max` (number, optional): 最大任务数量

*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "data": [
            {
                "session_id": "sess-123",
                "backend_type": "duckdb",
                "session_type": "standard",
                "state": "RUNNING",
                "created_at": "2025-05-14T12:00:00Z",
                "updated_at": "2025-05-14T12:30:00Z",
                "tasks": ["task-1", "task-2"],
                "task_count": 2,
                "metadata": {
                    "display_name": "数据分析会话",
                    "description": "用于数据分析的处理会话",
                    "labels": {"project": "analytics"},
                    "annotations": {"created_by": "user123"}
                }
            }
        ],
        "pageParam": {
            "pageIndex": 0,
            "limit": 15,
            "pageTotal": 1,
            "recordTotal": 1,
            "sortField": "created_at",
            "sortType": "desc",
            "sortList": [
                {"field": "created_at", "type": "desc"},
                {"field": "session_id", "type": "asc"}
            ],
            "isTop": false
        }
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，无效的分页参数或过滤条件)
    *   **Content**: `{"detail": "查询会话列表失败: <error_message>"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "查询会话列表失败: <error_message>"}`

#### 2.2 创建会话

*   **URL**: `/api/sessions`
*   **Method**: `POST`
*   **Description**: 在指定的后端（如果提供）或默认后端上创建一个新的数据处理会话。
*   **Request Body (optional)**:
    ```json
    {
        "backend_type": "duckdb",           // 可选，指定用于创建会话的后端名称。如果未提供，则使用默认后端
        "session_id": "my_custom_session_id", // 可选，指定会话ID。如果未提供，则由后端自动生成
        "config": {                         // 可选，会话配置参数
            "max_memory": "4GB",           // 示例：最大内存限制
            "timeout": 3600,               // 示例：会话超时时间（秒）
            "temp_directory": "/tmp/dpe",  // 示例：临时文件目录
            "enable_cache": true           // 示例：是否启用缓存
        }
    }
    ```
    *   `backend_type` (string, optional): 指定用于创建会话的后端名称。如果未提供，则使用默认后端。
    *   `session_id` (string, optional): 指定会话ID。如果未提供，则由后端自动生成。
    *   `config` (object, optional): 会话配置参数，包含后端特定的配置选项。具体可用配置项取决于后端实现。
*   **Success Response**:
    *   **Code**: `201 Created`
    *   **Content**: `{"session_id": "session-abcdef12", "backend_type": "duckdb", "state": "CREATED", "created_at": "2025-05-14T12:00:00", "tasks": []}`
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，后端不存在或会话ID已存在)
    *   **Content**: `{"detail": "创建会话失败: <error_message>"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "创建会话失败: <error_message>"}`

#### 2.3 创建DAG会话

*   **URL**: `/api/sessions/dag`
*   **Method**: `POST`
*   **Description**: 在指定的后端（如果提供）或默认后端上创建一个新的支持DAG调度的数据处理会话。可选择性地传入初始执行图来创建预配置的工作流会话。
*   **Request Body (optional)**:
    ```json
    {
        "backend_type": "duckdb",           // 可选，指定用于创建会话的后端名称。如果未提供，则使用默认后端
        "session_id": "my_custom_dag_session", // 可选，指定会话ID。如果未提供，则由后端自动生成
        "config": {                         // 可选，会话配置参数
            "max_memory": "4GB",           // 示例：最大内存限制
            "timeout": 3600,               // 示例：会话超时时间（秒）
            "temp_directory": "/tmp/dpe",  // 示例：临时文件目录
            "enable_cache": true,          // 示例：是否启用缓存
            "dag_config": {                // DAG特定配置
                "max_parallel_tasks": 10,  // 最大并行任务数
                "enable_auto_retry": true, // 是否启用自动重试
                "retry_count": 3           // 重试次数
            }
        },
        "graph": {                          // 可选，初始执行图，用于根据执行图创建会话
            "id": "sales-processing-workflow",
            "metadata": {
                "display_name": "销售数据处理工作流",
                "description": "从执行图模板创建的会话",
                "labels": {"source": "template", "version": "1.0"},
                "annotations": {"created_from": "execution-graph-template"}
            },
            "nodes": {
                "read-sales": {
                    "id": "read-sales",
                    "metadata": {
                        "display_name": "读取销售数据",
                        "description": "从数据源读取销售数据"
                    },
                    "op_type": "ReadStructuredDataResource",
                    "op_config": {
                        "source": "data-resource://sales-2023",
                        "format": "csv",
                        "options": {"header": true}
                    }
                },
                "filter-data": {
                    "id": "filter-data",
                    "metadata": {
                        "display_name": "过滤数据",
                        "description": "过滤有效的销售记录"
                    },
                    "op_type": "FilterStructuredData",
                    "op_config": {
                        "resourceId": "session://__SESSION_ID__/data-resource/read-sales",
                        "condition": {
                            "type": "FuncCall",
                            "name": "gt",
                            "args": [
                                {"type": "Identifier", "name": "amount"},
                                {"type": "Literal", "val": 0}
                            ]
                        }
                    }
                }
            },
            "edges": [
                {
                    "source": "read-sales",
                    "target": "filter-data",
                    "target_config": "resourceId"
                }
            ]
        }
    }
    ```
    
    **参数说明**:
    *   `backend_type` (string, optional): 指定用于创建会话的后端名称。如果未提供，则使用默认后端。
    *   `session_id` (string, optional): 指定会话ID。如果未提供，则由后端自动生成。
    *   `config` (object, optional): 会话配置参数，包含后端特定的配置选项。
    *   `graph` (ExecutionGraph, optional): 初始执行图，用于根据执行图创建预配置的DAG会话。当提供此参数时，会话创建后将自动加载该执行图中的所有节点和边。
    
    **执行图参数特殊处理**:
    - 执行图中的 `resourceId` 可以使用占位符 `__SESSION_ID__`，系统会在创建会话时自动替换为实际的会话ID
    - 如果执行图中包含全局资源ID（如 `data-resource://xxx`），系统会自动创建相应的适配任务
    - 系统会验证执行图的有效性，包括节点配置、依赖关系和循环检测

*   **Success Response**:
    *   **Code**: `201 Created`
    *   **Content**: 
    ```json
    {
        "session_id": "dag-session-abcdef12",
        "backend_type": "duckdb",
        "session_type": "dag",
        "state": "CREATED",
        "created_at": "2025-05-14T12:00:00",
        "tasks": [],
        "dag_capabilities": {
            "supports_batch_run": true,
            "supports_execution_graph": true,
            "supports_dependency_management": true
        },
        "initial_graph_loaded": true,
        "loaded_nodes": ["read-sales", "filter-data"],
        "loaded_edges": 1,
        "validation_result": {
            "valid": true,
            "failed_details": []
        }
    }
    ```
    
    **响应字段说明**:
    *   `initial_graph_loaded` (boolean): 是否成功加载了初始执行图
    *   `loaded_nodes` (array): 成功加载的节点ID列表
    *   `loaded_edges` (integer): 成功加载的边数量
    *   `validation_result` (VerifyResult): 执行图验证结果
    
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，后端不支持DAG、会话ID已存在或执行图无效)
    *   **Content**: 
    ```json
    {
        "detail": "创建DAG会话失败: 执行图验证失败",
        "validation_errors": [
            {
                "rule_type": "DEPENDENCY_CYCLE",
                "message": "检测到循环依赖：node-1 -> node-2 -> node-1",
                "extra_info": {
                    "cycle_path": ["node-1", "node-2", "node-1"]
                }
            }
        ]
    }
    ```
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "创建DAG会话失败: <error_message>"}`

#### 2.4 获取会话信息

*   **URL**: `/api/sessions/{session_id}`
*   **Method**: `GET`
*   **Description**: 获取指定会话的详细信息。
*   **Path Parameters**:
    *   `session_id` (string, required): 要获取信息的会话ID。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: `{"session_id": "session-abcdef12", "backend_type": "duckdb", "state": "RUNNING", "created_at": "2025-05-14T12:00:00", "tasks": ["task-1", "task-2"]}` (具体内容取决于后端实现)
*   **Error Response**:
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "会话 'session-abcdef12' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取会话信息失败: <error_message>"}`

#### 2.5 关闭会话

*   **URL**: `/api/sessions/{session_id}`
*   **Method**: `DELETE`
*   **Description**: 关闭指定的数据处理会话并释放相关资源。
*   **Path Parameters**:
    *   `session_id` (string, required): 要关闭的会话ID。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: `{"session_id": "session-abcdef12", "closed": true, "message": "会话 'session-abcdef12' 已成功关闭"}`
*   **Error Response**:
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "会话 'session-abcdef12' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "关闭会话失败: <error_message>"}`

#### 2.6 会话生命周期监听

*   **URL**: `/api/sessions/lifecycle-subscribe`
*   **Method**: `GET` (Server-Sent Events)
*   **Description**: 通过 Server-Sent Events (SSE) 实时监听会话生命周期变化和会话内任务状态变更。客户端可以订阅特定会话或全局会话事件。
*   **Query Parameters (optional)**:
    *   `session_id` (string, optional): 指定要监听的会话ID。如果未提供，则监听所有会话的事件。
    *   `event_types` (string, optional): 逗号分隔的事件类型列表。可选值：`session_created`, `session_state_changed`, `session_closed`, `task_status_changed`, `task_completed`, `task_error`。如果未提供，则监听所有事件类型。
*   **Headers**:
    *   `Accept: text/event-stream`
    *   `Cache-Control: no-cache`

##### SSE 消息格式

**会话生命周期事件**:
```
event: session_lifecycle
id: event-123
data: {
data:   "event_type": "session_created",
data:   "timestamp": "2025-01-20T10:00:00Z",
data:   "session_id": "session-abcdef12",
data:   "backend_type": "duckdb",
data:   "data": {
data:     "session_state": "CREATED",
data:     "metadata": {
data:       "display_name": "数据处理会话",
data:       "created_by": "user123"
data:     }
data:   }
data: }

event: session_lifecycle  
id: event-124
data: {
data:   "event_type": "session_state_changed",
data:   "timestamp": "2025-01-20T10:05:00Z", 
data:   "session_id": "session-abcdef12",
data:   "backend_type": "duckdb",
data:   "data": {
data:     "old_state": "CREATED",
data:     "new_state": "RUNNING"
data:   }
data: }
```

**任务状态变化事件**:
```
event: task_lifecycle
id: event-125
data: {
data:   "event_type": "task_status_changed",
data:   "timestamp": "2025-01-20T10:08:00Z",
data:   "session_id": "session-abcdef12", 
data:   "task_id": "task-xyz789",
data:   "data": {
data:     "old_status": "PENDING",
data:     "new_status": "RUNNING",
data:     "operator_type": "FilterStructuredData",
data:     "node_id": "filter-node-1"
data:   }
data: }

event: task_lifecycle
id: event-126
data: {
data:   "event_type": "task_completed",
data:   "timestamp": "2025-01-20T10:10:00Z",
data:   "session_id": "session-abcdef12",
data:   "task_id": "task-xyz789", 
data:   "data": {
data:     "status": "SUCCESS",
data:     "result_resource_id": "session://session-abcdef12/data-resource/task-xyz789",
data:     "execution_time_ms": 2000,
data:     "operator_type": "FilterStructuredData",
data:     "node_id": "filter-node-1"
data:   }
data: }

event: task_lifecycle
id: event-127  
data: {
data:   "event_type": "task_error",
data:   "timestamp": "2025-01-20T10:12:00Z",
data:   "session_id": "session-abcdef12",
data:   "task_id": "task-abc456",
data:   "data": {
data:     "status": "ERROR",
data:     "error_message": "数据源无法访问",
data:     "error_code": "RESOURCE_UNAVAILABLE", 
data:     "operator_type": "ReadStructuredDataResource",
data:     "node_id": "read-node-1"
data:   }
data: }
```

##### 连接管理

**保持连接**:
```
event: heartbeat
data: {"timestamp": "2025-01-20T10:15:00Z"}
```

**错误处理**:
```
event: error
data: {
data:   "error_type": "SUBSCRIPTION_ERROR",
data:   "message": "会话订阅失败",
data:   "details": {"session_id": "invalid-session"}
data: }
```

*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content-Type**: `text/event-stream`
    *   **Content**: SSE 数据流，包含会话和任务生命周期事件
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，无效的事件类型或会话ID)
    *   **Content**: `{"detail": "订阅会话生命周期失败: <error_message>"}`
    *   **Code**: `404 Not Found` (指定的会话ID不存在)
    *   **Content**: `{"detail": "会话 'session-abcdef12' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "建立SSE连接失败: <error_message>"}`

##### 客户端使用示例

```javascript
// 监听特定会话的所有事件
const eventSource = new EventSource('/api/sessions/lifecycle-subscribe?session_id=session-123');

eventSource.addEventListener('session_lifecycle', function(event) {
    const data = JSON.parse(event.data);
    console.log('会话事件:', data.event_type, data);
});

eventSource.addEventListener('task_lifecycle', function(event) {
    const data = JSON.parse(event.data);
    console.log('任务事件:', data.event_type, data);
});

eventSource.addEventListener('error', function(event) {
    console.error('SSE错误:', event);
});

// 监听全局会话的特定事件类型
const globalEventSource = new EventSource('/api/sessions/lifecycle-subscribe?event_types=session_created,task_completed');
```

### 3. 算子执行与任务管理接口

#### 3.1 执行算子

*   **URL**: `/api/sessions/{session_id}/tasks/run`
*   **Method**: `POST`
*   **Description**: 在指定的会话中执行一个数据处理算子。
*   **Path Parameters**:
    *   `session_id` (string, required): 算子执行的目标会话ID。
*   **Query Parameters (optional)**:
    *   `wait` (boolean, optional): 是否等待任务完成，默认为 `false`。
*   **Request Body**: 
    ```json
    {
        "type": "ReadStructuredDataResource",
        "source": "data-resource://customer_data", 
        "format": "CSV",
        "options": {"header": true, "delimiter": ","}
    }
    ```
    或者
    ```json
    {
        "type": "FilterStructuredData",
        "resourceId": "session://session-abcdef12/data-resource/task-xyz789",
        "condition": {
            "type": "FuncCall",
            "name": "gt",
            "args": [
                {"type": "Identifier", "name": "amount"},
                {"type": "Literal", "val": 1000}
            ]
        }
    }
    ```
    
    **说明**: 请求体应直接包含算子的配置字段，而不是嵌套在 `operator` 字段中。`type` 字段指定算子类型，其余字段为算子特定的配置参数。
    
*   **Success Response**:
    *   **Code**: `202 Accepted` (任务已提交，正在异步执行)
    *   **Content**: `{"task_id": "task-xyz789", "status": "PENDING", "resource_id": null, "task_info": null}`
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，算子定义无效，或会话状态不允许执行)
    *   **Content**: `{"detail": "执行算子失败: <validation_error_details_or_message>"}`
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "会话 'session-abcdef12' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "执行算子失败: <error_message>"}`

#### 3.2 获取任务状态

*   **URL**: `/api/sessions/{session_id}/tasks/{task_id}`
*   **Method**: `GET`
*   **Description**: 获取指定任务的状态和结果（如果已完成）。
*   **Path Parameters**:
    *   `session_id` (string, required): 任务所属的会话ID。
    *   `task_id` (string, required): 要查询的任务ID。
*   **Query Parameters (optional)**:
    *   `wait` (boolean, optional): 是否等待任务完成，默认为 `false`。
    *   `timeout` (number, optional): 等待超时时间（秒）。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content (任务正在运行/待处理)**: `{"task_id": "task-xyz789", "session_id": "session-abcdef12", "status": "RUNNING", "created_at": "2025-05-14T12:00:00", "updated_at": null, "message": null, "result_resource_id": null}`
    *   **Content (任务成功)**: `{"task_id": "task-xyz789", "session_id": "session-abcdef12", "status": "SUCCESS", "created_at": "2025-05-14T12:00:00", "updated_at": "2025-05-14T12:01:00", "message": "任务成功完成", "result_resource_id": "session://session-abcdef12/data-resource/task-xyz789"}`
    *   **Content (任务失败)**: `{"task_id": "task-xyz789", "session_id": "session-abcdef12", "status": "ERROR", "created_at": "2025-05-14T12:00:00", "updated_at": "2025-05-14T12:01:00", "message": "执行失败：数据源无法访问", "result_resource_id": null}`
*   **Error Response**:
    *   **Code**: `404 Not Found` (会话或任务不存在)
    *   **Content**: `{"detail": "任务 'task-xyz789' 在会话 'session-abcdef12' 中未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取任务状态失败: <error_message>"}`

#### 3.3 取消任务

*   **URL**: `/api/sessions/{session_id}/tasks/{task_id}/cancel`
*   **Method**: `POST`
*   **Description**: 取消正在执行或等待执行的任务。
*   **Path Parameters**:
    *   `session_id` (string, required): 任务所属的会话ID。
    *   `task_id` (string, required): 要取消的任务ID。
*   **Success Response**:
    *   **Code**: `204 No Content` (任务取消请求已提交)
*   **Error Response**:
    *   **Code**: `404 Not Found` (会话或任务不存在)
    *   **Content**: `{"detail": "任务 'task-xyz789' 在会话 'session-abcdef12' 中未找到"}`
    *   **Code**: `409 Conflict` (任务已完成，无法取消)
    *   **Content**: `{"detail": "任务 'task-xyz789' 已完成，无法取消"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "取消任务失败: <error_message>"}`

#### 3.4 查询会话内的任务列表

*   **URL**: `/api/sessions/{session_id}/tasks/query`
*   **Method**: `POST`
*   **Description**: 获取指定会话内的任务列表，支持分页、排序和过滤功能。
*   **Path Parameters**:
    *   `session_id` (string, required): 任务所属的会话ID。
*   **Request Body**:
    ```json
    {
        "pageParam": {
            "pageIndex": 0,
            "limit": 20,
            "sortField": "created_at",
            "sortType": "desc",
            "sortList": [
                {"field": "created_at", "type": "desc"},
                {"field": "status", "type": "asc"}
            ],
            "isTop": false
        },
        "filters": {
            "statuses": ["RUNNING", "SUCCESS", "ERROR"],
            "operator_types": ["FilterStructuredData", "ReadStructuredDataResource"],
            "created_after": "2025-01-01T00:00:00Z",
            "created_before": "2025-12-31T23:59:59Z",
            "node_ids": ["node-1", "node-2"],
            "has_result_resource": true,
            "task_id_pattern": "task-"
        }
    }
    ```
    
    **参数说明**:
    *   `pageParam` (PageParam, optional): 分页参数
    *   `filters` (object, optional): 过滤条件
        - `statuses` (array, optional): 任务状态过滤（支持多选）
        - `operator_types` (array, optional): 算子类型过滤（支持多选）
        - `created_after` (string, optional): 创建时间下限（ISO 8601格式）
        - `created_before` (string, optional): 创建时间上限（ISO 8601格式）
        - `node_ids` (array, optional): 节点ID过滤（支持多选）
        - `has_result_resource` (boolean, optional): 是否有结果资源
        - `task_id_pattern` (string, optional): 任务ID模糊匹配

*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "data": [
            {
                "task_id": "task-xyz789",
                "session_id": "session-abcdef12",
                "status": "SUCCESS",
                "created_at": "2025-05-14T12:00:00Z",
                "updated_at": "2025-05-14T12:01:30Z",
                "message": "任务成功完成",
                "result_resource_id": "session://session-abcdef12/data-resource/task-xyz789",
                "node_id": "filter-node-1",
                "operator_type": "FilterStructuredData",
                "execution_time_ms": 2000,
                "metadata": {
                    "display_name": "过滤高价值客户",
                    "description": "筛选交易金额大于1000的客户记录"
                }
            }
        ],
        "pageParam": {
            "pageIndex": 0,
            "limit": 20,
            "pageTotal": 1,
            "recordTotal": 1,
            "sortField": "created_at",
            "sortType": "desc",
            "sortList": [
                {"field": "created_at", "type": "desc"},
                {"field": "status", "type": "asc"}
            ],
            "isTop": false
        }
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，无效的分页参数或过滤条件)
    *   **Content**: `{"detail": "查询任务列表失败: <error_message>"}`
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "会话 'session-abcdef12' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "查询任务列表失败: <error_message>"}`

### 4. DAG会话管理接口

本章节定义了支持DAG调度的会话特有的接口，这些接口在普通的数据处理会话基础上增加了批量任务管理、执行图操作和依赖关系管理功能。

#### 4.1 批量运行DAG任务

*   **URL**: `/api/sessions/{session_id}/dag/run_task`
*   **Method**: `POST`
*   **Description**: 批量运行指定的任务，自动处理依赖关系和执行顺序。
*   **Path Parameters**:
    *   `session_id` (string, required): DAG会话ID。
*   **Request Body**:
    ```json
    {
        "task_ids": ["task-1", "task-2", "task-3"],
        "rerun_mode": "NEVER"
    }
    ```
    *   `task_ids` (array, required): 要运行的任务ID列表
    *   `rerun_mode` (string, optional): 重跑模式，可选值：
        - `"NEVER"` (默认): 从不重跑已成功的任务
        - `"FORCE"`: 强制重跑所有指定任务
        - `"FAILED_ONLY"`: 仅重跑失败的任务
*   **Success Response**:
    *   **Code**: `202 Accepted` (任务已提交批量执行)
    *   **Content**: 
    ```json
    {
        "executed_tasks": [
            {
                "task_id": "task-1",
                "session_id": "dag-session-123",
                "status": "RUNNING",
                "node_id": "node-1",
                "operator": {
                    "type": "ReadStructuredDataResource",
                    "source": "data-resource://sales-data"
                },
                "dependencies": [],
                "dependents": ["task-2"],
                "upstream_changed": false
            },
            {
                "task_id": "task-2", 
                "session_id": "dag-session-123",
                "status": "PENDING",
                "node_id": "node-2",
                "operator": {
                    "type": "FilterStructuredData",
                    "resourceId": "session://dag-session-123/data-resource/task-1"
                },
                "dependencies": ["task-1"],
                "dependents": ["task-3"],
                "upstream_changed": false
            }
        ]
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，任务ID不存在或存在循环依赖)
    *   **Content**: `{"detail": "批量运行任务失败: 检测到循环依赖"}`
    *   **Code**: `404 Not Found` (会话不存在或会话不支持DAG)
    *   **Content**: `{"detail": "DAG会话 'dag-session-123' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "批量运行任务失败: <error_message>"}`

#### 4.2 批量删除DAG任务

*   **URL**: `/api/sessions/{session_id}/dag/tasks`
*   **Method**: `DELETE`
*   **Description**: 批量删除指定任务并清理对应的数据资源。
*   **Path Parameters**:
    *   `session_id` (string, required): DAG会话ID。
*   **Request Body**:
    ```json
    {
        "task_ids": ["task-1", "task-2"],
        "force": false,
        "cleanup_resources": true
    }
    ```
    *   `task_ids` (array, required): 要删除的任务ID列表
    *   `force` (boolean, optional): 是否强制删除，即使有其他任务依赖，默认为 `false`
    *   `cleanup_resources` (boolean, optional): 是否清理关联的数据资源，默认为 `true`
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "deleted_tasks": ["task-1", "task-2"],
        "skipped_tasks": [],
        "cleaned_resources": ["session://dag-session-123/data-resource/task-1", "session://dag-session-123/data-resource/task-2"]
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，存在依赖关系且未强制删除)
    *   **Content**: `{"detail": "删除任务失败: 任务 'task-1' 被其他任务依赖"}`
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "DAG会话 'dag-session-123' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "批量删除任务失败: <error_message>"}`

#### 4.3 导出执行图

*   **URL**: `/api/sessions/{session_id}/dag/export_graph`
*   **Method**: `GET`
*   **Description**: 导出当前会话对应的执行图。
*   **Path Parameters**:
    *   `session_id` (string, required): DAG会话ID。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "id": "execution-graph-123",
        "metadata": {
            "display_name": "销售数据处理工作流",
            "description": "处理销售数据的完整工作流",
            "labels": {"version": "1.0", "team": "data-team"},
            "annotations": {"created_by": "user123"}
        },
        "nodes": {
            "node-1": {
                "id": "node-1",
                "metadata": {
                    "display_name": "读取销售数据",
                    "description": "",
                    "labels": {},
                    "annotations": {}
                },
                "op_type": "ReadStructuredDataResource",
                "op_config": {
                    "source": "data-resource://sales-data",
                    "format": "csv"
                }
            },
            "node-2": {
                "id": "node-2", 
                "metadata": {
                    "display_name": "过滤高价值客户",
                    "description": "",
                    "labels": {},
                    "annotations": {}
                },
                "op_type": "FilterStructuredData",
                "op_config": {
                    "resourceId": "session://dag-session-123/data-resource/task-1",
                    "condition": {"type": "FuncCall", "name": "gt", "args": [...]}
                }
            }
        },
        "edges": [
            {
                "source": "node-1",
                "target": "node-2",
                "target_config": "resourceId"
            }
        ]
    }
    ```
*   **Error Response**:
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "DAG会话 'dag-session-123' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "导出执行图失败: <error_message>"}`

#### 4.4 运行所有DAG任务

*   **URL**: `/api/sessions/{session_id}/dag/run_all_tasks`
*   **Method**: `POST`
*   **Description**: 运行当前会话中的所有任务。
*   **Path Parameters**:
    *   `session_id` (string, required): DAG会话ID。
*   **Request Body**:
    ```json
    {
        "rerun_mode": "NEVER"
    }
    ```
    *   `rerun_mode` (string, optional): 重跑模式，同批量运行任务接口
*   **Success Response**:
    *   **Code**: `202 Accepted` (所有任务已提交执行)
    *   **Content**: 
    ```json
    {
        "executed_tasks": [
            {
                "task_id": "task-1",
                "status": "RUNNING",
                "dependencies": [],
                "dependents": ["task-2", "task-3"]
            },
            {
                "task_id": "task-2",
                "status": "PENDING", 
                "dependencies": ["task-1"],
                "dependents": []
            }
        ],
        "total_tasks": 2,
        "execution_plan": ["task-1", "task-2"]
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，存在循环依赖)
    *   **Content**: `{"detail": "运行所有任务失败: 检测到循环依赖"}`
    *   **Code**: `404 Not Found` (会话不存在)
    *   **Content**: `{"detail": "DAG会话 'dag-session-123' 未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "运行所有任务失败: <error_message>"}`

#### 4.5 执行图编辑长连接接口

*   **URL**: `/api/sessions/{session_id}/dag/graph-client`
*   **Method**: `WebSocket`
*   **Description**: 提供ExecutionGraphClient的WebSocket长连接接口，支持图的实时编辑操作。
*   **Path Parameters**:
    *   `session_id` (string, required): DAG会话ID。

##### WebSocket消息格式

**请求消息格式**:
```json
{
    "message_id": "msg-123",
    "action": "add_nodes",
    "payload": {
        "nodes": [
            {
                "id": "new-node-1",
                "metadata": {
                    "display_name": "新节点",
                    "description": "新添加的处理节点"
                },
                "op_type": "TransformStructuredData",
                "op_config": {
                    "resourceId": "session://dag-session-123/data-resource/task-1",
                    "outputColumns": [...]
                }
            }
        ]
    }
}
```

**响应消息格式**:
```json
{
    "message_id": "msg-123",
    "action": "add_nodes",
    "success": true,
    "result": ["new-node-1"],
    "error": null
}
```

##### 支持的操作列表

| Action | 描述 | Payload格式 | 返回值 |
|--------|------|-------------|--------|
| `add_nodes` | 批量添加节点 | `{"nodes": [GraphNode]}` | `["node_id1", "node_id2"]` |
| `update_nodes` | 批量更新节点配置 | `{"id_ref_configs": {"node_id": {"metadata": {"display_name": "new name"}, "op_config": {"key": "value"}}}}` | `["node_id1"]` |
| `delete_nodes` | 批量删除节点 | `{"node_ids": ["node_id1"]}` | `["node_id1"]` |
| `add_edges` | 批量添加边 | `{"edges": [GraphEdge]}` | `["edge_id1"]` |
| `delete_edges` | 批量删除边 | `{"edges": [GraphEdge]}` | `["edge_id1"]` |
| `add_op` | 添加算子并自动生成节点 | `{"op": Operator}` | `Operator (带task_id)` |
| `load_graph` | 加载执行图 | `{"graph": ExecutionGraph, "merge_mode": "FAILED_ON_CONFLICT"}` | `["affected_node_ids"]` |
| `verify` | 验证图结构 | `{}` | `VerifyResult` |
| `save` | 保存当前编辑 | `{}` | `["changed_node_ids"]` |
| `export_graph` | 导出当前图 | `{}` | `ExecutionGraph` |
| `resolve_task` | 解析算子对应的任务 | `{"op": Operator}` | `DagTask` |
| `current_edit_status` | 获取客户端当前编辑状态 | `{}` | `ClientEditStatus` |

##### 错误处理

当操作失败时，响应消息格式为：
```json
{
    "message_id": "msg-123",
    "action": "add_nodes",
    "success": false,
    "result": null,
    "error": {
        "error_type": "GraphValidationError",
        "message": "节点ID已存在",
        "details": {"conflicting_nodes": ["node-1"]}
    }
}
```

### 5. 资源访问接口

本章节定义了全局的数据资源访问接口，支持通过资源ID访问不同类型的资源。API路径设计为 `/api/resources/<resource_type>/`，其中 `<resource_type>` 表示资源类型。

#### 5.1 结构化数据资源

##### 5.1.1 获取数据资源预览

*   **URL**: `/api/resources/data-resource/structured/preview`
*   **Method**: `POST`
*   **Description**: 获取指定数据资源的前N行预览数据。支持会话资源和全局资源。
*   **Request Body**:
    ```json
    {
        "resource_id": "session://session-abcdef12/data-resource/task-xyz789",
        "limit": 100,
        "backend_type": "duckdb"
    }
    ```
    *   `resource_id` (string, required): 资源ID，支持会话资源ID (`session://...`) 和全局资源ID (`data-resource://...`)。
    *   `limit` (integer, optional): 预览的行数限制，默认为100。
    *   `backend_type` (string, optional): 指定处理该资源的后端名称。对于会话资源，系统会自动识别；对于全局资源，可能需要指定。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "data_schema": {
            "fields": [
                {"name": "col1", "type": "string", "nullable": true, "description": null},
                {"name": "col2", "type": "integer", "nullable": false, "description": "数量"}
            ],
            "metadata": {}
        },
        "data": [
            {"col1": "val1", "col2": 123},
            {"col1": "val2", "col2": 456}
        ],
        "total_rows": 1000,
        "truncated": true
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，resource_id 无效或后端名称无效)
    *   **Content**: `{"detail": "获取资源预览失败: <error_message>"}`
    *   **Code**: `404 Not Found` (资源不存在)
    *   **Content**: `{"detail": "资源未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取资源预览失败: <error_message>"}`

##### 5.1.2 获取数据资源Schema

*   **URL**: `/api/resources/data-resource/structured/schema`
*   **Method**: `POST`
*   **Description**: 获取指定数据资源的结构（Schema）。支持会话资源和全局资源。
*   **Request Body**:
    ```json
    {
        "resource_id": "session://session-abcdef12/data-resource/task-xyz789",
        "backend_type": "duckdb"
    }
    ```
    *   `resource_id` (string, required): 资源ID，支持会话资源ID和全局资源ID。
    *   `backend_type` (string, optional): 指定处理该资源的后端名称。
*   **Success Response**:
    *   **Code**: `200 OK`
    *   **Content**: 
    ```json
    {
        "data_schema": {
            "fields": [
                {"name": "col1", "type": "string", "nullable": true, "description": null},
                {"name": "col2", "type": "integer", "nullable": false, "description": "数量"},
                {"name": "col3", "type": "double", "nullable": true, "description": "价格"}
            ],
            "metadata": {
                "table_name": "customer_data",
                "created_at": "2025-05-14T12:00:00"
            }
        }
    }
    ```
*   **Error Response**:
    *   **Code**: `400 Bad Request` (例如，resource_id 无效或后端名称无效)
    *   **Content**: `{"detail": "获取资源Schema失败: <error_message>"}`
    *   **Code**: `404 Not Found` (资源不存在)
    *   **Content**: `{"detail": "资源未找到"}`
    *   **Code**: `500 Internal Server Error`
    *   **Content**: `{"detail": "获取资源Schema失败: <error_message>"}`

#### 5.2 资源类型扩展

未来可以添加其他类型的资源访问接口，例如：

*   `/api/resources/ml-model/info` - 获取机器学习模型信息
*   `/api/resources/ml-model/predict` - 使用模型进行预测
*   `/api/resources/graph-data/query` - 查询图数据资源
*   `/api/resources/graph-data/schema` - 获取图数据Schema

每种资源类型都有其特定的操作接口，但都遵循统一的路径规范 `/api/resources/<resource_type>/<operation>`。

**注意**:
-   所有接口都应该处理认证和授权（未在示例中详述）。
-   错误响应格式应保持一致。
-   对于耗时操作，应采用异步处理模式，并提供查询任务状态的接口。
-   资源ID (`ResourceId`) 在请求体和响应体中应使用其字符串表示形式。
-   具体的Pydantic模型定义（用于请求体和响应体验证）将基于本文档中的接口描述来创建。

## 注意事项

### API 前缀约定
- 所有接口都使用 `/api` 作为统一前缀
- 完整的API地址格式为：`http://host:port/api/{path}`

### 分页接口设计原则
- **向后兼容性**：保留原有的GET接口作为简化版本，新增POST接口支持分页和过滤
- **统一分页格式**：所有分页接口使用相同的PageParam结构和响应格式
- **灵活过滤条件**：每个列表接口根据业务需求定义专用的过滤条件
- **排序支持**：支持单字段和多字段排序，支持嵌套字段排序
- **性能优化**：大列表建议使用分页接口以提高性能和用户体验

### 分页接口命名规范
- 简化版本：使用原有路径，如 `/api/backends`（GET方法）
- 分页版本：在路径末尾添加 `/query`，如 `/api/backends/query`（POST方法）
- 确保接口路径的一致性和可预测性

### 接口设计原则
- **任务管理统一使用 `/tasks/` 路径**：所有任务相关操作都使用统一的路径前缀
- **资源访问采用全局入口**：资源访问接口不依赖特定会话，使用 `/api/resources/<resource_type>/` 的统一格式
- **取消操作使用POST方法**：考虑到取消操作的复杂性和幂等性要求，使用POST而非DELETE

### 认证和授权
- 所有接口都应该处理认证和授权（具体实现细节将在后续版本中详述）
- 建议使用Bearer Token或API Key进行身份验证

### 错误处理
- 错误响应格式保持一致，使用标准的HTTP状态码
- 错误详情包含在响应体的 `detail` 字段中
- 对于验证错误，可能包含更详细的字段级错误信息

### 异步处理
- 对于耗时操作（如算子执行），采用异步处理模式
- 提供任务状态查询和取消功能
- 支持超时控制和轮询机制

### 资源ID规范
- 资源ID使用URI格式标识不同类型的资源
- 会话资源：`session://{session_id}/data-resource/{task_id}`
- 全局资源：`data-resource://{resource_id}` 或 `ml-model://{model_id}`
- 资源ID在请求体和响应体中使用字符串表示形式

### 资源访问设计
- 系统不直接暴露资源实体，而是通过客户端接口（如ResourceAccessor）提供访问能力
- 资源访问接口设计为全局入口，支持跨会话的资源访问
- 不同资源类型有各自的专用接口，遵循统一的路径规范

### 版本兼容性
- API设计考虑向后兼容性
- 新增字段采用可选方式，不破坏现有客户端
- 废弃的功能会提前通知并保持一定时间的支持

**注意**:
具体的Pydantic模型定义（用于请求体和响应体验证）将基于本文档中的接口描述来创建。OpenAPI规范文档将自动生成并与本设计文档保持同步。

## 数据结构定义

### 分页相关数据模型

#### PageParam (分页参数)

```json
{
    "pageIndex": 0,
    "limit": 10,
    "pageTotal": 5,
    "recordTotal": 50,
    "sortField": "created_at",
    "sortType": "desc",
    "sortList": [
        {"field": "created_at", "type": "desc"},
        {"field": "session_id", "type": "asc"}
    ],
    "isTop": false
}
```

**字段说明**:
- `pageIndex` (number): 当前页码，从0开始
- `limit` (number): 每页记录数，默认10
- `pageTotal` (number): 总页数（响应时由服务器计算返回）
- `recordTotal` (number): 总记录数（响应时由服务器计算返回）
- `sortField` (string, optional): 主排序字段
- `sortType` (string, optional): 主排序类型，可选值："asc"、"desc"
- `sortList` (array, optional): 多字段排序配置，每个元素包含field和type
- `isTop` (boolean, optional): 是否置顶显示，默认false

#### SortItem (排序项)

```json
{
    "field": "metadata.display_name",
    "type": "asc"
}
```

**字段说明**:
- `field` (string): 排序字段，支持嵌套字段（用点号分隔）
- `type` (string): 排序类型，可选值："asc"、"desc"

#### PagedResponse (分页响应)

```json
{
    "data": [...],
    "pageParam": {
        "pageIndex": 0,
        "limit": 10,
        "pageTotal": 5,
        "recordTotal": 50,
        "sortField": "created_at",
        "sortType": "desc",
        "sortList": [...],
        "isTop": false
    }
}
```

**字段说明**:
- `data` (array): 当前页的数据列表
- `pageParam` (PageParam): 分页信息，包含实际的分页状态

### 过滤条件数据模型

#### BackendListFilters (后端列表过滤条件)

```json
{
    "backend_type_pattern": "duck",
    "display_name_pattern": "DuckDB",
    "has_session_configs": true,
    "labels": {
        "version": "0.9.2",
        "category": "sql"
    }
}
```

#### OperatorListFilters (算子列表过滤条件)

```json
{
    "category": "transform",
    "operator_type_pattern": "Filter",
    "display_name_pattern": "过滤",
    "has_input_fields": true,
    "labels": {
        "complexity": "simple"
    }
}
```

#### SessionListFilters (会话列表过滤条件)

```json
{
    "backend_types": ["duckdb", "spark"],
    "states": ["RUNNING", "CREATED"],
    "created_after": "2025-01-01T00:00:00Z",
    "created_before": "2025-12-31T23:59:59Z",
    "session_id_pattern": "test-",
    "has_tasks": true,
    "task_count_min": 1,
    "task_count_max": 10
}
```

#### TaskListFilters (任务列表过滤条件)

```json
{
    "statuses": ["RUNNING", "SUCCESS", "ERROR"],
    "operator_types": ["FilterStructuredData", "ReadStructuredDataResource"],
    "created_after": "2025-01-01T00:00:00Z",
    "created_before": "2025-12-31T23:59:59Z",
    "node_ids": ["node-1", "node-2"],
    "has_result_resource": true,
    "task_id_pattern": "task-"
}
```

### 核心数据模型

#### BackendInfo (后端信息)

```json
{
    "backend_type": "duckdb",
    "metadata": {
        "display_name": "DuckDB数据处理后端",
        "description": "基于DuckDB的高性能数据处理后端",
        "labels": {"version": "0.9.2", "category": "sql"},
        "annotations": {"maintainer": "data-team"}
    },
    "session_configs": [
        {
            "key": "max_memory",
            "schema": {
                "type": "string",
                "description": "最大内存限制",
                "default": "4GB",
                "pattern": "^\\d+[KMGT]B$"
            }
        },
        {
            "key": "timeout",
            "schema": {
                "type": "integer",
                "description": "会话超时时间（秒）",
                "default": 3600,
                "minimum": 1
            }
        }
    ]
}
```

#### OperatorInfo (算子信息)

```json
{
    "type": "FilterStructuredData",
    "metadata": {
        "display_name": "过滤结构化数据",
        "description": "根据指定条件过滤结构化数据中的行",
        "labels": {"category": "transform", "complexity": "simple"},
        "annotations": {"documentation_url": "https://docs.example.com/operators/filter"}
    },
    "op_config_metadata": [
        {
            "key": "resourceId",
            "schema": {
                "type": "string",
                "description": "输入数据资源ID，支持会话资源和全局资源",
                "pattern": "^(session://[^/]+/data-resource/[^/]+|data-resource://[^/]+)$",
                "required": true
            }
        },
        {
            "key": "condition",
            "schema": {
                "type": "object",
                "description": "过滤条件，使用表达式DSL格式",
                "required": true,
                "properties": {
                    "type": {"type": "string", "enum": ["FuncCall", "Literal", "Identifier"]},
                    "name": {"type": "string"},
                    "args": {"type": "array"}
                }
            }
        }
    ],
    "input_fields": ["resourceId"]
}
```

#### TaskInfo (任务信息)

```json
{
    "task_id": "task-xyz789",
    "session_id": "session-abcdef12",
    "status": "SUCCESS",
    "created_at": "2025-05-14T12:00:00Z",
    "updated_at": "2025-05-14T12:01:30Z",
    "message": "任务成功完成，处理了1000行数据",
    "result_resource_id": "session://session-abcdef12/data-resource/task-xyz789"
}
```

#### DagTask (DAG任务信息)

```json
{
    "task_id": "task-xyz789",
    "session_id": "dag-session-123",
    "status": "SUCCESS",
    "created_at": "2025-05-14T12:00:00Z",
    "updated_at": "2025-05-14T12:01:30Z", 
    "message": "任务成功完成",
    "result_resource_id": "session://dag-session-123/data-resource/task-xyz789",
    "node_id": "node-1",
    "operator": {
        "type": "FilterStructuredData",
        "resourceId": "session://dag-session-123/data-resource/task-abc123",
        "condition": {
            "type": "FuncCall",
            "name": "gt",
            "args": [
                {"type": "Identifier", "name": "amount"},
                {"type": "Literal", "val": 1000}
            ]
        }
    },
    "dependencies": ["task-abc123"],
    "dependents": ["task-def456", "task-ghi789"],
    "upstream_changed": false
}
```

### DAG相关数据模型

#### ExecutionGraph (执行图)

```json
{
    "id": "execution-graph-123",
    "metadata": {
        "display_name": "销售数据处理工作流",
        "description": "完整的销售数据处理和分析工作流",
        "labels": {
            "version": "1.2",
            "team": "data-analytics",
            "environment": "production"
        },
        "annotations": {
            "created_by": "user123",
            "last_modified": "2025-05-14T10:30:00Z"
        }
    },
    "nodes": {
        "read-sales": {
            "id": "read-sales",
            "metadata": {
                "display_name": "读取销售数据",
                "description": "从数据仓库读取原始销售数据",
                "labels": {"step": "input"},
                "annotations": {}
            },
            "op_type": "ReadStructuredDataResource",
            "op_config": {
                "source": "data-resource://sales-2023",
                "format": "parquet",
                "options": {"compression": "snappy"}
            }
        },
        "filter-valid": {
            "id": "filter-valid",
            "metadata": {
                "display_name": "过滤有效数据",
                "description": "过滤掉无效或测试数据",
                "labels": {"step": "cleaning"},
                "annotations": {}
            },
            "op_type": "FilterStructuredData",
            "op_config": {
                "resourceId": "session://dag-session-123/data-resource/read-sales",
                "condition": {
                    "type": "FuncCall",
                    "name": "and",
                    "args": [
                        {"type": "FuncCall", "name": "is_not_null", "args": [{"type": "Identifier", "name": "customer_id"}]},
                        {"type": "FuncCall", "name": "gt", "args": [{"type": "Identifier", "name": "amount"}, {"type": "Literal", "val": 0}]}
                    ]
                }
            }
        }
    },
    "edges": [
        {
            "source": "read-sales",
            "target": "filter-valid",
            "target_config": "resourceId"
        }
    ]
}
```

#### GraphNode (图节点)

```json
{
    "id": "transform-node-1",
    "metadata": {
        "display_name": "数据转换节点",
        "description": "将原始数据转换为标准格式",
        "labels": {"category": "transform", "priority": "high"},
        "annotations": {"estimated_runtime": "30s"}
    },
    "op_type": "TransformStructuredData",
    "op_config": {
        "resourceId": "session://dag-session-123/data-resource/source-task",
        "outputColumns": [
            {
                "key": "customer_name",
                "source": "customer_name"
            },
            {
                "key": "total_amount",
                "eval": {
                    "type": "FuncCall",
                    "name": "multiply",
                    "args": [
                        {"type": "Identifier", "name": "unit_price"},
                        {"type": "Identifier", "name": "quantity"}
                    ]
                }
            }
        ]
    }
}
```

#### GraphEdge (图边)

```json
{
    "source": "read-data-node",
    "target": "transform-data-node",
    "target_config": "resourceId"
}
```

### 图编辑相关数据模型

#### PendingChanges (待提交的图变更集合)

```json
{
    "nodes_to_add": {
        "node-id-1": {
            "id": "node-id-1",
            "metadata": {
                "display_name": "新读取节点",
                "description": ""
            },
            "op_type": "ReadStructuredDataResource",
            "op_config": {
                "source": "data/new_sales.csv",
                "format": "csv"
            }
        }
    },
    "nodes_to_update": {
        "existing-node-id": {
            "op_config": {
                "format": "parquet"
            }
        }
    },
    "nodes_to_delete": [
        "old-node-id-1",
        "old-node-id-2"
    ],
    "edges_to_add": [
        {
            "source": "source-node",
            "target": "target-node",
            "target_config": "resourceId"
        }
    ],
    "edges_to_delete": [
        {
            "source": "old-source-node",
            "target": "old-target-node",
            "target_config": "resourceId"
        }
    ]
}
```

**字段说明**:
- `nodes_to_add` (object): 要添加的节点映射，键为节点ID，值为 `GraphNode` 对象。
- `nodes_to_update` (object): 要更新的节点配置映射，键为节点ID，值为包含要更新配置的字典。
- `nodes_to_delete` (array): 要删除的节点ID列表。
- `edges_to_add` (array): 要添加的边列表，每个元素为 `GraphEdge` 对象。
- `edges_to_delete` (array): 要删除的边列表，每个元素为 `GraphEdge` 对象。

#### ClientEditStatus (客户端编辑状态信息)

```json
{
    "has_pending_changes": true,
    "current_effective_graph": {
        "id": "graph-effective-123",
        "metadata": {},
        "nodes": {
            "node-a": {
                "id": "node-a",
                "metadata": {},
                "op_type": "ReadData",
                "op_config": {}
            }
        },
        "edges": []
    },
    "current_graph": {
        "id": "graph-client-123",
        "metadata": {},
        "nodes": {
            "node-a": {
                "id": "node-a",
                "metadata": {},
                "op_type": "ReadData",
                "op_config": {}
            },
            "new-node-b": {
                "id": "new-node-b",
                "metadata": {},
                "op_type": "FilterData",
                "op_config": {}
            }
        },
        "edges": [
            {
                "source": "node-a",
                "target": "new-node-b",
                "target_config": "input"
            }
        ]
    },
    "pending_changes": {
        "nodes_to_add": {
            "new-node-b": {
                "id": "new-node-b",
                "metadata": {},
                "op_type": "FilterData",
                "op_config": {}
            }
        },
        "nodes_to_update": {},
        "nodes_to_delete": [],
        "edges_to_add": [
            {
                "source": "node-a",
                "target": "new-node-b",
                "target_config": "input"
            }
        ],
        "edges_to_delete": []
    }
}
```

**字段说明**:
- `has_pending_changes` (boolean): 指示是否存在未提交的变更。
- `current_effective_graph` (ExecutionGraph): 当前会话实际生效的执行图（即 `save()` 后的图）。
- `current_graph` (ExecutionGraph): 客户端当前对应的执行图，包含所有未提交的变更。
- `pending_changes` (PendingChanges): 未提交的执行图变更详情。

### 枚举类型

#### RerunMode (重跑模式)

- `"NEVER"`: 从不重跑已成功的任务
- `"FORCE"`: 强制重跑所有指定任务
- `"FAILED_ONLY"`: 仅重跑失败的任务

#### MergeMode (合并模式)

- `"FAILED_ON_CONFLICT"`: 如果存在冲突则失败
- `"OVERWRITE_EXISTING"`: 覆盖已存在的节点
- `"MERGE_AND_UPDATE"`: 合并并更新冲突节点

#### TaskStatus (任务状态)

- `"CREATED"`: 任务已创建但未开始执行
- `"PENDING"`: 任务已提交执行但存在需要等待满足的条件
- `"RUNNING"`: 任务正在执行
- `"SUCCESS"`: 任务成功完成
- `"ERROR"`: 任务执行失败
- `"CANCELLED"`: 任务被取消
- `"UNKNOWN"`: 状态未知或无法确定

#### SessionState (会话状态)

- `"CREATED"`: 会话已创建但未使用
- `"RUNNING"`: 会话正在运行
- `"CLOSING"`: 会话正在关闭
- `"CLOSED"`: 会话已关闭

### 验证结果模型

#### VerifyResult (验证结果)

```json
{
    "valid": false,
    "failed_details": [
        {
            "rule_type": "DEPENDENCY_CYCLE",
            "message": "检测到循环依赖：node-1 -> node-2 -> node-1",
            "extra_info": {
                "cycle_path": ["node-1", "node-2", "node-1"],
                "involved_edges": [
                    {"source": "node-1", "target": "node-2"},
                    {"source": "node-2", "target": "node-1"}
                ]
            }
        },
        {
            "rule_type": "MISSING_RESOURCE",
            "message": "节点 'node-3' 引用了不存在的资源",
            "extra_info": {
                "node_id": "node-3",
                "missing_resource": "session://unknown-session/data-resource/missing-task",
                "config_field": "resourceId"
            }
        }
    ]
}
```

#### VerifyFailureDetail (验证失败详情)

```json
{
    "rule_type": "OPERATOR_CONFIG_INVALID",
    "message": "算子配置验证失败：缺少必需字段 'source'",
    "extra_info": {
        "node_id": "read-node-1",
        "operator_type": "ReadStructuredDataResource",
        "missing_fields": ["source"],
        "invalid_fields": {"format": "unsupported format 'xlsx'"}
    }
}
```
