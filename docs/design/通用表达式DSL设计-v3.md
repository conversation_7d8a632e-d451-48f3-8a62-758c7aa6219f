# 通用表达式DSL设计

## 简介

通用表达式DSL（领域特定语言）设计旨在提供一种基于JSON格式的、统一的表达式描述方式，能够覆盖SQL-99级别的表达式能力，并支持通过类型系统实现跨语言的类型推断和校验。该DSL特别适用于各种数据处理场景，如ETL流程、数据转换、查询构建等，可以作为多种后端（如SQL引擎、数据处理框架等）的前端表达层。

本设计的核心目标包括：

- 提供简洁统一的表达式描述方式
- 支持SQL-99级别的表达式能力（不包括子查询、聚合函数、开窗函数）
- 通过类型系统实现跨语言的类型推断和校验
- 便于与现有数据处理框架集成
- 实现语义分析与后端执行的解耦

核心设计理念是通过仅使用三种基本AST节点（字面量、函数调用、标识符）来表达复杂的表达式，并结合强大的类型系统和语义分析机制，在不依赖特定后端实现的情况下实现表达式的类型检查、推断和语义验证。

## DuckDB 表达式语义解析详细介绍

DuckDB是一个高性能的分析型数据库系统，其表达式语义解析部分对我们的DSL设计具有重要的参考价值。下面详细介绍DuckDB的语义解析过程和关键组件。

### Binder组件

在DuckDB中，`Binder`是负责语义分析的核心组件。它的主要职责是将SQL解析器生成的原始AST（Abstract Syntax Tree）转换为绑定后的查询树（Bound Query Tree）。这个过程包括：

1. **名称解析**：解析表名、列名、函数名等标识符，将它们与Catalog（元数据存储）中的实体关联。
2. **类型检查与推断**：对表达式进行自底向上的类型检查和推断。
3. **语义验证**：验证表达式在特定上下文（如WHERE子句、GROUP BY子句等）中的合法性。

### 函数签名与类型系统

DuckDB使用函数签名（Function Signature）来描述函数的参数类型、返回类型和函数类别（标量、聚合、窗口等）。这些签名存储在Catalog中，供Binder在语义分析过程中使用。关键点：

- **函数重载**：同名函数可以有多个不同参数类型的实现。
- **类型转换**：DuckDB定义了一套类型转换规则，用于在必要时进行隐式类型转换。
- **函数类别**：明确区分标量函数、聚合函数和窗口函数，以便在不同上下文中进行适当的验证。

### 绑定表达式树

DuckDB的绑定表达式树由多种`BoundExpression`节点组成，包括：

- `BoundLiteralExpression`：表示字面量值
- `BoundColumnRefExpression`：表示列引用
- `BoundFunctionExpression`：表示标量函数调用
- `BoundAggregateExpression`：表示聚合函数调用
- `BoundWindowExpression`：表示窗口函数调用

每个绑定节点都包含了推断出的类型信息，这对于后续的查询优化和执行至关重要。

### 后端无关性

DuckDB的语义分析过程是高度**后端无关**的，它主要依赖于函数签名和类型系统，而不依赖于函数的具体实现。这种设计允许Binder专注于语义验证和类型检查，而将执行细节留给后续的执行引擎处理。

### 聚合和窗口函数处理

对于聚合和窗口函数，DuckDB有专门的处理机制：

- **聚合函数**：Binder会检查聚合函数是否出现在允许的上下文中（如SELECT列表、HAVING子句等）。
- **窗口函数**：Binder会解析和验证窗口函数的OVER子句，包括PARTITION BY和ORDER BY部分。

DuckDB的这些设计为我们提供了重要启示：通过在语义分析阶段将函数明确分类为标量、聚合或窗口函数，可以在不依赖后端实现的情况下完成大部分语义验证工作。

## 整体架构设计

我们的表达式DSL架构设计借鉴了DuckDB的语义分析思路，但保持了更简洁的AST节点设计。
**新的用户工作流**：用户提供JSON表示的AST表达式以及与目标后端匹配的语义上下文配置，直接调用目标后端的转译器（Translator）进行处理。转译器内部会先使用语义分析器（SemanticAnalyzer）结合上下文进行语义分析和类型检查，生成内部的语义树（SemanticTree），然后将语义树转译为后端可执行的代码或对象。

整体业务流程如下：

```mermaid
graph TD
    A[JSON-AST表达式] --> T[转译器 Translator]
    C["语义上下文配置 (后端相关)"] --> T
    
    subgraph T [转译器内部流程]
        direction LR
        Input[AST + Context] --> SA(语义分析器 SemanticAnalyzer)
        SA --> |生成| ST[语义树 SemanticTree]
        ST --> |转译| BE[后端代码/对象]
    end

    T --> E[执行结果]
```

这个架构的核心优势在于：

1.  **用户接口简化**：用户只需调用一次`translate`方法，传入AST和上下文即可。
2.  **强耦合性体现**：明确了语义上下文（尤其是函数表）必须与选择的转译器后端相匹配。
3.  **解耦依然保持**：语义分析逻辑 (`SemanticAnalyzer`) 和后端转译逻辑 (`Translator`的具体实现) 在内部仍然是分离的，只是由`Translator`统一调度。

### 核心业务对象

以下是核心业务对象之间的关系（已更新以反映泛型设计）：

```mermaid
classDiagram
    class ExpressionNode { <<abstract>> }
    class Literal
    class Identifier
    class FuncCall
    ExpressionNode <|-- Literal
    ExpressionNode <|-- Identifier
    ExpressionNode <|-- FuncCall

    class DataStruct { <<abstract>> }

    %% Evaluation Contexts %%
    class BaseEvaluationContext { 
        <<abstract>>
        +context_type: ContextType
    }
    class RowLevelEvaluationContext
    class AggregateEvaluationContext {
        +group_by_columns: List~String~
        +pre_aggregation: bool
    }
    class WindowEvaluationContext {
        +partition_columns: Optional~List~String~~
        +order_by_columns: Optional~List~String~~
        +post_window: bool
    }
    BaseEvaluationContext <|-- RowLevelEvaluationContext
    BaseEvaluationContext <|-- AggregateEvaluationContext
    BaseEvaluationContext <|-- WindowEvaluationContext

    %% Node Extension Contexts %%
    class BaseNodeExtensionContext { <<abstract>> }
    class RowLevelNodeExtensionContext  // 可以为空或包含通用信息
    class AggregateNodeExtensionContext {
        +is_aggregate_argument: bool // 标识节点是否是聚合函数的直接参数
        +is_grouping_key: bool      // 标识节点是否是GROUP BY键的一部分
    }
    class WindowNodeExtensionContext {
        +is_partition_key: bool
        +is_order_key: bool
        +window_spec: Optional~WindowSpec~ // 函数调用节点可以关联具体的窗口定义
    }
    BaseNodeExtensionContext <|-- RowLevelNodeExtensionContext
    BaseNodeExtensionContext <|-- AggregateNodeExtensionContext
    BaseNodeExtensionContext <|-- WindowNodeExtensionContext

    %% Core Semantic Classes %%
    class IdentifierInfo {
        name: String
        struct: DataStruct
    }
    class FunctionInfo {
        name: String
        description: String
        function_category: FunctionCategory
        args_list: List~FunctionArgInfo~
        var_arg: FunctionVarArgInfo
        return_struct: DataStruct
        options_json_schema: Dict
    }
    class FunctionArgInfo
    class FunctionVarArgInfo
    FunctionInfo -- FunctionArgInfo
    FunctionInfo -- FunctionVarArgInfo

    class SemanticContext~T_EvalContext, T_NodeExtContext~ {
        +identifier_table: List~IdentifierInfo~
        +function_table: List~FunctionInfo~
        +evaluation_context: T_EvalContext
    }
    SemanticContext -- IdentifierInfo : contains
    SemanticContext -- FunctionInfo : contains
    SemanticContext -- BaseEvaluationContext : contains

    class SemanticNode~T_EvalContext, T_NodeExtContext~ {
        <<abstract>>
        node_type: String
        inferred_type: DataStruct
        extension_context: T_NodeExtContext
    }
    SemanticNode -- DataStruct : has inferred
    SemanticNode -- BaseNodeExtensionContext : carries

    class SemanticLiteral~T_EvalContext, T_NodeExtContext~
    class SemanticIdentifier~T_EvalContext, T_NodeExtContext~
    class SemanticFuncCall~T_EvalContext, T_NodeExtContext~
    SemanticNode <|-- SemanticLiteral
    SemanticNode <|-- SemanticIdentifier
    SemanticNode <|-- SemanticFuncCall

    SemanticIdentifier -- IdentifierInfo : resolved to
    SemanticFuncCall -- FunctionInfo : resolved to

    %% Translator %%
    class ExpressionTranslator~T_EvalContext, T_NodeExtContext~ {
        <<interface>>
        +translate(node: ExpressionNode, context: SemanticContext~T_EvalContext, T_NodeExtContext~) -> Any
    }

    ExpressionNode --> ExpressionTranslator : input
    SemanticContext --> ExpressionTranslator : input
    ExpressionTranslator ..> SemanticNode : creates internaly
    SemanticNode --> ExpressionTranslator : used internaly
```

#### 表达式AST

表达式AST是表达式的JSON表示形式，它由三种基本节点类型组成：

1. **Literal（字面量）**：表示一个具体的值，如数字、字符串、布尔值等。
   - `val`: 字面量值，使用JSON表示
   - `struct`: 可选的类型描述，使用基于DataStruct的类型系统

2. **Identifier（标识符）**：表示对变量或字段的引用。
   - `name`: 标识符名称
   - `attrPath`: 可选的属性访问路径，用于深层次访问结构体内的字段

3. **FuncCall（函数调用）**：表示函数调用或操作符表达式。
   - `name`: 函数名称
   - `args`: 参数列表，每个参数都是一个表达式节点
   - `options`: 可选的函数配置选项

这种简洁的设计使得复杂的表达式也能以统一的方式表示。例如，算术运算、逻辑运算、比较运算等都表示为对应函数的调用。

#### 语义上下文配置 (SemanticContext)

语义上下文配置提供了进行语义分析所需的环境信息。现在它是一个**泛型类** `SemanticContext[T_EvalContext, T_NodeExtContext]`，其中：
- `T_EvalContext` 继承自 `BaseEvaluationContext`，指定了具体的**求值上下文类型** (如 `RowLevelEvaluationContext`, `AggregateEvaluationContext`, `WindowEvaluationContext`)，包含了该场景所需的特定信息（如 `group_by_columns`）。
- `T_NodeExtContext` 继承自 `BaseNodeExtensionContext`，定义了该场景下**语义节点应携带的扩展上下文类型** (如 `RowLevelNodeExtensionContext`, `AggregateNodeExtensionContext`, `WindowNodeExtensionContext`)，用于存储分析过程中节点特有的语义信息（如 `is_aggregate_argument`）。

它主要包含：

1.  **identifier_table**: 标识符表 (List[IdentifierInfo])。
2.  **function_table**: 函数表 (List[FunctionInfo])，**必须与目标后端的能力匹配**。
3.  **evaluation_context**: 具体的求值上下文实例 (`T_EvalContext`)。

#### 表达式Semantic Tree (SemanticNode)

表达式Semantic Tree是经过语义分析后的表达式树。现在它是一个**泛型类** `SemanticNode[T_EvalContext, T_NodeExtContext]`，表示该节点是在特定求值上下文 (`T_EvalContext`) 下分析得出的，并携带了特定场景的扩展语义信息 (`T_NodeExtContext`)。

每个节点包含：
- `node_type`: 节点类型 (Literal, Identifier, FuncCall)
- `inferred_type`: 推断出的节点求值结果类型 (DataStruct)
- `extension_context`: 特定场景的扩展语义信息实例 (`T_NodeExtContext`)

**在新的工作流中，Semantic Tree 依然是转译器内部的中间产物。**

#### 执行后端实现 (ExpressionTranslator)

执行后端实现负责将原始AST和匹配的 `SemanticContext` 转译为特定后端的执行代码或对象。现在它是一个**泛型接口** `ExpressionTranslator[T_EvalContext, T_NodeExtContext]`，明确了它处理的求值场景和节点扩展信息类型。

其核心方法 `translate(node: ExpressionNode, context: SemanticContext[T_EvalContext, T_NodeExtContext]) -> Any` 封装了整个流程：
1.  内部实例化并调用对应的 `SemanticAnalyzer[T_EvalContext, T_NodeExtContext]`。
2.  生成内部的 `SemanticNode[T_EvalContext, T_NodeExtContext]`。
3.  根据生成的语义树和扩展信息进行后端代码转译。

## 表达式AST 详细设计

表达式AST（抽象语法树）是整个DSL的基础部分，它使用简洁的JSON格式来表示各种复杂的表达式。遵循我们的核心设计理念，AST仅由三种基本节点类型组成：Literal（字面量）、Identifier（标识符）和FuncCall（函数调用）。

### AST JSON-SCHEMA

以下是表达式AST的JSON Schema定义：

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "JSON表达式AST",
  "description": "定义基于JSON的表达式AST节点（Literal、FuncCall、Identifier）的结构，使用自定义的DataStruct类型系统。",

  "definitions": {
    "ExpressionNode": {
      "description": "表达式AST中任何节点的基类型。",
      "oneOf": [
        { "$ref": "#/definitions/Literal" },
        { "$ref": "#/definitions/FuncCall" },
        { "$ref": "#/definitions/Identifier" }
      ]
    },

    "Literal": {
      "type": "object",
      "description": "表示字面量值。",
      "required": ["type", "val"],
      "properties": {
        "type": {
          "const": "Literal"
        },
        "val": {
          "description": "以标准JSON格式表示的字面量值（字符串、数字、布尔值、数组、对象、null）。"
        },
        "struct": {
          "$ref": "#/definitions/DataStruct",
          "description": "字面量值的数据类型结构（可选，如果可以推断出类型则可以省略）。"
        }
      },
      "additionalProperties": false
    },

    "Identifier": {
      "type": "object",
      "description": "表示标识符，可能包含属性访问。",
      "required": ["type", "name"],
      "properties": {
        "type": {
          "const": "Identifier"
        },
        "name": {
          "type": "string",
          "description": "标识符的基本名称（如变量名）。"
        },
        "attrPath": {
          "type": "array",
          "items": { "type": "string" },
          "description": "可选的属性访问部分列表（如['address', 'city']表示'user.address.city'）。"
        }
      },
      "additionalProperties": false
    },

    "FuncCall": {
      "type": "object",
      "description": "表示函数或运算符调用。",
      "required": ["type", "name", "args"],
      "properties": {
        "type": {
          "const": "FuncCall"
        },
        "name": {
          "type": "string",
          "description": "函数或运算符的名称（如'add'、'concat'、'in'、'case'）。"
        },
        "args": {
          "type": "array",
          "items": { "$ref": "#/definitions/ExpressionNode" },
          "description": "函数调用的参数列表，每个参数都是一个表达式节点。"
        },
        "options": {
          "type": "object",
          "description": "特定于函数的可选键值选项（结构由函数的'options_json_schema'定义）。"
        }
      },
      "additionalProperties": false
    },

    "DataStruct": {
      "description": "参考2.4.6数据结构设计中定义的数据类型系统",
      "type": "object"
    }
  },

  "$ref": "#/definitions/ExpressionNode"
}
```

这个JSON Schema定义了表达式AST的结构，确保了AST的格式正确性和完整性。它引用了在2.4.6数据结构设计中定义的`DataStruct`类型系统，用于描述字面量的类型。

### AST 模型类定义

下面是表达式AST的Python模型类定义：

```python
from __future__ import annotations
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field

from dpe_dsl.types.struct import DataStruct


class ExpressionNodeType(str, Enum):
    """表达式节点类型枚举"""
    LITERAL = "Literal"
    IDENTIFIER = "Identifier"
    FUNC_CALL = "FuncCall"


class ExpressionNode(BaseModel):
    """表达式节点基类"""
    type: ExpressionNodeType

    class Config:
        use_enum_values = True


class Literal(ExpressionNode):
    """字面量节点，表示一个具体的值"""
    type: ExpressionNodeType = ExpressionNodeType.LITERAL
    val: Any = Field(description="使用JSON表示的字面量值")
    struct: Optional[DataStruct] = Field(
        default=None, 
        description="字面量的类型描述（可选，如果可以推断出类型则可以省略）"
    )


class Identifier(ExpressionNode):
    """标识符节点，表示对变量或字段的引用"""
    type: ExpressionNodeType = ExpressionNodeType.IDENTIFIER
    name: str = Field(description="标识符名称")
    attrPath: Optional[List[str]] = Field(
        default=None, 
        description="可选的属性访问路径（如['address', 'city']表示'user.address.city'）"
    )


class FuncCall(ExpressionNode):
    """函数调用节点，表示函数调用或操作符表达式"""
    type: ExpressionNodeType = ExpressionNodeType.FUNC_CALL
    name: str = Field(description="函数名称")
    args: List[ExpressionNode] = Field(description="函数调用的参数列表")
    options: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="特定于函数的可选键值选项"
    )


# 为了类型提示的方便，定义一个联合类型
ExprNodeUnion = Union[Literal, Identifier, FuncCall]
```

这些模型类基于Pydantic和我们已有的类型系统（`DataStruct`），提供了对表达式AST的强类型支持和验证能力。

#### 示例：表达式AST的JSON表示

为了更好地理解表达式AST的设计，下面给出一些常见表达式的JSON表示示例：

1. **简单算术表达式**：`a + b * 2`

```json
{
  "type": "FuncCall",
  "name": "add",
  "args": [
    {
      "type": "Identifier",
      "name": "a"
    },
    {
      "type": "FuncCall",
      "name": "multiply",
      "args": [
        {
          "type": "Identifier",
          "name": "b"
        },
        {
          "type": "Literal",
          "val": 2,
          "struct": {
            "type": "Int",
            "bitWidth": 32,
            "nullable": false
          }
        }
      ]
    }
  ]
}
```

2. **条件表达式**：`if(age > 18, 'Adult', 'Minor')`

```json
{
  "type": "FuncCall",
  "name": "if",
  "args": [
    {
      "type": "FuncCall",
      "name": "greater",
      "args": [
        {
          "type": "Identifier",
          "name": "age"
        },
        {
          "type": "Literal",
          "val": 18,
          "struct": {
            "type": "Int",
            "bitWidth": 32,
            "nullable": false
          }
        }
      ]
    },
    {
      "type": "Literal",
      "val": "Adult",
      "struct": {
        "type": "String",
        "nullable": false
      }
    },
    {
      "type": "Literal",
      "val": "Minor",
      "struct": {
        "type": "String",
        "nullable": false
      }
    }
  ]
}
```

3. **结构体属性访问**：`user.address.city`

```json
{
  "type": "Identifier",
  "name": "user",
  "attrPath": ["address", "city"]
}
```

这些例子展示了表达式DSL如何使用仅仅三种基本节点类型来表示各种复杂的表达式，实现了设计的简洁性和表达能力的平衡。

## 表达式 Semantic Tree 详细设计

表达式Semantic Tree（语义树）是在原始AST基础上，经过类型推断和语义分析后得到的带有类型信息和语义信息的表达式树。Semantic Tree保持了与原始AST相似的结构，但每个节点都增加了推断出的类型信息，函数调用节点还增加了函数类别信息。

```mermaid
classDiagram
    class SemanticNode {
        <<abstract>>
        node_type: str
        inferred_type: DataStruct
    }

    class SemanticLiteral {
        val: Any
        struct: DataStruct
    }

    class SemanticIdentifier {
        name: str
        attrPath: List[str]
        resolved_identifier: IdentifierInfo
    }

    class SemanticFuncCall {
        name: str
        args: List[SemanticNode]
        options: Dict
        function_category: FunctionCategory
        resolved_function: FunctionInfo
    }

    class SemanticContext {
        identifier_table: List[IdentifierInfo]
        function_table: List[FunctionInfo]
        evaluation_context: EvaluationContext
    }

    class EvaluationContext {
        context_type: ContextType
        window_spec: Optional[WindowSpec]
        group_by_columns: Optional[List[str]]
    }

    class FunctionCategory {
        <<enumeration>>
        SCALAR
        AGGREGATE
        WINDOW
    }

    class ContextType {
        <<enumeration>>
        ROW_LEVEL
        AGGREGATE
        WINDOW
    }

    SemanticNode <|-- SemanticLiteral
    SemanticNode <|-- SemanticIdentifier
    SemanticNode <|-- SemanticFuncCall
    
    SemanticContext -- EvaluationContext
    SemanticContext -- IdentifierInfo
    SemanticContext -- FunctionInfo
    
    SemanticFuncCall -- FunctionCategory
    EvaluationContext -- ContextType
```

### 语义上下文配置 详细设计

语义上下文配置（SemanticContext）是进行语义分析的环境信息，它包含了标识符表、函数表和求值上下文信息。在语义分析过程中，这些信息用于解析标识符、匹配函数签名、推断类型，以及验证表达式的语义合法性。

#### 标识符表（Identifier Table）

标识符表包含了所有可用的标识符及其类型信息。每个标识符条目（IdentifierInfo）包含：

- `name`: 标识符名称
- `struct`: 标识符的类型结构（基于DataStruct类型系统）

标识符表由用户在创建语义分析上下文时提供，它可能来自数据源的元数据（如表的列信息）、程序变量等。

#### 函数表（Function Table）

函数表包含了所有可用的函数、运算符及其签名信息。每个函数条目（FunctionInfo）包含：

- `name`: 函数名称
- `description`: 函数描述
- `function_category`: 函数类别（SCALAR、AGGREGATE、WINDOW）
- `args_list`: 参数列表，每个参数包含名称、描述和可接受的类型
- `var_arg`: 可选的变长参数定义
- `return_struct`: 返回类型结构
- `options_json_schema`: 可选的函数配置选项的JSON Schema

函数表是语义分析的核心参考，它定义了所有可用的函数，以及它们的参数类型、返回类型和函数类别。函数类别是语义分析的重要依据，它决定了函数在不同上下文中的合法性。

#### 求值上下文（Evaluation Context）

求值上下文描述了表达式将在何种环境中求值，它包含：

- `context_type`: 上下文类型（ROW_LEVEL、AGGREGATE、WINDOW）
- `window_spec`: 窗口函数的窗口规范（当上下文类型为WINDOW时有效）
- `group_by_columns`: 分组列（当上下文类型为AGGREGATE时有效）

求值上下文用于验证表达式在特定环境中的合法性。例如，在ROW_LEVEL上下文中，不允许出现聚合函数；在AGGREGATE上下文中，非聚合列必须在group_by_columns中；在WINDOW上下文中，窗口函数必须提供window_spec。

#### 函数类别（Function Category）

函数类别是函数的基本属性，它决定了函数在不同上下文中的行为和合法性：

- **SCALAR**: 标量函数，按行进行求值，每行输入产生一行输出
- **AGGREGATE**: 聚合函数，对一组行进行求值，产生一个结果
- **WINDOW**: 窗口函数，对窗口内的行进行求值，每行产生一个结果，但考虑了窗口内的其他行

函数类别的明确区分是实现语义分析与后端执行解耦的关键，因为它允许语义分析器在不了解函数具体实现的情况下，根据函数类别和上下文类型进行合法性验证。

### Python 接口设计

#### 求值上下文与节点扩展上下文模型类定义

```python
from __future__ import annotations
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic, Literal
from abc import ABC, abstractmethod
from enum import Enum
from pydantic import BaseModel, Field

from dpe_dsl.types.struct import DataStruct
# 假设其他需要的模型已在此处或上方定义
# from dpe_dsl.ast import ... 

# --- 求值上下文类型 --- 

class ContextType(str, Enum):
    """求值上下文类型枚举"""
    ROW_LEVEL = "ROW_LEVEL"   # 行级求值
    AGGREGATE = "AGGREGATE"    # 聚合求值
    WINDOW = "WINDOW"          # 窗口函数求值

class BaseEvaluationContext(BaseModel, ABC):
    """所有求值上下文的抽象基类"""
    context_type: ContextType = Field(description="具体的求值上下文类型")

    class Config:
        frozen = True # 上下文信息通常是不可变的

class RowLevelEvaluationContext(BaseEvaluationContext):
    """行级表达式求值上下文"""
    context_type: Literal[ContextType.ROW_LEVEL] = ContextType.ROW_LEVEL
    # 可以添加行级特有的参数，例如：
    # current_row_index: Optional[int] = Field(default=None, description="当前处理的行号（如果可用）")

class AggregateEvaluationContext(BaseEvaluationContext):
    """聚合表达式求值上下文 (例如用于 SELECT 列表中的聚合或 HAVING 子句)"""
    context_type: Literal[ContextType.AGGREGATE] = ContextType.AGGREGATE
    group_by_columns: List[str] = Field(description="分组列名列表")
    pre_aggregation: bool = Field(
        description="指示表达式是否在聚合计算之前求值 (例如在 SELECT/GROUP BY 中直接引用的列 vs HAVING 中的表达式)"
    )

class WindowEvaluationContext(BaseEvaluationContext):
    """窗口函数求值上下文"""
    context_type: Literal[ContextType.WINDOW] = ContextType.WINDOW
    # 注意：WindowSpec（分区、排序、帧）通常与具体的窗口函数调用相关联，
    # 而不是整个求值上下文。因此，它更适合放在 WindowNodeExtensionContext 中。
    # 这里可以放一些窗口计算阶段的全局信息，例如：
    post_window: bool = Field(
        description="指示表达式是否在窗口函数计算之后求值（例如，对窗口函数结果进行进一步计算）"
    )
    # available_partitions: Optional[List[str]] = Field(default=None, description="当前可用的分区列")
    # available_orderings: Optional[List[str]] = Field(default=None, description="当前可用的排序列")

# 求值上下文的泛型变量和联合类型
T_EvalContext = TypeVar('T_EvalContext', bound=BaseEvaluationContext)
EvaluationContextUnion = Union[RowLevelEvaluationContext, AggregateEvaluationContext, WindowEvaluationContext]

# --- 语义节点扩展上下文 --- 

class BaseNodeExtensionContext(BaseModel, ABC):
    """所有语义节点扩展上下文的抽象基类 (用于存储特定场景的语义信息)"""
    pass

    class Config:
        frozen = True

class RowLevelNodeExtensionContext(BaseNodeExtensionContext):
    """行级求值场景下节点的扩展信息 (可以为空)"""
    # 可以添加通用信息，如节点来源等
    source_location: Optional[str] = Field(default=None, description="节点在原始表达式中的位置信息")

class AggregateNodeExtensionContext(BaseNodeExtensionContext):
    """聚合求值场景下节点的扩展信息"""
    is_aggregate_argument: bool = Field(
        default=False, 
        description="标识该节点是否是聚合函数的直接或间接参数"
    )
    is_grouping_key: bool = Field(
        default=False, 
        description="标识该节点是否直接或间接代表一个 GROUP BY 的键"
    )

class WindowNodeExtensionContext(BaseNodeExtensionContext):
    """窗口函数求值场景下节点的扩展信息"""
    is_partition_key: bool = Field(default=False, description="标识节点是否是窗口分区键的一部分")
    is_order_key: bool = Field(default=False, description="标识节点是否是窗口排序键的一部分")
    # WindowSpec 更适合放在这里，与具体的 FuncCall 节点关联
    window_spec: Optional[WindowSpec] = Field(
        default=None, 
        description="如果节点是窗口函数调用，这里存储其窗口规范"
    )

# 节点扩展上下文的泛型变量和联合类型
T_NodeExtContext = TypeVar('T_NodeExtContext', bound=BaseNodeExtensionContext)
NodeExtensionContextUnion = Union[RowLevelNodeExtensionContext, AggregateNodeExtensionContext, WindowNodeExtensionContext]


#### 语义树结构模型类定义 (泛型)

```python
# --- 核心语义上下文与节点定义 (泛型) ---

class SemanticContext(BaseModel, Generic[T_EvalContext, T_NodeExtContext]):
    """泛型语义上下文，提供语义分析所需的环境信息"""
    identifier_table: List[IdentifierInfo] = Field(description="标识符表")
    function_table: List[FunctionInfo] = Field(
        description="函数表 (必须与目标后端的能力匹配)"
    )
    evaluation_context: T_EvalContext = Field(description="具体的求值上下文实例")

    class Config:
        frozen = True
    
    # 辅助方法 (无实现)
    def find_identifier(self, name: str) -> Optional[IdentifierInfo]:
        """在标识符表中查找指定名称的标识符"""
        raise NotImplementedError
    
    def find_function(
        self, name: str, arg_types: List[DataStruct]
    ) -> Optional[FunctionInfo]:
        """在函数表中查找匹配的函数签名"""
        raise NotImplementedError


class SemanticNodeType(str, Enum):
    """语义节点类型枚举"""
    LITERAL = "Literal"
    IDENTIFIER = "Identifier"
    FUNC_CALL = "FuncCall"


class SemanticNode(BaseModel, Generic[T_EvalContext, T_NodeExtContext], ABC):
    """泛型语义节点基类"""
    node_type: SemanticNodeType = Field(description="语义节点类型")
    inferred_type: DataStruct = Field(description="推断出的节点求值结果类型")
    extension_context: T_NodeExtContext = Field(description="特定场景的扩展语义信息")

    class Config:
        frozen = True
        use_enum_values = True


class SemanticLiteral(
    SemanticNode[T_EvalContext, T_NodeExtContext]
):
    """泛型语义字面量节点"""
    node_type: Literal[SemanticNodeType.LITERAL] = SemanticNodeType.LITERAL
    val: Any = Field(description="字面量值")
    struct: DataStruct = Field(description="字面量的显式类型结构")


class SemanticIdentifier(
    SemanticNode[T_EvalContext, T_NodeExtContext]
):
    """泛型语义标识符节点"""
    node_type: Literal[SemanticNodeType.IDENTIFIER] = SemanticNodeType.IDENTIFIER
    name: str = Field(description="标识符名称")
    attrPath: Optional[List[str]] = Field(
        default=None, 
        description="属性访问路径"
    )
    resolved_identifier: IdentifierInfo = Field(
        description="解析后的标识符信息"
    )


class SemanticFuncCall(
    SemanticNode[T_EvalContext, T_NodeExtContext]
):
    """泛型语义函数调用节点"""
    node_type: Literal[SemanticNodeType.FUNC_CALL] = SemanticNodeType.FUNC_CALL
    name: str = Field(description="函数名称")
    # 注意：参数列表中的节点也必须是相同泛型类型的语义节点
    args: List[SemanticNode[T_EvalContext, T_NodeExtContext]] = Field(
        description="函数参数列表 (已解析为语义节点)"
    )
    options: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="函数选项"
    )
    function_category: FunctionCategory = Field(description="函数类别")
    resolved_function: FunctionInfo = Field(description="解析后的函数信息")


# 联合类型，方便类型提示
SemanticNodeUnion = Union[
    SemanticLiteral[Any, Any], 
    SemanticIdentifier[Any, Any], 
    SemanticFuncCall[Any, Any]
] # 使用 Any, Any 作为默认类型参数

```

#### 语义解析接口定义 (泛型)

```python
# --- 语义分析器接口 (泛型) ---

class SemanticAnalyzer(
    Generic[T_EvalContext, T_NodeExtContext], 
    ABC
):
    """
    泛型表达式语义分析器（内部组件）。
    负责将 AST 结合特定求值上下文 (T_EvalContext) 转换为带有
    特定节点扩展信息 (T_NodeExtContext) 的 Semantic Tree。
    """
    
    def __init__(self, context: SemanticContext[T_EvalContext, T_NodeExtContext]):
        """
        初始化特定场景的语义分析器。
        
        Args:
            context: 包含特定求值上下文和后端匹配函数表的泛型语义上下文。
        """
        self.context: SemanticContext[T_EvalContext, T_NodeExtContext] = context
    
    @abstractmethod
    def analyze(
        self, node: ExpressionNode
    ) -> SemanticNode[T_EvalContext, T_NodeExtContext]:
        """
        分析表达式AST节点，生成特定上下文的语义树节点。
        
        Args:
            node: 原始表达式AST节点。
        
        Returns:
            携带特定上下文信息的语义树节点。
        
        Raises:
            SemanticError: 当表达式存在语义错误时抛出。
        """
        pass
    
    # 内部辅助方法 (通常需要，但接口层面不强制要求，具体实现类会定义)
    # @abstractmethod
    # def _analyze_literal(self, node: Literal) -> SemanticLiteral[T_EvalContext, T_NodeExtContext]: ...
    
    # @abstractmethod
    # def _analyze_identifier(self, node: Identifier) -> SemanticIdentifier[T_EvalContext, T_NodeExtContext]: ...
    
    # @abstractmethod
    # def _analyze_func_call(self, node: FuncCall) -> SemanticFuncCall[T_EvalContext, T_NodeExtContext]: ...

    # @abstractmethod
    # def check_type_compatibility(...) -> ...:
    #     """检查类型兼容性"""
    #     pass

    # @abstractmethod
    # def validate_node_in_context(self, node: SemanticNode[T_EvalContext, T_NodeExtContext]) -> None:
    #     """根据具体的 T_EvalContext 验证节点语义"""
    #     pass

class SemanticError(Exception):
    """语义错误，表示表达式存在语义问题"""
    pass

```



#### 适配不同的求值场景

通过将 `EvaluationContext` 和 `SemanticNode` / `SemanticAnalyzer` 设计为泛型，并为每种求值场景（ROW_LEVEL, AGGREGATE, WINDOW）定义具体的上下文类和分析器类，我们可以更清晰地处理不同场景的逻辑：

- **类型安全**：`SemanticContext[RowLevelEvaluationContext, RowLevelNodeExtensionContext]` 明确表示这是一个用于行级求值的上下文。
- **职责分离**：`RowLevelSemanticAnalyzer` 只需关注行级规则（如禁止聚合/窗口函数），`AggregateSemanticAnalyzer` 关注聚合规则（如 `group_by_columns` 和 `pre_aggregation` 状态），`WindowSemanticAnalyzer` 关注窗口规则。
- **信息传递**：`SemanticNode.extension_context` 可以携带特定场景的信息，例如 `AggregateNodeExtensionContext` 可以标记一个标识符是否为分组键，供后续转译器使用。
- **明确的API**：`ExpressionTranslator[RowLevelEvaluationContext, RowLevelNodeExtensionContext]` 的签名清晰地表明它处理行级表达式。

**示例（伪代码）：**

```python
# AggregateSemanticAnalyzer 内部逻辑示例
def _analyze_identifier(
    self, node: Identifier
) -> SemanticIdentifier[AggregateEvaluationContext, AggregateNodeExtensionContext]:
    resolved_id = self.context.find_identifier(node.name)
    if not resolved_id:
        raise SemanticError(f"标识符 '{node.name}' 未定义")
    
    is_group_key = node.name in self.context.evaluation_context.group_by_columns
    
    # 如果不在聚合前且不是分组键，则通常是错误的
    if not self.context.evaluation_context.pre_aggregation and not is_group_key:
         # 在聚合后（如 HAVING）直接引用非分组键通常不允许
         # （除非它被用在聚合函数内部，这会在分析 FuncCall 时处理）
         pass # 可能需要更复杂的逻辑来判断是否在聚合函数内
         
    ext_context = AggregateNodeExtensionContext(is_grouping_key=is_group_key)
    
    # ... 推断类型 logic ...
    inferred_type = resolved_id.struct # 简化示例
    
    return SemanticIdentifier(
        ..., 
        extension_context=ext_context, 
        inferred_type=inferred_type
    )
```



## 表达式语义转译 详细设计

表达式语义转译是将内部生成的、带有上下文信息的 Semantic Tree 转换为特定后端执行代码的过程。

```mermaid
classDiagram
    %% Generic Translator Interface %%
    class ExpressionTranslator~T_EvalContext, T_Result~ {
        <<interface>>
        +translate(node: ExpressionNode, context: T_EvalContext) -> T_Result
        #_build_semantic_context(context: T_EvalContext) -> SemanticContext
        #_create_semantic_analyzer(semantic_context: SemanticContext) -> SemanticAnalyzer
    }
    
    %% Concrete Translators (Example) %%
    class PandasRowLevelTranslator {
      <<implementation>>
      +translate(node: ExpressionNode, context: RowLevelEvaluationContext) -> Callable[[pd.DataFrame], Union[pd.Series, Any]]
      +register_function(func_name: str, implementation: Callable) -> None
    }
    class SqlExpressionTranslator {
      <<implementation>>
      +translate(node: ExpressionNode, context: AggregateEvaluationContext) -> str
      +register_sql_template(func_name: str, template: str, dialect: Optional[str]) -> None
    }
    ExpressionTranslator~RowLevelEvaluationContext, Callable~ <|-- PandasRowLevelTranslator
    ExpressionTranslator~AggregateEvaluationContext, str~ <|-- SqlExpressionTranslator

    %% Input/Output %%
    ExpressionNode --|> ExpressionTranslator : input
    T_EvalContext --|> ExpressionTranslator : input
    ExpressionTranslator --|> T_Result : output (backend specific)
```

上图展示了泛型化的表达式转译器接口 (`ExpressionTranslator`) 及其具体实现示例。转译器的设计侧重于用户视角，关注输入上下文类型和输出结果类型，而将内部语义分析过程隐藏起来。

### Python 接口设计

```python
# --- 转译器接口 (泛型) ---

class ExpressionTranslator(
    Generic[T_EvalContext, T_Result],
    ABC
):
    """
    泛型表达式转译器接口。
    T_EvalContext: 求值上下文类型（如RowLevelEvalContext）
    T_Result: 转译结果类型（如Callable[[pd.DataFrame], pd.Series]或str）
    """

    @abstractmethod
    def translate(
        self, 
        node: ExpressionNode, 
        context: T_EvalContext
    ) -> T_Result:
        """
        将表达式AST节点结合求值上下文转译为后端执行代码或对象。
        
        Args:
            node: 原始表达式AST节点。
            context: 特定求值上下文（如行级、聚合或窗口上下文）。
            
        Returns:
            T_Result类型的执行代码或可调用对象。
        """
        pass
```

这个接口设计有几个重要的特点：

1. 使用泛型参数 `T_EvalContext` 和 `T_Result`，明确表示转译器处理的上下文类型和产生的结果类型。
2. 简化的方法签名 `translate(node, context) -> result`，使调用者仅需关注输入和输出。
3. 内部实现细节（如语义分析、节点扩展信息等）被隐藏，不暴露给用户。

### 转译后端示例实现

#### Pandas行级转译器

```python
class PandasRowLevelTranslator(
    ExpressionTranslator[RowLevelEvaluationContext, Callable[[pd.DataFrame], Union[pd.Series, Any]]]
):
    """Pandas行级表达式转译器，将表达式转换为DataFrame上的可调用函数"""
    
    def __init__(self):
        """初始化转译器，内部管理函数实现表"""
        self._func_implementations: Dict[str, Callable] = {}
        self._init_default_functions()

    def register_function(self, func_name: str, implementation: Callable) -> None:
        """注册Pandas函数实现"""
        self._func_implementations[func_name] = implementation

    def translate(
        self, 
        node: ExpressionNode, 
        context: RowLevelEvaluationContext
    ) -> Callable[[pd.DataFrame], Union[pd.Series, Any]]:
        """
        将表达式转译为作用于DataFrame的闭包函数。
        
        返回的闭包函数封装了完整的DataFrame转换逻辑，
        调用者可以直接应用于pandas.DataFrame获取结果。
        """
        # 内部构建完整的语义上下文（包含标识符表和函数表）
        semantic_context = self._build_semantic_context(context)
        
        # 内部实例化行级语义分析器
        analyzer = self._create_semantic_analyzer(semantic_context)
        
        # 进行语义分析生成内部语义树
        semantic_tree = analyzer.analyze(node)
        
        # 从语义树生成可执行的闭包函数
        return self._generate_dataframe_callable(semantic_tree)

    def _build_semantic_context(
        self, 
        context: RowLevelEvaluationContext
    ) -> SemanticContext[RowLevelEvaluationContext, RowLevelNodeExtensionContext]:
        """内部构建完整的语义上下文"""
        # 实现略...
        pass

    def _create_semantic_analyzer(
        self, 
        context: SemanticContext[RowLevelEvaluationContext, RowLevelNodeExtensionContext]
    ) -> SemanticAnalyzer[RowLevelEvaluationContext, RowLevelNodeExtensionContext]:
        """创建内部使用的语义分析器"""
        # 实现略...
        pass

    def _generate_dataframe_callable(
        self, 
        semantic_tree: SemanticNode[RowLevelEvaluationContext, RowLevelNodeExtensionContext]
    ) -> Callable[[pd.DataFrame], Union[pd.Series, Any]]:
        """从语义树生成作用于DataFrame的闭包函数"""
        # 实现略...递归访问语义树，构建Pandas表达式
        
        # 返回接受DataFrame的闭包函数
        def expression_evaluator(df: pd.DataFrame) -> Union[pd.Series, Any]:
            # 实现略...
            pass
        
        return expression_evaluator

    def _init_default_functions(self) -> None:
        """初始化默认的函数实现"""
        # 加法实现示例
        self.register_function("add", lambda s1, s2: s1 + s2)
        # 更多函数...
```

#### SQL表达式转译器

```python
class SqlExpressionTranslator(
    ExpressionTranslator[AggregateEvaluationContext, str]
):
    """SQL表达式转译器，将表达式转换为SQL字符串片段"""
    
    def __init__(self, dialect: str = "standard"):
        """
        初始化SQL转译器
        
        Args:
            dialect: SQL方言，如"standard"、"mysql"、"postgresql"
        """
        self.dialect = dialect
        self._sql_templates: Dict[str, Dict[str, str]] = {
            "standard": {},
            "mysql": {},
            "postgresql": {}
        }
        self._init_dialect_templates()

    def register_sql_template(self, func_name: str, template: str, dialect: Optional[str] = None):
        """注册SQL函数模板"""
        target_dialect = dialect or self.dialect
        self._sql_templates[target_dialect][func_name] = template

    def translate(
        self, 
        node: ExpressionNode, 
        context: AggregateEvaluationContext
    ) -> str:
        """
        将表达式转译为SQL字符串片段
        
        Returns:
            SQL表达式片段，可直接嵌入到SQL查询中
        """
        # 内部构建完整的语义上下文
        semantic_context = self._build_semantic_context(context)
        
        # 内部实例化聚合语义分析器
        analyzer = self._create_semantic_analyzer(semantic_context)
        
        # 进行语义分析生成内部语义树
        semantic_tree = analyzer.analyze(node)
        
        # 从语义树生成SQL字符串
        return self._generate_sql_expression(semantic_tree)

    # 内部实现方法略...

    def _init_dialect_templates(self) -> None:
        """初始化各方言的SQL模板"""
        # 标准SQL
        standard_templates = {
            "add": "({0} + {1})",
            "multiply": "({0} * {1})",
            "equal": "({0} = {1})",
            "if": "CASE WHEN {0} THEN {1} ELSE {2} END",
            # 更多函数...
        }
        
        for name, template in standard_templates.items():
            self.register_sql_template(name, template, "standard")
        
        # 可以为特定方言添加不同的模板
        # MySQL特有模板
        mysql_templates = {
            "concat": "CONCAT({0}, {1})",
            # 更多函数...
        }
        
        for name, template in mysql_templates.items():
            self.register_sql_template(name, template, "mysql")
```

### 转译后端示例使用代码

以下示例展示了如何在实际应用中使用转译器：

```python
# Pandas行级表达式使用示例
def pandas_example():
    # 创建表达式: a + b * 2
    expr = FuncCall(
        type="FuncCall",
        name="add",
        args=[
            Identifier(type="Identifier", name="a"),
            FuncCall(
                type="FuncCall",
                name="multiply",
                args=[
                    Identifier(type="Identifier", name="b"),
                    Literal(type="Literal", val=2)
                ]
            )
        ]
    )
    
    # 创建行级评估上下文
    context = RowLevelEvaluationContext()
    
    # 创建转译器
    translator = PandasRowLevelTranslator()
    
    # 转译表达式为可调用函数
    result_func = translator.translate(expr, context)
    
    # 应用到DataFrame
    df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
    result = result_func(df)  # 返回 Series [9, 12, 15]
    
    return result


# SQL聚合表达式使用示例
def sql_example():
    # 创建表达式: SUM(price) > 1000
    expr = FuncCall(
        type="FuncCall",
        name="greater",
        args=[
            FuncCall(
                type="FuncCall",
                name="sum",
                args=[Identifier(type="Identifier", name="price")]
            ),
            Literal(type="Literal", val=1000)
        ]
    )
    
    # 创建聚合评估上下文
    context = AggregateEvaluationContext(
        group_by_columns=["category"],
        pre_aggregation=False
    )
    
    # 创建SQL转译器
    translator = SqlExpressionTranslator(dialect="postgresql")
    
    # 转译表达式为SQL片段
    sql_fragment = translator.translate(expr, context)
    # 结果: "(SUM(price) > 1000)"
    
    # 可以直接在SQL中使用
    complete_sql = f"""
    SELECT category 
    FROM products 
    GROUP BY category 
    HAVING {sql_fragment}
    """
    
    return complete_sql
```

这些例子展示了:

1. **强类型接口**: 转译器明确约定了输入类型和输出类型，便于IDE提示和静态类型检查。
2. **简洁的调用方式**: 用户只需提供表达式AST和上下文，无需关心内部语义分析细节。
3. **特定后端的结果使用方式**: Pandas后端返回的是可直接作用于DataFrame的函数，SQL后端返回的是可嵌入SQL查询的字符串。

这种设计使得转译器可以针对不同场景和后端提供最适合的结果类型，同时保持统一的接口模式。
