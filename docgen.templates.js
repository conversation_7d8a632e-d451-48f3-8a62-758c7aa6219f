const path = require('path');

/**
 * 过滤出带有 @public 标签的方法，并渲染它们。
 * @param {Array<import('vue-docgen-api').MethodDescriptor>} methods - 方法描述符数组。
 * @returns {string} 渲染后的 Markdown 字符串。
 */
function renderMethods(methods) {
  if (!methods || !methods.length) {
    return ''; // 如果没有方法，则不渲染任何内容。
  }

  // 过滤出带有 @access public 标签的方法
  const publicMethods = methods.filter(method => {
    return method.tags && method.tags.access && 
           method.tags.access.some(tag => tag.description === 'public');
  });

  if (!publicMethods.length) {
    return ''; // 如果没有公共方法，则不渲染任何内容。
  }

  let markdown = '## Methods\n\n';
  markdown += '| Name | Parameters | Description |\n';
  markdown += '| ---- | ---------- | ----------- |\n';

  publicMethods.forEach(method => {
    const name = method.name;
    // 格式化参数列表
    const parameters = method.params
      ? method.params.map(p => `${p.name}${p.type ? `: ${p.type.name}` : ''}`).join(', ')
      : '';
    const description = method.description || '';

    markdown += `| ${name} | ${parameters} | ${description} |\n`;
  });

  return markdown;
}

/**
 * 渲染属性 (Props)。
 * @param {Array<import('vue-docgen-api').PropDescriptor>} props - 属性描述符数组。
 * @returns {string} 渲染后的 Markdown 字符串。
 */
function renderProps(props) {
  if (!props.length) {
    return '';
  }
  let markdown = '## Props\n\n';
  markdown += '| Name | Type | Default | Description |\n';
  markdown += '| ---- | ---- | ------- | ----------- |\n';
  props.forEach(prop => {
    const name = prop.name;
    const type = prop.type ? prop.type.name : '';
    const defaultValue = prop.defaultValue ? `\`${prop.defaultValue.value}\`` : '';
    const description = prop.description || '';
    markdown += `| ${name} | ${type} | ${defaultValue} | ${description} |\n`;
  });
  return markdown;
}

/**
 * 渲染事件 (Events)。
 * @param {Array<import('vue-docgen-api').EventDescriptor>} events - 事件描述符数组。
 * @returns {string} 渲染后的 Markdown 字符串。
 */
function renderEvents(events) {
  if (!events.length) {
    return '';
  }
  let markdown = '## Events\n\n';
  markdown += '| Name | Description | Parameters |\n';
  markdown += '| ---- | ----------- | ---------- |\n';
  events.forEach(event => {
    const description = event.description || '';
    let parameters = '';
    if (event.properties && event.properties.length > 0) {
      parameters = event.properties.map(p => {
        const type = p.type ? `\`${p.type.name}\`` : '';
        const paramDescription = p.description ? `- ${p.description}` : '';
        return `${p.name}${type ? ` ${type}` : ''} ${paramDescription}`.trim();
      }).join('<br>');
    }
    markdown += `| \`${event.name}\` | ${description} | ${parameters} |\n`;
  });
  return markdown;
}

/**
 * 渲染插槽 (Slots)。
 * @param {Array<import('vue-docgen-api').SlotDescriptor>} slots - 插槽描述符数组。
 * @returns {string} 渲染后的 Markdown 字符串。
 */
function renderSlots(slots) {
  if (!slots.length) {
    return '';
  }
  let markdown = '## Slots\n\n';
  markdown += '| Name | Description |\n';
  markdown += '| ---- | ----------- |\n';
  slots.forEach(slot => {
    markdown += `| \`${slot.name}\` | ${slot.description || ''} |\n`;
  });
  return markdown;
}

/**
 * 渲染 provide/inject。
 * **注意：** `vue-docgen-api` 默认不直接从 Vue 组件的 Options API 中解析 `provide` 和 `inject` 选项的详细结构。
 * 如果要实现从 Options API 中自动提取 `provide` 和 `inject` 的详细信息 (包括其提供的具体属性和类型)，
 * 您需要在 `docgen.config.js` 的 `apiOptions` 中编写自定义的 `addScriptHandlers` 来遍历 AST 并收集这些数据。
 * 当前模板仅能处理通过 JSDoc 的 `@provide` 或 `@inject` 标签手动标注的简单描述性信息。
 *
 * @param {import('vue-docgen-api').ComponentDoc} doc - vue-docgen-api返回的组件文档对象。
 * @returns {string}
 */
function renderProvideInject(doc) {
  const provideTags = doc.tags && Array.isArray(doc.tags) && doc.tags.filter(tag => tag.title === 'provide');
  const injectTags = doc.tags && Array.isArray(doc.tags) && doc.tags.filter(tag => tag.title === 'inject');

  let markdown = '';
  if (provideTags && provideTags.length > 0) {
    markdown += '## Provided Properties\n\n';
    provideTags.forEach(tag => {
      // 假设 JSDoc 使用 @provide {Type} name - description 格式
      const description = tag.description || 'No description';
      markdown += `- ${description}\n`;
    });
  }
  if (injectTags && injectTags.length > 0) {
    markdown += '## Injected Properties\n\n';
    injectTags.forEach(tag => {
      // 假设 JSDoc 使用 @inject {Type} name - description 格式
      const description = tag.description || 'No description';
      markdown += `- ${description}\n`;
    });
  }
  return markdown;
}


/**
 * 渲染整个组件的模板。
 * @param {import('vue-docgen-api').RenderedUsage} renderedUsage - 渲染后的props, events, methods, slots文档。
 * @param {import('vue-docgen-api').ComponentDoc} doc - vue-docgen-api返回的组件文档对象。
 * @param {import('vue-docgen-cli').DocgenCLIConfig} config - 本地配置。
 * @param {string} fileName - 当前文档的文件名。
 * @returns {string} 渲染后的 Markdown 字符串
 */
function renderComponent(renderedUsage, doc, config, fileName) {
  const { displayName, description, docsBlocks } = doc;
  const provideInjectMarkdown = renderProvideInject(doc); // 渲染 provide/inject

  return `
# ${displayName}

${description ? '> ' + description : ''}

${renderedUsage.props}
${renderedUsage.methods}
${renderedUsage.events}
${renderedUsage.slots}
${provideInjectMarkdown}
${docsBlocks ? '---\n' + docsBlocks.join('\n---\n') : ''}
`;
}

module.exports = {
  methods: renderMethods,
  props: renderProps,
  events: renderEvents,
  slots: renderSlots,
  component: renderComponent,
  // 如果需要更精细的控制，也可以在这里添加其他模板
};