{"name": "deepinsight-vue-demo", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-dev": "eslint --fix --ext .js,.vue src && vue-cli-service build --mode development", "lint": "vue-cli-service lint", "docgen:vue": "vue-docgen", "docgen:js": "node -e \"const fs = require('fs'); const path = require('path'); const { execSync } = require('child_process'); const files = fs.readdirSync('src/services').filter(f => f.endsWith('.js')); files.forEach(file => { const basename = path.basename(file, '.js'); execSync(`documentation build src/services/${file} -f md -o docs/references/services/${basename}.md`); });\"", "docgen": "npm run docgen:vue && npm run docgen:js", "test:unit": "vue-cli-service test:unit", "test:e2e": "playwright test", "test": "npm run test:unit && npm run test:e2e", "postinstall": "npx playwright install"}, "dependencies": {"@antv/g6": "^3.8.5", "@antv/x6": "^2.18.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "ant-design-vue": "1.7.8", "axios": "^1.7.9", "dompurify": "^3.2.3", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "highlight.js": "^11.11.0", "js-yaml": "^4.1.0", "jsbn": "^1.1.0", "marked": "^15.0.4", "quill": "^2.0.2", "uuid": "^11.0.5", "vue": "^2.7.16", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vuedraggable": "^2.24.2", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@playwright/test": "^1.52.0", "@stagewise/toolbar": "^0.3.0", "@types/node": "^22.15.30", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^1.3.6", "@vue/vue2-jest": "^27.0.0", "babel-jest": "^27.5.1", "documentation": "^14.0.3", "eslint": "^7.32.0", "eslint-plugin-jsdoc": "^51.3.1", "eslint-plugin-vue": "^8.0.3", "less": "^4.2.2", "less-loader": "^12.2.0", "mark-down-my-docs": "^1.1.2", "sass": "^1.81.0", "sass-loader": "^16.0.3", "vue-docgen-api": "^4.79.2", "vue-docgen-cli": "^4.79.0", "vue-template-compiler": "^2.7.16"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/recommended", "eslint:recommended", "plugin:jsdoc/recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"jsdoc/require-description": "error", "jsdoc/require-param": "error", "jsdoc/require-param-type": "error", "jsdoc/require-returns": "error", "jsdoc/require-returns-type": "error", "jsdoc/check-types": "error", "jsdoc/require-property": "error", "jsdoc/require-property-type": "error", "jsdoc/require-jsdoc": ["error", {"checkConstructors": false, "checkGetters": false, "checkSetters": false, "checkEvents": true, "require": {"FunctionDeclaration": true, "MethodDefinition": true, "ClassDeclaration": true, "ArrowFunctionExpression": true}}], "jsdoc/require-example": "off", "jsdoc/empty-block-tag-name": "warn", "jsdoc/tag-lines": ["error", "any", {"startLines": 1}], "jsdoc/require-description-complete-sentence": "warn", "jsdoc/require-hyphen-before-param-description": "error", "jsdoc/check-param-names": ["error", {"checkDestructured": false, "allowExtraTrailingParamaters": true}]}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}