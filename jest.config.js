module.exports = {
  preset: '@vue/cli-plugin-unit-jest',
  testMatch: [
    '**/tests/**/*-simple.spec.[jt]s?(x)',
    '**/tests/**/*.spec.[jt]s?(x)',
    '**/__tests__/*.[jt]s?(x)'
  ],
  testPathIgnorePatterns: [
    '/tests/e2e/',
    '<rootDir>/tests/e2e/'
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  moduleFileExtensions: [
    'js',
    'json',
    'vue'
  ],
  transformIgnorePatterns: [
    'node_modules/(?!(uuid|lodash-es|@antv|axios)/)'
  ],
  setupFiles: [
    '<rootDir>/tests/setup.js'
  ]
}; 