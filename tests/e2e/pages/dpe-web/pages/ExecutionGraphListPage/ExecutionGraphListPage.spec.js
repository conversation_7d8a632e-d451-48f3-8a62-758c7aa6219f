/**
 * ExecutionGraphListPage组件端到端测试
 * @description 测试执行图列表页面的完整功能，包括搜索、分页、导入和创建会话等
 */
import { test, expect } from '@playwright/test';

test.describe('ExecutionGraphListPage页面测试', () => {
  // Mock数据
  const mockGraphs = [
    {
      id: 'graph-1',
      metadata: {
        displayName: '示例数据分析流程',
        description: '用于分析用户行为数据的流程',
        labels: {
          category: 'analysis',
          version: '1.0'
        }
      },
      nodes: {
        node1: { type: 'input' },
        node2: { type: 'transform' },
        node3: { type: 'output' }
      }
    },
    {
      id: 'graph-2',
      metadata: {
        displayName: '数据清洗流程',
        description: '清洗和标准化输入数据的流程',
        labels: {
          category: 'etl'
        }
      },
      nodes: {
        node1: { type: 'input' },
        node2: { type: 'clean' }
      }
    },
    {
      id: 'graph-3',
      metadata: {
        displayName: '机器学习训练流程',
        description: '训练和评估机器学习模型',
        labels: {
          category: 'ml',
          version: '2.0'
        }
      },
      nodes: {
        node1: { type: 'data' },
        node2: { type: 'train' },
        node3: { type: 'eval' },
        node4: { type: 'deploy' }
      }
    }
  ];

  const mockPageParam = {
    pageIndex: 0,
    limit: 10,
    recordTotal: 3,
    sortField: 'metadata.displayName',
    sortType: 'asc'
  };

  test.beforeEach(async ({ page }) => {
    // 启用控制台日志
    page.on('console', msg => {
      console.log(`控制台输出 [${msg.type()}]: ${msg.text()}`);
    });

    // 拦截执行图列表API - 使用正确的路径和方法
    await page.route('**/api/graphs/query**', async (route) => {
      if (route.request().method() === 'POST') {
        const requestBody = await route.request().postDataJSON();
        const filters = requestBody?.filters || {};
        const displayNamePattern = filters.displayNamePattern;
        
        let filteredGraphs = mockGraphs;
        if (displayNamePattern) {
          filteredGraphs = mockGraphs.filter(g => 
            g.metadata.displayName.includes(displayNamePattern)
          );
        }
        
        console.log(`Mock API被调用，返回${filteredGraphs.length}个执行图`);
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            data: filteredGraphs,
            pageParam: {
              ...mockPageParam,
              recordTotal: filteredGraphs.length
            }
          })
        });
      }
    });

    // 拦截导入执行图API
    await page.route('**/api/graphs/import**', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '导入成功'
          })
        });
      }
    });

    // 拦截后端信息API（用于CreateSessionDialog）
    await page.route('**/api/backends**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          backends: [
            {
              type: 'Local Backend',
              metadata: {
                displayName: 'Local Backend'
              },
              sessionConfigs: [
                {
                  key: 'maxWorkers',
                  schema: {
                    type: 'number',
                    title: '最大工作线程数',
                    default: 4,
                    required: true
                  }
                }
              ]
            }
          ]
        })
      });
    });

    // 拦截会话创建API
    await page.route('**/api/sessions/dag**', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'session-123',
          name: 'Test Session',
          status: 'CREATED',
          createdAt: new Date().toISOString()
        })
      });
    });

    // 导航到执行图列表页面
    await page.goto('/dpe-web/graphs');
  });

  test('页面应该正确加载和显示基本元素', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('[data-testid="page-title"]')).toContainText('执行图列表');
    
    // 验证页面描述
    await expect(page.locator('[data-testid="page-description"]')).toContainText('管理和运行所有执行图');
    
    // 验证导入按钮
    await expect(page.locator('[data-testid="import-graph-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="import-graph-btn"]')).toContainText('导入执行图');
    
    // 验证搜索区域
    await expect(page.locator('[data-testid="search-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="search-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="reset-btn"]')).toBeVisible();
    
    // 验证表格存在
    await expect(page.locator('[data-testid="graphs-table"]')).toBeVisible();
    
    // 截图验证整体页面布局
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-page-layout.png',
      fullPage: true 
    });
  });

  test('执行图列表数据应该正确显示', async ({ page }) => {
    // 等待表格加载完成，增加超时时间
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 验证表格行数
    const tableRows = page.locator('[data-testid="graphs-table"] .ant-table-tbody tr');
    await expect(tableRows).toHaveCount(3);
    
    // 验证第一个执行图的信息
    const firstRow = tableRows.first();
    await expect(firstRow.locator('[data-testid="graph-title"]')).toContainText('示例数据分析流程');
    await expect(firstRow.locator('[data-testid="graph-description"]')).toContainText('用于分析用户行为数据的流程');
    await expect(firstRow.locator('[data-testid="node-count"]')).toContainText('3');
    
    // 验证标签显示
    const firstRowLabels = firstRow.locator('[data-testid="graph-labels"] .graph-label');
    await expect(firstRowLabels).toHaveCount(2);
    
    // 验证操作按钮
    await expect(firstRow.locator('[data-testid="create-session-btn"]')).toBeVisible();
    await expect(firstRow.locator('[data-testid="view-detail-btn"]')).toBeVisible();
    
    // 截图验证表格数据显示
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-table-data.png' 
    });
  });

  test('搜索功能应该工作正常', async ({ page }) => {
    // 先等待初始数据加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 输入搜索关键词
    await page.fill('[data-testid="search-input"]', '数据清洗');
    
    // 点击搜索按钮
    await page.click('[data-testid="search-btn"]');
    
    // 等待搜索结果
    await page.waitForTimeout(2000);
    
    // 验证只显示匹配的结果
    const tableRows = page.locator('[data-testid="graphs-table"] .ant-table-tbody tr');
    await expect(tableRows).toHaveCount(1);
    
    // 验证搜索结果正确
    await expect(tableRows.first().locator('[data-testid="graph-title"]')).toContainText('数据清洗流程');
    
    // 截图验证搜索结果
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-search-results.png' 
    });
  });

  test('重置功能应该工作正常', async ({ page }) => {
    // 先等待初始数据加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 先进行搜索
    await page.fill('[data-testid="search-input"]', '数据清洗');
    await page.click('[data-testid="search-btn"]');
    await page.waitForTimeout(2000);
    
    // 点击重置按钮
    await page.click('[data-testid="reset-btn"]');
    
    // 验证搜索框被清空
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('');
    
    // 等待数据重新加载
    await page.waitForTimeout(2000);
    
    // 验证显示所有数据
    const tableRows = page.locator('[data-testid="graphs-table"] .ant-table-tbody tr');
    await expect(tableRows).toHaveCount(3);
    
    // 截图验证重置结果
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-reset.png' 
    });
  });

  test('Enter键搜索功能应该工作正常', async ({ page }) => {
    // 先等待初始数据加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 输入搜索关键词
    await page.fill('[data-testid="search-input"]', '机器学习');
    
    // 按Enter键搜索
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // 等待搜索结果
    await page.waitForTimeout(2000);
    
    // 验证搜索结果
    const tableRows = page.locator('[data-testid="graphs-table"] .ant-table-tbody tr');
    await expect(tableRows).toHaveCount(1);
    await expect(tableRows.first().locator('[data-testid="graph-title"]')).toContainText('机器学习训练流程');
  });

  test('导入执行图功能应该工作正常', async ({ page }) => {
    // 创建文件选择器事件监听
    const fileChooserPromise = page.waitForEvent('filechooser');
    
    // 点击导入按钮
    await page.click('[data-testid="import-graph-btn"]');
    
    const fileChooser = await fileChooserPromise;
    
    // 验证文件选择器被触发
    expect(fileChooser).toBeTruthy();
    
    // 验证文件输入元素存在
    await expect(page.locator('[data-testid="file-input"]')).toBeAttached();
    
    // 截图验证导入功能触发
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-import.png' 
    });
  });

  test('创建会话功能应该工作正常', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 点击第一行的创建会话按钮
    await page.click('[data-testid="graphs-table"] .ant-table-tbody tr:first-child [data-testid="create-session-btn"]');
    
    // 等待Vue响应式更新完成
    await page.waitForTimeout(1000);
    
    // 验证创建会话对话框是否打开
    await expect(page.locator('.ant-modal')).toBeVisible();
    await expect(page.locator('.ant-modal-title')).toContainText('创建会话');
    
    // 截图验证创建会话对话框
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-create-session.png' 
    });
    
    // 关闭对话框
    await page.click('.ant-modal-close');
    await page.waitForTimeout(500); // 等待关闭动画
    await expect(page.locator('.ant-modal')).not.toBeVisible();
  });

  test('查看详情功能应该工作正常', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 点击第一行的查看详情按钮
    await page.click('[data-testid="graphs-table"] .ant-table-tbody tr:first-child [data-testid="view-detail-btn"]');
    
    // 验证提示消息（当前是placeholder功能）
    await expect(page.locator('.ant-message')).toBeVisible();
    
    // 截图验证查看详情功能
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-view-detail.png' 
    });
  });

  test('分页功能应该正确显示', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 验证分页组件存在
    await expect(page.locator('.ant-pagination')).toBeVisible();
    
    // 验证分页信息显示
    await expect(page.locator('.ant-pagination-total-text')).toContainText('共 3 条');
    
    // 验证页码显示
    await expect(page.locator('.ant-pagination-item-active')).toContainText('1');
    
    // 截图验证分页组件
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-pagination.png' 
    });
  });

  test('表格列标题应该正确显示', async ({ page }) => {
    // 验证表格列标题
    const tableHeaders = page.locator('[data-testid="graphs-table"] .ant-table-thead th');
    
    await expect(tableHeaders.nth(0)).toContainText('执行图名称');
    await expect(tableHeaders.nth(1)).toContainText('节点数量');
    await expect(tableHeaders.nth(2)).toContainText('标签');
    await expect(tableHeaders.nth(3)).toContainText('操作');
    
    // 截图验证表格头部
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-table-headers.png' 
    });
  });

  test('空搜索结果应该正确处理', async ({ page }) => {
    // 先等待初始数据加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 搜索不存在的内容
    await page.fill('[data-testid="search-input"]', '不存在的执行图');
    await page.click('[data-testid="search-btn"]');
    
    // 等待搜索完成
    await page.waitForTimeout(2000);
    
    // 验证无数据状态
    await expect(page.locator('.ant-empty')).toBeVisible();
    
    // 截图验证空结果状态
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-empty-results.png' 
    });
  });

  test('页面整体设计应该符合设计图', async ({ page }) => {
    // 等待页面完全加载
    await page.waitForSelector('[data-testid="graphs-table"] .ant-table-tbody tr', { timeout: 10000 });
    
    // 对整个页面进行截图，用于与设计图对比
    await page.screenshot({ 
      path: 'test-results/screenshots/execution-graph-list-page-full.png',
      fullPage: true 
    });
    
    // 验证关键布局元素的位置和样式
    await expect(page.locator('.page-header')).toBeVisible();
    await expect(page.locator('.search-section')).toBeVisible();
    await expect(page.locator('.list-section')).toBeVisible();
    
    // 验证页面标题样式
    const pageTitle = page.locator('[data-testid="page-title"]');
    await expect(pageTitle).toHaveCSS('font-size', '24px');
    await expect(pageTitle).toHaveCSS('font-weight', '600');
  });
});
