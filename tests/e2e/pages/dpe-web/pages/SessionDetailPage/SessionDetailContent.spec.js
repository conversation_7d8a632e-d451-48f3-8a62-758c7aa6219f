/**
 * SessionDetailContent 组件端到端测试
 * @description 测试 SessionDetailContent 组件的完整功能，包括图编辑、节点配置、任务执行等
 * 
 * 测试覆盖的用例：
 * - UC-SDP-1, UC-SDP-2: 页面加载与初始化
 * - UC-SDP-3: 算子拖拽到画布创建节点
 * - UC-SDP-4: 通过拖拽创建边连接
 * - UC-SDP-5: 删除节点和边
 * - UC-SDP-6: 点击节点显示详情
 * - UC-SDP-7: 修改节点配置
 * - UC-SDP-8: 保存配置
 * - UC-SDP-9: 运行单个节点
 * - UC-SDP-11: 运行整个执行图
 * - UC-SDP-12, UC-SDP-13, UC-SDP-14: 工具栏操作
 */

import { test, expect } from '@playwright/test';

test.describe('SessionDetailContent 组件测试', () => {
  // 测试页面URL
  const TEST_URL = '/test/session-detail-content';

  test.beforeEach(async ({ page }) => {
    // 监听和记录页面的控制台输出，方便调试
    page.on('console', msg => {
      console.log(`浏览器控制台[${msg.type()}]:`, msg.text());
    });

    // 导航到测试页面
    await page.goto(TEST_URL);
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="session-detail-content-test"]');
    await page.waitForSelector('[data-testid="session-detail-content"]');
    
    // 等待组件初始化完成
    await page.waitForTimeout(1000);
  });

  test.describe('页面初始化测试 (UC-SDP-1, UC-SDP-2)', () => {
    test('应该正确加载并显示页面基本元素', async ({ page }) => {
      // 验证测试页面标题
      await expect(page.locator('[data-testid="test-header"] h1')).toHaveText('SessionDetailContent 组件测试页面');
      
      // 验证会话信息显示
      const sessionCard = page.locator('[data-testid="test-status"] .ant-card').first();
      await expect(sessionCard.locator('.ant-card-head-title')).toHaveText('会话信息');
      await expect(sessionCard.locator('p').first()).toContainText('test-session-');
      
      // 验证算子信息显示
      const operatorCard = page.locator('[data-testid="test-status"] .ant-card').nth(1);
      await expect(operatorCard.locator('.ant-card-head-title')).toHaveText('算子信息');
      await expect(operatorCard.locator('p').first()).toHaveText('可用算子数量: 2');
    });

    test('应该正确初始化三个核心组件', async ({ page }) => {
      // 验证 SessionToolbar 组件存在
      await expect(page.locator('[data-testid="session-toolbar"]')).toBeVisible();
      
      // 验证 OperatorPanel 组件存在
      await expect(page.locator('[data-testid="operator-panel"]')).toBeVisible();
      
      // 验证 GraphEditor 组件存在
      await expect(page.locator('[data-testid="graph-editor"]')).toBeVisible();
      
      // 验证算子列表显示
      await expect(page.locator('[data-testid="operator-item"]').first()).toBeVisible();
      
      // 验证算子名称正确显示
      const firstOperator = page.locator('[data-testid="operator-item"]').first();
      await expect(firstOperator).toContainText('从MySQL读取结构化数据资源');
    });

    test('应该显示正确的会话状态和工具栏信息', async ({ page }) => {
      // 验证工具栏中的会话信息
      const sessionInfo = page.locator('[data-testid="session-toolbar"] .session-info');
      await expect(sessionInfo.locator('.session-title')).toContainText('测试会话');
      
      // 验证图状态信息
      const graphInfo = page.locator('[data-testid="session-toolbar"] .graph-info');
      await expect(graphInfo.locator('.graph-stats').first()).toContainText('节点: 0');
      await expect(graphInfo.locator('.graph-stats').nth(1)).toContainText('连接: 0');
      
      // 验证操作按钮状态
      await expect(page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '保存' })).toBeEnabled();
      await expect(page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '运行' })).toBeDisabled();
    });
  });

  test.describe('算子拖拽和节点创建测试 (UC-SDP-3)', () => {
    test('应该能够从算子面板拖拽算子到画布创建节点', async ({ page }) => {
      // 获取第一个算子（MySQL读取算子）
      const firstOperator = page.locator('[data-testid="operator-item"]').first();
      await expect(firstOperator).toBeVisible();
      
      // 获取画布区域
      const graphEditor = page.locator('[data-testid="graph-editor"]');
      await expect(graphEditor).toBeVisible();
      
      // 执行拖拽操作：从算子拖拽到画布中心
      const operatorBox = await firstOperator.boundingBox();
      const graphBox = await graphEditor.boundingBox();
      
      await page.mouse.move(operatorBox.x + operatorBox.width / 2, operatorBox.y + operatorBox.height / 2);
      await page.mouse.down();
      await page.mouse.move(graphBox.x + graphBox.width / 2, graphBox.y + graphBox.height / 2);
      await page.mouse.up();
      
      // 等待节点创建完成（包括后端同步）
      await page.waitForTimeout(2000);
      
      // 验证节点已创建（使用通用的节点选择器）
      await expect(page.locator('.graph-editor .node, .graph-editor [class*="node"], .graph-editor .graph-node').first()).toBeVisible({ timeout: 10000 });
      
      // 验证右侧节点详情面板打开
      await expect(page.locator('[data-testid="node-detail-panel"]')).toBeVisible({ timeout: 5000 });
      
      // 验证图状态更新
      const graphInfo = page.locator('[data-testid="session-toolbar"] .graph-info');
      await expect(graphInfo.locator('.graph-stats').first()).toContainText('节点: 1');
    });

    test('应该能够创建多个不同类型的节点', async ({ page }) => {
      // 创建第一个节点（MySQL读取算子）
      const mysqlOperator = page.locator('[data-testid="operator-item"]').first();
      const graphEditor = page.locator('[data-testid="graph-editor"]');
      
      const mysqlBox = await mysqlOperator.boundingBox();
      const graphBox = await graphEditor.boundingBox();
      
      // 拖拽第一个算子到画布左侧
      await page.mouse.move(mysqlBox.x + mysqlBox.width / 2, mysqlBox.y + mysqlBox.height / 2);
      await page.mouse.down();
      await page.mouse.move(graphBox.x + graphBox.width / 3, graphBox.y + graphBox.height / 2);
      await page.mouse.up();
      
      await page.waitForTimeout(2000);
      
      // 创建第二个节点（过滤算子）
      const filterOperator = page.locator('[data-testid="operator-item"]').nth(1);
      const filterBox = await filterOperator.boundingBox();
      
      // 拖拽第二个算子到画布右侧
      await page.mouse.move(filterBox.x + filterBox.width / 2, filterBox.y + filterBox.height / 2);
      await page.mouse.down();
      await page.mouse.move(graphBox.x + (graphBox.width * 2) / 3, graphBox.y + graphBox.height / 2);
      await page.mouse.up();
      
      await page.waitForTimeout(2000);
      
      // 验证两个节点都已创建
      await expect(page.locator('.graph-editor .node, .graph-editor [class*="node"], .graph-editor .graph-node')).toHaveCount(2);
      
      // 验证图状态更新
      const graphInfo = page.locator('[data-testid="session-toolbar"] .graph-info');
      await expect(graphInfo.locator('.graph-stats').first()).toContainText('节点: 2');
    });
  });

  test.describe('边连接创建测试 (UC-SDP-4)', () => {
    test('应该能够通过拖拽连接两个节点', async ({ page }) => {
      // 先创建两个节点
      await createTestNodes(page);
      
      // 获取两个节点
      const nodes = page.locator('[data-testid="graph-node"]');
      await expect(nodes).toHaveCount(2);
      
      const firstNode = nodes.first();
      const secondNode = nodes.nth(1);
      
      // 获取第一个节点的输出端口
      const firstNodeBox = await firstNode.boundingBox();
      const secondNodeBox = await secondNode.boundingBox();
      
      // 从第一个节点的右侧拖拽到第二个节点的左侧
      await page.mouse.move(firstNodeBox.x + firstNodeBox.width - 10, firstNodeBox.y + firstNodeBox.height / 2);
      await page.mouse.down();
      await page.mouse.move(secondNodeBox.x + 10, secondNodeBox.y + secondNodeBox.height / 2);
      await page.mouse.up();
      
      await page.waitForTimeout(500);
      
      // 验证边已创建
      await expect(page.locator('[data-testid="graph-edge"]').first()).toBeVisible();
      
      // 验证图状态更新
      const graphInfo = page.locator('[data-testid="session-toolbar"] .graph-info');
      await expect(graphInfo.locator('.graph-stats').nth(1)).toContainText('连接: 1');
    });
  });

  test.describe('节点选择和详情显示测试 (UC-SDP-6)', () => {
    test('应该能够点击节点并显示节点详情', async ({ page }) => {
      // 创建一个测试节点
      await createTestNodes(page, 1);
      
      // 点击节点
      const node = page.locator('[data-testid="graph-node"]').first();
      await node.click();
      
      // 验证节点详情面板显示
      await expect(page.locator('[data-testid="node-detail-panel"]')).toBeVisible();
      
      // 验证标签页结构
      await expect(page.locator('[data-testid="node-detail-tabs"] .ant-tabs-tab').filter({ hasText: '配置' })).toBeVisible();
      await expect(page.locator('[data-testid="node-detail-tabs"] .ant-tabs-tab').filter({ hasText: '任务' })).toBeVisible();
      await expect(page.locator('[data-testid="node-detail-tabs"] .ant-tabs-tab').filter({ hasText: '数据' })).toBeVisible();
      
      // 验证节点配置面板显示
      await expect(page.locator('[data-testid="node-config-panel"]')).toBeVisible();
    });

    test('应该能够在不同标签页之间切换', async ({ page }) => {
      // 创建一个测试节点并选中
      await createTestNodes(page, 1);
      await page.locator('[data-testid="graph-node"]').first().click();
      
      // 默认应该在配置标签页
      await expect(page.locator('[data-testid="node-config-panel"]')).toBeVisible();
      
      // 切换到任务标签页
      await page.locator('[data-testid="node-detail-tabs"] .ant-tabs-tab').filter({ hasText: '任务' }).click();
      await expect(page.locator('[data-testid="node-task-info-panel"]')).toBeVisible();
      
      // 切换到数据标签页
      await page.locator('[data-testid="node-detail-tabs"] .ant-tabs-tab').filter({ hasText: '数据' }).click();
      await expect(page.locator('[data-testid="node-data-panel"]')).toBeVisible();
      
      // 切换回配置标签页
      await page.locator('[data-testid="node-detail-tabs"] .ant-tabs-tab').filter({ hasText: '配置' }).click();
      await expect(page.locator('[data-testid="node-config-panel"]')).toBeVisible();
    });
  });

  test.describe('节点配置编辑测试 (UC-SDP-7)', () => {
    test('应该能够修改节点的基本配置', async ({ page }) => {
      // 创建测试节点并选中
      await createTestNodes(page, 1);
      await page.locator('[data-testid="graph-node"]').first().click();
      
      // 确保在配置标签页
      await expect(page.locator('[data-testid="node-config-panel"]')).toBeVisible();
      
      // 修改节点名称
      const nameInput = page.locator('[data-testid="node-name-input"]');
      await expect(nameInput).toBeVisible();
      await nameInput.clear();
      await nameInput.fill('修改后的节点名称');
      
      // 修改节点描述
      const descInput = page.locator('[data-testid="node-description-input"]');
      if (await descInput.isVisible()) {
        await descInput.clear();
        await descInput.fill('这是修改后的节点描述');
      }
      
      // 验证输入值已更改
      await expect(nameInput).toHaveValue('修改后的节点名称');
    });

    test('应该能够配置算子特定的参数', async ({ page }) => {
      // 创建 MySQL 读取算子节点并选中
      await createTestNodes(page, 1);
      await page.locator('[data-testid="graph-node"]').first().click();
      
      // 等待算子配置表单加载
      await page.waitForSelector('[data-testid="operator-config-form"]');
      
      // 查找并配置数据库连接参数（根据算子配置动态生成）
      const hostInput = page.locator('[data-testid="config-field-db_options"] input[placeholder*="host"]').first();
      if (await hostInput.isVisible()) {
        await hostInput.fill('localhost');
      }
      
      const portInput = page.locator('[data-testid="config-field-db_options"] input[placeholder*="port"]').first();
      if (await portInput.isVisible()) {
        await portInput.fill('3306');
      }
      
      const tableInput = page.locator('[data-testid="config-field-table_name"] input').first();
      if (await tableInput.isVisible()) {
        await tableInput.fill('test_table');
      }
    });
  });

  test.describe('配置保存测试 (UC-SDP-8)', () => {
    test('应该能够保存节点配置', async ({ page }) => {
      // 创建测试节点并配置
      await createTestNodes(page, 1);
      await page.locator('[data-testid="graph-node"]').first().click();
      
      // 修改节点名称
      const nameInput = page.locator('[data-testid="node-name-input"]');
      await nameInput.clear();
      await nameInput.fill('已保存的节点');
      
      // 点击保存按钮
      const saveButton = page.locator('[data-testid="node-save-button"]');
      await expect(saveButton).toBeVisible();
      await saveButton.click();
      
      // 等待保存完成
      await page.waitForTimeout(1000);
      
      // 验证保存成功消息
      await expect(page.locator('.ant-message')).toContainText('保存成功');
    });

    test('应该能够保存整个执行图', async ({ page }) => {
      // 创建一些节点
      await createTestNodes(page, 2);
      
      // 点击工具栏保存按钮
      const saveButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '保存' });
      await expect(saveButton).toBeEnabled();
      await saveButton.click();
      
      // 等待保存完成
      await page.waitForTimeout(1000);
      
      // 验证保存成功
      await expect(page.locator('.ant-message')).toContainText('保存成功');
    });
  });

  test.describe('任务运行测试 (UC-SDP-9, UC-SDP-11)', () => {
    test('应该能够运行单个节点', async ({ page }) => {
      // 创建并配置节点
      await createTestNodes(page, 1);
      await page.locator('[data-testid="graph-node"]').first().click();
      
      // 配置节点基本参数
      const nameInput = page.locator('[data-testid="node-name-input"]');
      await nameInput.clear();
      await nameInput.fill('可运行节点');
      
      // 点击节点运行按钮
      const runButton = page.locator('[data-testid="node-run-button"]');
      if (await runButton.isVisible()) {
        await runButton.click();
        
        // 等待任务提交
        await page.waitForTimeout(1000);
        
        // 应该自动切换到任务标签页
        await expect(page.locator('[data-testid="node-task-info-panel"]')).toBeVisible();
        
        // 验证任务状态显示
        await expect(page.locator('[data-testid="task-status"]')).toBeVisible();
      }
    });

    test('应该能够运行整个执行图', async ({ page }) => {
      // 创建多个节点
      await createTestNodes(page, 2);
      
      // 点击工具栏运行按钮
      const runButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '运行' });
      
      // 运行按钮应该在有节点时可用
      await expect(runButton).toBeEnabled();
      await runButton.click();
      
      // 等待运行开始
      await page.waitForTimeout(1000);
      
      // 验证运行状态反馈
      await expect(page.locator('.ant-message')).toContainText('运行');
    });
  });

  test.describe('工具栏操作测试 (UC-SDP-12, UC-SDP-13, UC-SDP-14)', () => {
    test('应该能够打开图设置对话框', async ({ page }) => {
      // 点击图设置按钮
      const settingsButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '图设置' });
      await expect(settingsButton).toBeVisible();
      await settingsButton.click();
      
      // 验证设置对话框打开
      await expect(page.locator('[data-testid="graph-settings-modal"]')).toBeVisible();
    });

    test('应该能够导出执行图', async ({ page }) => {
      // 创建一些测试数据
      await createTestNodes(page, 2);
      
      // 点击导出按钮
      const exportButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '导出' });
      await expect(exportButton).toBeEnabled();
      await exportButton.click();
      
      // 验证导出操作触发
      await page.waitForTimeout(500);
    });

    test('应该能够查看任务列表', async ({ page }) => {
      // 点击任务列表按钮
      const taskListButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '任务列表' });
      await expect(taskListButton).toBeVisible();
      await taskListButton.click();
      
      // 验证任务列表页面或对话框打开
      await page.waitForTimeout(500);
    });
  });

  test.describe('删除操作测试 (UC-SDP-5)', () => {
    test('应该能够删除选中的节点', async ({ page }) => {
      // 创建测试节点
      await createTestNodes(page, 2);
      
      // 选中第一个节点
      const firstNode = page.locator('[data-testid="graph-node"]').first();
      await firstNode.click();
      
      // 按 Delete 键删除
      await page.keyboard.press('Delete');
      
      // 等待删除完成
      await page.waitForTimeout(500);
      
      // 验证节点已删除
      await expect(page.locator('[data-testid="graph-node"]')).toHaveCount(1);
      
      // 验证图状态更新
      const graphInfo = page.locator('[data-testid="session-toolbar"] .graph-info');
      await expect(graphInfo.locator('.graph-stats').first()).toContainText('节点: 1');
    });

    test('应该能够删除选中的边', async ({ page }) => {
      // 创建两个节点并连接
      await createTestNodes(page, 2);
      await createTestEdge(page);
      
      // 选中边
      const edge = page.locator('[data-testid="graph-edge"]').first();
      await edge.click();
      
      // 按 Delete 键删除
      await page.keyboard.press('Delete');
      
      // 等待删除完成
      await page.waitForTimeout(500);
      
      // 验证边已删除
      await expect(page.locator('[data-testid="graph-edge"]')).toHaveCount(0);
      
      // 验证图状态更新
      const graphInfo = page.locator('[data-testid="session-toolbar"] .graph-info');
      await expect(graphInfo.locator('.graph-stats').nth(1)).toContainText('连接: 0');
    });
  });

  test.describe('状态控制测试', () => {
    test('应该正确响应 canSave 和 canRun 状态变化', async ({ page }) => {
      // 测试初始状态
      let saveButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '保存' });
      await expect(saveButton).toBeEnabled();
      
      // 切换 canSave 状态为 false
      await page.locator('[data-testid="toggle-can-save"]').click();
      await expect(saveButton).toBeDisabled();
      
      // 切换回 true
      await page.locator('[data-testid="toggle-can-save"]').click();
      await expect(saveButton).toBeEnabled();
      
      // 创建节点后运行按钮应该可用
      await createTestNodes(page, 1);
      let runButton = page.locator('[data-testid="session-toolbar"] .ant-btn').filter({ hasText: '运行' });
      await expect(runButton).toBeEnabled();
      
      // 切换 canRun 状态为 false
      await page.locator('[data-testid="toggle-can-run"]').click();
      await expect(runButton).toBeDisabled();
    });
  });

  // 辅助函数：创建测试节点
  async function createTestNodes(page, count = 2) {
    const graphEditor = page.locator('[data-testid="graph-editor"]');
    const graphBox = await graphEditor.boundingBox();
    
    for (let i = 0; i < count; i++) {
      const operator = page.locator('[data-testid="operator-item"]').nth(i % 2);
      const operatorBox = await operator.boundingBox();
      
      // 拖拽算子到画布的不同位置
      const targetX = graphBox.x + (graphBox.width / (count + 1)) * (i + 1);
      const targetY = graphBox.y + graphBox.height / 2;
      
      await page.mouse.move(operatorBox.x + operatorBox.width / 2, operatorBox.y + operatorBox.height / 2);
      await page.mouse.down();
      await page.mouse.move(targetX, targetY);
      await page.mouse.up();
      
      await page.waitForTimeout(500);
    }
  }

  // 辅助函数：创建测试边
  async function createTestEdge(page) {
    const nodes = page.locator('[data-testid="graph-node"]');
    const firstNode = nodes.first();
    const secondNode = nodes.nth(1);
    
    const firstNodeBox = await firstNode.boundingBox();
    const secondNodeBox = await secondNode.boundingBox();
    
    // 从第一个节点拖拽到第二个节点
    await page.mouse.move(firstNodeBox.x + firstNodeBox.width - 10, firstNodeBox.y + firstNodeBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(secondNodeBox.x + 10, secondNodeBox.y + secondNodeBox.height / 2);
    await page.mouse.up();
    
    await page.waitForTimeout(500);
  }
});
