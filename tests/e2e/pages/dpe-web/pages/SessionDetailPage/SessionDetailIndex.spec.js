/**
 * SessionDetailIndex 组件端到端测试
 * @description 测试SessionDetailIndex页面的初始化流程、错误处理和用户交互
 */
import { test, expect } from '@playwright/test';

// 模拟数据
const mockSessionInfo = {
  id: 'test-session-001',
  backendType: 'duckdb',
  state: 'RUNNING',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  metadata: {
    displayName: '测试会话',
    description: '用于测试的模拟会话'
  }
};

const mockOperators = [
  {
    type: 'data_source',
    metadata: {
      displayName: '数据源',
      description: '从文件或数据库读取数据'
    },
    opConfigMetadata: [
      {
        key: 'path',
        schema: { type: 'string', title: '数据路径' }
      }
    ],
    inputFields: []
  },
  {
    type: 'filter',
    metadata: {
      displayName: '过滤器',
      description: '根据条件过滤数据'
    },
    opConfigMetadata: [
      {
        key: 'condition',
        schema: { type: 'string', title: '过滤条件' }
      }
    ],
    inputFields: ['input']
  }
];

const mockExecutionGraphClient = {
  isConnected: true,
  close: () => Promise.resolve(),
  get_edit_status: () => Promise.resolve({
    currentGraph: {
      id: 'test-graph-001',
      metadata: { displayName: '测试图', description: '测试执行图' },
      nodes: {},
      edges: {}
    },
    hasUnsavedChanges: false
  }),
  export_graph: () => Promise.resolve({
    id: 'test-graph-001',
    metadata: { displayName: '测试图', description: '测试执行图' },
    nodes: {},
    edges: {}
  })
};

test.describe('SessionDetailIndex 组件测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 设置默认的成功响应
    await setupDefaultMockResponses(page);
    
    // Mock WebSocket连接 - 防止ExecutionGraphClient连接失败
    await page.addInitScript(() => {
      // 保存原始WebSocket
      window.originalWebSocket = window.WebSocket;
      
      // Mock WebSocket
      window.WebSocket = class MockWebSocket extends EventTarget {
        constructor(url) {
          super();
          this.url = url;
          this.readyState = WebSocket.CONNECTING;
          
          // 立即模拟连接成功 - 不使用setTimeout避免异步问题
          this.readyState = WebSocket.OPEN;
          
          // 使用setImmediate或nextTick模拟异步但立即执行
          if (typeof setImmediate !== 'undefined') {
            setImmediate(() => {
              this.dispatchEvent(new Event('open'));
            });
          } else {
            Promise.resolve().then(() => {
              this.dispatchEvent(new Event('open'));
            });
          }
        }
        
        send(data) {
          // 模拟消息响应
          try {
            const message = JSON.parse(data);
            const response = {
              message_id: message.message_id,
              success: true,
              result: this._getDefaultResponse(message.action)
            };
            
            setTimeout(() => {
              this.dispatchEvent(new MessageEvent('message', {
                data: JSON.stringify(response)
              }));
            }, 10);
          } catch (error) {
            console.warn('Mock WebSocket send error:', error);
          }
        }
        
        close() {
          this.readyState = WebSocket.CLOSED;
          this.dispatchEvent(new Event('close'));
        }
        
        _getDefaultResponse(action) {
          switch (action) {
            case 'get_edit_status':
              return {
                currentGraph: {
                  id: 'test-graph-001',
                  metadata: { displayName: '测试图', description: '测试执行图' },
                  nodes: {},
                  edges: {}
                },
                hasUnsavedChanges: false
              };
            case 'export_graph':
              return {
                id: 'test-graph-001',
                metadata: { displayName: '测试图', description: '测试执行图' },
                nodes: {},
                edges: {}
              };
            default:
              return {};
          }
        }
      };
      
      // 复制静态属性
      window.WebSocket.CONNECTING = 0;
      window.WebSocket.OPEN = 1;
      window.WebSocket.CLOSING = 2;
      window.WebSocket.CLOSED = 3;
    });
  });

  test('应该正确处理正常的初始化流程', async ({ page }) => {
    // 导航到SessionDetailIndex页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 验证加载状态显示
    await expect(page.locator('[data-testid="loading-container"]')).toBeVisible();
    await expect(page.locator('text=正在加载会话数据...')).toBeVisible();

    // 等待初始化完成，SessionDetailContent应该显示
    await expect(page.locator('[data-testid="session-detail-content"]')).toBeVisible({ timeout: 15000 });
    
    // 验证加载状态消失
    await expect(page.locator('[data-testid="loading-container"]')).not.toBeVisible();
    
    // 验证错误状态不显示
    await expect(page.locator('[data-testid="error-container"]')).not.toBeVisible();
  });

  test('应该处理缺失sessionId参数的情况', async ({ page }) => {
    // 监听路由变化
    let redirectedUrl = null;
    page.on('framenavigated', (frame) => {
      if (frame === page.mainFrame()) {
        redirectedUrl = frame.url();
      }
    });

    // 导航到不带sessionId的路径
    await page.goto('/dpe-web/sessions/detail/');

    // 验证跳转到会话列表页
    await page.waitForURL('**/dpe-web/sessions', { timeout: 5000 });
    expect(page.url()).toContain('/dpe-web/sessions');
  });

  test('应该正确处理会话信息加载失败', async ({ page }) => {
    // Mock会话信息加载失败
    await page.route('**/api/sessions/test-session-001', async (route) => {
      await route.fulfill({
        status: 404,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Session not found',
          message: '会话不存在'
        })
      });
    });

    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待错误状态显示
    await expect(page.locator('[data-testid="error-container"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=页面初始化失败')).toBeVisible();
    await expect(page.locator('text=加载会话信息失败')).toBeVisible();
    
    // 验证重试和返回按钮存在
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    await expect(page.locator('[data-testid="back-to-list-button"]')).toBeVisible();
    
    // 验证SessionDetailContent不显示
    await expect(page.locator('[data-testid="session-detail-content"]')).not.toBeVisible();
  });

  test('应该正确处理ExecutionGraphClient初始化失败', async ({ page }) => {
    // ExecutionGraphClient现在不依赖HTTP API，而是直接连接WebSocket
    // 在测试环境中，WebSocket连接会失败，但组件应该继续加载其他功能
    
    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待SessionDetailContent显示（即使ExecutionGraphClient失败）
    await expect(page.locator('[data-testid="session-detail-content"]')).toBeVisible({ timeout: 10000 });
    
    // 验证组件能正常渲染，不会因为ExecutionGraphClient失败而崩溃
    await expect(page.locator('[data-testid="error-container"]')).not.toBeVisible();
  });

  test('应该正确处理算子列表加载失败', async ({ page }) => {
    // Mock算子列表加载失败 - 需要同时mock两个API，因为组件有fallback逻辑
    await page.route('**/api/backends/duckdb/supported_operators', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal Server Error',
          message: '算子列表服务不可用'
        })
      });
    });

    // Mock分页版本也失败（组件的fallback API）
    await page.route('**/api/backends/duckdb/operators/query**', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal Server Error',
          message: '分页算子列表服务也不可用'
        })
      });
    });

    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待错误状态显示
    await expect(page.locator('[data-testid="error-container"]')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=加载算子列表失败')).toBeVisible();
  });

  test('应该支持错误重试机制', async ({ page }) => {
    let apiCallCount = 0;
    
    // Mock第一次失败，第二次成功
    await page.route('**/api/sessions/test-session-001', async (route) => {
      apiCallCount++;
      if (apiCallCount === 1) {
        // 第一次失败
        await route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Temporary Error',
            message: '临时网络错误'
          })
        });
      } else {
        // 第二次成功
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockSessionInfo)
        });
      }
    });

    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待错误状态显示
    await expect(page.locator('[data-testid="error-container"]')).toBeVisible({ timeout: 10000 });
    
    // 点击重试按钮
    await page.click('[data-testid="retry-button"]');
    
    // 验证重新显示加载状态
    await expect(page.locator('[data-testid="loading-container"]')).toBeVisible();
    
    // 等待重试成功，SessionDetailContent显示
    await expect(page.locator('[data-testid="session-detail-content"]')).toBeVisible({ timeout: 10000 });
    
    // 验证错误状态消失
    await expect(page.locator('[data-testid="error-container"]')).not.toBeVisible();
    
    // 验证API被调用了两次
    expect(apiCallCount).toBe(2);
  });

  test('应该支持返回会话列表功能', async ({ page }) => {
    // Mock会话加载失败
    await page.route('**/api/sessions/test-session-001', async (route) => {
      await route.fulfill({
        status: 404,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Not Found',
          message: '会话不存在'
        })
      });
    });

    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待错误状态显示
    await expect(page.locator('[data-testid="error-container"]')).toBeVisible({ timeout: 10000 });
    
    // 点击返回会话列表按钮
    await page.click('[data-testid="back-to-list-button"]');
    
    // 验证跳转到会话列表页
    await page.waitForURL('**/dpe-web/sessions', { timeout: 5000 });
    expect(page.url()).toContain('/dpe-web/sessions');
  });

  test('应该正确传递props给SessionDetailContent', async ({ page }) => {
    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待SessionDetailContent显示
    await expect(page.locator('[data-testid="session-detail-content"]')).toBeVisible({ timeout: 10000 });
    
    // 检查SessionDetailContent是否接收到正确的props
    // 验证会话信息传递正确 - 通过检查页面元素间接验证
    const sessionDetailContent = page.locator('[data-testid="session-detail-content"]');
    await expect(sessionDetailContent).toBeVisible();
    
    // 注意：无法直接访问Vue组件的props，但可以通过UI状态间接验证
    // SessionDetailContent如果正确接收到props，应该能正常渲染其子组件
    // 这里可以检查一些已知的子组件元素存在
  });

  test('应该正确处理分页版本的算子列表API', async ({ page }) => {
    // Mock简单版本失败，分页版本成功
    let listOperatorsCallCount = 0;
    
    await page.route('**/api/backends/duckdb/supported_operators', async (route) => {
      listOperatorsCallCount++;
      // 简单版本失败
      await route.fulfill({
        status: 404,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Not Implemented',
          message: '简单版本不可用'
        })
      });
    });
    
    await page.route('**/api/backends/duckdb/operators/query**', async (route) => {
      // 分页版本成功
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: mockOperators,
          pageParam: {
            pageIndex: 0,
            limit: 1000,
            total: mockOperators.length
          }
        })
      });
    });

    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');

    // 等待初始化完成
    await expect(page.locator('[data-testid="session-detail-content"]')).toBeVisible({ timeout: 10000 });
    
    // 验证简单版本被调用了
    expect(listOperatorsCallCount).toBe(1);
  });

  test('应该在组件销毁时清理ExecutionGraphClient', async ({ page }) => {
    // 这个测试主要验证beforeDestroy生命周期被正确调用
    // 由于Playwright主要测试用户可见的行为，这里主要确保页面能正常卸载
    
    // 导航到页面
    await page.goto('/dpe-web/sessions/detail/test-session-001');
    
    // 等待初始化完成
    await expect(page.locator('[data-testid="session-detail-content"]')).toBeVisible({ timeout: 10000 });
    
    // 导航到其他页面，触发组件销毁
    await page.goto('/');
    
    // 验证页面能正常导航，没有抛出错误
    await expect(page.locator('body')).toBeVisible();
  });
});

/**
 * 设置默认的Mock响应
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 */
async function setupDefaultMockResponses(page) {
  // Mock会话信息API
  await page.route('**/api/sessions/test-session-001', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockSessionInfo)
    });
  });

  // Mock WebSocket连接 - ExecutionGraphClient直接连接WebSocket，无需HTTP API
  // 注意：在真实场景中，ExecutionGraphClient会尝试连接WebSocket
  // 在测试环境中，我们需要Mock WebSocket或者让连接失败后继续

  // Mock算子列表API（简单版本）
  await page.route('**/api/backends/duckdb/supported_operators', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({ operators: mockOperators })
    });
  });

  // Mock算子列表API（分页版本）
  await page.route('**/api/backends/duckdb/operators/query**', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        data: mockOperators,
        pageParam: {
          pageIndex: 0,
          limit: 1000,
          total: mockOperators.length
        }
      })
    });
  });
  
  // Mock会话列表页面（用于重定向测试）
  await page.route('**/dpe-web/sessions', async (route) => {
    await route.fulfill({
      status: 200,
      contentType: 'text/html',
      body: `
        <!DOCTYPE html>
        <html>
        <head><title>会话列表</title></head>
        <body><h1>会话列表页面</h1></body>
        </html>
      `
    });
  });
}
