import OperatorInfo from "@/pages/dpe-web/models/OperatorInfo";

/**
 * 实际的OperatorInfo列表
 * @type {OperatorInfo[]}
 */
const RealOperatorInfos = ([
    {
        "type": "ReadStructuredDataResourceFromMySQL",
        "metadata": {
            "displayName": "从MySQL读取结构化数据资源",
            "description": "从指定MySQL数据库加载某种格式的结构化数据到会话中，支持配置不同类型数据库的连接信息、路径以及不同格式的文件解析配置。",
            "labels": {
                "category": "data-source"
            },
            "annotations": {
                "icon": "database"
            }
        },
        "opConfigMetadata": [
            {
                "key": "filter_expression",
                "schema": {
                    "$defs": {
                        "ExpressionNode": {
                            "description": "表达式节点基类，所有表达式节点均继承自此类。\n\n此类是抽象类，不应直接实例化，应通过其子类实例化，如 `Literal`、\n`Identifier` 或 `FuncCall` 等。\n\nAttributes:\n    type: 节点类型，用于标识节点的具体子类型。\n\nExample:\n    >>> # 构造具体类型的实例，会自动选择正确的子类，不需要指定type字段\n    >>> Literal(val=10)  # 直接使用构造函数\n    >>> Identifier(name=\"user_id\")  # 直接使用构造函数\n    >>> FuncCall(name=\"add\", args=[Literal(val=1), Literal(val=2)])  # 直接使用构造函数\n    >>> # 反序列化\n    >>> ExpressionNode.model_validate(data)  # 反序列化\n    >>> # 直接使用字典构造\n    >>> ExpressionNode(**data)  # 直接使用字典构造，会自动选择正确的子类",
                            "properties": {
                                "type": {
                                    "$ref": "#/$defs/ExpressionNodeType"
                                }
                            },
                            "required": [
                                "type"
                            ],
                            "title": "ExpressionNode",
                            "type": "object"
                        },
                        "ExpressionNodeType": {
                            "description": "定义表达式AST节点的类型枚举。\n\nAttributes:\n    LITERAL: 表示字面量节点。\n    IDENTIFIER: 表示标识符节点。\n    FUNC_CALL: 表示函数调用节点。",
                            "enum": [
                                "Literal",
                                "Identifier",
                                "FuncCall"
                            ],
                            "title": "ExpressionNodeType",
                            "type": "string"
                        },
                        "RowEval": {
                            "description": "行级求值配置",
                            "properties": {
                                "expression": {
                                    "$ref": "#/$defs/ExpressionNode",
                                    "description": "计算表达式"
                                }
                            },
                            "required": [
                                "expression"
                            ],
                            "title": "RowEval",
                            "type": "object"
                        }
                    },
                    "anyOf": [
                        {
                            "$ref": "#/$defs/RowEval"
                        },
                        {
                            "type": "null"
                        }
                    ],
                    "name": "filter_expression",
                    "description": "过滤表达式"
                }
            },
            {
                "key": "columns_only",
                "schema": {
                    "anyOf": [
                        {
                            "items": {
                                "type": "string"
                            },
                            "type": "array"
                        },
                        {
                            "type": "null"
                        }
                    ],
                    "name": "columns_only",
                    "description": "限制返回的列，空代表返回所有字段"
                }
            },
            {
                "key": "preview_data",
                "schema": {
                    "anyOf": [
                        {
                            "type": "boolean"
                        },
                        {
                            "type": "null"
                        }
                    ],
                    "name": "preview_data",
                    "description": "是否预览数据，默认不预览",
                    "default": false
                }
            },
            {
                "key": "preview_options",
                "schema": {
                    "$defs": {
                        "ExpressionNode": {
                            "description": "表达式节点基类，所有表达式节点均继承自此类。\n\n此类是抽象类，不应直接实例化，应通过其子类实例化，如 `Literal`、\n`Identifier` 或 `FuncCall` 等。\n\nAttributes:\n    type: 节点类型，用于标识节点的具体子类型。\n\nExample:\n    >>> # 构造具体类型的实例，会自动选择正确的子类，不需要指定type字段\n    >>> Literal(val=10)  # 直接使用构造函数\n    >>> Identifier(name=\"user_id\")  # 直接使用构造函数\n    >>> FuncCall(name=\"add\", args=[Literal(val=1), Literal(val=2)])  # 直接使用构造函数\n    >>> # 反序列化\n    >>> ExpressionNode.model_validate(data)  # 反序列化\n    >>> # 直接使用字典构造\n    >>> ExpressionNode(**data)  # 直接使用字典构造，会自动选择正确的子类",
                            "properties": {
                                "type": {
                                    "$ref": "#/$defs/ExpressionNodeType"
                                }
                            },
                            "required": [
                                "type"
                            ],
                            "title": "ExpressionNode",
                            "type": "object"
                        },
                        "ExpressionNodeType": {
                            "description": "定义表达式AST节点的类型枚举。\n\nAttributes:\n    LITERAL: 表示字面量节点。\n    IDENTIFIER: 表示标识符节点。\n    FUNC_CALL: 表示函数调用节点。",
                            "enum": [
                                "Literal",
                                "Identifier",
                                "FuncCall"
                            ],
                            "title": "ExpressionNodeType",
                            "type": "string"
                        },
                        "Identifier": {
                            "description": "表示表达式中的标识符，通常是变量名或数据字段名。\n\n例如，`column_a`、`user_id` 等。\n支持通过 `attrPath` 访问嵌套结构的属性。\n\nAttributes:\n    type: 节点类型，固定为 `ExpressionNodeType.IDENTIFIER`。\n    name: 标识符的名称，例如 `'user'`。\n    attrPath: 可选的属性访问路径列表，用于访问复杂类型（如 Struct）的嵌套字段。\n             例如 `['address', 'city']` 表示访问 `identifier.address.city`。",
                            "properties": {
                                "type": {
                                    "$ref": "#/$defs/ExpressionNodeType",
                                    "default": "Identifier"
                                },
                                "name": {
                                    "description": "标识符名称，不能为空。",
                                    "minLength": 1,
                                    "title": "Name",
                                    "type": "string"
                                },
                                "attrPath": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "可选的属性访问路径列表，用于访问复杂类型（如 Struct）的嵌套字段。例如 `['address', 'city']` 表示访问 `identifier.address.city`。",
                                    "title": "Attrpath"
                                }
                            },
                            "required": [
                                "name"
                            ],
                            "title": "Identifier",
                            "type": "object"
                        },
                        "PreviewOption": {
                            "description": "数据预览选项",
                            "properties": {
                                "head": {
                                    "anyOf": [
                                        {
                                            "type": "integer"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "限制返回前n行",
                                    "title": "Head"
                                },
                                "filter_expression": {
                                    "anyOf": [
                                        {
                                            "$ref": "#/$defs/RowEval"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "过滤表达式"
                                },
                                "columns_only": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "type": "string"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "限制返回的列，空代表返回所有字段",
                                    "title": "Columns Only"
                                },
                                "order_by": {
                                    "anyOf": [
                                        {
                                            "items": {
                                                "$ref": "#/$defs/SortOption"
                                            },
                                            "type": "array"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "排序规则",
                                    "title": "Order By"
                                },
                                "formatter": {
                                    "anyOf": [
                                        {
                                            "type": "string"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "格式化模式，可选 json、markdown、xml",
                                    "title": "Formatter"
                                },
                                "formatter_args": {
                                    "anyOf": [
                                        {
                                            "additionalProperties": true,
                                            "type": "object"
                                        },
                                        {
                                            "type": "null"
                                        }
                                    ],
                                    "default": null,
                                    "description": "格式化参数",
                                    "title": "Formatter Args"
                                }
                            },
                            "title": "PreviewOption",
                            "type": "object"
                        },
                        "RowEval": {
                            "description": "行级求值配置",
                            "properties": {
                                "expression": {
                                    "$ref": "#/$defs/ExpressionNode",
                                    "description": "计算表达式"
                                }
                            },
                            "required": [
                                "expression"
                            ],
                            "title": "RowEval",
                            "type": "object"
                        },
                        "SortOption": {
                            "description": "排序选项模型",
                            "properties": {
                                "sort_key": {
                                    "$ref": "#/$defs/Identifier",
                                    "description": "排序使用的字段"
                                },
                                "desc": {
                                    "default": false,
                                    "description": "是否倒序排列",
                                    "title": "Desc",
                                    "type": "boolean"
                                },
                                "null_as_max": {
                                    "default": true,
                                    "description": "null值是否视为最大值",
                                    "title": "Null As Max",
                                    "type": "boolean"
                                }
                            },
                            "required": [
                                "sort_key"
                            ],
                            "title": "SortOption",
                            "type": "object"
                        }
                    },
                    "anyOf": [
                        {
                            "$ref": "#/$defs/PreviewOption"
                        },
                        {
                            "type": "null"
                        }
                    ],
                    "name": "preview_options",
                    "description": "数据预览选项"
                }
            },
            {
                "key": "db_options",
                "schema": {
                    "description": "MySQL连接选项",
                    "properties": {
                        "host": {
                            "description": "主机地址",
                            "title": "Host",
                            "type": "string"
                        },
                        "port": {
                            "description": "端口号",
                            "title": "Port",
                            "type": "integer"
                        },
                        "username": {
                            "description": "用户名",
                            "title": "Username",
                            "type": "string"
                        },
                        "password": {
                            "description": "密码",
                            "title": "Password",
                            "type": "string"
                        },
                        "database": {
                            "description": "数据库名称",
                            "title": "Database",
                            "type": "string"
                        },
                        "schema_name": {
                            "anyOf": [
                                {
                                    "type": "string"
                                },
                                {
                                    "type": "null"
                                }
                            ],
                            "default": null,
                            "description": "模式名称",
                            "title": "Schema Name"
                        }
                    },
                    "required": true,
                    "title": "MySQLOptions",
                    "type": "object",
                    "name": "db_options"
                }
            },
            {
                "key": "table_name",
                "schema": {
                    "type": "string",
                    "name": "table_name",
                    "description": "表名",
                    "required": true
                }
            }
        ],
        "inputFields": []
    },
    {
        "type": "FilterStructuredData",
        "metadata": {
            "displayName": "数据过滤",
            "description": "根据条件过滤结构化数据。",
            "labels": {
                "category": "data-process"
            },
            "annotations": {
                "icon": "filter"
            }
        },
        "opConfigMetadata": [
            {
                "key": "resource_id",
                "schema": {
                    "$defs": {
                        "ResourceId": {
                            "description": "资源ID的基类\n\nResourceId 提供了一个统一的方式来标识和引用系统中的各类资源，替代直接使用字符串。\n通过使用强类型的资源ID，可以在编译时捕获错误并提供更好的类型安全性。\n\n主要特点:\n- 不可变对象: 所有ResourceId及其子类的实例创建后不可修改\n- 字符串兼容: 可以与字符串格式互相转换，用于API交互和存储\n- 类型安全: 提供类型校验和格式验证\n- Pydantic集成: 可以在Pydantic模型中直接使用并自动处理序列化/反序列化\n\n使用方式:\n1. 从字符串创建:\n    ```python\n    # 全局资源ID\n    resource_id = ResourceId.from_string(\"data-resource://abc123\")\n    print(isinstance(resource_id, GlobalResourceId))  # True\n\n    # 会话资源ID\n    session_resource = ResourceId.from_string(\"session://s123/data-resource/abc123\")\n    print(isinstance(session_resource, SessionResourceId))  # True\n    ```\n\n2. 直接实例化子类:\n    ```python\n    global_id = GlobalResourceId(\n        resource_type=ResourceType.DATA_RESOURCE,\n        resource_id=\"abc123\"\n    )\n\n    session_id = SessionResourceId(\n        session_id=\"s123\",\n        resource_type=ResourceType.ML_MODEL,\n        resource_id=\"model456\"\n    )\n    ```\n\n3. 在Pydantic模型中使用:\n    ```python\n    class DataModel(BaseModel):\n        resource_id: ResourceId\n        name: str\n\n    # 使用字符串初始化（自动转换）\n    model1 = DataModel(resource_id=\"data-resource://abc123\", name=\"测试\")\n\n    # 使用ResourceId对象初始化\n    model2 = DataModel(resource_id=session_id, name=\"测试2\")\n\n    # 序列化为JSON - ResourceId自动转为字符串\n    json_data = model1.model_dump_json()\n    # 结果: {\"resource_id\":\"data-resource://abc123\",\"name\":\"测试\"}\n    ```\n\n4. 等效性比较:\n    ```python\n    resource_id = GlobalResourceId(resource_type=ResourceType.DATA_RESOURCE, resource_id=\"abc123\")\n\n    # 与字符串比较\n    assert resource_id == \"data-resource://abc123\"\n\n    # 与相同类型的ResourceId比较\n    assert resource_id == GlobalResourceId(\n        resource_type=ResourceType.DATA_RESOURCE,\n        resource_id=\"abc123\"\n    )\n    ```\n\n5. 类型约束与验证:\n    ```python\n    # 限制只接受GlobalResourceId类型\n    class GlobalResourceModel(BaseModel):\n        resource_id: GlobalResourceId\n\n    # 有效的实例化（自动转换字符串）\n    valid_model = GlobalResourceModel(resource_id=\"data-resource://abc123\")\n\n    # 无效的实例化 - 会引发ValueError\n    # invalid_model = GlobalResourceModel(resource_id=\"session://s123/data-resource/abc123\")\n    ```\n\n子类必须实现:\n- pattern类属性: 定义资源ID的格式正则表达式\n- parse_string方法: 从字符串解析为具体的资源ID对象\n- to_string方法: 将资源ID对象转换为字符串",
                            "properties": {},
                            "title": "ResourceId",
                            "type": "object"
                        }
                    },
                    "anyOf": [
                        {
                            "$ref": "#/$defs/ResourceId"
                        },
                        {
                            "type": "string"
                        }
                    ],
                    "description": "URI格式的数据资源ID",
                    "title": "Resource ID",
                    "type": "string",
                    "name": "resource_id",
                    "required": true
                }
            },
            {
                "key": "condition",
                "schema": {
                    "$defs": {
                        "ExpressionNode": {
                            "description": "表达式节点基类，所有表达式节点均继承自此类。\n\n此类是抽象类，不应直接实例化，应通过其子类实例化，如 `Literal`、\n`Identifier` 或 `FuncCall` 等。\n\nAttributes:\n    type: 节点类型，用于标识节点的具体子类型。\n\nExample:\n    >>> # 构造具体类型的实例，会自动选择正确的子类，不需要指定type字段\n    >>> Literal(val=10)  # 直接使用构造函数\n    >>> Identifier(name=\"user_id\")  # 直接使用构造函数\n    >>> FuncCall(name=\"add\", args=[Literal(val=1), Literal(val=2)])  # 直接使用构造函数\n    >>> # 反序列化\n    >>> ExpressionNode.model_validate(data)  # 反序列化\n    >>> # 直接使用字典构造\n    >>> ExpressionNode(**data)  # 直接使用字典构造，会自动选择正确的子类",
                            "properties": {
                                "type": {
                                    "$ref": "#/$defs/ExpressionNodeType"
                                }
                            },
                            "required": [
                                "type"
                            ],
                            "title": "ExpressionNode",
                            "type": "object"
                        },
                        "ExpressionNodeType": {
                            "description": "定义表达式AST节点的类型枚举。\n\nAttributes:\n    LITERAL: 表示字面量节点。\n    IDENTIFIER: 表示标识符节点。\n    FUNC_CALL: 表示函数调用节点。",
                            "enum": [
                                "Literal",
                                "Identifier",
                                "FuncCall"
                            ],
                            "title": "ExpressionNodeType",
                            "type": "string"
                        }
                    },
                    "description": "过滤条件配置",
                    "properties": {
                        "expression": {
                            "$ref": "#/$defs/ExpressionNode",
                            "description": "计算表达式"
                        }
                    },
                    "required": true,
                    "title": "RowEval",
                    "type": "object",
                    "name": "condition"
                }
            },
            {
                "key": "columnsOnly",
                "schema": {
                    "anyOf": [
                        {
                            "items": {
                                "type": "string"
                            },
                            "type": "array"
                        },
                        {
                            "type": "null"
                        }
                    ],
                    "name": "columnsOnly",
                    "description": "限制输出的数据资源的字段，空代表返回所有字段"
                }
            }
        ],
        "inputFields": [
            "resource_id"
        ]
    }
]).map(item => new OperatorInfo(item));


export { RealOperatorInfos };