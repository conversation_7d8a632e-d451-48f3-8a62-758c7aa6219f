<template>
  <div class="session-detail-content-test" data-testid="session-detail-content-test">
    <!-- 测试页面标题 -->
    <div class="test-header" data-testid="test-header">
      <h1>SessionDetailContent 组件测试页面</h1>
      <div class="test-controls">
        <a-button @click="resetTestData" data-testid="reset-test-data">重置测试数据</a-button>
        <a-button @click="toggleCanSave" data-testid="toggle-can-save">
          切换保存状态: {{ canSave ? '可保存' : '不可保存' }}
        </a-button>
        <a-button @click="toggleCanRun" data-testid="toggle-can-run">
          切换运行状态: {{ canRun ? '可运行' : '不可运行' }}
        </a-button>
      </div>
    </div>

    <!-- 测试状态显示 -->
    <div class="test-status" data-testid="test-status">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card title="会话信息" size="small">
            <p>ID: {{ sessionInfo.id }}</p>
            <p>状态: {{ sessionInfo.state }}</p>
            <p>名称: {{ sessionInfo.metadata?.displayName }}</p>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card title="算子信息" size="small">
            <p>可用算子数量: {{ operators.length }}</p>
            <ul>
              <li v-for="op in operators" :key="op.type">{{ op.metadata.displayName }}</li>
            </ul>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card title="客户端状态" size="small">
            <p>连接状态: {{ clientStatus }}</p>
            <p>会话ID: {{ executionGraphClient?._sessionId }}</p>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card title="事件日志" size="small">
            <div class="event-log" style="height: 100px; overflow-y: auto;">
              <div v-for="(event, index) in eventLog" :key="index" class="event-item">
                {{ event }}
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- SessionDetailContent 组件 -->
    <div class="content-container" style="height: calc(100vh - 200px);" data-testid="content-container">
      <SessionDetailContent
        v-if="executionGraphClient && sessionInfo && operators.length > 0"
        :session-info="sessionInfo"
        :execution-graph-client="executionGraphClient"
        :operators="operators"
        :can-save="canSave"
        :can-run="canRun"
        @error="handleContentError"
        @graph-data-change="handleGraphDataChange"
        data-testid="session-detail-content"
      />
      <div v-else class="loading-message">
        <a-spin size="large" tip="正在初始化测试环境...">
          <div style="height: 200px;"></div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * SessionDetailContent 组件测试页面
 * 
 * @description
 * 这个测试页面用于验证 SessionDetailContent 组件的各种功能，包括：
 * 1. 组件初始化和数据加载
 * 2. 算子拖拽和节点创建
 * 3. 节点选择和配置编辑
 * 4. 图的保存和运行操作
 * 5. 错误处理和状态管理
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */

import SessionDetailContent from '@/pages/dpe-web/pages/SessionDetailPage/content.vue';
import MockExecutionGraphClient from '../../../../../unit/pages/dpe-web/models/MockExecutionGraphClient';
import { RealOperatorInfos } from './SessionDetailContentTestData.js';
import {
  Session,
  SessionState,
  ObjectMetadata,
  ExecutionGraph,
  GraphNode,
  GraphEdge,
  OperatorInfo
} from '@/pages/dpe-web/models';

export default {
  name: 'SessionDetailContentTest',
  
  components: {
    SessionDetailContent
  },

  data() {
    return {
      /**
       * 测试用会话信息
       * @type {Session}
       */
      sessionInfo: null,

      /**
       * 模拟的执行图客户端
       * @type {MockExecutionGraphClient}
       */
      executionGraphClient: null,

      /**
       * 可用的算子列表
       * @type {Array<OperatorInfo>}
       */
      operators: [],

      /**
       * 是否可以保存
       * @type {boolean}
       */
      canSave: true,

      /**
       * 是否可以运行
       * @type {boolean}
       */
      canRun: true,

      /**
       * 客户端连接状态
       * @type {string}
       */
      clientStatus: '未连接',

      /**
       * 事件日志
       * @type {Array<string>}
       */
      eventLog: []
    };
  },

  async created() {
    this.logEvent('测试页面开始初始化');
    await this.initializeTestData();
    this.logEvent('测试页面初始化完成');
  },

  async beforeDestroy() {
    if (this.executionGraphClient) {
      await this.executionGraphClient.close();
      this.logEvent('执行图客户端已关闭');
    }
  },

  methods: {
    /**
     * 初始化测试数据
     * @async
     * @returns {Promise<void>}
     */
    async initializeTestData() {
      try {
        // 创建测试会话信息
        this.sessionInfo = new Session({
          id: 'test-session-' + Date.now(),
          state: SessionState.CREATED,
          metadata: new ObjectMetadata({
            displayName: '测试会话 - SessionDetailContent',
            description: '用于测试 SessionDetailContent 组件功能的测试会话'
          }),
          backendType: 'duckdb',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        // 设置算子列表（包含真实算子），转换为OperatorInfo实例
        this.operators = RealOperatorInfos.map(opData => new OperatorInfo(opData));
        this.logEvent(`加载了 ${this.operators.length} 个算子`);
        console.log(`已加载算子类型:`, this.operators.map(op => op.type));

        // 创建带有初始图的执行图客户端
        const initialGraph = new ExecutionGraph({
          id: 'test-initial-graph',
          metadata: new ObjectMetadata({
            displayName: '测试初始图',
            description: '用于测试的初始执行图'
          }),
          nodes: {},
          edges: []
        });

        this.executionGraphClient = new MockExecutionGraphClient(
          this.operators,
          this.sessionInfo.id,
          initialGraph
        );

        // 连接客户端
        await this.executionGraphClient.connect();
        this.clientStatus = '已连接';
        this.logEvent('执行图客户端连接成功');

      } catch (error) {
        this.logEvent(`初始化失败: ${error.message}`);
        console.error('测试数据初始化失败:', error);
      }
    },

    /**
     * 重置测试数据
     * @async
     * @returns {Promise<void>}
     */
    async resetTestData() {
      this.logEvent('开始重置测试数据');
      
      if (this.executionGraphClient) {
        await this.executionGraphClient.close();
      }
      
      this.eventLog = [];
      await this.initializeTestData();
      
      this.logEvent('测试数据重置完成');
    },

    /**
     * 切换保存状态
     */
    toggleCanSave() {
      this.canSave = !this.canSave;
      this.logEvent(`保存状态切换为: ${this.canSave ? '可保存' : '不可保存'}`);
    },

    /**
     * 切换运行状态
     */
    toggleCanRun() {
      this.canRun = !this.canRun;
      this.logEvent(`运行状态切换为: ${this.canRun ? '可运行' : '不可运行'}`);
    },

    /**
     * 处理内容组件错误
     * @param {Error} error - 错误对象
     */
    handleContentError(error) {
      this.logEvent(`内容组件错误: ${error.message}`);
      console.error('SessionDetailContent 错误:', error);
      
      this.$message.error(`组件错误: ${error.message}`);
    },

    /**
     * 处理图数据变化
     * @param {Object} graphData - 图数据
     */
    handleGraphDataChange(graphData) {
      console.log('图数据已变化:', graphData);
      this.logEvent(`图数据变化: ${graphData ? '有数据' : '无数据'}`);
    },

    /**
     * 记录事件日志
     * @param {string} message - 日志消息
     */
    logEvent(message) {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${message}`;
      this.eventLog.unshift(logMessage);
      
      // 限制日志数量
      if (this.eventLog.length > 50) {
        this.eventLog = this.eventLog.slice(0, 50);
      }
      
      console.log(logMessage);
    }
  }
};
</script>

<style scoped>
.session-detail-content-test {
  padding: 16px;
  height: 100vh;
  overflow: hidden;
}

.test-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.test-header h1 {
  margin: 0 0 16px 0;
  color: #1890ff;
}

.test-controls {
  display: flex;
  gap: 8px;
}

.test-status {
  margin-bottom: 16px;
}

.event-log {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.event-item {
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
  word-break: break-all;
}

.content-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

/* 确保组件占满容器 */
.content-container >>> .session-detail-content {
  height: 100%;
}
</style>
