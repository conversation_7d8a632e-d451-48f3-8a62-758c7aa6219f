/**
 * NodeDetailPanel API Mock 管理器
 * @description 统一管理NodeDetailPanel组件系统中的所有API拦截和模拟逻辑
 * <AUTHOR> Assistant
 * @since 1.0.0
 */

/**
 * 预设的Mock数据场景
 * @constant {Object}
 */
export const MOCK_DATA_SCENARIOS = {
  // 正常数据场景
  normal: {
    preview: {
      total_rows: 25,
      data_schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false, description: '主键ID' },
          { name: 'name', type: 'string', nullable: false, description: '名称' },
          { name: 'type', type: 'string', nullable: false, description: '数据类型' },
          { name: 'value', type: 'mixed', nullable: true, description: '数值' },
          { name: 'created_at', type: 'datetime', nullable: false, description: '创建时间' }
        ]
      },
      data: Array.from({ length: 25 }, (_, i) => ({
        id: i + 1,
        name: `数据项_${i + 1}`,
        type: i % 3 === 0 ? 'string' : i % 3 === 1 ? 'number' : 'boolean',
        value: i % 3 === 0 ? `值_${i + 1}` : i % 3 === 1 ? (i + 1) * 100 : (i + 1) % 2 === 0,
        created_at: `2024-01-${String(i + 1).padStart(2, '0')}T10:00:00Z`
      }))
    },
    schema: {
      data_schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false, description: '唯一标识符' },
          { name: 'name', type: 'string', nullable: false, description: '数据项名称' },
          { name: 'type', type: 'string', nullable: false, description: '数据类型' },
          { name: 'value', type: 'mixed', nullable: true, description: '数据值' },
          { name: 'created_at', type: 'datetime', nullable: false, description: '创建时间' }
        ]
      }
    }
  },
  
  // 大数据场景
  large: {
    preview: {
      total_rows: 10000,
      data_schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false, description: '记录ID' },
          { name: 'title', type: 'string', nullable: false, description: '标题' },
          { name: 'score', type: 'number', nullable: true, description: '评分' },
          { name: 'status', type: 'string', nullable: false, description: '状态' },
          { name: 'timestamp', type: 'datetime', nullable: false, description: '时间戳' }
        ]
      },
      data: Array.from({ length: 50 }, (_, i) => ({
        id: i + 1,
        title: `大数据集记录_${i + 1}`,
        score: Math.round((Math.random() * 100) * 100) / 100,
        status: ['active', 'inactive', 'pending'][i % 3],
        timestamp: new Date(Date.now() - i * 86400000).toISOString()
      }))
    },
    schema: {
      data_schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false, description: '记录唯一标识' },
          { name: 'title', type: 'string', nullable: false, description: '记录标题' },
          { name: 'score', type: 'number', nullable: true, description: '评分数值' },
          { name: 'status', type: 'string', nullable: false, description: '记录状态' },
          { name: 'timestamp', type: 'datetime', nullable: false, description: '记录时间戳' }
        ]
      }
    }
  },
  
  // 空数据场景
  empty: {
    preview: {
      total_rows: 0,
      data_schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false, description: 'ID字段' },
          { name: 'placeholder', type: 'string', nullable: true, description: '占位字段' }
        ]
      },
      data: []
    },
    schema: {
      data_schema: {
        fields: [
          { name: 'id', type: 'integer', nullable: false, description: 'ID字段' },
          { name: 'placeholder', type: 'string', nullable: true, description: '占位字段' }
        ]
      }
    }
  },
  
  // 默认场景
  default: {
    preview: {
      total_rows: 2,
      data_schema: {
        fields: [
          { name: 'col1', type: 'string', nullable: false, description: 'Column 1' },
          { name: 'col2', type: 'string', nullable: false, description: 'Column 2' }
        ]
      },
      data: [
        { col1: 'data1', col2: 'data2' },
        { col1: 'data3', col2: 'data4' }
      ]
    },
    schema: {
      data_schema: {
        fields: [
          { name: 'col1', type: 'string', nullable: false, description: 'Column 1' },
          { name: 'col2', type: 'string', nullable: false, description: 'Column 2' }
        ]
      }
    }
  }
};

/**
 * 预设的任务状态场景
 * @constant {Object}
 */
export const TASK_STATUS_SCENARIOS = {
  running: {
    id: 'task-running-001',
    sessionId: 'test-session-003',
    nodeId: 'test-node-003',
    status: 'RUNNING',
    progress: 50,
    message: '正在处理数据...',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    resultResourceId: null,
    result: null,
    isUpstreamChanged: false
  },
  
  completed: {
    id: 'task-completed-001',
    sessionId: 'test-session-004',
    nodeId: 'test-node-004',
    status: 'SUCCESS',
    progress: 100,
    message: '任务执行完成',
    createdAt: new Date(Date.now() - 60000).toISOString(),
    updatedAt: new Date().toISOString(),
    resultResourceId: 'structured-data-resource-123',
    resultResourceType: 'structured',
    result: { records: 100 },
    isUpstreamChanged: false
  },
  
  failed: {
    id: 'task-error-001',
    sessionId: 'test-session-005',
    nodeId: 'test-node-005',
    status: 'ERROR',
    progress: 0,
    message: '数据处理失败',
    error: 'Connection timeout',
    createdAt: new Date(Date.now() - 120000).toISOString(),
    updatedAt: new Date(Date.now() - 60000).toISOString(),
    resultResourceId: null,
    result: null,
    isUpstreamChanged: false
  }
};

/**
 * 结构化数据资源API模拟管理器
 * @class StructuredDataApiMocker
 */
export class StructuredDataApiMocker {
  /**
   * 设置结构化数据相关API的模拟拦截
   * @param {Object} page - Playwright页面对象
   * @param {Object} options - 配置选项
   * @param {string} options.scenario - 数据场景 (normal|large|empty|default)
   * @param {Object} options.customData - 自定义数据覆盖
   * @returns {Promise<void>}
   */
  static async setupMocks(page, options = {}) {
    const { 
      scenario = 'default',
      customData = {},
      enableErrorHandling = true
    } = options;

    console.debug('Mock: 设置结构化数据API拦截', { scenario, enableErrorHandling });

    // 模拟结构化数据预览接口 - POST请求
    await page.route('**/api/resources/data-resource/structured/preview', async (route) => {
      if (route.request().method() === 'POST') {
        console.debug('Mock: 拦截结构化数据预览POST请求', route.request().url());
        
        // 解析请求参数
        const requestBody = JSON.parse(route.request().postData() || '{}');
        const { resource_id, limit = 20, offset = 0 } = requestBody;
        
        console.debug('Mock: 预览请求参数', { resource_id, limit, offset });
        
        // 根据资源ID确定使用的数据场景
        let responseScenario = scenario;
        if (resource_id) {
          // 支持通过资源ID后缀指定场景，如: test-resource-large, test-resource-empty
          if (resource_id.includes('-large')) responseScenario = 'large';
          else if (resource_id.includes('-empty')) responseScenario = 'empty';
          else if (resource_id.includes('-normal')) responseScenario = 'normal';
          else if (resource_id.includes('-error') && enableErrorHandling) {
            await route.fulfill({
              status: 500,
              contentType: 'application/json',
              body: JSON.stringify({
                error: 'Internal Server Error',
                message: '服务器内部错误'
              })
            });
            return;
          }
        }
        
        // 获取预设数据，自定义数据只作为默认场景的备选
        let mockData;
        if (MOCK_DATA_SCENARIOS[responseScenario]) {
          // 使用预设场景数据
          mockData = MOCK_DATA_SCENARIOS[responseScenario].preview;
        } else if (customData.preview) {
          // 只有在没有匹配的预设场景时才使用自定义数据
          mockData = customData.preview;
        } else {
          // 最后的备选是默认场景
          mockData = MOCK_DATA_SCENARIOS.default.preview;
        }
        

        
        // 计算分页数据
        const startIndex = offset;
        const endIndex = Math.min(startIndex + limit, mockData.data.length);
        const paginatedData = mockData.data.slice(startIndex, endIndex);
        
        const response = {
          total_rows: mockData.total_rows,
          data_schema: mockData.data_schema,
          data: paginatedData
        };
        
        console.debug('Mock: 预览响应数据', { 
          scenario: responseScenario,
          total_rows: response.total_rows, 
          schema_fields: response.data_schema.fields.length,
          data_rows: response.data.length 
        });
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(response)
        });
      } else {
        await route.continue();
      }
    });

    // 模拟结构化数据结构接口 - POST请求
    await page.route('**/api/resources/data-resource/structured/schema', async (route) => {
      if (route.request().method() === 'POST') {
        console.debug('Mock: 拦截结构化数据结构POST请求', route.request().url());
        
        const requestBody = JSON.parse(route.request().postData() || '{}');
        const { resource_id } = requestBody;
        
        console.debug('Mock: 结构请求参数', { resource_id });
        
        // 根据资源ID确定使用的数据场景
        let responseScenario = scenario;
        if (resource_id) {
          if (resource_id.includes('-large')) responseScenario = 'large';
          else if (resource_id.includes('-empty')) responseScenario = 'empty';
          else if (resource_id.includes('-normal')) responseScenario = 'normal';
          else if (resource_id.includes('-error') && enableErrorHandling) {
            await route.fulfill({
              status: 500,
              contentType: 'application/json',
              body: JSON.stringify({
                error: 'Internal Server Error',
                message: '服务器内部错误'
              })
            });
            return;
          }
        }
        
        // 获取预设数据，自定义数据只作为默认场景的备选
        let mockData;
        if (MOCK_DATA_SCENARIOS[responseScenario]) {
          // 使用预设场景数据
          mockData = MOCK_DATA_SCENARIOS[responseScenario].schema;
        } else if (customData.schema) {
          // 只有在没有匹配的预设场景时才使用自定义数据
          mockData = customData.schema;
        } else {
          // 最后的备选是默认场景
          mockData = MOCK_DATA_SCENARIOS.default.schema;
        }
        
        console.debug('Mock: 结构响应数据', { 
          scenario: responseScenario,
          schema_fields: mockData.data_schema.fields.length 
        });
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockData)
        });
      } else {
        await route.continue();
      }
    });
  }

  /**
   * 清除结构化数据API拦截
   * @param {Object} page - Playwright页面对象
   * @returns {Promise<void>}
   */
  static async clearMocks(page) {
    await page.unroute('**/api/resources/data-resource/structured/preview');
    await page.unroute('**/api/resources/data-resource/structured/schema');
    console.debug('Mock: 已清除结构化数据API拦截');
  }

  // 保持向后兼容性的方法
  static getDefaultPreviewData() {
    return MOCK_DATA_SCENARIOS.default.preview;
  }

  static getDefaultSchemaData() {
    return MOCK_DATA_SCENARIOS.default.schema.data_schema.fields;
  }
}

/**
 * 任务API模拟管理器
 * @class TaskApiMocker
 */
export class TaskApiMocker {
  /**
   * 设置任务相关API的模拟拦截
   * @param {Object} page - Playwright页面对象
   * @param {Object} options - 配置选项
   * @param {string} options.defaultStatus - 默认任务状态 (running|completed|failed|none)
   * @param {boolean} options.enableDynamicScenarios - 是否启用动态场景检测
   * @returns {Promise<void>}
   */
  static async setupMocks(page, options = {}) {
    const { 
      defaultStatus = 'none',
      enableDynamicScenarios = true,
      customTasks = {}
    } = options;

    console.debug('Mock: 设置任务API拦截', { defaultStatus, enableDynamicScenarios });

    // 设置任务查询API拦截
    await page.route('**/api/sessions/*/tasks/query', async route => {
      const request = route.request();
      const requestBody = JSON.parse(request.postData() || '{}');
      const nodeIds = requestBody.nodeIds || [];
      
      console.debug('Mock: 拦截任务查询请求', { nodeIds });
      
      let mockTasks = [];
      
      // 动态场景检测
      let currentScenario = defaultStatus;
      if (enableDynamicScenarios) {
        try {
          currentScenario = await page.evaluate(() => {
            const testPage = document.querySelector('.test-page');
            if (!testPage) return 'normal';
            
            // 检查当前激活的场景按钮
            const activeButton = testPage.querySelector('.ant-btn-primary');
            if (activeButton?.textContent?.includes('任务运行中')) return 'running';
            if (activeButton?.textContent?.includes('任务已完成')) return 'completed';
            if (activeButton?.textContent?.includes('任务失败')) return 'failed';
            if (activeButton?.textContent?.includes('只读场景')) return 'readonly';
            return 'normal';
          });
        } catch (e) {
          console.debug('Mock: 无法检测当前场景，使用默认状态', defaultStatus);
        }
      }
      
      // 根据场景返回相应的任务数据
      if (currentScenario === 'running') {
        const task = { ...TASK_STATUS_SCENARIOS.running };
        task.nodeId = nodeIds[0] || task.nodeId;
        mockTasks = [task];
      } else if (currentScenario === 'completed') {
        const task = { ...TASK_STATUS_SCENARIOS.completed };
        task.nodeId = nodeIds[0] || task.nodeId;
        mockTasks = [task];
      } else if (currentScenario === 'failed') {
        const task = { ...TASK_STATUS_SCENARIOS.failed };
        task.nodeId = nodeIds[0] || task.nodeId;
        mockTasks = [task];
      } else if (customTasks[currentScenario]) {
        mockTasks = Array.isArray(customTasks[currentScenario]) ? customTasks[currentScenario] : [customTasks[currentScenario]];
      }
      
      console.debug('Mock: 任务查询响应', { scenario: currentScenario, tasksCount: mockTasks.length });
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: mockTasks,
          pageParam: {
            pageIndex: 0,
            pageSize: 1,
            totalPages: mockTasks.length > 0 ? 1 : 0,
            totalRecords: mockTasks.length
          }
        })
      });
    });
  }

  /**
   * 清除任务API拦截
   * @param {Object} page - Playwright页面对象
   * @returns {Promise<void>}
   */
  static async clearMocks(page) {
    await page.unroute('**/api/sessions/*/tasks/query');
    console.debug('Mock: 已清除任务API拦截');
  }
}

/**
 * NodeDetailPanel 统一API模拟管理器
 * @class NodeDetailPanelApiMocker
 */
export class NodeDetailPanelApiMocker {
  /**
   * 设置所有相关API的模拟拦截
   * @param {Object} page - Playwright页面对象
   * @param {Object} options - 配置选项
   * @param {boolean} options.enableStructuredData - 是否启用结构化数据API
   * @param {boolean} options.enableTask - 是否启用任务API
   * @param {Object} options.structuredDataOptions - 结构化数据配置
   * @param {Object} options.taskOptions - 任务配置
   * @returns {Promise<void>}
   */
  static async setupAllMocks(page, options = {}) {
    const {
      enableStructuredData = true,
      enableTask = true,
      structuredDataOptions = {},
      taskOptions = {}
    } = options;

    console.debug('Mock: 开始设置所有API拦截', { enableStructuredData, enableTask });

    // 通用资源API拦截（兜底）- 必须先设置，以便具体的拦截器可以覆盖
    await page.route('**/api/resources/**', async (route) => {
      console.debug('Mock: 拦截通用资源请求', route.request().method(), route.request().url());
      console.debug('Mock: 通用资源请求详情', route.request().postData());
      
      // 检查是否为结构化数据相关的请求，如果是则转发给专门的处理器
      const url = route.request().url();
      const method = route.request().method();
      
      if (method === 'POST' && (url.includes('/structured/preview') || url.includes('/structured/schema'))) {
        console.warn('Mock: 结构化数据请求被通用拦截器捕获，这可能表示专门的拦截器配置有问题');
        console.warn('Mock: URL:', url);
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Resource request handled successfully'
        })
      });
    });

    if (enableTask) {
      await TaskApiMocker.setupMocks(page, taskOptions);
    }

    if (enableStructuredData) {
      await StructuredDataApiMocker.setupMocks(page, structuredDataOptions);
    }

    console.debug('Mock: 所有API拦截设置完成');
  }

  /**
   * 清除所有API拦截
   * @param {Object} page - Playwright页面对象
   * @returns {Promise<void>}
   */
  static async clearAllMocks(page) {
    await StructuredDataApiMocker.clearMocks(page);
    await TaskApiMocker.clearMocks(page);
    await page.unroute('**/api/resources/**');
    console.debug('Mock: 已清除所有API拦截');
  }

  /**
   * 重置所有API拦截
   * @param {Object} page - Playwright页面对象
   * @param {Object} options - 配置选项
   * @returns {Promise<void>}
   */
  static async resetAllMocks(page, options = {}) {
    await this.clearAllMocks(page);
    await this.setupAllMocks(page, options);
    console.debug('Mock: 已重置所有API拦截');
  }
}

// 导出默认的API模拟管理器
export default NodeDetailPanelApiMocker; 