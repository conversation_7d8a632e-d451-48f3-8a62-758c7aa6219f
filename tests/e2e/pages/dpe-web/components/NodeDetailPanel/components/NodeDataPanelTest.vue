<template>
  <div class="node-data-panel-test">
    <div class="test-header">
      <h2>NodeDataPanel 组件测试</h2>
      <p>测试数据预览面板容器组件的各种状态和数据类型支持</p>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <a-card title="测试控制面板" size="small">
                 <div class="control-row">
           <label>测试场景:</label>
           <a-select 
             v-model="selectedScenario" 
             style="width: 200px;"
             @change="handleScenarioChange"
             data-testid="scenario-select"
           >
             <a-select-option value="empty">空状态</a-select-option>
             <a-select-option value="loading">加载状态</a-select-option>
             <a-select-option value="error">错误状态</a-select-option>
             <a-select-option value="structured">结构化数据</a-select-option>
             <a-select-option value="unsupported">不支持的数据类型</a-select-option>
             <a-select-option value="no-resource">无结果资源</a-select-option>
           </a-select>
         </div>

                 <div class="control-row">
           <label>会话ID:</label>
           <a-input v-model="sessionId" style="width: 200px;" placeholder="输入会话ID" />
         </div>

         <div class="control-row">
           <label>节点ID:</label>
           <a-input v-model="nodeId" style="width: 200px;" placeholder="输入节点ID" />
         </div>

        <div class="control-row">
          <a-button type="primary" @click="refreshComponent">
            刷新组件
          </a-button>
          <a-button @click="logCurrentState">
            输出当前状态
          </a-button>
        </div>
      </a-card>
    </div>

    <!-- 组件测试区域 -->
    <div class="component-test-area">
      <a-card title="NodeDataPanel 组件" size="small">
        <div class="component-container" data-testid="component-container">
          <NodeDataPanel
            ref="nodeDataPanel"
            :session-id="sessionId"
            :node-id="nodeId"
            :task-info="currentTaskInfo"
            data-testid="node-data-panel"
          />
        </div>
      </a-card>
    </div>

    <!-- 状态显示区域 -->
    <div class="status-display">
      <a-card title="当前状态信息" size="small">
        <div class="status-info">
          <h4>组件 Props:</h4>
          <pre>{{ JSON.stringify({
            sessionId,
            nodeId,
            taskInfo: currentTaskInfo
          }, null, 2) }}</pre>
        </div>
      </a-card>
    </div>

    <!-- 结果显示区域 -->
    <div class="result-display">
      <a-card title="操作结果" size="small">
        <div class="result-content">
          <div v-for="(result, index) in operationResults" :key="index" class="result-item">
            <span class="timestamp">{{ result.timestamp }}</span>
            <span class="action">{{ result.action }}</span>
            <span class="result">{{ result.result }}</span>
          </div>
          <div v-if="operationResults.length === 0" class="no-results">
            暂无操作结果
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script>
import NodeDataPanel from '@/pages/dpe-web/components/NodeDetailPanel/components/NodeDataPanel';

export default {
  name: 'NodeDataPanelTest',

  components: {
    NodeDataPanel
  },

  data() {
    return {
      // 基础参数
      sessionId: 'test-session-123',
      nodeId: 'test-node-456',
      
      // 测试场景
      selectedScenario: 'empty',
      
      // 当前任务信息
      currentTaskInfo: null,
      
      // 操作结果记录
      operationResults: [],
      
      // 预定义的测试数据
      testScenarios: {
        empty: null,
        loading: {
          id: 'task-loading-123',
          status: 'RUNNING',
          progress: 50,
          message: '正在处理数据...'
        },
        error: {
          id: 'task-error-123',
          status: 'ERROR',
          message: '数据处理失败',
          error: 'Connection timeout'
        },
        structured: {
          id: 'task-success-123',
          status: 'SUCCESS',
          progress: 100,
          message: '数据处理完成',
          resultResourceId: 'structured-data-resource-123',
          resultResourceType: 'structured-data'
        },
        unsupported: {
          id: 'task-success-456',
          status: 'SUCCESS',
          progress: 100,
          message: '数据处理完成',
          resultResourceId: 'unknown-data-resource-456',
          resultResourceType: 'unknown-type'
        },
        'no-resource': {
          id: 'task-success-789',
          status: 'SUCCESS',
          progress: 100,
          message: '数据处理完成'
          // 没有 resultResourceId
        }
      }
    };
  },

  mounted() {
    this.handleScenarioChange(this.selectedScenario);
  },

  methods: {
    /**
     * 处理测试场景变化
     */
    handleScenarioChange(scenario) {
      this.currentTaskInfo = this.testScenarios[scenario];
      this.addOperationResult('切换场景', `切换到: ${scenario}`);
    },

    /**
     * 刷新组件
     */
    async refreshComponent() {
      try {
        if (this.$refs.nodeDataPanel && this.$refs.nodeDataPanel.loadData) {
          await this.$refs.nodeDataPanel.loadData();
          this.addOperationResult('刷新组件', '成功');
        } else {
          this.addOperationResult('刷新组件', '组件引用不存在');
        }
      } catch (error) {
        this.addOperationResult('刷新组件', `失败: ${error.message}`);
      }
    },

    /**
     * 输出当前状态
     */
    logCurrentState() {
      const state = {
        sessionId: this.sessionId,
        nodeId: this.nodeId,
        selectedScenario: this.selectedScenario,
        currentTaskInfo: this.currentTaskInfo
      };
      console.log('NodeDataPanelTest 当前状态:', state);
      this.addOperationResult('输出状态', '已输出到控制台');
    },

    /**
     * 添加操作结果记录
     */
    addOperationResult(action, result) {
      this.operationResults.unshift({
        timestamp: new Date().toLocaleTimeString(),
        action,
        result
      });
      
      // 限制记录数量
      if (this.operationResults.length > 10) {
        this.operationResults = this.operationResults.slice(0, 10);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.node-data-panel-test {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    margin-bottom: 20px;
    text-align: center;

    h2 {
      margin: 0 0 8px 0;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .control-panel {
    margin-bottom: 20px;

    .control-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      label {
        display: inline-block;
        width: 80px;
        font-weight: 500;
        color: #333;
      }

      .ant-btn {
        margin-right: 8px;
      }
    }
  }

  .component-test-area {
    margin-bottom: 20px;

    .component-container {
      height: 500px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      overflow: hidden;
    }
  }

  .status-display,
  .result-display {
    margin-bottom: 20px;

    .status-info pre {
      background: #f6f8fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.5;
      overflow-x: auto;
    }

    .result-content {
      max-height: 200px;
      overflow-y: auto;

      .result-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        .timestamp {
          width: 80px;
          font-size: 12px;
          color: #999;
        }

        .action {
          width: 100px;
          font-weight: 500;
          color: #333;
        }

        .result {
          flex: 1;
          color: #666;
          font-size: 14px;
        }
      }

      .no-results {
        text-align: center;
        color: #999;
        padding: 20px;
        font-style: italic;
      }
    }
  }
}

// 深色主题适配
.ant-layout.dark {
  .node-data-panel-test {
    background: #141414;

    .test-header {
      h2 {
        color: rgba(255, 255, 255, 0.85);
      }

      p {
        color: rgba(255, 255, 255, 0.65);
      }
    }

    .control-row label {
      color: rgba(255, 255, 255, 0.85);
    }

    .component-container {
      border-color: #434343;
    }

    .status-info pre {
      background: #262626;
      color: rgba(255, 255, 255, 0.85);
    }
  }
}
</style> 