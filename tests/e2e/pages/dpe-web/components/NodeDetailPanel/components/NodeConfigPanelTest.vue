<template>
  <div class="node-config-panel-test">
    <h1>NodeConfigPanel 组件测试</h1>
    
    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select 
            v-model="selectedOperatorType" 
            placeholder="选择算子类型"
            style="width: 100%"
            @change="handleOperatorTypeChange"
            data-testid="operator-type-select"
          >
            <a-select-option 
              v-for="(operator, key) in mockOperators"
              :key="key"
              :value="key"
            >
              {{ operator.metadata.displayName }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-switch 
            v-model="editable" 
            checked-children="可编辑" 
            un-checked-children="只读"
          />
        </a-col>
        <a-col :span="4">
          <a-button 
            type="primary" 
            @click="triggerValidate"
            data-testid="validate-btn"
          >
            验证表单
          </a-button>
        </a-col>
        <a-col :span="4">
          <a-button 
            type="primary" 
            @click="triggerSave"
            data-testid="save-btn"
          >
            保存配置
          </a-button>
        </a-col>
        <a-col :span="6">
          <a-button 
            @click="resetForm"
            data-testid="reset-btn"
          >
            重置表单
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- NodeConfigPanel 组件 -->
    <div class="component-container">
      <NodeConfigPanel
        ref="nodeConfigPanel"
        :session-id="sessionId"
        :node-id="nodeId"
        :operator-info="currentOperatorInfo"
        :editable="editable"
        v-model="nodeFormData"
        :callbacks="callbacks"
        @input="handleFormDataChange"
        :available-upstream-nodes="availableUpstreamNodes"
        data-testid="node-config-panel"
      />
    </div>

    <!-- 测试结果展示 -->
    <div class="test-results">
      <h3>测试结果</h3>
      <a-row :gutter="16">
        <a-col :span="12">
          <h4>当前表单数据:</h4>
          <pre data-testid="form-data-display">{{ JSON.stringify(nodeFormData, null, 2) }}</pre>
        </a-col>
        <a-col :span="12">
          <h4>操作日志:</h4>
          <div class="log-container" data-testid="log-container">
            <div 
              v-for="(log, index) in operationLogs" 
              :key="index"
              :class="['log-item', `log-${log.type}`]"
            >
              <span class="log-time">[{{ log.time }}]</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import NodeConfigPanel from '@/pages/dpe-web/components/NodeDetailPanel/components/NodeConfigPanel.vue';
import { createDefaultNodeFormData } from '@/pages/dpe-web/components/NodeDetailPanel/models.js';
import OperatorInfo from '@/pages/dpe-web/models/OperatorInfo.js';
import ObjectMetadata from '@/pages/dpe-web/models/ObjectMetadata.js';
import GraphNode from '@/pages/dpe-web/models/ExecutionGraph';
import { ConfigField } from '@/pages/dpe-web/models/OperatorInfo.js';

export default {
  name: 'NodeConfigPanelTest',
  
  components: {
    NodeConfigPanel
  },
  
  data() {
    return {
      sessionId: 'test-session-123',
      nodeId: 'test-node-456',
      editable: true,
      selectedOperatorType: 'dataLoader',
      nodeFormData: createDefaultNodeFormData(),
      operationLogs: [],
      availableUpstreamNodes: [
        new GraphNode({
          id: 'mock-upstream-node-1',
          metadata: new ObjectMetadata({ displayName: '模拟上游节点 A' })
        }),
        new GraphNode({
          id: 'mock-upstream-node-2',
          metadata: new ObjectMetadata({ displayName: '模拟上游节点 B' })
        }),
        new GraphNode({
          id: 'mock-upstream-node-3',
          metadata: new ObjectMetadata({ displayName: '模拟上游节点 C' })
        })
      ],
      
      // 模拟不同类型的算子信息
      mockOperators: {
        dataLoader: new OperatorInfo({
          type: 'dataLoader',
          metadata: new ObjectMetadata({
            displayName: '数据加载器',
            description: '从各种数据源加载数据'
          }),
          opConfigMetadata: [
            new ConfigField({
              key: 'filePath',
              schema: {
                type: 'string',
                title: '文件路径',
                description: '要加载的数据文件路径',
                required: true,
                placeholder: '请输入文件路径'
              }
            }),
            new ConfigField({
              key: 'format',
              schema: {
                type: 'string',
                title: '文件格式',
                enum: ['csv', 'json', 'parquet', 'excel'],
                default: 'csv',
                required: true
              }
            }),
            new ConfigField({
              key: 'encoding',
              schema: {
                type: 'string',
                title: '编码格式',
                default: 'utf-8',
                placeholder: '文件编码格式'
              }
            }),
            new ConfigField({
              key: 'skipRows',
              schema: {
                type: 'integer',
                title: '跳过行数',
                minimum: 0,
                default: 0,
                description: '从开头跳过的行数'
              }
            })
          ],
          inputFields: []
        }),
        
        dataTransformer: new OperatorInfo({
          type: 'dataTransformer',
          metadata: new ObjectMetadata({
            displayName: '数据转换器',
            description: '对数据进行清洗和转换操作'
          }),
          opConfigMetadata: [
            new ConfigField({
              key: 'operations',
              schema: {
                type: 'array',
                title: '转换操作',
                items: {
                  type: 'object',
                  properties: {
                    type: { type: 'string', enum: ['filter', 'map', 'aggregate'] },
                    expression: { type: 'string' }
                  }
                },
                minItems: 1,
                required: true
              }
            }),
            new ConfigField({
              key: 'outputColumns',
              schema: {
                type: 'array',
                title: '输出列',
                items: { type: 'string' },
                description: '指定输出的列名'
              }
            }),
            new ConfigField({
              key: 'enableCache',
              schema: {
                type: 'boolean',
                title: '启用缓存',
                default: false
              }
            })
          ],
          inputFields: ['inputData']
        }),
        
        dataJoiner: new OperatorInfo({
          type: 'dataJoiner',
          metadata: new ObjectMetadata({
            displayName: '数据连接器',
            description: '连接多个数据集'
          }),
          opConfigMetadata: [
            new ConfigField({
              key: 'joinType',
              schema: {
                type: 'string',
                title: '连接类型',
                enum: ['inner', 'left', 'right', 'outer'],
                default: 'inner',
                required: true
              }
            }),
            new ConfigField({
              key: 'joinKeys',
              schema: {
                type: 'object',
                title: '连接键',
                properties: {
                  left: { type: 'string', title: '左表键' },
                  right: { type: 'string', title: '右表键' }
                },
                required: ['left', 'right']
              }
            }),
            new ConfigField({
              key: 'suffixes',
              schema: {
                type: 'object',
                title: '列名后缀',
                properties: {
                  left: { type: 'string', default: '_x' },
                  right: { type: 'string', default: '_y' }
                }
              }
            })
          ],
          inputFields: ['leftData', 'rightData']
        })
      },
      
      callbacks: {
        validate: this.mockValidate,
        save: this.mockSave
      }
    };
  },
  
  computed: {
    currentOperatorInfo() {
      return this.mockOperators[this.selectedOperatorType];
    }
  },
  
  mounted() {
    this.handleOperatorTypeChange(this.selectedOperatorType);
    this.addLog('info', '页面加载完成，初始化数据加载器算子');
  },
  
  methods: {
    handleOperatorTypeChange(operatorType) {
      const operator = this.mockOperators[operatorType];
      if (operator) {
        // 重置表单数据
        this.nodeFormData = createDefaultNodeFormData({
          metadata: {
            displayName: `测试${operator.metadata.displayName}`,
            description: `测试用的${operator.metadata.description}`
          }
        });
        
        // 初始化输入字段
        const opInputs = {};
        operator.inputFields.forEach(field => {
          opInputs[field] = [];
        });
        this.nodeFormData.opInputs = opInputs;
        
        this.addLog('info', `切换到算子类型: ${operator.metadata.displayName}`);
      }
    },
    
    handleFormDataChange(newFormData) {
      this.addLog('debug', `表单数据变化: ${JSON.stringify(newFormData, null, 2)}`);
    },
    
    async triggerValidate() {
      try {
        this.addLog('info', '开始验证表单...');
        const isValid = await this.$refs.nodeConfigPanel.validate();
        if (isValid) {
          this.addLog('success', '表单验证通过');
        } else {
          this.addLog('error', '表单验证失败');
        }
        return isValid;
      } catch (error) {
        this.addLog('error', `验证过程中发生错误: ${error.message}`);
        return false;
      }
    },
    
    async triggerSave() {
      try {
        this.addLog('info', '开始保存配置...');
        const saved = await this.$refs.nodeConfigPanel.save();
        if (saved) {
          this.addLog('success', '配置保存成功');
        } else {
          this.addLog('error', '配置保存失败');
        }
        return saved;
      } catch (error) {
        this.addLog('error', `保存过程中发生错误: ${error.message}`);
        return false;
      }
    },
    
    resetForm() {
      this.handleOperatorTypeChange(this.selectedOperatorType);
      this.$refs.nodeConfigPanel.clearValidate();
      this.addLog('info', '表单已重置');
    },
    
    async mockValidate(formData) {
      // 模拟验证延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      this.addLog('info', '执行自定义验证逻辑...');
      
      // 模拟一些业务验证规则
      if (!formData.metadata.displayName) {
        this.addLog('error', '节点显示名称不能为空');
        return { valid: false, message: '节点显示名称不能为空' };
      }
      
      if (this.selectedOperatorType === 'dataLoader') {
        if (!formData.opConfigs.filePath) {
          this.addLog('error', '数据加载器必须指定文件路径');
          return { valid: false, message: '文件路径不能为空' };
        }
      }
      
      if (this.selectedOperatorType === 'dataTransformer') {
        if (!formData.opInputs.inputData || formData.opInputs.inputData.length === 0) {
          this.addLog('warning', '数据转换器需要输入数据源');
          // 这里不阻止保存，只是警告
        }
      }
      
      this.addLog('success', '自定义验证通过');
      return { valid: true, message: '验证通过' };
    },
    
    async mockSave(formData) {
      // 模拟保存延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.addLog('info', '向后端发送保存请求...');
      this.addLog('info', `保存数据: ${JSON.stringify(formData, null, 2)}`);
      
      // 模拟随机的保存结果
      const success = Math.random() > 0.2; // 80% 成功率
      
      if (success) {
        this.addLog('success', '数据已成功保存到后端');
      } else {
        this.addLog('error', '后端保存失败，请重试');
      }
      
      return success;
    },
    
    addLog(type, message) {
      const timestamp = new Date().toLocaleTimeString();
      this.operationLogs.push({
        type,
        time: timestamp,
        message
      });
      
      // 限制日志数量
      if (this.operationLogs.length > 50) {
        this.operationLogs = this.operationLogs.slice(-50);
      }
    }
  }
};
</script>

<style scoped>
.node-config-panel-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 24px;
}

.component-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 24px;
  background: #fff;
}

.test-results {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
}

.test-results h3,
.test-results h4 {
  margin-top: 0;
  color: #262626;
}

.test-results pre {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.log-container {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 4px;
  word-wrap: break-word;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-success .log-message {
  color: #52c41a;
}

.log-error .log-message {
  color: #ff4d4f;
}

.log-warning .log-message {
  color: #faad14;
}

.log-info .log-message {
  color: #1890ff;
}

.log-debug .log-message {
  color: #666;
}
</style> 