<template>
  <div class="node-task-info-panel-test">
    <h1>NodeTaskInfoPanel 组件测试页面</h1>
    
    <!-- 控制面板 -->
    <div class="test-controls">
      <h3>测试控制</h3>
      <div class="control-group">
        <label>选择任务状态：</label>
        <a-select 
          v-model="selectedStatus" 
          style="width: 200px; margin-right: 16px;"
          @change="updateTaskInfo"
        >
          <a-select-option value="CREATED">已创建</a-select-option>
          <a-select-option value="PENDING">等待中</a-select-option>
          <a-select-option value="RUNNING">运行中</a-select-option>
          <a-select-option value="SUCCESS">成功</a-select-option>
          <a-select-option value="ERROR">失败</a-select-option>
          <a-select-option value="CANCELLED">已取消</a-select-option>
          <a-select-option value="UNKNOWN">未知</a-select-option>
        </a-select>
        
        <a-checkbox v-model="hasMessage" @change="updateTaskInfo">包含消息</a-checkbox>
        <a-checkbox v-model="hasResultResource" @change="updateTaskInfo">包含结果资源</a-checkbox>
        <a-checkbox v-model="isUpstreamChanged" @change="updateTaskInfo">上游已变更</a-checkbox>
      </div>
      
      <div class="control-group">
        <a-button @click="simulateStatusChange" type="primary">模拟状态变化</a-button>
        <a-button @click="clearTaskInfo" type="default">清空任务信息</a-button>
      </div>
    </div>

    <!-- 分隔线 -->
    <a-divider />

    <!-- 组件展示区域 -->
    <div class="component-demo">
      <h3>组件展示</h3>
      <div class="demo-container">
        <NodeTaskInfoPanel
          :task-info="taskInfo"
          :session-id="sessionId"
          :node-id="nodeId"
          :callbacks="callbacks"
          @view-result="handleViewResult"
        />
      </div>
    </div>

    <!-- 回调结果展示 -->
    <div class="callback-results" v-if="callbackResults.length > 0">
      <h3>回调执行结果</h3>
      <div class="results-list">
        <div 
          v-for="(result, index) in callbackResults" 
          :key="index"
          class="result-item"
        >
          <a-tag :color="result.type === 'success' ? 'green' : 'red'">
            {{ result.type }}
          </a-tag>
          <span>{{ result.message }}</span>
          <small>{{ result.timestamp }}</small>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NodeTaskInfoPanel from '@/pages/dpe-web/components/NodeDetailPanel/components/NodeTaskInfoPanel.vue';
import TaskInfo, { TaskStatus } from '@/pages/dpe-web/models/TaskInfo';

export default {
  name: 'NodeTaskInfoPanelTest',
  
  components: {
    NodeTaskInfoPanel
  },

  data() {
    return {
      // 测试数据
      sessionId: 'test-session-123',
      nodeId: 'test-node-456',
      
      // 控制状态
      selectedStatus: 'CREATED',
      hasMessage: true,
      hasResultResource: false,
      isUpstreamChanged: false,
      
      // 任务信息
      taskInfo: null,
      
      // 回调结果记录
      callbackResults: [],
      
      // 状态变化模拟
      statusSequence: ['CREATED', 'PENDING', 'RUNNING', 'SUCCESS'],
      currentStatusIndex: 0,
      
      // 回调函数
      callbacks: {
        cancelTask: this.mockCancelTask
      }
    };
  },

  mounted() {
    this.updateTaskInfo();
  },

  methods: {
    /**
     * 更新任务信息
     */
    updateTaskInfo() {
      const baseData = {
        id: `task-${Date.now()}`,
        sessionId: this.sessionId,
        nodeId: this.nodeId,
        status: this.selectedStatus,
        createdAt: new Date(Date.now() - 3600000), // 1小时前
        updatedAt: new Date(),
        isUpstreamChanged: this.isUpstreamChanged
      };

      // 根据状态添加相应的数据
      if (this.hasMessage) {
        const messageMap = {
          [TaskStatus.CREATED]: '任务已创建，等待执行',
          [TaskStatus.PENDING]: '任务已提交，等待资源分配',
          [TaskStatus.RUNNING]: '任务正在执行中，当前进度 65%',
          [TaskStatus.SUCCESS]: '任务执行成功，结果已生成',
          [TaskStatus.ERROR]: '任务执行失败：输入数据格式不正确',
          [TaskStatus.CANCELLED]: '任务已被用户取消',
          [TaskStatus.UNKNOWN]: '无法确定任务状态'
        };
        baseData.message = messageMap[this.selectedStatus];
      }

      if (this.hasResultResource && this.selectedStatus === TaskStatus.SUCCESS) {
        baseData.resultResourceId = `resource-${Date.now()}`;
        baseData.result = {
          rows: 1250,
          columns: 8,
          size: '2.3MB'
        };
      }

      this.taskInfo = new TaskInfo(baseData);
    },

    /**
     * 模拟状态变化
     */
    simulateStatusChange() {
      let index = 0;
      const interval = setInterval(() => {
        if (index >= this.statusSequence.length) {
          clearInterval(interval);
          return;
        }
        
        this.selectedStatus = this.statusSequence[index];
        this.hasMessage = true;
        this.hasResultResource = (this.selectedStatus === TaskStatus.SUCCESS);
        this.updateTaskInfo();
        
        index++;
      }, 2000);
    },

    /**
     * 清空任务信息
     */
    clearTaskInfo() {
      this.taskInfo = null;
      this.callbackResults = [];
    },

    /**
     * 模拟取消任务回调
     */
    async mockCancelTask(taskId) {
      this.addCallbackResult('info', `开始取消任务: ${taskId}`);
      
      // 模拟异步操作
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 随机成功或失败
      const success = Math.random() > 0.3; // 70% 成功率
      
      if (success) {
        this.addCallbackResult('success', `任务取消成功: ${taskId}`);
        // 更新任务状态为已取消
        this.selectedStatus = TaskStatus.CANCELLED;
        this.hasMessage = true;
        this.updateTaskInfo();
      } else {
        this.addCallbackResult('error', `任务取消失败: ${taskId} - 任务已完成或不存在`);
      }
      
      return success;
    },

    /**
     * 处理查看结果事件
     */
    handleViewResult(resourceId) {
      this.addCallbackResult('success', `查看结果请求: ${resourceId}`);
    },

    /**
     * 添加回调结果记录
     */
    addCallbackResult(type, message) {
      this.callbackResults.unshift({
        type,
        message,
        timestamp: new Date().toLocaleTimeString()
      });
      
      // 限制记录数量
      if (this.callbackResults.length > 10) {
        this.callbackResults = this.callbackResults.slice(0, 10);
      }
    }
  }
};
</script>

<style scoped>
.node-task-info-panel-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.control-group {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.control-group label {
  font-weight: 500;
  min-width: 100px;
}

.component-demo {
  margin-bottom: 24px;
}

.demo-container {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  background-color: #fff;
}

.callback-results {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 8px;
}

.results-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e1e4e8;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item small {
  color: #666;
  margin-left: auto;
}
</style> 