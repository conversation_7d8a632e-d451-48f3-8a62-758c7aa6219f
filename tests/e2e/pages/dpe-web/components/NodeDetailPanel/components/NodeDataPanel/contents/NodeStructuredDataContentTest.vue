<template>
  <div class="node-structured-data-content-test-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 data-testid="page-title">NodeStructuredDataContent 组件测试</h1>
      <p data-testid="page-description">
        测试结构化数据预览组件的各种功能，包括数据展示、分页、结构视图等
      </p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls" data-testid="test-controls">
      <a-card title="测试控制" size="small">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="测试资源">
              <a-select 
                v-model="selectedResourceId" 
                data-testid="resource-id-select"
                placeholder="选择测试资源"
              >
                <a-select-option 
                  v-for="resource in testResources" 
                  :key="resource.id" 
                  :value="resource.id"
                >
                  {{ resource.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="操作">
              <a-button-group>
                <a-button 
                  type="primary" 
                  data-testid="apply-test-btn"
                  @click="applyTestConfig"
                  :loading="applying"
                >
                  应用配置
                </a-button>
                <a-button 
                  data-testid="reset-test-btn"
                  @click="resetTestConfig"
                >
                  重置
                </a-button>
              </a-button-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 组件展示区域 -->
    <div class="component-container" data-testid="component-container">
      <a-card title="NodeStructuredDataContent 组件展示" size="small">
        <div class="component-wrapper" data-testid="component-wrapper">
          <NodeStructuredDataContent
            v-if="currentResourceId"
            :resource-id="currentResourceId"
            :key="`${currentResourceId}-${testKey}`"
            @error="handleComponentError"
            data-testid="structured-data-content"
          />
          <div v-else class="no-resource-message" data-testid="no-resource-message">
            <a-empty description="请先选择一个测试资源" />
          </div>
        </div>
      </a-card>
    </div>

    <!-- 测试信息面板 -->
    <div class="test-info" data-testid="test-info">
      <a-card title="测试信息" size="small">
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="当前资源ID">
            <code data-testid="current-resource-id">{{ currentResourceId || '未选择' }}</code>
          </a-descriptions-item>
          <a-descriptions-item label="资源类型">
            <a-tag data-testid="resource-type">{{ getResourceType(currentResourceId) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="组件状态">
            <a-tag 
              :color="componentError ? 'red' : 'green'" 
              data-testid="component-status"
            >
              {{ componentError ? '错误' : '正常' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="错误信息" v-if="componentError">
            <code data-testid="error-message">{{ componentError }}</code>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 操作日志 -->
    <div class="operation-log" data-testid="operation-log">
      <a-card title="操作日志" size="small">
        <div class="log-container" data-testid="log-container">
          <div 
            v-for="(log, index) in operationLogs" 
            :key="index"
            class="log-item"
            :data-testid="`log-item-${index}`"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-level" :class="`log-${log.level}`">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <div class="log-actions">
          <a-button size="small" @click="clearLogs" data-testid="clear-logs-btn">
            清空日志
          </a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script>
import NodeStructuredDataContent from '@/pages/dpe-web/components/NodeDetailPanel/components/NodeDataPanel/contents/NodeStructuredDataContent.vue';

/**
 * NodeStructuredDataContentTest 测试页面
 * @description 用于测试 NodeStructuredDataContent 组件的各种功能和场景
 */
export default {
  name: 'NodeStructuredDataContentTest',
  
  components: {
    NodeStructuredDataContent
  },

  data() {
    return {
      // 测试配置
      selectedResourceId: '',
      currentResourceId: '',
      testKey: 0,
      applying: false,
      
      // 组件状态
      componentError: null,
      
      // 测试资源列表 - 每个资源对应一种测试场景
      testResources: [
        { id: 'test-resource-normal', name: '正常数据资源 (标准表格数据)' },
        { id: 'test-resource-empty', name: '空数据资源 (无数据记录)' },
        { id: 'test-resource-large', name: '大数据量资源 (分页测试)' },
        { id: 'test-resource-error', name: '错误数据资源 (异常处理)' }
      ],
      
      // 操作日志
      operationLogs: []
    };
  },

  mounted() {
    this.addLog('info', '测试页面已加载');
    // 在测试环境中不自动应用配置，让测试脚本控制
    if (process.env.NODE_ENV !== 'test' && this.testResources.length > 0) {
      this.selectedResourceId = this.testResources[0].id;
      this.applyTestConfig();
    }
  },

  methods: {
    /**
     * 应用测试配置
     * @returns {Promise<void>}
     */
    async applyTestConfig() {
      if (!this.selectedResourceId) {
        this.$message.warning('请先选择一个测试资源');
        return;
      }

      this.applying = true;
      this.componentError = null;

      try {
        this.addLog('info', `应用测试配置: 资源=${this.selectedResourceId}`);
        
        // 更新当前资源ID，触发组件重新加载
        this.currentResourceId = this.selectedResourceId;
        this.testKey++;
        
        // 模拟应用配置的延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        this.addLog('success', '测试配置已应用');
        this.$message.success('测试配置已应用');
        
      } catch (error) {
        this.addLog('error', `应用配置失败: ${error.message}`);
        this.$message.error('应用配置失败');
      } finally {
        this.applying = false;
      }
    },

    /**
     * 重置测试配置
     */
    resetTestConfig() {
      this.selectedResourceId = '';
      this.currentResourceId = '';
      this.componentError = null;
      this.testKey++;
      
      this.addLog('info', '测试配置已重置');
      this.$message.info('测试配置已重置');
    },

    /**
     * 处理组件错误
     * @param {Error} error - 错误对象
     */
    handleComponentError(error) {
      this.componentError = error.message || '组件发生未知错误';
      this.addLog('error', `组件错误: ${this.componentError}`);
      console.error('NodeStructuredDataContent 组件错误:', error);
    },

    /**
     * 获取资源类型描述
     * @param {String} resourceId - 资源ID
     * @returns {String} 资源类型描述
     */
    getResourceType(resourceId) {
      if (!resourceId) return '未选择';
      
      const typeMap = {
        'test-resource-normal': '正常数据',
        'test-resource-empty': '空数据',
        'test-resource-large': '大数据量',
        'test-resource-error': '错误数据'
      };
      
      return typeMap[resourceId] || '未知类型';
    },

    /**
     * 添加操作日志
     * @param {String} level - 日志级别 (info, success, warning, error)
     * @param {String} message - 日志消息
     */
    addLog(level, message) {
      const timestamp = new Date();
      const timeString = timestamp.toLocaleTimeString();
      
      this.operationLogs.unshift({
        time: timeString,
        level,
        message,
        timestamp
      });
      
      // 保持最多50条日志
      if (this.operationLogs.length > 50) {
        this.operationLogs = this.operationLogs.slice(0, 50);
      }
    },

    /**
     * 清空操作日志
     */
    clearLogs() {
      this.operationLogs = [];
      this.addLog('info', '操作日志已清空');
    }
  }
};
</script>

<style scoped>
.node-structured-data-content-test-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  color: #1890ff;
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.test-controls {
  margin-bottom: 24px;
}

.component-container {
  margin-bottom: 24px;
}

.component-wrapper {
  min-height: 600px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 16px;
}

.no-resource-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.test-info {
  margin-bottom: 24px;
}

.operation-log {
  margin-bottom: 24px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
}

.log-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}

.log-info {
  background-color: #e6f7ff;
  color: #1890ff;
}

.log-success {
  background-color: #f6ffed;
  color: #52c41a;
}

.log-warning {
  background-color: #fff7e6;
  color: #fa8c16;
}

.log-error {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.log-message {
  flex: 1;
  color: #333;
}

.log-actions {
  margin-top: 12px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-structured-data-content-test-page {
    padding: 16px;
  }
  
  .component-wrapper {
    min-height: 400px;
    padding: 12px;
  }
}
</style> 