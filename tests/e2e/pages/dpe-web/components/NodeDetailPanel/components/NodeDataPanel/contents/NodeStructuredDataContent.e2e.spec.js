/**
 * NodeStructuredDataContent 组件端到端测试
 * @description 测试结构化数据预览组件的完整功能，包括数据展示、分页、结构视图等
 */
import { test, expect } from '@playwright/test';
import { NodeDetailPanelApiMocker, MOCK_DATA_SCENARIOS } from '../../../helpers/api-mock-manager.js';

/**
 * 强制移除webpack覆盖层
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 */
async function forceRemoveWebpackOverlay(page) {
  try {
    await page.evaluate(() => {
      const overlays = document.querySelectorAll('iframe[id*="webpack-dev-server-client-overlay"], #webpack-dev-server-client-overlay, .webpack-dev-server-client-overlay');
      overlays.forEach(overlay => {
        overlay.remove();
      });
      
      // 也移除可能的错误覆盖层
      const errorOverlays = document.querySelectorAll('[id*="webpack"], [class*="webpack"]');
      errorOverlays.forEach(overlay => {
        if (overlay.style) {
          overlay.style.display = 'none';
          overlay.style.visibility = 'hidden';
          overlay.style.opacity = '0';
          overlay.style.pointerEvents = 'none';
        }
      });
    });
  } catch (e) {
    console.log('Force remove overlay failed:', e.message);
  }
}

/**
 * 等待webpack热更新覆盖层消失
 * @param {import('@playwright/test').Page} page 
 * @param {number} timeout 
 */
async function waitForWebpackOverlayToDisappear(page, timeout = 10000) {
  try {
    // 等待覆盖层元素消失
    await page.waitForSelector('.webpack-dev-server-client-overlay', { state: 'hidden', timeout });
  } catch (error) {
    // 如果覆盖层不存在或已经消失，这是正常的
    console.log('Webpack overlay not found or already hidden');
  }
  
  // 强制移除可能残留的覆盖层
  await forceRemoveWebpackOverlay(page);
}

/**
 * 等待元素可点击
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 * @param {string} selector - 选择器
 * @param {number} timeout - 超时时间
 */
async function waitForElementClickable(page, selector, timeout = 15000) {
  await page.waitForSelector(selector, { state: 'visible', timeout });
  await page.waitForSelector(selector, { state: 'attached', timeout });
  
  // 等待元素不被覆盖层遮挡
  await page.waitForFunction((sel) => {
    const element = document.querySelector(sel);
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const topElement = document.elementFromPoint(centerX, centerY);
    
    return element === topElement || element.contains(topElement);
  }, selector, { timeout: 5000 }).catch(() => {
    console.log(`Element ${selector} might be covered, will try force click`);
  });
}

/**
 * 安全点击元素
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 * @param {string} selector - 选择器
 * @param {object} options - 点击选项
 */
async function safeClick(page, selector, options = {}) {
  // 等待覆盖层消失
  await waitForWebpackOverlayToDisappear(page);
  
  // 等待元素可点击
  await waitForElementClickable(page, selector);
  
  const element = page.locator(selector);
  
  try {
    // 尝试正常点击
    await element.click({ timeout: 8000, ...options });
  } catch (e) {
    console.log(`Normal click failed for ${selector}: ${e.message}, trying force click...`);
    try {
      // 强制点击
      await element.click({ force: true, timeout: 8000, ...options });
    } catch (e2) {
      console.log(`Force click also failed for ${selector}: ${e2.message}, trying evaluate click...`);
      // 最后尝试：通过JavaScript点击
      await page.evaluate((sel) => {
        const element = document.querySelector(sel);
        if (element) {
          element.click();
        }
      }, selector);
    }
  }
}

/**
 * 增强的Ant Design Vue下拉选择功能
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 * @param {string} selectSelector - 下拉选择器的选择器
 * @param {string} optionText - 要选择的选项文本
 */
async function selectDropdownOption(page, selectSelector, optionText) {
  console.log(`Selecting option "${optionText}" from ${selectSelector}`);
  
  // 确保覆盖层已移除
  await waitForWebpackOverlayToDisappear(page);
  
  // 首先关闭任何可能已经打开的下拉菜单
  await page.evaluate(() => {
    const openDropdowns = document.querySelectorAll('.ant-select-dropdown:not(.ant-select-dropdown-hidden)');
    openDropdowns.forEach(dropdown => {
      if (dropdown.style) {
        dropdown.style.display = 'none';
      }
      dropdown.classList.add('ant-select-dropdown-hidden');
    });
  });
  
  await page.waitForTimeout(300);
  
  // 点击下拉框触发器
  const selectTrigger = `${selectSelector} .ant-select-selection`;
  await safeClick(page, selectTrigger);
  
  // 等待当前下拉菜单出现，使用更宽松的选择器
  let dropdown;
  try {
    await page.waitForSelector('.ant-select-dropdown', { 
      state: 'visible', 
      timeout: 15000 
    });
    
    // 获取可见的下拉菜单（不是隐藏的）
    dropdown = page.locator('.ant-select-dropdown').locator('visible=true').first();
    await dropdown.waitFor({ state: 'visible', timeout: 5000 });
    
  } catch (e) {
    console.log(`Dropdown visibility check failed: ${e.message}`);
    // 如果等待失败，尝试查找任何下拉菜单
    dropdown = page.locator('.ant-select-dropdown').first();
  }
  
  // 等待下拉菜单完全加载
  await page.waitForTimeout(800);
  
  // 尝试多种选择器策略，在下拉菜单内查找选项
  const strategies = [
    // 策略1: 在下拉菜单内查找带title的选项
    `.ant-select-dropdown-menu-item[title="${optionText}"]`,
    // 策略2: 在下拉菜单内查找文本匹配的选项
    `.ant-select-dropdown-menu-item:has-text("${optionText}")`,
    // 策略3: 通过li元素查找
    `.ant-select-dropdown li:has-text("${optionText}")`,
    // 策略4: 通过role属性查找
    `.ant-select-dropdown [role="option"]:has-text("${optionText}")`,
    // 策略5: 更宽泛的文本搜索
    `.ant-select-dropdown *:has-text("${optionText}")`
  ];
  
  let selected = false;
  for (const strategy of strategies) {
    try {
      const options = page.locator(strategy);
      const optionCount = await options.count();
      
      if (optionCount > 0) {
        // 如果有多个匹配，选择第一个可见的
        for (let i = 0; i < optionCount; i++) {
          const option = options.nth(i);
          if (await option.isVisible()) {
            await option.click({ timeout: 5000 });
            selected = true;
            console.log(`Successfully selected using strategy: ${strategy} (index ${i})`);
            break;
          }
        }
        if (selected) break;
      }
    } catch (e) {
      console.log(`Strategy ${strategy} failed: ${e.message}`);
      continue;
    }
  }
  
  if (!selected) {
    // 最后尝试：通过JavaScript查找并点击
    try {
      await page.evaluate((text) => {
        // 查找所有可能的选项元素
        const selectors = [
          '.ant-select-dropdown-menu-item',
          '.ant-select-dropdown li[role="option"]',
          '.ant-select-dropdown [role="option"]',
          '.ant-select-dropdown li',
          '.ant-select-dropdown div'
        ];
        
        for (const selector of selectors) {
          const options = Array.from(document.querySelectorAll(selector));
          const targetOption = options.find(option => {
            const textContent = option.textContent?.trim();
            const title = option.title;
            const innerText = option.innerText?.trim();
            
            return textContent === text || title === text || innerText === text;
          });
          
          if (targetOption) {
            // 确保元素可见
            const rect = targetOption.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              targetOption.click();
              return true;
            }
          }
        }
        
        throw new Error(`Option "${text}" not found in any selector`);
      }, optionText);
      selected = true;
      console.log(`Selected using JavaScript fallback`);
    } catch (e) {
      throw new Error(`Failed to select option "${optionText}": ${e.message}`);
    }
  }
  
  // 等待下拉菜单关闭
  await page.waitForTimeout(500);
  
  // 验证下拉菜单是否关闭
  try {
    await page.waitForSelector('.ant-select-dropdown', { 
      state: 'hidden', 
      timeout: 5000 
    });
  } catch (e) {
    console.log('Dropdown close timeout, trying to force close...');
    // 如果自动关闭失败，手动关闭
    await page.evaluate(() => {
      const dropdowns = document.querySelectorAll('.ant-select-dropdown');
      dropdowns.forEach(dropdown => {
        if (dropdown.style) {
          dropdown.style.display = 'none';
        }
        dropdown.classList.add('ant-select-dropdown-hidden');
      });
    });
  }
  
  // 额外等待确保状态稳定
  await page.waitForTimeout(500);
}

/**
 * 等待组件加载完成
 * @param {import('@playwright/test').Page} page - Playwright页面对象
 */
async function waitForComponentLoaded(page) {
  // 等待loading状态消失
  await page.waitForFunction(() => {
    const spinners = document.querySelectorAll('.ant-spin-spinning');
    return spinners.length === 0 || Array.from(spinners).every(spinner => 
      spinner.style.display === 'none' || !spinner.offsetParent
    );
  }, { timeout: 25000 });
  
  // 额外等待确保渲染完成
  await page.waitForTimeout(1000);
}

test.describe('NodeStructuredDataContent 组件测试', () => {
  let testPageUrl;

  test.beforeEach(async ({ page }) => {
    // 设置更长的超时时间
    test.setTimeout(90000);
    
    // 使用统一的API mock管理器设置拦截
    await NodeDetailPanelApiMocker.setupAllMocks(page, {
      enableStructuredData: true,
      enableTask: false, // NodeStructuredDataContent不需要任务API
      structuredDataOptions: {
        scenario: 'normal', // 默认使用normal场景
        enableErrorHandling: true,
        // 可以根据测试需要覆盖预设数据
        customData: {
          preview: MOCK_DATA_SCENARIOS.normal.preview,
          schema: MOCK_DATA_SCENARIOS.normal.schema
        }
      }
    });
    
    // 导航到测试页面
    testPageUrl = '/test-pages/NodeStructuredDataContentTest';
    await page.goto(testPageUrl);
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="page-title"]', { timeout: 20000 });
    await expect(page.locator('[data-testid="page-title"]')).toContainText('NodeStructuredDataContent 组件测试');
    
    // 等待覆盖层消失
    await waitForWebpackOverlayToDisappear(page);
    
    // 确保页面完全加载
    await page.waitForTimeout(2000);
  });

  test('应该正确渲染测试页面基本元素', async ({ page }) => {
    // 验证页面标题和描述
    await expect(page.locator('[data-testid="page-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="page-description"]')).toBeVisible();
    
    // 验证测试控制面板
    await expect(page.locator('[data-testid="test-controls"]')).toBeVisible();
    await expect(page.locator('[data-testid="resource-id-select"]')).toBeVisible();
    
    // 验证组件容器
    await expect(page.locator('[data-testid="component-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="component-wrapper"]')).toBeVisible();
    
    // 验证测试信息面板
    await expect(page.locator('[data-testid="test-info"]')).toBeVisible();
    await expect(page.locator('[data-testid="operation-log"]')).toBeVisible();
    
    // 截图验证页面初始状态
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-initial-state.png',
      fullPage: true 
    });
  });

  test('应该能够选择测试资源并应用配置', async ({ page }) => {
    // 选择正常数据资源
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '正常数据资源 (标准表格数据)');
    
    // 应用配置
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待应用配置的处理完成
    await page.waitForTimeout(3000);
    
    // 检查是否有错误信息
    const errorElement = page.locator('[data-testid="error-message"]');
    if (await errorElement.count() > 0) {
      const errorText = await errorElement.textContent();
      console.log(`Component error detected: ${errorText}`);
    }
    
    // 验证currentResourceId是否已设置
    const currentResourceId = page.locator('[data-testid="current-resource-id"]');
    await expect(currentResourceId).toContainText('test-resource-normal');
    
    // 检查组件包装器是否存在
    await expect(page.locator('[data-testid="component-wrapper"]')).toBeVisible();
    
    // 尝试等待组件出现，如果不出现则检查是否显示了"未选择"消息
    try {
      await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 15000 });
      // 验证组件已加载
      await expect(page.locator('[data-testid="structured-data-content"]')).toBeVisible();
    } catch (e) {
      // 如果组件没有出现，检查是否显示了"未选择"消息
      const noResourceMessage = page.locator('[data-testid="no-resource-message"]');
      if (await noResourceMessage.count() > 0) {
        console.log('Component not rendered, still showing "no resource" message');
        // 打印当前状态用于调试
        const currentId = await page.locator('[data-testid="current-resource-id"]').textContent();
        console.log(`Current resource ID: ${currentId}`);
        
        // 检查是否是vue响应式数据更新的问题，手动触发更新
        await page.evaluate(() => {
          const app = document.querySelector('#app').__vue__;
          if (app && app.$forceUpdate) {
            app.$forceUpdate();
          }
        });
        
        await page.waitForTimeout(2000);
        
        // 再次尝试等待组件
        await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 15000 });
      } else {
        throw e;
      }
    }
    
    // 验证组件已加载
    await expect(page.locator('[data-testid="structured-data-content"]')).toBeVisible();
    
    // 验证测试信息更新
    await expect(page.locator('[data-testid="current-resource-id"]')).toContainText('test-resource-normal');
    await expect(page.locator('[data-testid="resource-type"]')).toContainText('正常数据');
    await expect(page.locator('[data-testid="component-status"]')).toContainText('正常');
    
    // 截图验证组件加载状态
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-loaded.png',
      fullPage: true 
    });
  });

  test('应该正确显示数据表格和分页功能', async ({ page }) => {
    // 选择正常数据资源并应用配置
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '正常数据资源 (标准表格数据)');
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待组件加载
    await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 20000 });
    await waitForComponentLoaded(page);
    
    // 验证表格元素存在
    const tableLocator = page.locator('.ant-table-wrapper');
    await expect(tableLocator).toBeVisible();
    
    // 验证表格头部存在
    const tableHeaderLocator = page.locator('.ant-table-thead');
    await expect(tableHeaderLocator).toBeVisible();
    
    // 验证表格内容存在
    const tableBodyLocator = page.locator('.ant-table-tbody');
    await expect(tableBodyLocator).toBeVisible();
    
    // 验证分页组件存在
    const paginationLocator = page.locator('.ant-pagination');
    await expect(paginationLocator).toBeVisible();
    
    // 验证工具栏按钮
    const structureViewBtn = page.locator('button:has-text("结构视图")');
    await expect(structureViewBtn).toBeVisible();
    
    const refreshBtn = page.locator('button:has-text("刷新")');
    await expect(refreshBtn).toBeVisible();
    
    // 截图验证数据表格显示
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-data-table.png',
      fullPage: true 
    });
  });

  test('应该能够切换到结构视图并显示数据结构', async ({ page }) => {
    // 选择正常数据资源并应用配置
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '正常数据资源 (标准表格数据)');
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待组件加载
    await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 20000 });
    await waitForComponentLoaded(page);
    
    // 点击切换到结构视图
    await safeClick(page, 'button:has-text("结构视图")');
    
    // 等待结构视图加载
    await page.waitForSelector('.schema-view', { timeout: 15000 });
    
    // 验证结构视图表格存在
    const schemaTable = page.locator('.schema-view .ant-table-wrapper');
    await expect(schemaTable).toBeVisible();
    
    // 验证结构表格包含预期的列
    const fieldNameColumn = page.locator('.ant-table-thead th:has-text("字段名")');
    await expect(fieldNameColumn).toBeVisible();
    
    const dataTypeColumn = page.locator('.ant-table-thead th:has-text("数据类型")');
    await expect(dataTypeColumn).toBeVisible();
    
    // 验证按钮文本变为"数据视图"
    const dataViewBtn = page.locator('button:has-text("数据视图")');
    await expect(dataViewBtn).toBeVisible();
    
    // 截图验证结构视图
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-schema-view.png',
      fullPage: true 
    });
  });

  test('应该支持大数据量资源的分页功能', async ({ page }) => {
    // 选择大数据量资源
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '大数据量资源 (分页测试)');
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待组件加载
    await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 20000 });
    await waitForComponentLoaded(page);
    
    // 验证分页组件存在并显示总数
    const pagination = page.locator('.ant-pagination');
    await expect(pagination).toBeVisible();
    
    // 验证总数显示
    const totalInfo = page.locator('.ant-pagination-total-text');
    await expect(totalInfo).toBeVisible();
    
    // 截图验证分页功能
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-pagination.png',
      fullPage: true 
    });
  });

  test('应该正确处理空数据资源', async ({ page }) => {
    // 选择空数据资源
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '空数据资源 (无数据记录)');
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待组件加载
    await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 20000 });
    await waitForComponentLoaded(page);
    
    // 验证空状态显示
    const emptyState = page.locator('.ant-empty, .empty-state');
    await expect(emptyState.first()).toBeVisible();
    
    // 截图验证空数据状态
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-empty-state.png',
      fullPage: true 
    });
  });

  test('应该能够刷新数据', async ({ page }) => {
    // 选择正常数据资源并应用配置
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '正常数据资源 (标准表格数据)');
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待组件加载
    await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 20000 });
    await waitForComponentLoaded(page);
    
    // 点击刷新按钮
    await safeClick(page, 'button:has-text("刷新")');
    
    // 等待刷新完成
    await waitForComponentLoaded(page);
    
    // 验证表格仍然存在
    const table = page.locator('.ant-table-wrapper');
    await expect(table).toBeVisible();
    
    // 截图验证刷新后状态
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-refreshed.png',
      fullPage: true 
    });
  });

  test('应该能够重置测试配置', async ({ page }) => {
    // 先选择并应用一个资源配置
    await selectDropdownOption(page, '[data-testid="resource-id-select"]', '正常数据资源 (标准表格数据)');
    await safeClick(page, '[data-testid="apply-test-btn"]');
    
    // 等待组件加载
    await page.waitForSelector('[data-testid="structured-data-content"]', { timeout: 20000 });
    
    // 点击重置按钮
    await safeClick(page, '[data-testid="reset-test-btn"]');
    
    // 验证配置已重置
    await expect(page.locator('[data-testid="current-resource-id"]')).toContainText('未选择');
    await expect(page.locator('[data-testid="resource-type"]')).toContainText('未选择');
    await expect(page.locator('[data-testid="no-resource-message"]')).toBeVisible();
    
    // 截图验证重置状态
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-reset.png',
      fullPage: true 
    });
  });

  test('界面布局应该响应式适配', async ({ page }) => {
    // 测试桌面端布局
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.reload();
    await page.waitForSelector('[data-testid="page-title"]', { timeout: 20000 });
    await waitForWebpackOverlayToDisappear(page);
    
    // 截图桌面版本
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-desktop.png',
      fullPage: true 
    });
    
    // 测试平板端布局
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await page.waitForSelector('[data-testid="page-title"]', { timeout: 20000 });
    await waitForWebpackOverlayToDisappear(page);
    
    // 截图平板版本
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-tablet.png',
      fullPage: true 
    });
    
    // 测试移动端布局
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForSelector('[data-testid="page-title"]', { timeout: 20000 });
    await waitForWebpackOverlayToDisappear(page);
    
    // 截图移动端版本
    await page.screenshot({ 
      path: 'test-results/node-structured-data-content-mobile.png',
      fullPage: true 
    });
  });
}); 