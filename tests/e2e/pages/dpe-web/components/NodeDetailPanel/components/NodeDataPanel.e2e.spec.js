import { test, expect } from '@playwright/test';
import { NodeDetailPanelApiMocker } from '../helpers/api-mock-manager.js';

test.describe('NodeDataPanel 组件测试', () => {
  test.beforeEach(async ({ page }) => {
    // 使用统一的API mock管理器设置所有拦截
    await NodeDetailPanelApiMocker.setupAllMocks(page, {
      enableStructuredData: true,
      enableTask: false, // NodeDataPanel测试不需要任务API
      structuredDataOptions: {
        previewData: {
          total: 2,
          schema: [
            { name: 'col1', type: 'string', nullable: false, description: 'Column 1' },
            { name: 'col2', type: 'string', nullable: false, description: 'Column 2' }
          ],
          data: [
            { col1: 'data1', col2: 'data2' },
            { col1: 'data3', col2: 'data4' }
          ]
        },
        schemaData: [
          { name: 'col1', type: 'string', nullable: false, description: 'Column 1' },
          { name: 'col2', type: 'string', nullable: false, description: 'Column 2' }
        ]
      }
    });

    await page.goto('test-routes/NodeDataPanelTest');
    // 等待页面完全加载
    await page.waitForSelector('[data-testid="node-data-panel"]');
  });

  test.afterEach(async ({ page }) => {
    // 清理API mock拦截
    await NodeDetailPanelApiMocker.clearAllMocks(page);
  });

  test('01-基本渲染和初始状态', async ({ page }) => {
    // 验证组件基本结构存在
    const dataPanel = page.locator('[data-testid="node-data-panel"]');
    await expect(dataPanel).toBeVisible();

    // 默认应该是空状态
    const emptyState = page.locator('[data-testid="empty-state"]');
    await expect(emptyState).toBeVisible();

    // 验证空状态文本
    await expect(emptyState.locator('text=暂无数据预览')).toBeVisible();
  });

  test('02-状态切换-加载状态', async ({ page }) => {
    // 点击下拉框
    await page.click('[data-testid="scenario-select"]');
    await page.waitForTimeout(500);
    
    // 选择加载状态 - 使用文本内容匹配
    await page.click('text=加载状态');
    await page.waitForTimeout(1000);
    
    // 验证加载状态显示
    const loadingState = page.locator('[data-testid="loading-state"]');
    await expect(loadingState).toBeVisible();
    
    // 验证加载文本 - 加载状态会显示任务消息
    await expect(loadingState.locator('text=正在处理数据...')).toBeVisible();
    
    // 验证加载动画存在
    await expect(loadingState.locator('.ant-spin')).toBeVisible();
  });

  test('03-状态切换-错误状态', async ({ page }) => {
    // 点击下拉框
    await page.click('[data-testid="scenario-select"]');
    await page.waitForTimeout(500);
    
    // 选择错误状态
    await page.click('text=错误状态');
    await page.waitForTimeout(1000);
    
    // 验证错误状态显示
    const errorState = page.locator('[data-testid="error-state"]');
    await expect(errorState).toBeVisible();
    
    // 验证错误信息 - 错误状态会显示任务的错误信息
    await expect(errorState.locator('text=数据处理失败')).toBeVisible();
    
    // 验证重新加载按钮存在
    const reloadButton = errorState.locator('button:has-text("重新加载")');
    await expect(reloadButton).toBeVisible();
    
    // 测试重新加载按钮点击
    await reloadButton.click();
    await page.waitForTimeout(500);
  });

  test('04-状态切换-结构化数据', async ({ page }) => {
    // 点击下拉框
    await page.click('[data-testid="scenario-select"]');
    await page.waitForTimeout(500);
    
    // 选择结构化数据
    await page.click('text=结构化数据');
    
    // 等待异步组件加载完成 - 增加更长的等待时间
    await page.waitForTimeout(3000);
    
    // 验证数据内容区域显示
    const dataContent = page.locator('[data-testid="data-content"]');
    await expect(dataContent).toBeVisible({ timeout: 10000 });
    
    // 验证结构化数据组件存在
    const structuredDataContent = page.locator('[data-testid="structured-data-content"]');
    await expect(structuredDataContent).toBeVisible({ timeout: 10000 });
  });

  test('05-状态切换-不支持的数据类型', async ({ page }) => {
    // 点击下拉框
    await page.click('[data-testid="scenario-select"]');
    await page.waitForTimeout(500);
    
    // 选择不支持的数据类型
    await page.click('text=不支持的数据类型');
    await page.waitForTimeout(1000);
    
    // 验证不支持类型状态显示
    const unsupportedState = page.locator('[data-testid="unsupported-data-type"]');
    await expect(unsupportedState).toBeVisible();
    
    // 验证提示信息
    await expect(unsupportedState.locator('text=暂不支持此数据类型')).toBeVisible();
    await expect(unsupportedState.locator('text=数据类型: unknown-type')).toBeVisible();
    
    // 验证重新检测按钮存在
    const redetectButton = unsupportedState.locator('button:has-text("重新检测")');
    await expect(redetectButton).toBeVisible();
    
    // 测试重新检测按钮点击
    await redetectButton.click();
    await page.waitForTimeout(500);
  });

  test('06-状态切换-无结果资源', async ({ page }) => {
    // 点击下拉框
    await page.click('[data-testid="scenario-select"]');
    await page.waitForTimeout(500);
    
    // 选择无结果资源
    await page.click('text=无结果资源');
    await page.waitForTimeout(1000);
    
    // 验证显示空状态（因为没有resultResourceId）
    const emptyState = page.locator('[data-testid="empty-state"]');
    await expect(emptyState).toBeVisible();
    await expect(emptyState.locator('text=暂无数据预览')).toBeVisible();
  });

  test('07-状态流转测试', async ({ page }) => {
    const scenarios = [
      { value: '空状态', expected: 'empty-state' },
      { value: '加载状态', expected: 'loading-state' },
      { value: '错误状态', expected: 'error-state' },
      { value: '结构化数据', expected: 'structured-data-content' },
      { value: '不支持的数据类型', expected: 'unsupported-data-type' }
    ];

    for (const scenario of scenarios) {
      // 点击下拉框
      await page.click('[data-testid="scenario-select"]');
      await page.waitForTimeout(500);
      
      // 选择场景 - 使用文本匹配
      await page.click(`text=${scenario.value}`);
      
      // 对于结构化数据场景，需要更长的等待时间
      if (scenario.value === '结构化数据') {
        await page.waitForTimeout(3000);
      } else {
        await page.waitForTimeout(1000);
      }
      
      // 验证对应状态显示
      const element = page.locator(`[data-testid="${scenario.expected}"]`);
      await expect(element).toBeVisible({ timeout: 10000 });
    }
  });

  test('08-截图测试', async ({ page }) => {
    const scenarios = ['空状态', '加载状态', '错误状态', '结构化数据'];
    
    for (const scenario of scenarios) {
      // 点击下拉框
      await page.click('[data-testid="scenario-select"]');
      await page.waitForTimeout(500);
      
      // 选择场景
      await page.click(`text=${scenario}`);
      
      // 对于结构化数据场景，需要更长的等待时间
      if (scenario === '结构化数据') {
        await page.waitForTimeout(3000);
      } else {
        await page.waitForTimeout(1000);
      }
      
      // 截图
      await page.screenshot({
        path: `test-results/screenshots/NodeDataPanel-${scenario}.png`,
        fullPage: true
      });
    }
  });

  test('09-Props参数验证', async ({ page }) => {
    // 修改会话ID
    await page.fill('input[placeholder="输入会话ID"]', 'new-session-999');
    
    // 修改节点ID  
    await page.fill('input[placeholder="输入节点ID"]', 'new-node-888');
    
    // 验证状态信息显示更新
    const statusInfo = page.locator('.status-info pre');
    await expect(statusInfo).toContainText('new-session-999');
    await expect(statusInfo).toContainText('new-node-888');
  });

  test('10-组件容器样式验证', async ({ page }) => {
    const componentContainer = page.locator('[data-testid="component-container"]');
    
    // 验证容器存在且可见
    await expect(componentContainer).toBeVisible();
    
    // 验证组件占满容器
    const dataPanel = page.locator('[data-testid="node-data-panel"]');
    await expect(dataPanel).toBeVisible();
    
    // 验证容器有固定高度
    const containerBox = await componentContainer.boundingBox();
    expect(containerBox.height).toBeGreaterThan(400);
  });
}); 