/**
 * NodeConfigPanel 组件端到端测试
 * @description 测试 NodeConfigPanel 组件的各种功能和交互场景
 */
import { test, expect } from '@playwright/test';

test.describe('NodeConfigPanel 组件测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 导航到测试页面
    await page.goto('/dpe-web/components/NodeDetailPanel/components/NodeConfigPanelTest');
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="node-config-panel"]', { state: 'visible' });
  });

  test('应该正确显示页面标题和基本元素', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('h1')).toHaveText('NodeConfigPanel 组件测试');
    
    // 验证主要控制元素存在
    await expect(page.locator('[data-testid="validate-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="save-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="reset-btn"]')).toBeVisible();
    
    // 验证组件容器存在
    await expect(page.locator('[data-testid="node-config-panel"]')).toBeVisible();
    
    // 验证结果展示区域存在
    await expect(page.locator('[data-testid="form-data-display"]')).toBeVisible();
    await expect(page.locator('[data-testid="log-container"]')).toBeVisible();
  });

  test('数据加载器算子配置测试', async ({ page }) => {
    // 点击下拉选择器打开选项
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据加载器"选项（使用正确的CSS类名）
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据加载器")');
    
    // 等待表单更新
    await page.waitForTimeout(500);
    
    // 验证节点信息区域
    await expect(page.locator('h4:has-text("节点信息")')).toBeVisible();
    await expect(page.locator('input[placeholder="请输入节点显示名称"]')).toBeVisible();
    await expect(page.locator('textarea[placeholder="请输入节点描述信息"]')).toBeVisible();
    
    // 验证算子配置区域
    await expect(page.locator('h4:has-text("算子配置")')).toBeVisible();
    await expect(page.locator('input[placeholder="请输入文件路径"]')).toBeVisible();
    
    // 填写表单数据
    await page.fill('input[placeholder="请输入节点显示名称"]', '测试数据加载器节点');
    await page.fill('textarea[placeholder="请输入节点描述信息"]', '这是一个用于测试的数据加载器节点');
    await page.fill('input[placeholder="请输入文件路径"]', '/data/test.csv');
    
    // 选择文件格式 - 使用更准确的选择器定位文件格式字段
    const fileFormatSelector = page.locator('text=文件格式').locator('..').locator('..').locator('[role="combobox"]');
    await fileFormatSelector.click();
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("json")');
    
    // 设置编码格式
    await page.fill('input[placeholder*="编码格式"]', 'gbk');
    
    // 设置跳过行数
    await page.fill('input[role="spinbutton"]', '2');
    
    // 截图验证表单状态
    await page.screenshot({ path: 'test-results/node-config-panel-data-loader-form.png' });
    
    // 验证表单
    await page.click('[data-testid="validate-btn"]');
    
    // 等待验证完成 - 查找任何包含"验证"或"通过"的日志消息
    await page.waitForSelector('[data-testid="log-container"] .log-item', { timeout: 10000 });
    await page.waitForTimeout(1000); // 等待验证过程完成
    
    // 保存配置
    await page.click('[data-testid="save-btn"]');
    
    // 等待保存完成（成功或失败都可以）
    await page.waitForSelector('.log-item:has-text("保存")', { timeout: 15000 });
    
    // 截图验证最终状态
    await page.screenshot({ path: 'test-results/node-config-panel-data-loader-saved.png' });
  });

  test('数据转换器算子配置测试', async ({ page }) => {
    // 点击下拉选择器打开选项
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据转换器"选项
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据转换器")');
    
    // 等待表单更新
    await page.waitForTimeout(500);
    
    // 验证输入连接区域出现
    await expect(page.locator('h4:has-text("输入连接")')).toBeVisible();
    
    // 验证输入连接选择器存在 - 使用更准确的选择器
    await expect(page.locator('text=请选择 inputData 的上游节点').locator('..')).toBeVisible();
    
    // 填写基本信息
    await page.fill('input[placeholder="请输入节点显示名称"]', '测试数据转换器');
    await page.fill('textarea[placeholder="请输入节点描述信息"]', '数据清洗和转换操作');
    
    // 截图验证转换器表单布局
    await page.screenshot({ path: 'test-results/node-config-panel-transformer-layout.png' });
    
    // 验证表单（应该会失败，因为没有填写必填项）
    await page.click('[data-testid="validate-btn"]');
    
    // 等待验证结果
    await page.waitForTimeout(1000);
    
    // 截图验证验证失败状态
    await page.screenshot({ path: 'test-results/node-config-panel-transformer-validation-failed.png' });
  });

  test('数据连接器算子配置测试', async ({ page }) => {
    // 点击下拉选择器打开选项
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据连接器"选项
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据连接器")');
    
    // 等待表单更新
    await page.waitForTimeout(500);
    
    // 验证有两个输入字段
    await expect(page.locator('h4:has-text("输入连接")')).toBeVisible();
    await expect(page.locator('text=请选择 leftData 的上游节点').locator('..')).toBeVisible();
    await expect(page.locator('text=请选择 rightData 的上游节点').locator('..')).toBeVisible();
    
    // 填写基本信息
    await page.fill('input[placeholder="请输入节点显示名称"]', '测试数据连接器');
    
    // 验证连接类型选择器 - 使用更精确的选择器
    await expect(page.locator('[role="combobox"]').filter({ hasText: 'inner' })).toBeVisible();
    
    // 更改连接类型
    const connectionTypeSelector = page.locator('text=连接类型').locator('..').locator('..').locator('[role="combobox"]');
    await connectionTypeSelector.click();
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("left")');
    
    // 截图验证连接器配置
    await page.screenshot({ path: 'test-results/node-config-panel-joiner-config.png' });
  });

  test('只读模式测试', async ({ page }) => {
    // 切换到只读模式
    await page.click('.ant-switch');
    
    // 等待状态更新
    await page.waitForTimeout(500);
    
    // 验证输入框都被禁用
    await expect(page.locator('input[placeholder="请输入节点显示名称"]')).toBeDisabled();
    await expect(page.locator('textarea[placeholder="请输入节点描述信息"]')).toBeDisabled();
    await expect(page.locator('input[placeholder="请输入文件路径"]')).toBeDisabled();
    
    // 截图验证只读模式
    await page.screenshot({ path: 'test-results/node-config-panel-readonly-mode.png' });
    
    // 尝试验证（应该仍然可以验证）
    await page.click('[data-testid="validate-btn"]');
    
    // 等待验证完成
    await page.waitForTimeout(1000);
    
    // 截图验证只读模式下的验证状态
    await page.screenshot({ path: 'test-results/node-config-panel-readonly-validation.png' });
  });

  test('表单重置功能测试', async ({ page }) => {
    // 填写一些数据
    await page.fill('input[placeholder="请输入节点显示名称"]', '将要被重置的名称');
    await page.fill('textarea[placeholder="请输入节点描述信息"]', '将要被重置的描述');
    await page.fill('input[placeholder="请输入文件路径"]', '/path/to/reset');
    
    // 验证数据已填写
    await expect(page.locator('input[placeholder="请输入节点显示名称"]')).toHaveValue('将要被重置的名称');
    
    // 点击重置按钮
    await page.click('[data-testid="reset-btn"]');
    
    // 等待重置完成
    await page.waitForTimeout(500);
    
    // 验证表单已重置
    await expect(page.locator('input[placeholder="请输入节点显示名称"]')).toHaveValue('测试数据加载器');
    await expect(page.locator('textarea[placeholder="请输入节点描述信息"]')).toHaveValue('测试用的从各种数据源加载数据');
    
    // 验证日志中有重置记录 - 使用更准确的选择器
    await expect(page.locator('[data-testid="log-container"]').locator('text=表单已重置')).toBeVisible({ timeout: 10000 });
    
    // 截图验证重置后的状态
    await page.screenshot({ path: 'test-results/node-config-panel-after-reset.png' });
  });

  test('表单数据双向绑定测试', async ({ page }) => {
    // 修改显示名称
    await page.fill('input[placeholder="请输入节点显示名称"]', '双向绑定测试');
    
    // 等待数据更新
    await page.waitForTimeout(300);
    
    // 验证表单数据显示区域已更新
    const formDataText = await page.locator('[data-testid="form-data-display"]').textContent();
    expect(formDataText).toContain('双向绑定测试');
    
    // 修改文件路径
    await page.fill('input[placeholder="请输入文件路径"]', '/test/binding.csv');
    
    // 等待数据更新
    await page.waitForTimeout(300);
    
    // 验证表单数据显示区域已更新
    const updatedFormDataText = await page.locator('[data-testid="form-data-display"]').textContent();
    expect(updatedFormDataText).toContain('/test/binding.csv');
    
    // 截图验证双向绑定效果
    await page.screenshot({ path: 'test-results/node-config-panel-two-way-binding.png' });
  });

  test('错误处理和用户反馈测试', async ({ page }) => {
    // 清空必填字段
    await page.fill('input[placeholder="请输入节点显示名称"]', '');
    
    // 尝试验证
    await page.click('[data-testid="validate-btn"]');
    
    // 等待验证完成
    await page.waitForTimeout(1000);
    
    // 验证显示了验证失败的日志 - 查找包含"失败"的日志消息
    await expect(page.locator('[data-testid="log-container"]').locator('text=失败')).toBeVisible();
    
    // 截图验证错误状态
    await page.screenshot({ path: 'test-results/node-config-panel-validation-error.png' });
    
    // 恢复显示名称
    await page.fill('input[placeholder="请输入节点显示名称"]', '错误处理测试');
    
    // 清空文件路径（数据加载器的必填项）
    await page.fill('input[placeholder="请输入文件路径"]', '');
    
    // 再次尝试验证
    await page.click('[data-testid="validate-btn"]');
    
    // 等待验证完成
    await page.waitForTimeout(1000);
    
    // 验证显示了自定义验证错误 - 使用更准确的选择器
    await expect(page.locator('[data-testid="log-container"]').locator('text=数据加载器必须指定文件路径')).toBeVisible({ timeout: 10000 });
    
    // 截图验证自定义验证错误
    await page.screenshot({ path: 'test-results/node-config-panel-custom-validation-error.png' });
  });

  test('算子类型切换测试', async ({ page }) => {
    // 验证初始状态
    await expect(page.locator('[data-testid="node-config-panel"]')).toBeVisible();
    
    // 点击下拉选择器打开选项
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据转换器"选项
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据转换器")');
    await page.waitForTimeout(500);
    
    // 验证输入连接区域出现
    await expect(page.locator('h4:has-text("输入连接")')).toBeVisible();
    
    // 切换到数据连接器
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据连接器"选项
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据连接器")');
    await page.waitForTimeout(500);
    
    // 验证连接配置区域出现
    await expect(page.locator('.ant-form-item-label:has-text("连接类型")')).toBeVisible();
    
    // 切换回数据加载器
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据加载器"选项
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据加载器")');
    await page.waitForTimeout(500);
    
    // 验证输入连接区域消失
    await expect(page.locator('h4:has-text("输入连接")')).not.toBeVisible();
    
    // 截图验证最终状态
    await page.screenshot({ path: 'test-results/node-config-panel-operator-switching.png' });
  });

  test('复杂表单配置测试', async ({ page }) => {
    // 点击下拉选择器打开选项
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    
    // 点击"数据连接器"选项进行复杂配置测试
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据连接器")');
    await page.waitForTimeout(500);
    
    // 填写完整的配置信息
    await page.fill('input[placeholder="请输入节点显示名称"]', '复杂配置测试节点');
    await page.fill('textarea[placeholder="请输入节点描述信息"]', '这是一个复杂配置的测试案例，包含多种类型的配置项');
    
    // 更改连接类型为 outer - 使用更准确的选择器
    const connectionTypeSelector = page.locator('text=连接类型').locator('..').locator('..').locator('[role="combobox"]');
    await connectionTypeSelector.click();
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("outer")');
    
    // 验证所有配置项都正确显示 - 使用label选择器
    await expect(page.locator('label', { hasText: '连接类型' })).toBeVisible();
    await expect(page.locator('label', { hasText: '连接键' })).toBeVisible();
    await expect(page.locator('label', { hasText: '列名后缀' })).toBeVisible();
    
    // 截图验证复杂表单布局
    await page.screenshot({ path: 'test-results/node-config-panel-complex-form.png' });
    
    // 验证和保存
    await page.click('[data-testid="validate-btn"]');
    await page.waitForTimeout(1000);
    
    await page.click('[data-testid="save-btn"]');
    await page.waitForTimeout(2000);
    
    // 截图验证最终完成状态
    await page.screenshot({ path: 'test-results/node-config-panel-complex-completed.png' });
  });

  test('上游节点选择功能测试', async ({ page }) => {
    // 切换到数据转换器，因为它有输入字段
    await page.click('[data-testid="operator-type-select"]');
    await page.waitForTimeout(300);
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("数据转换器")');
    await page.waitForTimeout(500);

    // 验证输入连接区域出现
    await expect(page.locator('h4:has-text("输入连接")')).toBeVisible();

    // 找到 inputData 对应的选择器并点击
    const inputDataSelector = page.locator('.inputs-section .ant-form-item:has-text("inputData") .ant-select');
    await page.waitForTimeout(200); // 增加短暂等待，确保组件完全渲染准备好交互
    await inputDataSelector.click();
    // 等待下拉菜单中的第一个选项可见，这隐含地等待了下拉菜单的出现和内容加载
    await page.waitForSelector('.ant-select-dropdown-menu-item:has-text("模拟上游节点 A")', { state: 'visible' });
    await page.waitForTimeout(300);

    // 选择模拟上游节点 A 和 B
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("模拟上游节点 A")');
    await page.waitForTimeout(100);
    await page.click('.ant-select-dropdown .ant-select-dropdown-menu-item:has-text("模拟上游节点 B")');
    await page.waitForTimeout(300);

    // 验证选择器中显示了选中的节点
    await expect(inputDataSelector).toContainText('模拟上游节点 A');
    await expect(inputDataSelector).toContainText('模拟上游节点 B');

    // 验证表单数据已更新以反映选择
    const formDataText = await page.locator('[data-testid="form-data-display"]').textContent();
    expect(formDataText).toContain(`"inputData": [
      "mock-upstream-node-1",
      "mock-upstream-node-2"
    ]`);

    // 截图验证选择状态
    await page.screenshot({ path: 'test-results/node-config-panel-upstream-selection.png' });
  });
}); 