<template>
  <div class="test-page">
    <div class="test-header">
      <h1>NodeDetailPanel 组件测试</h1>
      <div class="test-controls">
        <a-space>
          <a-button @click="switchTestScenario('normal')" :type="currentScenario === 'normal' ? 'primary' : 'default'">
            正常场景
          </a-button>
          <a-button @click="switchTestScenario('readonly')" :type="currentScenario === 'readonly' ? 'primary' : 'default'">
            只读场景
          </a-button>
          <a-button @click="switchTestScenario('task-running')" :type="currentScenario === 'task-running' ? 'primary' : 'default'">
            任务运行中
          </a-button>
          <a-button @click="switchTestScenario('task-completed')" :type="currentScenario === 'task-completed' ? 'primary' : 'default'">
            任务已完成
          </a-button>
          <a-button @click="switchTestScenario('long-content-scenario')" :type="currentScenario === 'long-content-scenario' ? 'primary' : 'default'">
            长内容场景
          </a-button>
          <a-button @click="resetFormData" type="dashed">重置数据</a-button>
        </a-space>
      </div>
    </div>

    <div class="test-content">
      <!-- 测试组件 -->
      <div class="component-container">
        <NodeDetailPanel
          ref="nodeDetailPanel"
          :session-id="testSessionId"
          :node-id="testNodeId"
          :operator-info="testOperatorInfo"
          :editable="testEditable"
          v-model="testNodeFormData"
          :callbacks="testCallbacks"
          :available-upstream-nodes="availableUpstreamNodes"
        />
      </div>
      
      <!-- 调试信息面板 -->
      <div class="debug-panel">
        <a-collapse :default-active-key="['data']">
          <a-collapse-panel key="data" header="当前数据状态">
            <pre>{{ JSON.stringify(testNodeFormData, null, 2) }}</pre>
          </a-collapse-panel>
          
          <a-collapse-panel key="scenario" header="测试场景配置">
            <pre>{{ JSON.stringify(scenarioConfig, null, 2) }}</pre>
          </a-collapse-panel>
          
          <a-collapse-panel key="logs" header="操作日志">
            <div class="log-container">
              <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
                <span class="log-time">{{ log.time }}</span>
                <span class="log-type" :class="log.type">{{ log.type }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import NodeDetailPanel from '@/pages/dpe-web/components/NodeDetailPanel/index.vue';
import { NodeDetailPanelCallbacks, createDefaultNodeFormData } from '@/pages/dpe-web/components/NodeDetailPanel/models.js';
import GraphNode from '@/pages/dpe-web/models/ExecutionGraph';
import ObjectMetadata from '@/pages/dpe-web/models/ObjectMetadata';
import OperatorInfo, { ConfigField } from '@/pages/dpe-web/models/OperatorInfo.js';

export default {
  name: 'NodeDetailPanelTest',
  
  components: {
    NodeDetailPanel
  },

  data() {
    return {
      // 当前测试场景
      currentScenario: 'normal',
      
      // 测试用例配置
      scenarioConfigs: {
        normal: {
          editable: true,
          sessionId: 'test-session-001',
          nodeId: 'test-node-001',
          operatorInfo: new OperatorInfo({
            type: 'DataProcessor',
            metadata: { displayName: '数据处理器', description: '用于处理结构化数据' },
            opConfigMetadata: [
              new ConfigField({ key: 'inputPath', schema: { type: 'string', title: '输入路径', description: '数据文件的输入路径', required: true } }),
              new ConfigField({ key: 'outputPath', schema: { type: 'string', title: '输出路径', description: '处理结果的输出路径', required: true } }),
              new ConfigField({ key: 'batchSize', schema: { type: 'number', title: '批处理大小', description: '每批处理的数据量', default: 1000, minimum: 1, maximum: 10000 } }),
              new ConfigField({ key: 'enableParallel', schema: { type: 'boolean', title: '启用并行处理', description: '是否启用多线程并行处理', default: false } })
            ],
            inputFields: []
          })
        },
        readonly: {
          editable: false,
          sessionId: 'test-session-002',
          nodeId: 'test-node-002',
          operatorInfo: new OperatorInfo({
            type: 'DataViewer',
            metadata: { displayName: '数据查看器', description: '只读数据查看组件' },
            opConfigMetadata: [
              new ConfigField({ key: 'dataSource', schema: { type: 'string', title: '数据源', description: '要查看的数据源路径', required: true } }),
              new ConfigField({ key: 'viewMode', schema: { type: 'string', title: '查看模式', enum: ['table', 'chart', 'raw'], default: 'table' } })
            ],
            inputFields: []
          })
        },
        'task-running': {
          editable: true,
          sessionId: 'test-session-003',
          nodeId: 'test-node-003',
          operatorInfo: new OperatorInfo({
            type: 'DataAnalyzer',
            metadata: { displayName: '数据分析器', description: '数据分析处理组件' },
            opConfigMetadata: [
              new ConfigField({ key: 'analysisType', schema: { type: 'string', title: '分析类型', enum: ['statistical', 'correlation', 'clustering'], default: 'statistical', required: true } }),
              new ConfigField({ key: 'parameters', schema: { type: 'object', title: '分析参数', properties: { threshold: { type: 'number', title: '阈值', default: 0.5 } } } })
            ],
            inputFields: ['inputData']
          })
        },
        'task-completed': {
          editable: true,
          sessionId: 'test-session-004',
          nodeId: 'test-node-004',
          operatorInfo: new OperatorInfo({
            type: 'DataTransformer',
            metadata: { displayName: '数据转换器', description: '数据格式转换组件' },
            opConfigMetadata: [
              new ConfigField({ key: 'sourceFormat', schema: { type: 'string', title: '源格式', enum: ['csv', 'json', 'xml'], default: 'csv', required: true } }),
              new ConfigField({ key: 'targetFormat', schema: { type: 'string', title: '目标格式', enum: ['csv', 'json', 'xml', 'parquet'], default: 'json', required: true } })
            ],
            inputFields: ['dataInput']
          })
        },
        'long-content-scenario': {
          editable: true,
          sessionId: 'test-session-005',
          nodeId: 'test-node-005',
          operatorInfo: new OperatorInfo({
            type: 'LongContentOperator',
            metadata: { displayName: '长内容操作符', description: '用于测试滚动条的长内容组件' },
            opConfigMetadata: Array.from({ length: 50 }).map((_, i) =>
              new ConfigField({
                key: `field${i + 1}`,
                schema: {
                  type: 'string',
                  title: `字段 ${i + 1}`,
                  description: `这是一个测试字段，用于生成长内容。这是第 ${i + 1} 个字段。`,
                  required: i % 3 === 0
                }
              })
            ),
            inputFields: []
          })
        }
      },
      
      // 操作日志
      operationLogs: [],
      
      // 测试数据
      testNodeFormData: null,

      // 可用的上游节点列表
      availableUpstreamNodes: [
        new GraphNode({
          id: 'upstream-node-A',
          metadata: new ObjectMetadata({ displayName: '上游节点 A (mock)' })
        }),
        new GraphNode({
          id: 'upstream-node-B',
          metadata: new ObjectMetadata({ displayName: '上游节点 B (mock)' })
        }),
        new GraphNode({
          id: 'upstream-node-C',
          metadata: new ObjectMetadata({ displayName: '上游节点 C (mock)' })
        })
      ]
    };
  },

  computed: {
    scenarioConfig() {
      return this.scenarioConfigs[this.currentScenario];
    },
    
    testSessionId() {
      return this.scenarioConfig.sessionId;
    },
    
    testNodeId() {
      return this.scenarioConfig.nodeId;
    },
    
    testOperatorInfo() {
      return this.scenarioConfig.operatorInfo;
    },
    
    testEditable() {
      return this.scenarioConfig.editable;
    },
    
    testCallbacks() {
      // 创建一个实现了所有方法的回调类实例
      const callbacks = new NodeDetailPanelCallbacks();
      
      // 重写基类的抽象方法
      callbacks.validate = this.handleValidate;
      callbacks.save = this.handleSave;
      callbacks.runTask = this.handleRunTask;
      callbacks.cancelTask = this.handleCancelTask;
      
      return callbacks;
    }
  },

  created() {
    this.initializeTestData();
  },

  methods: {
    /**
     * 初始化测试数据
     */
    initializeTestData() {
      this.testNodeFormData = createDefaultNodeFormData({
        metadata: {
          displayName: `测试节点 (${this.currentScenario})`,
          description: `这是一个用于 ${this.currentScenario} 场景的测试节点。`
        },
        // 根据当前算子类型初始化 opConfigs 和 opInputs
        opConfigs: this.testOperatorInfo.opConfigMetadata ? 
          this.testOperatorInfo.opConfigMetadata.reduce((acc, field) => {
            if (field.schema && field.schema.default !== undefined) acc[field.key] = field.schema.default;
            return acc;
          }, {}) : {},
        opInputs: this.testOperatorInfo.inputFields ? 
          this.testOperatorInfo.inputFields.reduce((acc, field) => {
            acc[field] = [];
            return acc;
          }, {}) : {}
      });
      this.addLog('info', '测试数据已初始化');
    },

    /**
     * 切换测试场景
     */
    async switchTestScenario(scenario) {
      this.currentScenario = scenario;
      this.initializeTestData(); // 重新初始化数据以匹配新场景的 operatorInfo
      this.addLog('info', `切换到测试场景: ${scenario}`);
      
      // 等待DOM更新后，触发NodeDetailPanel组件获取最新任务信息
      await this.$nextTick();
      if (this.$refs.nodeDetailPanel && this.$refs.nodeDetailPanel.fetchLatestTaskInfo) {
        try {
          await this.$refs.nodeDetailPanel.fetchLatestTaskInfo();
        } catch (error) {
          console.warn('触发fetchLatestTaskInfo失败:', error);
        }
      }
    },

    /**
     * 重置表单数据
     */
    resetFormData() {
      this.testNodeFormData = createDefaultNodeFormData();
      this.addLog('info', '表单数据已重置');
    },

    /**
     * 处理验证回调
     */
    async handleValidate(formData) {
      this.addLog('info', '执行表单验证');
      await this.delay(500);
      
      if (!formData.opConfigs || Object.keys(formData.opConfigs).length === 0) {
        this.addLog('warn', '验证失败: 配置数据为空');
        return false;
      }
      
      this.addLog('success', '验证通过');
      return true;
    },

    /**
     * 处理保存回调
     */
    async handleSave(formData) {
      this.addLog('info', '执行保存操作');
      await this.delay(1000);
      
      // 模拟偶尔失败的情况
      if (Math.random() < 0.1) {
        this.addLog('error', '保存失败: 网络错误');
        throw new Error('保存失败: 网络错误');
      }
      
      this.addLog('success', '保存成功');
      return true;
    },

    /**
     * 处理运行任务回调
     */
    async handleRunTask(formData) {
      this.addLog('info', '启动任务执行');
      await this.delay(800);
      
      const taskInfo = {
        id: `task-${Date.now()}`,
        nodeId: this.testNodeId,
        sessionId: this.testSessionId,
        status: 'RUNNING',
        progress: 0,
        startTime: new Date().toISOString(),
        message: '任务正在执行中...',
        resultResourceId: null
      };
      
      this.addLog('success', `任务已启动: ${taskInfo.id}`);
      
      // 模拟任务进度更新
      this.simulateTaskProgress(taskInfo);
      
      return taskInfo;
    },

    /**
     * 处理取消任务回调
     */
    async handleCancelTask(taskId) {
      this.addLog('info', `取消任务: ${taskId}`);
      await this.delay(500);
      this.addLog('warn', `任务已取消: ${taskId}`);
      return true;
    },

    /**
     * 模拟任务进度更新
     */
    simulateTaskProgress(taskInfo) {
      const progressInterval = setInterval(() => {
        taskInfo.progress += Math.random() * 20;
        
        if (taskInfo.progress >= 100) {
          taskInfo.progress = 100;
          taskInfo.status = 'SUCCESS';
          taskInfo.endTime = new Date().toISOString();
          taskInfo.message = '任务执行完成';
          taskInfo.resultResourceId = `structured-data-resource-123`;
          taskInfo.resultResourceType = 'structured';
          
          this.addLog('success', `任务完成: ${taskInfo.id}`);
          clearInterval(progressInterval);
        } else {
          taskInfo.message = `任务执行中... ${Math.floor(taskInfo.progress)}%`;
        }
      }, 1000);
      
      // 5% 概率模拟任务失败
      if (Math.random() < 0.05) {
        setTimeout(() => {
          taskInfo.status = 'ERROR';
          taskInfo.endTime = new Date().toISOString();
          taskInfo.message = '任务执行失败: 数据处理异常';
          taskInfo.error = {
            code: 'PROCESSING_ERROR',
            message: '数据处理过程中发生异常',
            details: 'Stack trace...'
          };
          
          this.addLog('error', `任务失败: ${taskInfo.id}`);
          clearInterval(progressInterval);
        }, 3000);
      }
    },

    /**
     * 添加操作日志
     */
    addLog(type, message) {
      this.operationLogs.unshift({
        time: new Date().toLocaleTimeString(),
        type,
        message
      });
      
      // 限制日志数量
      if (this.operationLogs.length > 100) {
        this.operationLogs = this.operationLogs.slice(0, 100);
      }
    },

    /**
     * 延迟工具函数
     */
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }
};
</script>

<style scoped>
.test-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.test-header h1 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
}

.test-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.component-container {
  flex: 2;
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
}

.debug-panel {
  flex: 1;
  padding: 16px;
  background: #f9f9f9;
  overflow: auto;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 4px;
  padding: 2px 8px;
  border-radius: 3px;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-type {
  font-weight: bold;
  margin-right: 8px;
  padding: 1px 6px;
  border-radius: 2px;
  font-size: 10px;
}

.log-type.info {
  background: #e6f7ff;
  color: #1890ff;
}

.log-type.success {
  background: #f6ffed;
  color: #52c41a;
}

.log-type.warn {
  background: #fff7e6;
  color: #fa8c16;
}

.log-type.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-message {
  color: #333;
}

pre {
  font-size: 12px;
  max-height: 200px;
  overflow: auto;
}
</style> 