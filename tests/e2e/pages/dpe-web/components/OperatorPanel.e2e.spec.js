/**
 * OperatorPanel 组件端到端测试
 * @description 测试算子面板组件的完整功能
 */
import { test, expect } from '@playwright/test';

test.describe('OperatorPanel 组件测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到测试页面
    await page.goto('/OperatorPanelTest.vue');
    
    // 等待页面加载完成
    await page.waitForSelector('[data-testid="page-title"]');
    await page.waitForSelector('[data-testid="operator-panel"]');
  });

  test('应该正确显示页面标题和基本元素', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('[data-testid="page-title"]')).toHaveText('OperatorPanel 组件测试页面');
    
    // 验证组件基本元素存在
    await expect(page.locator('[data-testid="operator-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="search-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="category-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="add-dataset-btn"]')).toBeVisible();
    await expect(page.locator('[data-testid="operators-list"]')).toBeVisible();
    
    // 验证算子总数显示正确
    await expect(page.locator('[data-testid="total-operators"]')).toHaveText('9');
  });

  test('应该正确显示所有算子项', async ({ page }) => {
    // 验证算子项存在
    const operatorItems = page.locator('[data-testid^="operator-item-"]');
    await expect(operatorItems).toHaveCount(9);
    
    // 验证特定算子项的名称和描述
    await expect(page.locator('[data-testid="operator-name-read-csv"]')).toHaveText('读取CSV');
    await expect(page.locator('[data-testid="operator-description-read-csv"]')).toContainText('从CSV文件读取数据');
    
    await expect(page.locator('[data-testid="operator-name-filter-data"]')).toHaveText('过滤数据');
    await expect(page.locator('[data-testid="operator-description-filter-data"]')).toContainText('根据指定条件过滤数据集');
  });

  test('应该能够进行搜索功能', async ({ page }) => {
    const searchInput = page.locator('[data-testid="search-input"]');
    
    // 搜索CSV相关算子
    await searchInput.fill('CSV');
    
    // 验证搜索结果
    const visibleOperators = page.locator('[data-testid^="operator-item-"]:visible');
    await expect(visibleOperators).toHaveCount(2); // read-csv 和 save-csv
    
    await expect(page.locator('[data-testid="operator-item-read-csv"]')).toBeVisible();
    await expect(page.locator('[data-testid="operator-item-save-csv"]')).toBeVisible();
    
    // 验证其他算子被隐藏
    await expect(page.locator('[data-testid="operator-item-filter-data"]')).not.toBeVisible();
    
    // 验证事件被正确触发
    await expect(page.locator('[data-testid="last-event-type"]')).toHaveText('search');
    await expect(page.locator('[data-testid="current-search"]')).toHaveText('CSV');
  });

  test('应该能够进行分类筛选', async ({ page }) => {
    const categorySelect = page.locator('[data-testid="category-select"]');
    
    // 选择数据源分类
    await categorySelect.click();
    await page.locator('[data-testid="category-data-source"]').click();
    
    // 验证只显示数据源类算子
    const visibleOperators = page.locator('[data-testid^="operator-item-"]:visible');
    await expect(visibleOperators).toHaveCount(2); // read-csv 和 read-json
    
    await expect(page.locator('[data-testid="operator-item-read-csv"]')).toBeVisible();
    await expect(page.locator('[data-testid="operator-item-read-json"]')).toBeVisible();
    
    // 验证其他分类的算子被隐藏
    await expect(page.locator('[data-testid="operator-item-filter-data"]')).not.toBeVisible();
    
    // 验证事件被正确触发
    await expect(page.locator('[data-testid="last-event-type"]')).toHaveText('category-change');
    await expect(page.locator('[data-testid="current-category"]')).toHaveText('data-source');
  });

  test('应该能够选择算子项', async ({ page }) => {
    // 点击第一个算子项
    await page.locator('[data-testid="operator-item-read-csv"]').click();
    
    // 验证算子被选中
    await expect(page.locator('[data-testid="last-event-type"]')).toHaveText('operator-select');
    await expect(page.locator('[data-testid="selected-operator-info"]')).toContainText('读取CSV (read-csv)');
    
    // 点击另一个算子项
    await page.locator('[data-testid="operator-item-filter-data"]').click();
    
    // 验证新算子被选中
    await expect(page.locator('[data-testid="selected-operator-info"]')).toContainText('过滤数据 (filter-data)');
  });

  test('应该能够处理添加数据集按钮点击', async ({ page }) => {
    // 点击添加数据集按钮
    await page.locator('[data-testid="add-dataset-btn"]').click();
    
    // 验证事件被正确触发
    await expect(page.locator('[data-testid="last-event-type"]')).toHaveText('add-dataset');
    await expect(page.locator('[data-testid="last-event-data"]')).toHaveText('dataset-button-clicked');
  });

  test('应该支持拖拽操作', async ({ page }) => {
    const operatorItem = page.locator('[data-testid="operator-item-read-csv"]');
    
    // 开始拖拽操作
    await operatorItem.dragTo(page.locator('.test-info-panel'));
    
    // 验证拖拽事件被触发
    await expect(page.locator('[data-testid="last-event-type"]')).toHaveText('operator-drag-end');
  });

  test('应该正确显示空状态', async ({ page }) => {
    const searchInput = page.locator('[data-testid="search-input"]');
    
    // 搜索一个不存在的算子
    await searchInput.fill('不存在的算子');
    
    // 验证显示空状态
    await expect(page.locator('[data-testid="empty-state"]')).toBeVisible();
    await expect(page.locator('[data-testid="empty-state"] p')).toHaveText('未找到匹配的算子');
    
    // 验证没有算子项显示
    const visibleOperators = page.locator('[data-testid^="operator-item-"]:visible');
    await expect(visibleOperators).toHaveCount(0);
  });

  test('应该支持组合搜索和分类筛选', async ({ page }) => {
    const categorySelect = page.locator('[data-testid="category-select"]');
    const searchInput = page.locator('[data-testid="search-input"]');
    
    // 先选择数据处理分类
    await categorySelect.click();
    await page.locator('[data-testid="category-data-process"]').click();
    
    // 再搜索"数据"关键词
    await searchInput.fill('数据');
    
    // 验证同时满足分类和搜索条件的算子显示
    const visibleOperators = page.locator('[data-testid^="operator-item-"]:visible');
    await expect(visibleOperators).toHaveCount(3); // filter-data, sort-data, group-by
    
    await expect(page.locator('[data-testid="operator-item-filter-data"]')).toBeVisible();
    await expect(page.locator('[data-testid="operator-item-sort-data"]')).toBeVisible();
    await expect(page.locator('[data-testid="operator-item-group-by"]')).toBeVisible();
  });

  test('应该能够重置测试状态', async ({ page }) => {
    // 先进行一些操作
    await page.locator('[data-testid="search-input"]').fill('测试');
    await page.locator('[data-testid="category-select"]').click();
    await page.locator('[data-testid="category-data-source"]').click();
    
    // 清除搜索关键词以确保算子可见
    await page.locator('[data-testid="search-input"]').clear();
    await page.locator('[data-testid="search-input"]').fill('CSV');
    
    // 等待筛选完成后再点击算子
    await page.waitForTimeout(500);
    await page.locator('[data-testid="operator-item-read-csv"]').click();
    
    // 点击重置状态按钮
    await page.locator('[data-testid="reset-test-state"]').click();
    
    // 验证状态被重置
    await expect(page.locator('[data-testid="current-category"]')).toHaveText('all');
    await expect(page.locator('[data-testid="current-search"]')).toHaveText('');
    await expect(page.locator('[data-testid="last-event-type"]')).toHaveText('');
    await expect(page.locator('[data-testid="selected-operator-info"]')).toHaveText('未选中任何算子');
    
    // 验证所有算子都重新显示
    const visibleOperators = page.locator('[data-testid^="operator-item-"]:visible');
    await expect(visibleOperators).toHaveCount(9);
  });

  test('应该正确显示分类选项', async ({ page }) => {
    const categorySelect = page.locator('[data-testid="category-select"]');
    
    // 点击分类选择器
    await categorySelect.click();
    
    // 验证所有分类选项存在
    await expect(page.locator('[data-testid="category-all"]')).toBeVisible();
    await expect(page.locator('[data-testid="category-data-source"]')).toBeVisible();
    await expect(page.locator('[data-testid="category-data-process"]')).toBeVisible();
    await expect(page.locator('[data-testid="category-machine-learning"]')).toBeVisible();
    await expect(page.locator('[data-testid="category-data-output"]')).toBeVisible();
    
    // 验证分类显示名称正确
    await expect(page.locator('[data-testid="category-all"]')).toHaveText('全部');
    await expect(page.locator('[data-testid="category-data-source"]')).toHaveText('数据源');
    await expect(page.locator('[data-testid="category-data-process"]')).toHaveText('数据处理');
    await expect(page.locator('[data-testid="category-machine-learning"]')).toHaveText('机器学习');
    await expect(page.locator('[data-testid="category-data-output"]')).toHaveText('数据输出');
  });

  test('应该支持键盘操作', async ({ page }) => {
    const searchInput = page.locator('[data-testid="search-input"]');
    
    // 聚焦搜索框
    await searchInput.focus();
    
    // 使用键盘输入
    await page.keyboard.type('线性');
    
    // 验证搜索结果
    await expect(page.locator('[data-testid="operator-item-linear-regression"]')).toBeVisible();
    
    // 清除搜索内容
    await page.keyboard.press('Control+a');
    await page.keyboard.press('Delete');
    
    // 验证所有算子重新显示
    const visibleOperators = page.locator('[data-testid^="operator-item-"]:visible');
    await expect(visibleOperators).toHaveCount(9);
  });
}); 