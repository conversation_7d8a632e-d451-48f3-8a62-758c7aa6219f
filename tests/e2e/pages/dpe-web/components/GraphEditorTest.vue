<template>
  <div class="graph-editor-test-page">
    <!-- 测试控制面板 -->
    <div class="test-controls">
      <h2>GraphEditor组件测试</h2>
      <div class="control-row">
        <a-button id="load-sample-data" @click="loadSampleData">加载示例数据</a-button>
        <a-button id="clear-data" @click="clearData">清空数据</a-button>
        <a-button id="export-data" @click="exportData">导出数据</a-button>
      </div>
      <div class="control-row">
        <a-button id="add-test-node" @click="addTestNode">添加测试节点</a-button>
        <a-button id="remove-first-node" @click="removeFirstNode">移除第一个节点</a-button>
        <a-button id="connect-two-nodes" @click="connectTwoNodes">建立连线</a-button>
        <a-button id="reset" @click="reset">重置</a-button>
        <a-button id="test-readonly-restrictions" @click="testReadonlyRestrictions">测试只读限制</a-button>
      </div>
      <div class="control-row">
        <a-button id="undo" @click="undo">撤销</a-button>
        <a-button id="redo" @click="redo">重做</a-button>
        <a-button id="test-port-connection" @click="testPortConnection">测试端口连接</a-button>
      </div>
      <div class="control-row">
        <a-button id="toggle-readonly" @click="toggleReadonly">
          {{ readonly ? '切换到编辑模式' : '切换到只读模式' }}
        </a-button>
        <a-button id="toggle-minimap" @click="toggleMinimap">
          {{ showMinimap ? '隐藏小地图' : '显示小地图' }}
        </a-button>
        <a-button id="toggle-toolbar" @click="toggleToolbar">
          {{ showToolbar ? '隐藏工具栏' : '显示工具栏' }}
        </a-button>
      </div>
      <div class="control-row">
        <a-button id="select-node-by-id" @click="selectNodeById">选择节点(node1)</a-button>
        <a-button id="clear-graph-selection" @click="clearGraphSelection">清除选择</a-button>
        <a-button id="test-new-events" @click="testNewEvents">测试新事件</a-button>
      </div>
      <div class="status-info">
        <span>节点数: {{ nodeCount }}</span>
        <span>边数: {{ edgeCount }}</span>
        <span>选中: {{ selectedInfo }}</span>
      </div>
    </div>

    <!-- GraphEditor组件容器 -->
    <div class="graph-editor-container">
      <GraphEditor 
        ref="graphEditor"
        :graph-data="graphData"
        :initial-graph-data="initialGraphData"
        :readonly="readonly"
        :show-minimap="showMinimap"
        :show-toolbar="showToolbar"
        @node-click="handleNodeClick"
        @edge-click="handleEdgeClick"
        @blank-click="handleBlankClick"
        @node-select="handleNodeSelect"
        @edge-select="handleEdgeSelect"
        @nodes-deselect="handleNodesDeselect"
        @selection-change="handleSelectionChange"
        @graph-data-change="handleGraphDataChange"
        @init-error="handleInitError"
        @node-add="handleNodeAdd"
        @node-remove="handleNodeRemove"
        @node-connect="handleNodeConnect"
      />
    </div>
  </div>
</template>

<script>
import GraphEditor from '@/pages/dpe-web/components/GraphEditor';
import RichExecutionGraph, { RichGraphNode, RichGraphEdge } from '@/pages/dpe-web/models/RichGraph';

/**
 * GraphEditor组件简化测试页面
 * 专注测试README.md中定义的属性、方法和事件
 */
export default {
  name: 'GraphEditorTest',
  components: {
    GraphEditor
  },
  
  data: function() {
    return {
      /**
       * 富执行图数据
       * @type {RichExecutionGraph}
       */
      graphData: null,
      
      /**
       * 初始富执行图数据
       * @type {RichExecutionGraph}
       */
      initialGraphData: null,
      
      /**
       * 只读模式
       * @type {Boolean}
       */
      readonly: false,
      
      /**
       * 显示小地图
       * @type {Boolean}
       */
      showMinimap: true,
      
      /**
       * 显示工具栏
       * @type {Boolean}
       */
      showToolbar: true,
      
      /**
       * 节点数量
       * @type {Number}
       */
      nodeCount: 0,
      
      /**
       * 边数量
       * @type {Number}
       */
      edgeCount: 0,
      
      /**
       * 选中信息
       * @type {String}
       */
      selectedInfo: '无'
    };
  },
  
  computed: {
    
  },
  
  mounted: function() {
    var self = this;
    
    // 暴露到全局，供测试脚本访问
    window.testApp = self;
    
    // 暴露模型类到全局，供测试脚本使用
    window.RichGraphNode = RichGraphNode;
    window.RichGraphEdge = RichGraphEdge;
    window.RichExecutionGraph = RichExecutionGraph;
    
    // 创建初始数据
    self.initialGraphData = new RichExecutionGraph({
      id: 'test-graph-initial',
      metadata: {
        displayName: '初始测试图形',
        description: '用于重置的初始状态'
      },
      nodes: {
        'initial-node': new RichGraphNode({
          id: 'initial-node',
          metadata: { displayName: '初始节点' },
          opType: 'InitialOperator',
          opConfig: {},
          displayAttr: {
            x: 150,
            y: 150,
            width: 120,
            height: 60,
            shape: 'rect',
            attrs: {
              body: {
                stroke: '#52C41A',
                strokeWidth: 2,
                fill: '#F6FFED',
                rx: 8,
                ry: 8
              },
              label: {
                fill: '#52C41A',
                fontSize: 12,
                textAnchor: 'middle',
                textVerticalAnchor: 'middle'
              }
            }
          }
        })
      },
      edges: []
    });
    
    // 初始化空的富执行图作为当前数据
    self.graphData = new RichExecutionGraph({
      id: 'test-graph-empty',
      metadata: {
        displayName: '测试图形编辑器',
        description: 'GraphEditor组件测试'
      },
      nodes: {},
      edges: []
    });
    
    console.log('GraphEditorTest mounted, 已创建初始数据');
    console.log('initialGraphData:', self.initialGraphData);
    console.log('graphData:', self.graphData);
    
    // 默认加载初始数据让用户立即看到效果
    setTimeout(function() {
      self.loadInitialData();
    }, 1000);
  },
  
  methods: {
    /**
     * 加载示例数据
     */
    loadSampleData: function() {
      try {
        // 创建符合设计图的示例数据 - 修复后使用label选择器
        var sampleData = {
          id: 'test-graph-sample',
          metadata: {
            displayName: '示例执行图',
            description: '基于设计图的示例数据'
          },
          nodes: {
            'node1': new RichGraphNode({
              id: 'node1',
              metadata: { displayName: '案件关联库' },
              opType: 'DataSource',
              opConfig: {},
              displayAttr: {
                x: 200,
                y: 150,
                width: 120,
                height: 60,
                shape: 'rect',
                attrs: {
                  body: {
                    stroke: '#1890FF',
                    strokeWidth: 2,
                    fill: '#E6F7FF',
                    rx: 8,
                    ry: 8
                  },
                  label: {
                    fill: '#1890FF',
                    fontSize: 12,
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle'
                  }
                }
              }
            }),
            'node2': new RichGraphNode({
              id: 'node2',
              metadata: { displayName: '数据清洗' },
              opType: 'DataCleaning',
              opConfig: {},
              displayAttr: {
                x: 450,
                y: 200,
                width: 120,
                height: 60,
                shape: 'rect',
                attrs: {
                  body: {
                    stroke: '#722ED1',
                    strokeWidth: 2,
                    fill: '#F9F0FF',
                    rx: 8,
                    ry: 8
                  },
                  label: {
                    fill: '#722ED1',
                    fontSize: 12,
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle'
                  }
                }
              }
            }),
            'node3': new RichGraphNode({
              id: 'node3',
              metadata: { displayName: '关联规则' },
              opType: 'AssociationRules',
              opConfig: {},
              displayAttr: {
                x: 450,
                y: 350,
                width: 120,
                height: 60,
                shape: 'rect',
                attrs: {
                  body: {
                    stroke: '#722ED1',
                    strokeWidth: 2,
                    fill: '#F9F0FF',
                    rx: 8,
                    ry: 8
                  },
                  label: {
                    fill: '#722ED1',
                    fontSize: 12,
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle'
                  }
                }
              }
            }),
            'node4': new RichGraphNode({
              id: 'node4',
              metadata: { displayName: '人脸识别数据库' },
              opType: 'FaceRecognitionDB',
              opConfig: {},
              displayAttr: {
                x: 600,
                y: 500,
                width: 140,
                height: 60,
                shape: 'rect',
                attrs: {
                  body: {
                    stroke: '#1890FF',
                    strokeWidth: 2,
                    fill: '#E6F7FF',
                    rx: 8,
                    ry: 8
                  },
                  label: {
                    fill: '#1890FF',
                    fontSize: 12,
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle'
                  }
                }
              }
            }),
            'node5': new RichGraphNode({
              id: 'node5',
              metadata: { displayName: '案件数据过程程序' },
              opType: 'DataProcess',
              opConfig: {},
              displayAttr: {
                x: 100,
                y: 650,
                width: 160,
                height: 60,
                shape: 'rect',
                attrs: {
                  body: {
                    stroke: '#1890FF',
                    strokeWidth: 2,
                    fill: '#E6F7FF',
                    rx: 8,
                    ry: 8
                  },
                  label: {
                    fill: '#1890FF',
                    fontSize: 12,
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle'
                  }
                }
              }
            })
          },
          edges: [
            new RichGraphEdge({
              source: 'node1',
              target: 'node2',
              targetConfig: 'input',
              displayAttr: {
                shape: 'edge',
                attrs: {
                  line: {
                    stroke: '#A2B1C3',
                    strokeWidth: 2,
                    targetMarker: {
                      name: 'block',
                      width: 12,
                      height: 8
                    }
                  }
                },
                labels: [{
                  attrs: {
                    label: {
                      text: '连接',
                      fontSize: 12,
                      fill: '#666666'
                    }
                  }
                }]
              }
            }),
            new RichGraphEdge({
              source: 'node2',
              target: 'node3',
              targetConfig: 'input',
              displayAttr: {
                shape: 'edge',
                attrs: {
                  line: {
                    stroke: '#A2B1C3',
                    strokeWidth: 2,
                    targetMarker: {
                      name: 'block',
                      width: 12,
                      height: 8
                    }
                  }
                },
                labels: [{
                  attrs: {
                    label: {
                      text: '连接',
                      fontSize: 12,
                      fill: '#666666'
                    }
                  }
                }]
              }
            }),
            new RichGraphEdge({
              source: 'node3',
              target: 'node4',
              targetConfig: 'input',
              displayAttr: {
                shape: 'edge',
                attrs: {
                  line: {
                    stroke: '#A2B1C3',
                    strokeWidth: 2,
                    targetMarker: {
                      name: 'block',
                      width: 12,
                      height: 8
                    }
                  }
                },
                labels: [{
                  attrs: {
                    label: {
                      text: '连接',
                      fontSize: 12,
                      fill: '#666666'
                    }
                  }
                }]
              }
            })
          ]
        };
        
        this.graphData = new RichExecutionGraph(sampleData);
        this.updateCounts();
        
        console.log('加载示例数据成功:', this.graphData);
        
        // 强制触发GraphEditor数据加载
        var self = this;
        this.$nextTick(function() {
          if (self.$refs.graphEditor) {
            console.log('强制触发GraphEditor数据加载');
            self.$refs.graphEditor.loadGraphData(self.graphData);
          }
        });
      } catch (error) {
        console.error('加载示例数据失败:', error);
      }
    },
    
    /**
     * 清空数据
     */
    clearData: function() {
      this.graphData = new RichExecutionGraph({
        id: 'test-graph-empty',
        metadata: {
          displayName: '空图',
          description: '清空后的图'
        },
        nodes: {},
        edges: []
      });
      this.updateCounts();
      this.selectedInfo = '无';
      
      // 使用GraphEditor的clearGraphData方法来正确清空数据并重置历史记录
      var self = this;
      this.$nextTick(function() {
        if (self.$refs.graphEditor) {
          console.log('调用GraphEditor的clearGraphData方法');
          self.$refs.graphEditor.clearGraphData(self.graphData);
        }
      });
    },
    
    /**
     * 导出数据
     */
    exportData: function() {
      if (this.$refs.graphEditor) {
        var exportedData = this.$refs.graphEditor.exportData();
        console.log('导出的数据:', exportedData);
        alert('数据已导出到控制台');
      }
    },
    
    /**
     * 切换只读模式
     */
    toggleReadonly: function() {
      this.readonly = !this.readonly;
    },
    
    /**
     * 切换小地图显示
     */
    toggleMinimap: function() {
      this.showMinimap = !this.showMinimap;
    },
    
    /**
     * 切换工具栏显示
     */
    toggleToolbar: function() {
      this.showToolbar = !this.showToolbar;
    },

    /**
     * 测试选择节点方法
     */
    selectNodeById: function() {
      try {
        if (this.$refs.graphEditor) {
          // 选择 node1 节点
          this.$refs.graphEditor.selectNodeById('node1');
          console.log('已选择节点 node1');
        }
      } catch (error) {
        console.error('选择节点失败:', error);
      }
    },

    /**
     * 测试清除选择方法
     */
    clearGraphSelection: function() {
      try {
        if (this.$refs.graphEditor) {
          this.$refs.graphEditor.clearGraphSelection();
          console.log('已清除所有选择');
        }
      } catch (error) {
        console.error('清除选择失败:', error);
      }
    },

    /**
     * 测试新事件功能
     */
    testNewEvents: function() {
      console.log('新事件测试功能已激活，请点击节点或边来测试新事件：');
      console.log('- node-select: 点击节点时触发');
      console.log('- edge-select: 点击边时触发');
      console.log('- nodes-deselect: 点击空白处时触发');
      alert('新事件测试功能已激活，请检查控制台输出。点击节点、边或空白处来测试新事件。');
    },
    
    /**
     * 更新计数信息
     * @private
     */
    updateCounts: function() {
      if (this.graphData) {
        this.nodeCount = Object.keys(this.graphData.nodes).length;
        this.edgeCount = this.graphData.edges.length;
      } else {
        this.nodeCount = 0;
        this.edgeCount = 0;
      }
    },
    
    /**
     * 处理节点点击事件
     * @param {Object} event - 点击事件对象
     */
    handleNodeClick: function(event) {
      console.log('节点点击:', event);
      if (event.cell && event.cell.id) {
        this.selectedInfo = '节点: ' + event.cell.id;
      }
    },
    
    /**
     * 处理边点击事件
     * @param {Object} event - 点击事件对象
     */
    handleEdgeClick: function(event) {
      console.log('边点击:', event);
      if (event.edge && event.edge.id) {
        this.selectedInfo = '边: ' + event.edge.id;
      }
    },
    
    /**
     * 处理空白区域点击事件
     * @param {Object} event - 点击事件对象
     */
    handleBlankClick: function(event) {
      console.log('空白点击:', event);
      this.selectedInfo = '无';
    },

    /**
     * 处理节点选中事件
     * @param {Object} event - 选中事件对象 {node, event}
     */
    handleNodeSelect: function(event) {
      console.log('节点选中:', event);
      if (event.node && event.node.id) {
        this.selectedInfo = '选中节点: ' + event.node.id;
      }
    },

    /**
     * 处理边选中事件
     * @param {Object} event - 选中事件对象 {edge, event}
     */
    handleEdgeSelect: function(event) {
      console.log('边选中:', event);
      if (event.edge && event.edge.id) {
        this.selectedInfo = '选中边: ' + event.edge.id;
      }
    },

    /**
     * 处理所有节点和边取消选中事件
     */
    handleNodesDeselect: function() {
      console.log('所有元素取消选中');
      this.selectedInfo = '无选中';
    },
    
    /**
     * 处理选择变化事件
     * @param {Object} event - 选择变化事件对象
     */
    handleSelectionChange: function(event) {
      console.log('选择变化:', event);
      if (event.selected && event.selected.length > 0) {
        this.selectedInfo = '选中 ' + event.selected.length + ' 个元素';
      } else {
        this.selectedInfo = '无';
      }
    },
    
    /**
     * 处理图形数据变化事件
     * @param {RichExecutionGraph} updatedGraph - 更新后的图形数据
     */
    handleGraphDataChange: function(updatedGraph) {
      console.log('图形数据变化:', updatedGraph);
      this.graphData = updatedGraph;
      this.updateCounts();
    },
    
    /**
     * 处理初始化错误事件
     * @param {Error} error - 错误对象
     */
    handleInitError: function(error) {
      console.error('GraphEditor初始化错误:', error);
      alert('GraphEditor初始化失败: ' + error.message);
    },

    /**
     * 移除第一个节点
     */
    removeFirstNode: function() {
      if (this.$refs.graphEditor) {
        const nodes = this.$refs.graphEditor.graph.getNodes();
        if (nodes.length > 0) {
          const firstNodeId = nodes[0].id;
          this.$refs.graphEditor.removeNode(firstNodeId);
          console.log(`尝试移除节点: ${firstNodeId}`);
        } else {
          console.log('没有节点可移除');
        }
      }
    },

    /**
     * 处理节点添加事件
     * @param {Object} event - 事件对象 { node }
     */
    handleNodeAdd: function(event) {
      console.log('节点添加:', event.node ? event.node.id : '未知节点');
    },
    
    /**
     * 处理节点移除事件
     * @param {Object} event - 事件对象 { nodeId }
     */
    handleNodeRemove: function(event) {
      console.log('节点移除:', event.nodeId);
    },
    
    /**
     * 处理节点连接事件
     * @param {Object} event - 事件对象 { edge, source, target }
     */
    handleNodeConnect: function(event) {
      console.log('节点连接:', event.edge ? `从 ${event.source} 到 ${event.target}` : '未知连接');
    },

    /**
     * 添加单个测试节点
     */
    addTestNode: function() {
      try {
        var newNodeId = 'test-node-' + Date.now();
        var randomOffset = Math.floor(Math.random() * 100);
        var newNode = new RichGraphNode({
          id: newNodeId,
          metadata: { displayName: '测试节点' },
          opType: 'TestOperator',
          opConfig: {},
          displayAttr: {
            x: 300 + randomOffset,
            y: 200 + randomOffset,
            width: 120,
            height: 60,
            shape: 'rect',
            attrs: {
              body: {
                stroke: '#52C41A',
                strokeWidth: 2,
                fill: '#F6FFED',
                rx: 8,
                ry: 8
              },
              label: {
                fill: '#52C41A',
                fontSize: 12,
                textAnchor: 'middle',
                textVerticalAnchor: 'middle'
              }
            },
            // 添加端口配置，支持端点拖拽连线
            ports: {
              groups: {
                top: {
                  position: 'top',
                  attrs: {
                    circle: {
                      r: 6,
                      magnet: true,
                      stroke: '#52C41A',
                      strokeWidth: 2,
                      fill: '#fff',
                      style: {
                        visibility: 'visible',
                        cursor: 'crosshair'
                      }
                    }
                  }
                },
                right: {
                  position: 'right', 
                  attrs: {
                    circle: {
                      r: 6,
                      magnet: true,
                      stroke: '#52C41A',
                      strokeWidth: 2,
                      fill: '#fff',
                      style: {
                        visibility: 'visible',
                        cursor: 'crosshair'
                      }
                    }
                  }
                },
                bottom: {
                  position: 'bottom',
                  attrs: {
                    circle: {
                      r: 6,
                      magnet: true,
                      stroke: '#52C41A',
                      strokeWidth: 2,
                      fill: '#fff',
                      style: {
                        visibility: 'visible',
                        cursor: 'crosshair'
                      }
                    }
                  }
                },
                left: {
                  position: 'left',
                  attrs: {
                    circle: {
                      r: 6,
                      magnet: true,
                      stroke: '#52C41A',
                      strokeWidth: 2,
                      fill: '#fff',
                      style: {
                        visibility: 'visible',
                        cursor: 'crosshair'
                      }
                    }
                  }
                }
              },
              items: [
                { group: 'top' },
                { group: 'right' },
                { group: 'bottom' },
                { group: 'left' }
              ]
            }
          }
        });

        if (this.$refs.graphEditor) {
          this.$refs.graphEditor.addNode(newNode);
          console.log('添加测试节点成功:', newNodeId);
        }
      } catch (error) {
        console.error('添加测试节点失败:', error);
      }
    },

    /**
     * 建立两个节点的连线（如果存在至少两个节点）
     */
    connectTwoNodes: function() {
      var nodes = this.getGraphEditor().graph.getNodes();
      if (nodes.length >= 2) {
        // 选择前两个节点建立连线
        this.connectNodes(nodes[0].id, nodes[1].id);
      } else {
        console.warn('节点数量不足，无法建立连线');
      }
    },
    
    /**
     * 连接两个特定节点
     * @param {string} sourceId 源节点ID
     * @param {string} targetId 目标节点ID
     */
    connectNodes: function(sourceId, targetId) {
      console.log(`尝试连接节点 ${sourceId} -> ${targetId}`);
      var edge = new RichGraphEdge({
        source: sourceId,
        target: targetId,
        targetConfig: 'input',
        displayAttr: {
          attrs: {
            line: {
              stroke: '#52C41A',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8
              }
            }
          }
        }
      });
      
      var graphEditor = this.getGraphEditor();
      if (graphEditor) {
        var result = graphEditor.addEdge(edge);
        console.log('连接节点结果:', result ? '成功' : '失败');
      }
    },

    /**
     * 重置（重新加载初始数据）
     */
    reset: function() {
      if (this.$refs.graphEditor) {
        this.$refs.graphEditor.reset();
        console.log('调用GraphEditor的reset方法');
      } else {
        // 兜底逻辑
        this.graphData = new RichExecutionGraph(this.initialGraphData);
        this.updateCounts();
        console.log('重置，重新加载初始数据');
      }
    },
    
    /**
     * 测试只读模式下的操作限制
     */
    testReadonlyRestrictions: function() {
      if (!this.readonly) {
        alert('请先切换到只读模式再测试');
        return;
      }

      try {
        // 尝试添加节点（应该失败）
        var testNode = new RichGraphNode({
          id: 'readonly-test-node',
          metadata: { displayName: '只读测试节点' },
          opType: 'TestOperator',
          opConfig: {}
        });

        if (this.$refs.graphEditor) {
          var result = this.$refs.graphEditor.addNode(testNode);
          if (result === null) {
            console.log('只读模式下正确阻止了添加节点');
          } else {
            console.error('只读模式下应该阻止添加节点！');
          }
        }
      } catch (error) {
        console.log('只读模式下添加节点被正确阻止:', error.message);
      }
    },

    /**
     * 测试端口连接
     */
    testPortConnection: function() {
      // 实现测试端口连接的逻辑
      console.log('测试端口连接');
    },

    /**
     * 撤销
     */
    undo: function() {
      if (this.$refs.graphEditor) {
        this.$refs.graphEditor.undo();
      }
    },

    /**
     * 重做
     */
    redo: function() {
      if (this.$refs.graphEditor) {
        this.$refs.graphEditor.redo();
      }
    },

    /**
     * 加载初始数据
     */
    loadInitialData: function() {
      this.graphData = new RichExecutionGraph(this.initialGraphData);
      this.updateCounts();
      console.log('加载初始数据');
    },

    getGraphEditor: function() {
      return this.$refs.graphEditor;
    }
  }
};
</script>

<style scoped>
.graph-editor-test-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.test-controls {
  background-color: #ffffff;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.test-controls h2 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #262626;
}

.control-row {
  margin-bottom: 12px;
  display: flex;
  gap: 8px;
}

.status-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666666;
  margin-top: 8px;
}

.graph-editor-container {
  flex: 1;
  background-color: #ffffff;
  position: relative;
}

/* 确保GraphEditor组件填满容器 */
.graph-editor-container >>> .graph-editor {
  width: 100%;
  height: 100%;
}

.graph-editor-container >>> .graph-container {
  width: 100%;
  height: 100%;
}
</style> 