<template>
  <div id="app" class="test-app">
    <h1 data-testid="page-title">OperatorPanel 组件测试页面</h1>
    
    <div class="test-container">
      <div class="operator-panel-container">
        <OperatorPanel
          :operators="testOperators"
          :selected-category="selectedCategory"
          :search-keyword="searchKeyword"
          @search="handleSearch"
          @category-change="handleCategoryChange"
          @operator-select="handleOperatorSelect"
          @operator-drag-start="handleOperatorDragStart"
          @operator-drag-end="handleOperatorDragEnd"
          @add-dataset="handleAddDataset"
        />
      </div>
      
      <div class="test-info-panel">
        <h3>测试信息</h3>
        <div class="info-section">
          <h4>当前状态:</h4>
          <p>选中分类: <span data-testid="current-category">{{ selectedCategory }}</span></p>
          <p>搜索关键词: <span data-testid="current-search">{{ searchKeyword }}</span></p>
          <p>算子总数: <span data-testid="total-operators">{{ testOperators.length }}</span></p>
        </div>
        
        <div class="info-section">
          <h4>最后触发的事件:</h4>
          <p>事件类型: <span data-testid="last-event-type">{{ lastEventType }}</span></p>
          <p>事件数据: <span data-testid="last-event-data">{{ lastEventDataStr }}</span></p>
        </div>
        
        <div class="info-section">
          <h4>选中的算子:</h4>
          <p v-if="selectedOperator" data-testid="selected-operator-info">
            {{ selectedOperator.metadata.displayName }} ({{ selectedOperator.type }})
          </p>
          <p v-else data-testid="selected-operator-info">未选中任何算子</p>
        </div>
        
        <div class="test-controls">
          <h4>测试控制:</h4>
          <a-button @click="clearTestData" data-testid="clear-test-data">清除测试数据</a-button>
          <a-button @click="resetTestState" data-testid="reset-test-state">重置状态</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import OperatorPanel from '@/pages/dpe-web/components/OperatorPanel';
import OperatorInfo from '@/pages/dpe-web/models/OperatorInfo.js';

export default {
  name: 'OperatorPanelTest',
  
  components: {
    OperatorPanel
  },
  
  data() {
    return {
      selectedCategory: 'all',
      searchKeyword: '',
      selectedOperator: null,
      lastEventType: '',
      lastEventData: null,
      
      // 测试用的算子数据
      testOperators: [
        new OperatorInfo({
          type: 'read-csv',
          metadata: {
            displayName: '读取CSV',
            description: '从CSV文件读取数据，支持自定义分隔符和编码',
            labels: { category: 'data-source' }
          }
        }),
        new OperatorInfo({
          type: 'read-json',
          metadata: {
            displayName: '读取JSON',
            description: '从JSON文件读取数据，支持嵌套结构解析',
            labels: { category: 'data-source' }
          }
        }),
        new OperatorInfo({
          type: 'filter-data',
          metadata: {
            displayName: '过滤数据',
            description: '根据指定条件过滤数据集中的记录',
            labels: { category: 'data-process' }
          }
        }),
        new OperatorInfo({
          type: 'sort-data',
          metadata: {
            displayName: '数据排序',
            description: '按照指定字段对数据进行排序',
            labels: { category: 'data-process' }
          }
        }),
        new OperatorInfo({
          type: 'group-by',
          metadata: {
            displayName: '数据分组',
            description: '按照指定字段对数据进行分组聚合',
            labels: { category: 'data-process' }
          }
        }),
        new OperatorInfo({
          type: 'linear-regression',
          metadata: {
            displayName: '线性回归',
            description: '使用线性回归算法训练预测模型',
            labels: { category: 'machine-learning' }
          }
        }),
        new OperatorInfo({
          type: 'decision-tree',
          metadata: {
            displayName: '决策树',
            description: '使用决策树算法进行分类或回归',
            labels: { category: 'machine-learning' }
          }
        }),
        new OperatorInfo({
          type: 'save-csv',
          metadata: {
            displayName: '保存CSV',
            description: '将数据保存为CSV格式文件',
            labels: { category: 'data-output' }
          }
        }),
        new OperatorInfo({
          type: 'save-database',
          metadata: {
            displayName: '保存到数据库',
            description: '将数据保存到指定的数据库表中',
            labels: { category: 'data-output' }
          }
        })
      ]
    };
  },
  
  computed: {
    lastEventDataStr() {
      if (!this.lastEventData) return '无';
      if (typeof this.lastEventData === 'string') return this.lastEventData;
      if (this.lastEventData.type) return this.lastEventData.type;
      return JSON.stringify(this.lastEventData);
    }
  },
  
  methods: {
    handleSearch(keyword) {
      this.lastEventType = 'search';
      this.lastEventData = keyword;
      this.searchKeyword = keyword;
    },
    
    handleCategoryChange(category) {
      this.lastEventType = 'category-change';
      this.lastEventData = category;
      this.selectedCategory = category;
    },
    
    handleOperatorSelect(operator) {
      this.lastEventType = 'operator-select';
      this.lastEventData = operator;
      this.selectedOperator = operator;
    },
    
    handleOperatorDragStart({ event, operator }) {
      this.lastEventType = 'operator-drag-start';
      this.lastEventData = operator;
    },
    
    handleOperatorDragEnd(event) {
      this.lastEventType = 'operator-drag-end';
      this.lastEventData = 'drag-end';
    },
    
    handleAddDataset() {
      this.lastEventType = 'add-dataset';
      this.lastEventData = 'dataset-button-clicked';
    },
    
    clearTestData() {
      this.lastEventType = '';
      this.lastEventData = null;
      this.selectedOperator = null;
    },
    
    resetTestState() {
      this.selectedCategory = 'all';
      this.searchKeyword = '';
      this.clearTestData();
    }
  }
};
</script>

<style scoped>
.test-app {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

h1 {
  color: #262626;
  margin-bottom: 24px;
  text-align: center;
}

.test-container {
  display: flex;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.operator-panel-container {
  flex-shrink: 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 600px;
}

.test-info-panel {
  flex: 1;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 600px;
  overflow-y: auto;
}

.test-info-panel h3 {
  margin-top: 0;
  color: #262626;
  border-bottom: 2px solid #e8e8e8;
  padding-bottom: 8px;
}

.info-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.info-section h4 {
  margin: 0 0 12px 0;
  color: #595959;
  font-size: 14px;
  font-weight: 600;
}

.info-section p {
  margin: 8px 0;
  color: #262626;
  font-size: 14px;
}

.info-section span {
  font-weight: 500;
  color: #1890ff;
}

.test-controls {
  margin-top: 24px;
}

.test-controls h4 {
  margin-bottom: 12px;
  color: #595959;
  font-size: 14px;
  font-weight: 600;
}

.test-controls .ant-btn {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style> 