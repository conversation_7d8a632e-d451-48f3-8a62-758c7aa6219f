<template>
  <div class="create-session-dialog-test-page">
    <div class="page-header">
      <h1>CreateSessionDialog 组件测试页面</h1>
      <p>展示创建会话对话框组件的各种功能和状态</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-button 
          type="primary" 
          @click="showDialog"
          data-testid="show-dialog-btn"
        >
          显示对话框
        </a-button>
        <a-button 
          @click="showDialogWithGraph"
          data-testid="preselect-graph-btn"
        >
          预选执行图
        </a-button>
        <a-button 
          @click="toggleReadonly"
          data-testid="toggle-readonly-btn"
        >
          {{ readonly ? '启用编辑' : '禁用编辑' }}
        </a-button>
      </a-space>
    </div>

    <div class="info-panel">
      <h3>测试信息：</h3>
      <div class="info-item">
        <strong>对话框可见:</strong> {{ isDialogVisible }}
      </div>
      <div class="info-item">
        <strong>只读模式:</strong> {{ readonly }}
      </div>
      <div class="info-item">
        <strong>最后提交的数据:</strong>
        <pre v-if="lastSubmittedData">{{ JSON.stringify(lastSubmittedData, null, 2) }}</pre>
        <span v-else>无</span>
      </div>
    </div>

    <CreateSessionDialog
      ref="createSessionDialog"
      :visible="isDialogVisible"
      :execution-graph-options="executionGraphOptions"
      :session-configs="sessionConfigs"
      @submit="handleSubmit"
      @cancel="handleCancel"
      @visibility-change="handleVisibilityChange"
    />
  </div>
</template>

<script>
import CreateSessionDialog from '@/pages/dpe-web/components/CreateSessionDialog';

/**
 * CreateSessionDialog组件测试页面
 * 用于演示和测试创建会话对话框的各种功能
 */
export default {
  name: 'CreateSessionDialogTest',
  
  components: {
    CreateSessionDialog
  },
  
  data() {
    return {
      /**
       * 对话框是否可见
       * @type {boolean}
       */
      isDialogVisible: false,
      
      /**
       * 是否只读模式
       * @type {boolean}
       */
      readonly: false,
      
      /**
       * 最后提交的数据
       * @type {Object|null}
       */
      lastSubmittedData: null,
      
      /**
       * 执行图选项列表
       * @type {Array<Object>}
       */
      executionGraphOptions: [
        {
          id: 'graph-001',
          metadata: {
            displayName: '图表1',
            description: '用于数据清洗和转换的执行图'
          }
        },
        {
          id: 'graph-002',
          metadata: {
            displayName: '机器学习流水线',
            description: '包含特征工程和模型训练的执行图'
          }
        },
        {
          id: 'graph-003',
          metadata: {
            displayName: '数据分析报告',
            description: '生成数据分析报告的执行图'
          }
        }
      ],
      
      /**
       * 会话配置
       * @type {Object}
       */
      sessionConfigs: {
        timeout: 300,
        maxRetries: 3,
        enableLogging: true,
        customConfig: {
          theme: 'default',
          language: 'zh-CN'
        }
      }
    };
  },
  
  methods: {
    /**
     * 显示对话框
     */
    showDialog() {
      this.isDialogVisible = true;
    },
    
    /**
     * 显示对话框并预选执行图
     */
    showDialogWithGraph() {
      this.isDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.createSessionDialog.show({
          id: 'graph-001'
        });
      });
    },
    
    /**
     * 切换只读模式
     */
    toggleReadonly() {
      this.readonly = !this.readonly;
    },
    
    /**
     * 处理提交事件
     * @param {Object} data - 提交的数据
     */
    handleSubmit(data) {
      this.lastSubmittedData = data;
      this.isDialogVisible = false;
      
      // 模拟成功提示
      this.$message.success('会话创建成功！');
      
      console.log('会话提交数据:', data);
    },
    
    /**
     * 处理取消事件
     */
    handleCancel() {
      this.isDialogVisible = false;
      this.$message.info('已取消创建会话');
    },
    
    /**
     * 处理可见性变化事件
     * @param {boolean} visible - 可见状态
     */
    handleVisibilityChange(visible) {
      this.isDialogVisible = visible;
    }
  }
};
</script>

<style scoped>
.create-session-dialog-test-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  color: #262626;
  margin-bottom: 8px;
}

.page-header p {
  color: #8c8c8c;
  font-size: 14px;
}

.test-controls {
  margin-bottom: 24px;
  text-align: center;
}

.info-panel {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.info-panel h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #262626;
}

.info-item {
  margin-bottom: 12px;
}

.info-item strong {
  color: #262626;
}

.info-item pre {
  background-color: #f6f6f6;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}
</style> 