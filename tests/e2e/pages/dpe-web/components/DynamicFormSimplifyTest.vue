<template>
  <div class="dynamic-form-simplify-test-page">
    <div class="page-header">
      <h1>DynamicFormSimplify 组件测试页面</h1>
      <p>展示简化版动态表单组件的各种字段类型和功能（object和array使用JSON输入）</p>
    </div>

    <div class="test-controls">
      <a-space>
        <a-button @click="switchConfigType" data-testid="switch-config-btn">
          {{ currentConfigType === 'basic' ? '切换到复杂配置' : '切换到基础配置' }}
        </a-button>
        <a-button @click="toggleReadonly">
          {{ readonly ? '编辑模式' : '只读模式' }}
        </a-button>
        <a-button @click="resetForm" data-testid="reset-form-btn">重置</a-button>
        <a-button type="primary" @click="validateAndGetData">验证并获取数据</a-button>
      </a-space>
    </div>

    <div class="form-container">
      <DynamicFormSimplify
        ref="dynamicForm"
        :config-fields="configFields"
        :value="formValue"
        :readonly="readonly"
        @input="handleFormInput"
        @change="handleFormChange"
      />
    </div>

    <div class="form-data-display">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formValue, null, 2) }}</pre>
    </div>

    <div v-if="validationResult" class="validation-result">
      <h3>验证结果：</h3>
      <a-tag :color="validationResult.valid ? 'green' : 'red'">
        {{ validationResult.valid ? '验证通过' : '验证失败' }}
      </a-tag>
    </div>
  </div>
</template>

<script>
import DynamicFormSimplify from '@/pages/dpe-web/components/DynamicFormSimplify';
import { ConfigField } from '@/pages/dpe-web/models';

/**
 * DynamicFormSimplify组件测试页面
 * 用于演示和测试简化版动态表单的各种功能
 */
export default {
  name: 'DynamicFormSimplifyTest',
  components: {
    DynamicFormSimplify
  },
  
  data: function() {
    return {
      readonly: false,
      formValue: {},
      validationResult: null,
      currentConfigType: 'basic',
      basicConfigFields: [
        // 字符串类型字段
        new ConfigField({
          key: 'name',
          schema: {
            type: 'string',
            title: '姓名',
            placeholder: '请输入姓名',
            required: true,
            minLength: 2,
            maxLength: 50,
            description: '用户的真实姓名，长度为2-50个字符'
          }
        }),
        
        // 数字类型字段
        new ConfigField({
          key: 'age',
          schema: {
            type: 'number',
            title: '年龄',
            placeholder: '请输入年龄',
            minimum: 0,
            maximum: 120,
            description: '用户年龄，范围0-120岁'
          }
        }),
        
        // 布尔类型字段
        new ConfigField({
          key: 'enabled',
          schema: {
            type: 'boolean',
            title: '启用',
            description: '是否启用该用户账户'
          }
        }),
        
        // 选择框字段
        new ConfigField({
          key: 'type',
          schema: {
            type: 'string',
            title: '类型',
            enum: ['管理员', '普通用户', '访客'],
            description: '用户类型，影响权限设置'
          }
        }),
        
        // 文本域字段
        new ConfigField({
          key: 'description',
          schema: {
            type: 'string',
            title: '描述',
            format: 'textarea',
            placeholder: '请输入用户描述信息',
            maxLength: 500,
            description: '用户详细描述信息，最多500字符'
          }
        }),
        
        // 密码字段
        new ConfigField({
          key: 'password',
          schema: {
            type: 'string',
            title: '密码',
            format: 'password',
            placeholder: '请输入密码',
            minLength: 6,
            pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{6,}$',
            description: '密码需包含大小写字母和数字，至少6位'
          }
        }),
        
        // 简化的数组类型字段 - 使用JSON输入
        new ConfigField({
          key: 'tags',
          schema: {
            type: 'array',
            title: '标签',
            minItems: 0,
            maxItems: 10,
            items: {
              type: 'string',
              placeholder: '输入标签名'
            },
            description: '用户标签列表，最多10个，使用JSON格式输入'
          }
        }),
        
        // 简化的对象类型字段（有预定义属性）- 使用JSON输入
        new ConfigField({
          key: 'profile',
          schema: {
            type: 'object',
            title: '个人信息',
            properties: {
              phone: {
                type: 'string',
                title: '手机号',
                pattern: '^1[3-9]\\d{9}$'
              },
              email: {
                type: 'string',
                title: '邮箱',
                pattern: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$'
              },
              address: {
                type: 'string',
                title: '地址',
                format: 'textarea'
              }
            },
            required: ['phone'],
            description: '用户的个人联系信息，使用JSON格式输入'
          }
        }),
        
        // 简化的对象类型字段（无预定义属性，自由JSON）- 使用JSON输入
        new ConfigField({
          key: 'config',
          schema: {
            type: 'object',
            title: '配置',
            description: '自定义配置信息，使用JSON格式输入'
          }
        }),
        
        // 简化的数组对象嵌套字段 - 使用JSON输入
        new ConfigField({
          key: 'skills',
          schema: {
            type: 'array',
            title: '技能列表',
            items: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  title: '技能名称'
                },
                level: {
                  type: 'string',
                  title: '熟练度',
                  enum: ['初级', '中级', '高级', '专家']
                }
              }
            },
            description: '用户掌握的技能及熟练程度，使用JSON格式输入'
          }
        }),
        
        // anyOf 类型字段 - 使用 AnyInput 进行处理
        new ConfigField({
          key: 'flexible_value',
          schema: {
            title: '灵活值',
            anyOf: [
              {
                type: 'string',
                title: '字符串值',
                description: '输入字符串类型的值'
              },
              {
                type: 'number',
                title: '数字值',
                description: '输入数字类型的值'
              },
              {
                type: 'object',
                title: '对象值',
                properties: {
                  key: { type: 'string' },
                  value: { type: 'string' }
                },
                description: '输入对象类型的值'
              },
              {
                type: 'array',
                title: '数组值',
                items: { type: 'string' },
                description: '输入数组类型的值'
              }
            ],
            description: '可以是字符串、数字、对象或数组类型的灵活值，使用JSON格式输入'
          }
        })
      ],
      
      // 复杂配置字段 - 真实业务场景（简化版本）
      complexConfigFields: [
        new ConfigField({
          key: 'filter_expression',
          schema: {
            type: 'object',
            title: '过滤表达式',
            description: '过滤表达式，使用JSON格式输入'
          }
        }),
        new ConfigField({
          key: 'columns_only',
          schema: {
            type: 'array',
            title: '限制返回的列，空代表返回所有字段',
            items: {
              type: 'string'
            },
            description: '限制返回的列，空代表返回所有字段'
          }
        }),
        new ConfigField({
          key: 'preview_data',
          schema: {
            type: 'boolean',
            title: '是否预览数据，默认不预览',
            description: '是否预览数据，默认不预览',
            default: false
          }
        }),
        new ConfigField({
          key: 'db_options',
          schema: {
            type: 'object',
            title: 'MySQL连接选项',
            description: 'MySQL连接选项，使用JSON格式输入',
            properties: {
              host: {
                type: 'string',
                title: '主机地址'
              },
              port: {
                type: 'number',
                title: '端口号'
              },
              username: {
                type: 'string',
                title: '用户名'
              },
              password: {
                type: 'string',
                title: '密码'
              },
              database: {
                type: 'string',
                title: '数据库名称'
              }
            },
            required: ['host', 'port', 'username', 'password', 'database']
          }
        }),
        new ConfigField({
          key: 'table_name',
          schema: {
            type: 'string',
            title: '表名',
            description: '表名',
            required: true
          }
        })
      ]
    };
  },

  computed: {
    /**
     * 当前使用的配置字段
     */
    configFields: function() {
      return this.currentConfigType === 'basic' ? this.basicConfigFields : this.complexConfigFields;
    }
  },

  mounted: function() {
    var self = this;
    // 暴露方法给E2E测试使用
    window.setConfigFields = self.setConfigFields;
    window.getFormData = self.getFormData;
    window.validateForm = self.validateForm;
    // 暴露组件实例用于调试
    window.vm = self;
  },

  methods: {
    /**
     * 切换只读模式
     */
    toggleReadonly: function() {
      this.readonly = !this.readonly;
    },

    /**
     * 重置表单
     */
    resetForm: function() {
      this.$refs.dynamicForm.resetForm();
      this.validationResult = null;
    },

    /**
     * 验证并获取数据
     */
    validateAndGetData: function() {
      var self = this;
      this.$refs.dynamicForm.validateForm().then(function(valid) {
        self.validationResult = {
          valid: valid,
          data: self.$refs.dynamicForm.getFormData()
        };
      });
    },

    /**
     * 处理表单输入
     * @param {Object} value - 表单值
     */
    handleFormInput: function(value) {
      this.formValue = value;
    },

    /**
     * 处理表单变化
     * @param {Object} change - 变化信息
     */
    handleFormChange: function(change) {
      console.log('Form changed:', change);
    },

    /**
     * 设置配置字段（供测试使用）
     * @param {Array} configFields - 配置字段数组
     */
    setConfigFields: function(configFields) {
      this.configFields = configFields;
    },

    /**
     * 获取表单数据（供测试使用）
     * @returns {Object} 表单数据
     */
    getFormData: function() {
      return this.$refs.dynamicForm.getFormData();
    },

    /**
     * 验证表单（供测试使用）
     * @returns {Promise<boolean>} 验证结果
     */
    validateForm: function() {
      return this.$refs.dynamicForm.validateForm();
    },

    /**
     * 切换配置类型
     */
    switchConfigType: function() {
      this.currentConfigType = this.currentConfigType === 'basic' ? 'complex' : 'basic';
      // 重置表单值
      this.formValue = {};
      this.validationResult = null;
    }
  }
};
</script>

<style scoped>
.dynamic-form-simplify-test-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  color: #262626;
  margin-bottom: 8px;
}

.page-header p {
  color: #8c8c8c;
  font-size: 14px;
}

.test-controls {
  margin-bottom: 24px;
  text-align: center;
}

.form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.form-data-display {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.form-data-display h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #262626;
}

.form-data-display pre {
  background-color: #f6f6f6;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

.validation-result {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.validation-result h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #262626;
}
</style>
