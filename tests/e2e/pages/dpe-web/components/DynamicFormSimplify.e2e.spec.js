import { test, expect } from '@playwright/test';

/**
 * DynamicFormSimplify 组件 E2E 测试
 * 测试简化版动态表单的各种字段类型渲染和交互功能
 * 重点测试object和array类型使用JSON textarea的简化实现
 */

test.describe('DynamicFormSimplify 组件测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 导航到包含DynamicFormSimplify组件的测试页面
    await page.goto('/test/dynamic-form-simplify');
    await page.waitForLoadState('networkidle');
    
    // 确保页面完全加载
    await page.waitForSelector('.dynamic-form-simplify-test-page', { timeout: 10000 });
  });

  test('应该正确渲染基本表单结构', async ({ page }) => {
    // 检查动态表单容器是否存在
    const formContainer = page.locator('.dynamic-form-simplify');
    await expect(formContainer).toBeVisible();

    // 检查主要的表单是否存在
    const antForm = page.locator('.dynamic-form-simplify .ant-form').first();
    await expect(antForm).toBeVisible();

    // 检查表单项是否存在
    const formItems = page.locator('.dynamic-form-simplify .ant-form-item');
    const count = await formItems.count();
    expect(count).toBeGreaterThan(0);

    // 截图验证基本布局
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-basic-layout.png',
      fullPage: true 
    });
  });

  test('应该正确渲染字符串类型字段', async ({ page }) => {
    // 使用更具体的选择器查找姓名字段
    const nameField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("姓名")') 
    });
    await expect(nameField).toBeVisible();

    // 检查输入框是否存在
    const input = nameField.locator('.string-input .ant-input').first();
    await expect(input).toBeVisible();

    // 测试输入功能
    await input.fill('测试用户');
    await expect(input).toHaveValue('测试用户');

    // 截图验证字符串字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-string-field.png',
      fullPage: true 
    });
  });

  test('应该正确渲染数字类型字段', async ({ page }) => {
    // 使用更具体的选择器查找年龄字段
    const ageField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("年龄")') 
    });
    await expect(ageField).toBeVisible();

    // 检查数字输入框是否存在
    const numberInput = ageField.locator('.number-input .ant-input-number input');
    await expect(numberInput).toBeVisible();

    // 测试数字输入
    await numberInput.fill('25');
    await expect(numberInput).toHaveValue('25');

    // 截图验证数字字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-number-field.png',
      fullPage: true 
    });
  });

  test('应该正确渲染布尔类型字段', async ({ page }) => {
    // 使用更具体的选择器查找启用字段
    const enabledField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("启用")') 
    });
    await expect(enabledField).toBeVisible();

    // 检查开关组件是否存在
    const switchControl = enabledField.locator('.boolean-input .ant-switch');
    await expect(switchControl).toBeVisible();

    // 测试开关切换
    await switchControl.click();
    await page.waitForTimeout(100);
    await expect(switchControl).toHaveClass(/ant-switch-checked/);

    // 截图验证布尔字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-boolean-field.png',
      fullPage: true 
    });
  });

  test('应该正确渲染选择框字段', async ({ page }) => {
    // 使用更具体的选择器查找类型字段
    const typeField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("类型")') 
    });
    await expect(typeField).toBeVisible();

    // 检查选择框是否存在
    const select = typeField.locator('.string-input .ant-select');
    await expect(select).toBeVisible();

    // 测试选择功能
    await select.click();
    await page.waitForTimeout(500);
    
    // 查找下拉选项
    const dropdownVisible = await page.locator('.ant-select-dropdown').isVisible();
    if (dropdownVisible) {
      const options = page.locator('.ant-select-dropdown .ant-select-item');
      const optionCount = await options.count();
      
      if (optionCount > 0) {
        // 选择第一个选项
        await options.first().click();
      } else {
        // 点击空白处关闭下拉
        await page.click('body');
      }
    }

    // 截图验证选择框字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-select-field.png',
      fullPage: true 
    });
  });

  test('应该正确渲染文本域字段', async ({ page }) => {
    // 使用更具体的选择器查找描述字段
    const descField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("描述")') 
    });
    await expect(descField).toBeVisible();

    // 检查文本域是否存在
    const textarea = descField.locator('.string-input textarea.ant-input');
    await expect(textarea).toBeVisible();

    // 测试多行文本输入
    const testText = '这是一段多行文本\n用于测试文本域功能';
    await textarea.fill(testText);
    await expect(textarea).toHaveValue(testText);

    // 截图验证文本域字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-textarea-field.png',
      fullPage: true 
    });
  });

  test('应该正确渲染简化的数组类型字段（JSON输入）', async ({ page }) => {
    // 使用更具体的选择器查找标签字段
    const tagsField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("标签")') 
    });
    await expect(tagsField).toBeVisible();

    // 检查数组输入组件是否存在
    const arrayInput = tagsField.locator('.array-input');
    await expect(arrayInput).toBeVisible();

    // 检查JSON Schema显示区域
    const schemaCollapse = arrayInput.locator('.ant-collapse');
    await expect(schemaCollapse).toBeVisible();

    // 点击展开Schema信息
    const schemaHeader = schemaCollapse.locator('.ant-collapse-header');
    await schemaHeader.click();
    await page.waitForTimeout(300);

    // 检查Schema显示
    const schemaDisplay = arrayInput.locator('.schema-display');
    await expect(schemaDisplay).toBeVisible();

    // 检查JSON输入框
    const jsonTextarea = arrayInput.locator('textarea.ant-input');
    await expect(jsonTextarea).toBeVisible();

    // 测试JSON输入
    const testJsonArray = '["标签1", "标签2", "标签3"]';
    await jsonTextarea.fill(testJsonArray);

    // 检查操作按钮
    const formatButton = arrayInput.locator('[data-testid="array-format-btn"]');
    const validateButton = arrayInput.locator('[data-testid="array-validate-btn"]');
    const clearButton = arrayInput.locator('[data-testid="array-clear-btn"]');
    
    await expect(formatButton).toBeVisible();
    await expect(validateButton).toBeVisible();
    await expect(clearButton).toBeVisible();

    // 测试格式化功能
    await formatButton.click();
    await page.waitForTimeout(200);

    // 测试校验功能
    await validateButton.click();
    await page.waitForTimeout(200);

    // 截图验证数组字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-array-field.png',
      fullPage: true 
    });
  });

  test('应该正确渲染简化的对象类型字段（JSON输入）', async ({ page }) => {
    // 使用更具体的选择器查找个人信息字段
    const profileField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("个人信息")') 
    });
    await expect(profileField).toBeVisible();

    // 检查对象输入组件是否存在
    const objectInput = profileField.locator('.object-input');
    await expect(objectInput).toBeVisible();

    // 检查JSON Schema显示区域
    const schemaCollapse = objectInput.locator('.ant-collapse');
    await expect(schemaCollapse).toBeVisible();

    // 点击展开Schema信息
    const schemaHeader = schemaCollapse.locator('.ant-collapse-header');
    await schemaHeader.click();
    await page.waitForTimeout(300);

    // 检查Schema显示
    const schemaDisplay = objectInput.locator('.schema-display');
    await expect(schemaDisplay).toBeVisible();

    // 检查JSON输入框
    const jsonTextarea = objectInput.locator('textarea.ant-input');
    await expect(jsonTextarea).toBeVisible();

    // 测试JSON输入
    const testJsonObject = '{"phone": "13800138000", "email": "<EMAIL>", "address": "测试地址"}';
    await jsonTextarea.fill(testJsonObject);

    // 检查操作按钮
    const formatButton = objectInput.locator('[data-testid="object-format-btn"]');
    const validateButton = objectInput.locator('[data-testid="object-validate-btn"]');
    const clearButton = objectInput.locator('[data-testid="object-clear-btn"]');
    
    await expect(formatButton).toBeVisible();
    await expect(validateButton).toBeVisible();
    await expect(clearButton).toBeVisible();

    // 测试格式化功能
    await formatButton.click();
    await page.waitForTimeout(200);

    // 测试校验功能
    await validateButton.click();
    await page.waitForTimeout(200);

    // 截图验证对象字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-object-field.png',
      fullPage: true 
    });
  });

  test('应该正确处理表单验证', async ({ page }) => {
    // 查找必填字段（姓名）
    const nameField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("姓名")') 
    });
    const input = nameField.locator('.string-input .ant-input').first();

    // 清空必填字段并触发验证
    await input.fill('');
    await input.blur();

    // 等待验证触发
    await page.waitForTimeout(1000);

    // 检查是否显示验证错误
    const errorMessages = page.locator('.ant-form-item-explain-error, .ant-form-item-explain');
    if (await errorMessages.count() > 0) {
      await expect(errorMessages.first()).toBeVisible();
    }

    // 输入正确值应该清除错误
    await input.fill('有效姓名');
    await input.blur();
    await page.waitForTimeout(500);

    // 截图验证表单验证
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-validation.png',
      fullPage: true 
    });
  });

  test('应该能够检查出各种非法输入', async ({ page }) => {
    // 测试必填字段为空的验证
    await page.locator('[data-testid="reset-form-btn"]').click();
    await page.waitForTimeout(500);

    // 直接调用验证方法检查必填字段
    const validationResult1 = await page.evaluate(() => {
      return window.validateForm();
    });

    // 必填字段为空时应该验证失败
    expect(validationResult1).toBe(false);

    // 测试字符串长度不符合要求
    const nameField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("姓名")') 
    });
    const nameInput = nameField.locator('.string-input .ant-input').first();
    
    // 输入过短的姓名（少于2个字符）
    await nameInput.fill('a');
    await page.waitForTimeout(200);

    const validationResult2 = await page.evaluate(() => {
      return window.validateForm();
    });

    // 字符串过短时应该验证失败
    expect(validationResult2).toBe(false);

    // 测试密码格式验证
    const passwordField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("密码")') 
    });
    const passwordInput = passwordField.locator('.string-input .ant-input-password input');
    
    // 输入不符合格式的密码（缺少大写字母）
    await passwordInput.fill('password123');
    await page.waitForTimeout(200);

    const validationResult3 = await page.evaluate(() => {
      return window.validateForm();
    });

    // 密码格式不正确时应该验证失败
    expect(validationResult3).toBe(false);

    // 测试数值范围验证
    const ageField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("年龄")') 
    });
    const ageInput = ageField.locator('.number-input .ant-input-number input');
    
    // 输入超出范围的年龄
    await ageInput.fill('150');
    await page.waitForTimeout(200);

    const validationResult4 = await page.evaluate(() => {
      return window.validateForm();
    });

    // 数值超出范围时应该验证失败
    expect(validationResult4).toBe(false);

    // 测试JSON格式错误验证
    const tagsField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("标签")') 
    });
    const tagsTextarea = tagsField.locator('.array-input textarea.ant-input');
    
    // 输入无效的JSON格式
    await tagsTextarea.fill('[invalid json}');
    await page.waitForTimeout(200);

    // 检查是否显示JSON格式错误
    const jsonError = tagsField.locator('.error-message');
    await expect(jsonError).toBeVisible();

    // 测试所有字段都填写正确的情况
    await nameInput.fill('张三');
    await passwordInput.fill('Password123');
    await ageInput.fill('25');
    await tagsTextarea.fill('["标签1", "标签2"]');
    await page.waitForTimeout(500);

    const validationResult5 = await page.evaluate(() => {
      return window.validateForm();
    });

    // 所有字段正确时应该验证通过
    expect(validationResult5).toBe(true);

    // 截图验证非法输入检查
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-invalid-input-validation.png',
      fullPage: true 
    });
  });

  test('应该支持只读模式', async ({ page }) => {
    // 切换到只读模式
    const readonlyToggle = page.locator('.test-controls button').filter({ 
      hasText: /只读模式|编辑模式/ 
    });
    await expect(readonlyToggle).toBeVisible();
    await readonlyToggle.click();

    // 等待模式切换完成
    await page.waitForTimeout(500);

    // 检查字符串输入字段是否被禁用
    const disabledStringInputs = page.locator('.string-input .ant-input[disabled]');
    if (await disabledStringInputs.count() > 0) {
      await expect(disabledStringInputs.first()).toBeVisible();
    }

    // 检查数字输入是否被禁用
    const disabledNumbers = page.locator('.number-input .ant-input-number-disabled');
    if (await disabledNumbers.count() > 0) {
      await expect(disabledNumbers.first()).toBeVisible();
    }

    // 检查JSON textarea是否被禁用
    const disabledTextareas = page.locator('.array-input textarea[disabled], .object-input textarea[disabled]');
    if (await disabledTextareas.count() > 0) {
      await expect(disabledTextareas.first()).toBeVisible();
    }

    // 截图验证只读模式
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-readonly.png',
      fullPage: true 
    });
  });

  test('应该正确处理字段描述信息', async ({ page }) => {
    // 查找带描述信息的字段
    const descriptionElements = page.locator('.field-description');
    await expect(descriptionElements.first()).toBeVisible();

    // 检查描述文本是否正确显示
    const descriptionText = await descriptionElements.first().textContent();
    expect(descriptionText).toBeTruthy();
    expect(descriptionText.length).toBeGreaterThan(0);

    // 截图验证字段描述
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-field-description.png',
      fullPage: true 
    });
  });

  test('应该支持表单数据的获取和重置', async ({ page }) => {
    // 填写表单数据
    const nameField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("姓名")') 
    });
    const nameInput = nameField.locator('.string-input .ant-input').first();
    await nameInput.fill('测试用户');

    const ageField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("年龄")') 
    });
    const ageInput = ageField.locator('.number-input .ant-input-number input');
    await ageInput.fill('30');

    // 测试重置功能
    const resetButton = page.locator('[data-testid="reset-form-btn"]');
    await expect(resetButton).toBeVisible();
    await resetButton.click();

    // 等待重置完成
    await page.waitForTimeout(2000);

    // 检查表单数据显示区域
    const formDataDisplay = page.locator('.form-data-display pre');
    const formDataText = await formDataDisplay.textContent();
    console.log('Form data after reset:', formDataText);

    // 截图验证重置功能
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-reset.png',
      fullPage: true 
    });

    expect(true).toBe(true);
  });

  test('应该正确处理密码字段', async ({ page }) => {
    // 查找密码字段
    const passwordField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("密码")') 
    });
    await expect(passwordField).toBeVisible();

    // 检查密码输入框是否存在
    const passwordInput = passwordField.locator('.string-input .ant-input-password input');
    await expect(passwordInput).toBeVisible();

    // 测试密码输入
    await passwordInput.fill('Test123456');
    await expect(passwordInput).toHaveValue('Test123456');

    // 测试显示/隐藏密码功能
    const toggleButton = passwordField.locator('.ant-input-password-icon');
    if (await toggleButton.count() > 0) {
      await toggleButton.click();
      await page.waitForTimeout(200);
    }

    // 截图验证密码字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-password-field.png',
      fullPage: true 
    });
  });

  test('应该正确处理JSON格式错误', async ({ page }) => {
    // 查找数组字段
    const tagsField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("标签")') 
    });
    const arrayInput = tagsField.locator('.array-input');
    const jsonTextarea = arrayInput.locator('textarea.ant-input');

    // 输入无效的JSON
    await jsonTextarea.fill('{"invalid": json}');
    await page.waitForTimeout(500);

    // 检查是否显示错误信息
    const errorMessage = arrayInput.locator('.error-message');
    await expect(errorMessage).toBeVisible();

    // 测试清空功能
    const clearButton = arrayInput.locator('[data-testid="array-clear-btn"]');
    await clearButton.click();
    await page.waitForTimeout(200);

    // 验证错误信息消失
    await expect(errorMessage).not.toBeVisible();

    // 截图验证错误处理
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-json-error.png',
      fullPage: true 
    });
  });

  test('应该具有良好的响应式设计', async ({ page }) => {
    // 测试不同屏幕尺寸下的表现
    
    // 桌面尺寸
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(200);
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-desktop.png',
      fullPage: true 
    });

    // 平板尺寸
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(200);
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-tablet.png',
      fullPage: true 
    });

    // 手机尺寸
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(200);
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-mobile.png',
      fullPage: true 
    });

    // 恢复默认尺寸
    await page.setViewportSize({ width: 1200, height: 800 });
  });

  test('应该正确处理嵌套对象数组字段', async ({ page }) => {
    // 查找技能列表字段
    const skillsField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("技能列表")') 
    });
    await expect(skillsField).toBeVisible();

    // 检查数组输入组件是否存在
    const arrayInput = skillsField.locator('.array-input');
    await expect(arrayInput).toBeVisible();

    // 输入嵌套对象数组JSON
    const jsonTextarea = arrayInput.locator('textarea.ant-input');
    const testNestedJson = '[{"name": "JavaScript", "level": "高级"}, {"name": "Vue.js", "level": "中级"}]';
    await jsonTextarea.fill(testNestedJson);

    // 测试格式化
    const formatButton = arrayInput.locator('button:has-text("格式化")');
    await formatButton.click();
    await page.waitForTimeout(200);

    // 测试校验
    const validateButton = arrayInput.locator('[data-testid="array-validate-btn"]');
    await validateButton.click();
    await page.waitForTimeout(200);

    // 截图验证嵌套对象数组字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-nested-array-object.png',
      fullPage: true 
    });
  });

  test('应该正确处理 anyOf 类型字段', async ({ page }) => {
    // 查找灵活值字段（anyOf 类型）
    const flexibleValueField = page.locator('.ant-form-item').filter({ 
      has: page.locator('label:has-text("灵活值")') 
    });
    await expect(flexibleValueField).toBeVisible();

    // 检查 AnyInput 组件是否存在
    const anyInput = flexibleValueField.locator('.any-input');
    await expect(anyInput).toBeVisible();

    // 检查 JSON Schema 显示区域
    const schemaCollapse = anyInput.locator('.ant-collapse');
    await expect(schemaCollapse).toBeVisible();

    // 点击展开 Schema 信息
    const schemaHeader = schemaCollapse.locator('.ant-collapse-header');
    await schemaHeader.click();
    await page.waitForTimeout(300);

    // 检查 Schema 显示
    const schemaDisplay = anyInput.locator('.schema-display');
    await expect(schemaDisplay).toBeVisible();

    // 验证 Schema 内容包含 anyOf
    const schemaText = await schemaDisplay.textContent();
    expect(schemaText).toContain('anyOf');

    // 检查 JSON 输入框
    const jsonTextarea = anyInput.locator('textarea.ant-input');
    await expect(jsonTextarea).toBeVisible();

    // 测试输入字符串类型值
    await jsonTextarea.fill('"Hello World"');
    await page.waitForTimeout(200);

    // 测试输入数字类型值
    await jsonTextarea.fill('42');
    await page.waitForTimeout(200);

    // 测试输入对象类型值
    const objectValue = '{"key": "example", "value": "test"}';
    await jsonTextarea.fill(objectValue);
    await page.waitForTimeout(200);

    // 测试输入数组类型值
    const arrayValue = '["item1", "item2", "item3"]';
    await jsonTextarea.fill(arrayValue);
    await page.waitForTimeout(200);

    // 检查操作按钮
    const formatButton = anyInput.locator('[data-testid="any-format-btn"]');
    const validateButton = anyInput.locator('[data-testid="any-validate-btn"]');
    const clearButton = anyInput.locator('[data-testid="any-clear-btn"]');
    
    await expect(formatButton).toBeVisible();
    await expect(validateButton).toBeVisible();
    await expect(clearButton).toBeVisible();

    // 测试格式化功能
    await formatButton.click();
    await page.waitForTimeout(200);

    // 测试校验功能
    await validateButton.click();
    await page.waitForTimeout(200);

    // 测试清空功能
    await clearButton.click();
    await page.waitForTimeout(200);

    // 验证清空后的值
    const clearedValue = await jsonTextarea.inputValue();
    expect(clearedValue).toBe('null');

    // 截图验证 anyOf 字段
    await page.screenshot({ 
      path: 'test-results/dynamic-form-simplify-anyof-field.png',
      fullPage: true 
    });
  });

  test('should handle complex real-world configFields data', async ({ page }) => {
    // 检查初始状态的按钮文本
    const initialButtonText = await page.locator('[data-testid="switch-config-btn"]').textContent();
    console.log('初始按钮文本:', initialButtonText);

    // 点击切换到复杂配置按钮
    await page.locator('[data-testid="switch-config-btn"]').click();

    // 等待页面更新
    await page.waitForTimeout(1000);

    // 检查切换后的按钮文本
    const afterButtonText = await page.locator('[data-testid="switch-config-btn"]').textContent();
    console.log('切换后按钮文本:', afterButtonText);

    // 检查页面上实际存在的h3元素
    const h3Elements = await page.locator('h3').allTextContents();
    console.log('页面上的所有h3元素:', h3Elements);

    // 检查是否有表单项
    const formItems = await page.locator('.ant-form-item').count();
    console.log('表单项数量:', formItems);

    // 检查表单项的标签文本
    const formLabels = await page.locator('.ant-form-item-label').allTextContents();
    console.log('表单标签文本:', formLabels);

    // 检查是否有描述信息
    const descriptions = await page.locator('.field-description').allTextContents();
    console.log('字段描述信息:', descriptions);

    // 检查是否有控制台错误
    page.on('console', msg => console.log('浏览器控制台:', msg.text()));
    page.on('pageerror', error => console.log('页面错误:', error.message));

    // 获取当前配置类型状态
    const configType = await page.evaluate(() => window.vm ? window.vm.currentConfigType : 'unknown');
    console.log('当前配置类型:', configType);

    // 获取configFields信息
    const configFieldsCount = await page.evaluate(() => {
      if (window.vm && window.vm.configFields) {
        return {
          count: window.vm.configFields.length,
          keys: window.vm.configFields.map(f => f.key)
        };
      }
      return null;
    });
    console.log('configFields信息:', configFieldsCount);

    // 截图用于调试
    await page.screenshot({ path: 'test-results/debug-complex-config.png', fullPage: true });

    // 验证所有字段都正确渲染（使用表单标签而不是h3元素）
    await expect(page.locator('.ant-form-item-label').filter({ hasText: '过滤表达式' })).toBeVisible();
    await expect(page.locator('.ant-form-item-label').filter({ hasText: '限制返回的列，空代表返回所有字段' })).toBeVisible();
    await expect(page.locator('.ant-form-item-label').filter({ hasText: '是否预览数据，默认不预览' })).toBeVisible();
    await expect(page.locator('.ant-form-item-label').filter({ hasText: 'MySQL连接选项' })).toBeVisible();
    await expect(page.locator('.ant-form-item-label').filter({ hasText: '表名' })).toBeVisible();

    // 测试复杂对象类型字段 (filter_expression) - 应该使用ObjectInput
    const filterExpressionFormItem = page.locator('.ant-form-item').filter({ hasText: '过滤表达式' });
    const filterExpressionTextarea = filterExpressionFormItem.locator('textarea').first();
    await expect(filterExpressionTextarea).toBeVisible();
    
    // 输入复杂的表达式数据
    const expressionData = {
      "expression": {
        "type": "Identifier",
        "name": "user_id",
        "attrPath": null
      }
    };
    await filterExpressionTextarea.fill(JSON.stringify(expressionData, null, 2));

    // 测试数组类型字段 (columns_only) - 应该使用ArrayInput
    const columnsOnlyFormItem = page.locator('.ant-form-item').filter({ hasText: '限制返回的列，空代表返回所有字段' });
    const columnsOnlyTextarea = columnsOnlyFormItem.locator('textarea').first();
    await expect(columnsOnlyTextarea).toBeVisible();
    await columnsOnlyTextarea.fill('["id", "name", "email"]');

    // 测试布尔类型字段 (preview_data) - 应该使用BooleanInput
    const previewDataFormItem = page.locator('.ant-form-item').filter({ hasText: '是否预览数据，默认不预览' });
    const previewDataSwitch = previewDataFormItem.locator('.ant-switch').first();
    await expect(previewDataSwitch).toBeVisible();
    await previewDataSwitch.click(); // 切换到true

    // 测试对象类型字段 (db_options) - 应该使用ObjectInput
    const dbOptionsFormItem = page.locator('.ant-form-item').filter({ hasText: 'MySQL连接选项' });
    const dbOptionsTextarea = dbOptionsFormItem.locator('textarea').first();
    
    // 定义 db_options 数据（移到外部作用域）
    const dbOptionsData = {
      "host": "localhost",
      "port": 3306,
      "username": "test_user",
      "password": "test_password",
      "database": "test_db"
    };
    
    // 添加调试：检查db_options字段是否找到
    const dbOptionsExists = await dbOptionsTextarea.count();
    console.log('db_options textarea count:', dbOptionsExists);
    
    if (dbOptionsExists > 0) {
      await expect(dbOptionsTextarea).toBeVisible();
      
      // 使用evaluate直接设置值并触发Vue事件
      const dbOptionsJson = JSON.stringify(dbOptionsData, null, 2);
      await page.evaluate((json) => {
        // 找到db_options的textarea
        const dbOptionsTextarea = document.querySelector('.ant-form-item:has(.ant-form-item-label:contains("MySQL连接选项")) textarea');
        if (dbOptionsTextarea) {
          // 直接设置值
          dbOptionsTextarea.value = json;
          
          // 手动触发Vue的input和change事件
          const inputEvent = new Event('input', { bubbles: true });
          const changeEvent = new Event('change', { bubbles: true });
          
          dbOptionsTextarea.dispatchEvent(inputEvent);
          dbOptionsTextarea.dispatchEvent(changeEvent);
        }
      }, dbOptionsJson);
      
      // 验证填充是否成功
      const filledValue = await dbOptionsTextarea.inputValue();
      console.log('db_options填充后的值:', filledValue);
      
      // 等待Vue响应
      await page.waitForTimeout(300);
      
      // 检查填充后的表单数据
      const afterFillFormData = await page.evaluate(() => {
        return window.getFormData();
      });
      console.log('填充db_options后的表单数据:', JSON.stringify(afterFillFormData.db_options, null, 2));
      
      // 添加更详细的调试：检查Vue组件内部状态
      const vueDebugInfo = await page.evaluate(() => {
        if (window.vm) {
          const dbOptionsField = window.vm.configFields.find(f => f.key === 'db_options');
          return {
            formDataDbOptions: window.vm.formData.db_options,
            fieldExists: !!dbOptionsField,
            fieldSchema: dbOptionsField ? dbOptionsField.schema : null
          };
        }
        return null;
      });
      console.log('Vue内部调试信息:', JSON.stringify(vueDebugInfo, null, 2));
    } else {
      console.log('未找到db_options的textarea');
      // 查看所有textarea
      const allTextareas = await page.locator('textarea').count();
      console.log('页面上总textarea数量:', allTextareas);
    }

    // 测试字符串类型字段 (table_name) - 应该使用StringInput
    const tableNameFormItem = page.locator('.ant-form-item').filter({ hasText: '表名' });
    const tableNameInput = tableNameFormItem.locator('input[type="text"]').first();
    await expect(tableNameInput).toBeVisible();
    await tableNameInput.fill('users_table');

    // 测试格式化功能 - 应该有3个按钮：filter_expression(ObjectInput), db_options(ObjectInput), columns_only(ArrayInput)
    const formatButtons = page.locator('[data-testid$="-format-btn"]');
    await expect(formatButtons).toHaveCount(3);
    
    // 点击第一个格式化按钮
    await formatButtons.first().click();
    await page.waitForTimeout(300);

    // 测试校验功能
    const validateButtons = page.locator('[data-testid$="-validate-btn"]');
    await expect(validateButtons).toHaveCount(3);
    
    // 点击第一个校验按钮
    await validateButtons.first().click();
    await page.waitForTimeout(300);

    // 验证表单数据获取
    const formData = await page.evaluate(() => {
      return window.getFormData();
    });

    // 验证复杂数据结构
    expect(formData.filter_expression).toEqual(expressionData);
    expect(formData.columns_only).toEqual(["id", "name", "email"]);
    expect(formData.preview_data).toBe(true);
    expect(formData.db_options).toEqual(dbOptionsData);
    expect(formData.table_name).toBe('users_table');

    // 测试表单校验
    const isValid = await page.evaluate(() => {
      return window.validateForm();
    });
    expect(isValid).toBe(true);

    // 测试重置功能
    await page.locator('[data-testid="reset-form-btn"]').click();
    await page.waitForTimeout(300);

    // 验证重置后的状态
    const resetFormData = await page.evaluate(() => {
      return window.getFormData();
    });
    
    // 复杂对象字段应该重置为空对象
    expect(resetFormData.filter_expression).toEqual({});
    expect(resetFormData.columns_only).toEqual([]);
    expect(resetFormData.preview_data).toBe(false); // 默认值
    expect(resetFormData.db_options).toEqual({});
    expect(resetFormData.table_name).toBe('');
  });
});
