import { test, expect } from '@playwright/test';

test.describe('ExampleButton Component E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the test page for ExampleButton
    await page.goto('/test-example-button');

    // 尝试移除 webpack-dev-server-client-overlay iframe，以避免其拦截点击事件
    await page.evaluate(() => {
      const overlay = document.getElementById('webpack-dev-server-client-overlay');
      if (overlay) {
        overlay.remove();
      }
    });
  });

  test('应该正确渲染按钮并显示文本', async ({ page }) => {
    // Verify default button text
    await expect(page.locator('[data-testid="default-button"]')).toHaveText('Default Button');

    // Verify primary button text
    await expect(page.locator('[data-testid="primary-button"]')).toHaveText('Primary Button');

    // Verify disabled button text
    await expect(page.locator('[data-testid="disabled-button"]')).toHaveText('Disabled Button');

    // Verify link button text
    await expect(page.locator('[data-testid="link-button"]')).toHaveText('Link Button');

    // Take a screenshot of the entire page to verify rendering
    await page.screenshot({
      path: 'test-results/ExampleButton-rendering.png',
      fullPage: true,
    });
  });

  test('点击按钮后应该触发点击事件', async ({ page }) => {
    const defaultButton = page.locator('[data-testid="default-button"]');
    const defaultClickCount = defaultButton.locator('xpath=./following-sibling::p');

    // Initial check for click count
    await expect(defaultClickCount).toContainText('Clicked: 0 times');

    // Click the default button
    await defaultButton.click();

    // Verify click count increased
    await expect(defaultClickCount).toContainText('Clicked: 1 times');

    // Click again
    await defaultButton.click();
    await expect(defaultClickCount).toContainText('Clicked: 2 times');

    // Take a screenshot after clicks
    await page.screenshot({
      path: 'test-results/ExampleButton-click-event.png',
      fullPage: true,
    });
  });

  test('禁用状态的按钮不应该触发点击事件', async ({ page }) => {
    const disabledButton = page.locator('[data-testid="disabled-button"]');
    const disabledClickCount = disabledButton.locator('xpath=./following-sibling::p');

    // Initial check for disabled button click count
    await expect(disabledClickCount).toContainText('Clicked: 0 times');

    // Try to click the disabled button
    await disabledButton.click({ force: true }); // Use force to attempt click on disabled element

    // Verify click count remains 0
    await expect(disabledClickCount).toContainText('Clicked: 0 times');

    // Take a screenshot of the disabled button
    await page.screenshot({
      path: 'test-results/ExampleButton-disabled-state.png',
      fullPage: true,
    });
  });
}); 