<template>
  <div class="example-button-test-page">
    <h1>Example Button Test Page</h1>
    <p>This page is used to test the ExampleButton component in isolation.</p>
    
    <div class="button-container">
      <h2>Default But<PERSON></h2>
      <ExampleButton data-testid="default-button" label="Default Button" @click="handleButtonClick('default')" />
      <p>Clicked: {{ defaultButtonClickedCount }} times</p>
    </div>

    <div class="button-container">
      <h2>Primary Button</h2>
      <ExampleButton data-testid="primary-button" type="primary" label="Primary Button" @click="handleButtonClick('primary')" />
      <p>Clicked: {{ primaryButtonClickedCount }} times</p>
    </div>

    <div class="button-container">
      <h2>Disabled Button</h2>
      <ExampleButton data-testid="disabled-button" label="Disabled Button" :disabled="true" @click="handleButtonClick('disabled')" />
      <p>Clicked: {{ disabledButtonClickedCount }} times</p>
    </div>

    <div class="button-container">
      <h2>Link Button</h2>
      <ExampleButton data-testid="link-button" type="link" label="Link Button" @click="handleButtonClick('link')" />
      <p>Clicked: {{ linkButtonClickedCount }} times</p>
    </div>
  </div>
</template>

<script>
import ExampleButton from '@/components/ExampleButtom/index.vue';

/**
 * ExampleButton测试页面组件
 * @component ExampleButtonTest
 * @description 该页面用于Playwright测试，独立渲染ExampleButton组件的不同状态。
 */
export default {
  name: 'ExampleButtonTest',
  components: {
    ExampleButton,
  },
  data() {
    return {
      /**
       * 默认按钮点击次数
       * @type {number}
       */
      defaultButtonClickedCount: 0,
      /**
       * 主要按钮点击次数
       * @type {number}
       */
      primaryButtonClickedCount: 0,
      /**
       * 禁用按钮点击次数
       * @type {number}
       */
      disabledButtonClickedCount: 0,
      /**
       * 链接按钮点击次数
       * @type {number}
       */
      linkButtonClickedCount: 0,
    };
  },
  methods: {
    /**
     * 处理按钮点击事件
     * @param {string} type - 按钮类型 (default, primary, disabled, link)
     * @fires buttonClickEvent
     */
    handleButtonClick(type) {
      if (type === 'default') {
        this.defaultButtonClickedCount++;
      } else if (type === 'primary') {
        this.primaryButtonClickedCount++;
      } else if (type === 'disabled') {
        this.disabledButtonClickedCount++;
      } else if (type === 'link') {
        this.linkButtonClickedCount++;
      }
      /**
       * 按钮点击时触发，携带按钮类型
       * @event buttonClickEvent
       * @type {string}
       */
      this.$emit('buttonClickEvent', type);
    },
  },
};
</script>

<style scoped>
.example-button-test-page {
  padding: 20px;
}

.button-container {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.button-container h2 {
  margin-top: 0;
  color: #333;
}

.button-container p {
  margin-top: 10px;
  font-weight: bold;
}
</style> 