<template>
  <div class="pagination-test-page">
    <h1>Pagination Component Test Page</h1>
    <p>This page is used to test the Pagination component in isolation.</p>
    
    <!-- 基础分页测试 -->
    <div class="test-container">
      <h2>Basic Pagination</h2>
      <Pagination
        data-testid="basic-pagination"
        :page-param="basicPageParam"
        :loading="basicLoading"
        @page-change="handleBasicPageChange"
        @size-change="handleBasicSizeChange"
        @refresh="handleBasicRefresh"
      />
      <div class="status-info">
        <p>当前页: {{ basicPageParam.pageIndex + 1 }}</p>
        <p>页面大小: {{ basicPageParam.limit }}</p>
        <p>总记录数: {{ basicPageParam.recordTotal }}</p>
        <p>刷新次数: {{ basicRefreshCount }}</p>
      </div>
    </div>

    <!-- 带排序功能的分页测试 -->
    <div class="test-container">
      <h2>Pagination with Sorting</h2>
      <Pagination
        data-testid="sorting-pagination"
        :page-param="sortingPageParam"
        :loading="sortingLoading"
        :show-sort-options="true"
        :available-sort-fields="availableSortFields"
        @page-change="handleSortingPageChange"
        @size-change="handleSortingSizeChange"
        @sort-change="handleSortChange"
        @refresh="handleSortingRefresh"
      />
      <div class="status-info">
        <p>当前页: {{ sortingPageParam.pageIndex + 1 }}</p>
        <p>页面大小: {{ sortingPageParam.limit }}</p>
        <p>排序字段: {{ sortingPageParam.sortField }}</p>
        <p>排序类型: {{ sortingPageParam.sortType }}</p>
        <p>刷新次数: {{ sortingRefreshCount }}</p>
      </div>
    </div>

    <!-- 自定义配置的分页测试 -->
    <div class="test-container">
      <h2>Custom Configuration Pagination</h2>
      <Pagination
        data-testid="custom-pagination"
        :page-param="customPageParam"
        :loading="customLoading"
        :show-size-changer="false"
        :show-quick-jumper="false"
        :show-total="false"
        :show-refresh="false"
        :page-size-options="[5, 15, 25]"
        @page-change="handleCustomPageChange"
        @size-change="handleCustomSizeChange"
      />
      <div class="status-info">
        <p>当前页: {{ customPageParam.pageIndex + 1 }}</p>
        <p>页面大小: {{ customPageParam.limit }}</p>
        <p>总记录数: {{ customPageParam.recordTotal }}</p>
      </div>
    </div>

    <!-- 加载状态测试 -->
    <div class="test-container">
      <h2>Loading State Pagination</h2>
      <div class="controls">
        <button 
          data-testid="toggle-loading-btn"
          @click="toggleLoading"
          type="button"
        >
          {{ isLoadingTest ? '停止加载' : '开始加载' }}
        </button>
      </div>
      <Pagination
        data-testid="loading-pagination"
        :page-param="loadingPageParam"
        :loading="isLoadingTest"
        @page-change="handleLoadingPageChange"
        @size-change="handleLoadingSizeChange"
        @refresh="handleLoadingRefresh"
      />
      <div class="status-info">
        <p>加载状态: {{ isLoadingTest ? '加载中' : '已完成' }}</p>
        <p>当前页: {{ loadingPageParam.pageIndex + 1 }}</p>
        <p>页面大小: {{ loadingPageParam.limit }}</p>
      </div>
    </div>

    <!-- 大数据量测试 -->
    <div class="test-container">
      <h2>Large Dataset Pagination</h2>
      <Pagination
        data-testid="large-dataset-pagination"
        :page-param="largeDataPageParam"
        :loading="largeDataLoading"
        :show-sort-options="true"
        :available-sort-fields="availableSortFields"
        @page-change="handleLargeDataPageChange"
        @size-change="handleLargeDataSizeChange"
        @sort-change="handleLargeDataSortChange"
        @refresh="handleLargeDataRefresh"
      />
      <div class="status-info">
        <p>当前页: {{ largeDataPageParam.pageIndex + 1 }}</p>
        <p>总页数: {{ largeDataPageParam.pageTotal }}</p>
        <p>总记录数: {{ largeDataPageParam.recordTotal }}</p>
        <p>排序: {{ largeDataPageParam.sortField }} ({{ largeDataPageParam.sortType }})</p>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="test-container">
      <h2>Event Log</h2>
      <div class="controls">
        <button 
          data-testid="clear-log-btn"
          @click="clearEventLog"
          type="button"
        >
          清空日志
        </button>
      </div>
      <div class="event-log" data-testid="event-log">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="event-item"
          :data-testid="`event-${index}`"
        >
          <span class="event-time">{{ event.time }}</span>
          <span class="event-type">{{ event.type }}</span>
          <span class="event-data">{{ JSON.stringify(event.data) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue';

/**
 * Pagination测试页面组件
 * @component PaginationTest
 * @description 该页面用于Playwright测试，测试Pagination组件的各种功能和状态。
 */
export default {
  name: 'PaginationTest',
  components: {
    Pagination,
  },
  data() {
    return {
      // 基础分页参数
      basicPageParam: {
        pageIndex: 0,
        limit: 10,
        pageTotal: 10,
        recordTotal: 100,
        sortField: '',
        sortType: 'desc',
        sortList: [],
        isTop: false
      },
      basicLoading: false,
      basicRefreshCount: 0,

      // 带排序的分页参数
      sortingPageParam: {
        pageIndex: 0,
        limit: 20,
        pageTotal: 15,
        recordTotal: 300,
        sortField: 'created_at',
        sortType: 'desc',
        sortList: [
          { field: 'created_at', type: 'desc' }
        ],
        isTop: false
      },
      sortingLoading: false,
      sortingRefreshCount: 0,

      // 自定义配置分页参数
      customPageParam: {
        pageIndex: 0,
        limit: 5,
        pageTotal: 4,
        recordTotal: 20,
        sortField: '',
        sortType: 'asc',
        sortList: [],
        isTop: false
      },
      customLoading: false,

      // 加载状态测试
      loadingPageParam: {
        pageIndex: 0,
        limit: 10,
        pageTotal: 5,
        recordTotal: 50,
        sortField: '',
        sortType: 'desc',
        sortList: [],
        isTop: false
      },
      isLoadingTest: false,

      // 大数据量测试
      largeDataPageParam: {
        pageIndex: 0,
        limit: 50,
        pageTotal: 200,
        recordTotal: 10000,
        sortField: 'updated_at',
        sortType: 'asc',
        sortList: [
          { field: 'updated_at', type: 'asc' }
        ],
        isTop: false
      },
      largeDataLoading: false,

      // 可用排序字段
      availableSortFields: [
        { value: 'created_at', label: '创建时间' },
        { value: 'updated_at', label: '更新时间' },
        { value: 'name', label: '名称' },
        { value: 'status', label: '状态' }
      ],

      // 事件日志
      eventLog: []
    };
  },
  methods: {
    /**
     * 记录事件到日志
     * @param {string} type - 事件类型
     * @param {*} data - 事件数据
     */
    logEvent(type, data) {
      this.eventLog.unshift({
        time: new Date().toLocaleTimeString(),
        type,
        data
      });
      // 保持日志长度在合理范围内
      if (this.eventLog.length > 50) {
        this.eventLog = this.eventLog.slice(0, 50);
      }
    },

    /**
     * 清空事件日志
     */
    clearEventLog() {
      this.eventLog = [];
    },

    // 基础分页事件处理
    handleBasicPageChange(pageParam) {
      this.basicPageParam = { ...this.basicPageParam, ...pageParam };
      this.logEvent('basic-page-change', pageParam);
    },

    handleBasicSizeChange(pageSize) {
      this.basicPageParam = {
        ...this.basicPageParam,
        limit: pageSize,
        pageIndex: 0
      };
      this.logEvent('basic-size-change', pageSize);
    },

    handleBasicRefresh() {
      this.basicRefreshCount++;
      this.logEvent('basic-refresh', { count: this.basicRefreshCount });
    },

    // 排序分页事件处理
    handleSortingPageChange(pageParam) {
      this.sortingPageParam = { ...this.sortingPageParam, ...pageParam };
      this.logEvent('sorting-page-change', pageParam);
    },

    handleSortingSizeChange(pageSize) {
      this.sortingPageParam = {
        ...this.sortingPageParam,
        limit: pageSize,
        pageIndex: 0
      };
      this.logEvent('sorting-size-change', pageSize);
    },

    handleSortChange(sortConfig) {
      this.sortingPageParam = {
        ...this.sortingPageParam,
        ...sortConfig,
        pageIndex: 0
      };
      this.logEvent('sort-change', sortConfig);
    },

    handleSortingRefresh() {
      this.sortingRefreshCount++;
      this.logEvent('sorting-refresh', { count: this.sortingRefreshCount });
    },

    // 自定义分页事件处理
    handleCustomPageChange(pageParam) {
      this.customPageParam = { ...this.customPageParam, ...pageParam };
      this.logEvent('custom-page-change', pageParam);
    },

    handleCustomSizeChange(pageSize) {
      this.customPageParam = {
        ...this.customPageParam,
        limit: pageSize,
        pageIndex: 0
      };
      this.logEvent('custom-size-change', pageSize);
    },

    // 加载状态测试
    toggleLoading() {
      this.isLoadingTest = !this.isLoadingTest;
      this.logEvent('toggle-loading', { loading: this.isLoadingTest });
    },

    handleLoadingPageChange(pageParam) {
      this.loadingPageParam = { ...this.loadingPageParam, ...pageParam };
      this.logEvent('loading-page-change', pageParam);
    },

    handleLoadingSizeChange(pageSize) {
      this.loadingPageParam = {
        ...this.loadingPageParam,
        limit: pageSize,
        pageIndex: 0
      };
      this.logEvent('loading-size-change', pageSize);
    },

    handleLoadingRefresh() {
      this.logEvent('loading-refresh', {});
    },

    // 大数据量测试事件处理
    handleLargeDataPageChange(pageParam) {
      this.largeDataPageParam = { ...this.largeDataPageParam, ...pageParam };
      this.logEvent('large-data-page-change', pageParam);
    },

    handleLargeDataSizeChange(pageSize) {
      this.largeDataPageParam = {
        ...this.largeDataPageParam,
        limit: pageSize,
        pageIndex: 0
      };
      this.logEvent('large-data-size-change', pageSize);
    },

    handleLargeDataSortChange(sortConfig) {
      this.largeDataPageParam = {
        ...this.largeDataPageParam,
        ...sortConfig,
        pageIndex: 0
      };
      this.logEvent('large-data-sort-change', sortConfig);
    },

    handleLargeDataRefresh() {
      this.logEvent('large-data-refresh', {});
    }
  }
};
</script>

<style scoped>
.pagination-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container {
  margin-bottom: 40px;
  padding: 20px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fafafa;
}

.test-container h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.status-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f0f0f0;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.status-info p {
  margin: 4px 0;
  font-family: monospace;
  font-size: 14px;
}

.controls {
  margin-bottom: 16px;
}

.controls button {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.controls button:hover {
  background-color: #40a9ff;
}

.controls button:active {
  background-color: #096dd9;
}

.event-log {
  max-height: 300px;
  overflow-y: auto;
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.event-item {
  display: flex;
  margin-bottom: 4px;
  line-height: 1.4;
}

.event-time {
  color: #569cd6;
  width: 80px;
  flex-shrink: 0;
}

.event-type {
  color: #4ec9b0;
  width: 200px;
  flex-shrink: 0;
}

.event-data {
  color: #ce9178;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-test-page {
    padding: 10px;
  }
  
  .test-container {
    padding: 15px;
    margin-bottom: 30px;
  }
  
  .event-item {
    flex-direction: column;
  }
  
  .event-time,
  .event-type {
    width: auto;
  }
}
</style> 