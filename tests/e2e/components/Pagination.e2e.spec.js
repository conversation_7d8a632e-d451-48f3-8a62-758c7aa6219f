import { test, expect } from '@playwright/test';

/**
 * Pagination 组件端到端测试
 * @description 测试 Pagination 组件的完整功能
 */
test.describe('Pagination Component E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到 Pagination 组件测试页面
    await page.goto('/test-pagination');

    // 移除可能的 webpack-dev-server-client-overlay，避免干扰测试
    await page.evaluate(() => {
      const overlay = document.getElementById('webpack-dev-server-client-overlay');
      if (overlay) {
        overlay.remove();
      }
    });

    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
  });

  test('应该正确渲染基础分页组件', async ({ page }) => {
    // 验证基础分页组件存在
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    await expect(basicPagination).toBeVisible();

    // 验证分页信息显示
    await expect(basicPagination.locator('text=共 100 条记录')).toBeVisible();
    await expect(basicPagination.locator('text=第 1 / 10 页')).toBeVisible();

    // 验证刷新按钮存在
    await expect(basicPagination.locator('button:has-text("刷新")')).toBeVisible();

    // 验证页面大小选择器存在
    await expect(basicPagination.locator('text=每页显示')).toBeVisible();

    // 验证分页控件存在
    await expect(basicPagination.locator('.ant-pagination')).toBeVisible();

    // 截图验证渲染效果
    await page.screenshot({
      path: 'test-results/Pagination-basic-rendering.png',
      fullPage: true,
    });
  });

  test('应该支持页码变化功能', async ({ page }) => {
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    
    // 点击下一页
    await basicPagination.locator('.ant-pagination-next').click();
    
    // 等待状态更新
    await page.waitForTimeout(100);
    
    // 验证页码已更新
    await expect(basicPagination.locator('+ .status-info').locator('text=当前页: 2')).toBeVisible();
    
    // 验证事件已记录
    const eventLog = page.locator('[data-testid="event-log"]');
    await expect(eventLog.locator('text=basic-page-change')).toBeVisible();
    
    // 点击第3页
    await basicPagination.locator('text=3').click();
    await page.waitForTimeout(100);
    
    // 验证页码更新
    await expect(basicPagination.locator('+ .status-info').locator('text=当前页: 3')).toBeVisible();

    // 截图验证页码变化
    await page.screenshot({
      path: 'test-results/Pagination-page-change.png',
      fullPage: true,
    });
  });

  test('应该支持页面大小变化功能', async ({ page }) => {
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    
    // 点击页面大小选择器，使用更可靠的选择器
    await basicPagination.getByRole('combobox').click();
    
    // 等待下拉菜单出现并选择20
    await page.getByRole('option', { name: '20' }).click();
    
    // 验证页面大小已更新
    await expect(basicPagination.locator('+ .status-info').locator('text=页面大小: 20')).toBeVisible();
    
    // 验证事件已记录
    const eventLog = page.locator('[data-testid="event-log"]');
    await expect(eventLog.locator('text=basic-size-change')).toBeVisible();

    // 截图验证页面大小变化
    await page.screenshot({
      path: 'test-results/Pagination-size-change.png',
      fullPage: true,
    });
  });

  test('应该支持刷新功能', async ({ page }) => {
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    
    // 点击刷新按钮
    await basicPagination.locator('button:has-text("刷新")').click();
    
    // 验证刷新次数增加，等待文本更新
    await expect(page.locator('text=刷新次数: 1')).toBeVisible();
    
    // 验证事件已记录
    const eventLog = page.locator('[data-testid="event-log"]');
    await expect(eventLog.locator('text=basic-refresh')).toBeVisible();
    
    // 再次点击刷新
    await basicPagination.locator('button:has-text("刷新")').click();
    
    // 验证刷新次数继续增加，等待文本更新
    await expect(page.locator('text=刷新次数: 2')).toBeVisible();

    // 截图验证刷新功能
    await page.screenshot({
      path: 'test-results/Pagination-refresh.png',
      fullPage: true,
    });
  });

  test('应该支持排序功能', async ({ page }) => {
    const sortingPagination = page.locator('[data-testid="sorting-pagination"]');
    
    // 验证排序选项区域存在
    await expect(sortingPagination.locator('.sort-section')).toBeVisible();
    
    // 验证当前排序字段
    await expect(page.locator('text=排序字段: created_at')).toBeVisible();
    await expect(page.locator('text=排序类型: desc')).toBeVisible();
    
    // 获取所有的combobox，更精确地选择排序字段下拉框
    const sortComboboxes = sortingPagination.locator('.sort-section').getByRole('combobox');
    const sortFieldCombobox = sortComboboxes.nth(0);
    const sortTypeCombobox = sortComboboxes.nth(1);
    
    // 更改排序字段
    await sortFieldCombobox.click();
    await page.getByRole('option', { name: '更新时间' }).click();
    
    // 验证排序字段已更新
    await expect(page.locator('text=排序字段: updated_at')).toBeVisible();
    
    // 更改排序类型
    await sortTypeCombobox.click();
    await page.getByRole('option', { name: '升序' }).click();
    
    // 验证排序类型已更新
    await expect(page.locator('text=排序类型: asc')).toBeVisible();
    
    // 验证事件已记录
    const eventLog = page.locator('[data-testid="event-log"]');
    await expect(eventLog.locator('text=sort-change').first()).toBeVisible();

    // 截图验证排序功能
    await page.screenshot({
      path: 'test-results/Pagination-sorting.png',
      fullPage: true,
    });
  });

  test('应该支持自定义配置', async ({ page }) => {
    const customPagination = page.locator('[data-testid="custom-pagination"]');
    
    // 验证自定义配置生效（不显示页面大小选择器、快速跳转、总数和刷新按钮）
    await expect(customPagination.locator('.page-size-selector')).not.toBeVisible();
    await expect(customPagination.locator('button:has-text("刷新")')).not.toBeVisible();
    await expect(customPagination.locator('text=共')).not.toBeVisible();
    
    // 验证分页控件仍然存在
    await expect(customPagination.locator('.ant-pagination')).toBeVisible();
    
    // 验证页码功能正常
    await customPagination.locator('.ant-pagination-next').click();
    
    // 在状态信息中验证页码更新
    const statusInfo = page.locator('[data-testid="custom-pagination"]').locator('+ .status-info');
    await expect(statusInfo.locator('text=当前页: 2')).toBeVisible();

    // 截图验证自定义配置
    await page.screenshot({
      path: 'test-results/Pagination-custom-config.png',
      fullPage: true,
    });
  });

  test('应该正确处理加载状态', async ({ page }) => {
    const loadingPagination = page.locator('[data-testid="loading-pagination"]');
    
    // 初始状态应该不是加载中
    await expect(page.locator('text=加载状态: 已完成')).toBeVisible();
    
    // 验证分页控件可用
    await expect(loadingPagination.locator('.ant-pagination')).not.toHaveAttribute('disabled');
    
    // 点击开始加载按钮
    await page.locator('[data-testid="toggle-loading-btn"]').click();
    
    // 验证加载状态更新
    await expect(page.locator('text=加载状态: 加载中')).toBeVisible();
    await expect(page.locator('[data-testid="toggle-loading-btn"]')).toContainText('停止加载');
    
    // 验证分页控件被禁用
    await expect(loadingPagination.locator('.ant-pagination .ant-pagination-prev')).toHaveAttribute('aria-disabled', 'true');
    await expect(loadingPagination.locator('button:has-text("刷新")')).toBeDisabled();
    
    // 停止加载
    await page.locator('[data-testid="toggle-loading-btn"]').click();
    
    // 验证加载状态恢复
    await expect(page.locator('text=加载状态: 已完成')).toBeVisible();

    // 截图验证加载状态
    await page.screenshot({
      path: 'test-results/Pagination-loading-state.png',
      fullPage: true,
    });
  });

  test('应该支持大数据量分页', async ({ page }) => {
    const largePagination = page.locator('[data-testid="large-dataset-pagination"]');
    
    // 验证大数据量显示
    await expect(page.locator('text=总记录数: 10000')).toBeVisible();
    await expect(page.locator('text=总页数: 200')).toBeVisible();
    
    // 测试快速跳转到最后一页 - 使用更精确的选择器
    const gotoInput = largePagination.getByRole('textbox');
    await gotoInput.fill('200');
    await gotoInput.press('Enter');

    // 验证页码已更新
    await expect(largePagination.locator('+ .status-info').locator('text=当前页: 200')).toBeVisible();
    
    // 验证事件已记录
    const eventLog = page.locator('[data-testid="event-log"]');
    await expect(eventLog.locator('text=large-data-page-change')).toBeVisible();

    // 截图验证大数据量分页
    await page.screenshot({
      path: 'test-results/Pagination-large-dataset.png',
      fullPage: true,
    });
  });

  test('应该正确记录所有事件', async ({ page }) => {
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    const sortingPagination = page.locator('[data-testid="sorting-pagination"]');
    const loadingPagination = page.locator('[data-testid="loading-pagination"]');
    const eventLog = page.locator('[data-testid="event-log"]');

    // 清空日志
    await page.locator('[data-testid="clear-log-btn"]').click();
    await expect(eventLog).not.toContainText('page-change');

    // 触发 page-change 事件
    await basicPagination.locator('.ant-pagination-next').click();
    await expect(eventLog.locator('text=basic-page-change')).toBeVisible();

    // 触发 size-change 事件
    await basicPagination.getByRole('combobox').click();
    await page.getByRole('option', { name: '50' }).click(); // 选择一个不同的值
    await expect(eventLog.locator('text=basic-size-change')).toBeVisible();

    // 触发 refresh 事件
    await basicPagination.locator('button:has-text("刷新")').click();
    await expect(eventLog.locator('text=basic-refresh')).toBeVisible();

    // 触发 sort-change 事件
    const sortComboboxes = sortingPagination.locator('.sort-section').getByRole('combobox');
    await sortComboboxes.nth(0).click();
    await page.getByRole('option', { name: '更新时间' }).click();
    await expect(eventLog.locator('text=sort-change')).toBeVisible();

    // 触发 toggle-loading 事件
    await page.locator('[data-testid="toggle-loading-btn"]').click();
    await expect(eventLog.locator('text=toggle-loading').locator('text=loading')).toBeVisible();
    await page.locator('[data-testid="toggle-loading-btn"]').click(); // 停止加载
    await expect(eventLog.locator('text=toggle-loading').nth(1).locator('text=loading')).toBeVisible();

    // 截图验证事件记录
    await page.screenshot({
      path: 'test-results/Pagination-event-logging.png',
      fullPage: true,
    });
  });

  test('应该正确处理边界情况', async ({ page }) => {
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    
    // 初始状态，前一页按钮应该被禁用
    await expect(basicPagination.locator('.ant-pagination-prev')).toHaveAttribute('aria-disabled', 'true');
    
    // 点击下一页直到最后一页
    for (let i = 1; i < 10; i++) { // 从当前页1到第9页，共点击9次到第10页
      await basicPagination.locator('.ant-pagination-next').click();
      await expect(basicPagination.locator('+ .status-info').locator(`text=当前页: ${i + 1}`)).toBeVisible();
    }
    
    // 最后一页，下一页按钮应该被禁用
    await expect(basicPagination.locator('.ant-pagination-next')).toHaveAttribute('aria-disabled', 'true');
    
    // 验证跳转到不存在的页码，应该停留在当前页或跳转到最后一页（根据组件实现）
    const gotoInput = basicPagination.getByRole('textbox');
    await gotoInput.fill('999'); // 跳转到一个很大的页码
    await gotoInput.press('Enter');
    // 验证停留在最后一页
    await expect(basicPagination.locator('+ .status-info').locator('text=当前页: 10')).toBeVisible();

    // 验证跳转到负数页码，应该停留在当前页或跳转到第一页
    await gotoInput.fill('-1');
    await gotoInput.press('Enter');
    // 验证停留在第一页 (假设负数页码会跳转到第一页)
    await expect(basicPagination.locator('+ .status-info').locator('text=当前页: 1')).toBeVisible();

    // 截图验证边界情况
    await page.screenshot({
      path: 'test-results/Pagination-boundary-cases.png',
      fullPage: true,
    });
  });

  test('应该在移动端正确显示（响应式测试）', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    const basicPagination = page.locator('[data-testid="basic-pagination"]');
    
    // 验证分页组件在移动端仍然可见
    await expect(basicPagination).toBeVisible();
    
    // 验证响应式布局
    const paginationControls = basicPagination.locator('.pagination-controls');
    await expect(paginationControls).toBeVisible();
    
    // 页面大小选择器在移动端应该隐藏
    await expect(basicPagination.locator('.page-size-selector')).not.toBeVisible();
    
    // 分页控件应该仍然可用
    await expect(basicPagination.locator('.ant-pagination')).toBeVisible();
    
    // 测试移动端的分页操作
    await basicPagination.locator('.ant-pagination-next').click();
    await page.waitForTimeout(100);
    
    // 验证功能正常
    await expect(page.locator('text=当前页: 2')).toBeVisible();

    // 截图验证移动端显示
    await page.screenshot({
      path: 'test-results/Pagination-mobile-responsive.png',
      fullPage: true,
    });
    
    // 恢复桌面端视口
    await page.setViewportSize({ width: 1280, height: 720 });
  });
}); 