import { ResourceService } from '@/pages/dpe-web/services/ResourceService';
import { StructuredDataResourceClient } from '@/pages/dpe-web/models/client-api';
// import { SchemaField } from '@/pages/dpe-web/models'; // Removed as it's now handled internally by StructuredDataResourceClientImpl
import axios from 'axios';

jest.mock('axios');

describe('ResourceService', () => {
  let resourceService;

  beforeEach(() => {
    resourceService = new ResourceService('/api');
    axios.create.mockReturnThis(); // Mock axios.create to return itself for chaining .post
    axios.post.mockReset(); // Reset post mock before each test
  });

  it('should return an instance of StructuredDataResourceClient for openStructuredDataResourceClient', async () => {
    const resourceId = 'test-resource-id';
    const client = await resourceService.openStructuredDataResourceClient(resourceId);
    expect(client).toBeInstanceOf(StructuredDataResourceClient);
    expect(client.getResourceId()).toBe(resourceId);
  });

  describe('StructuredDataResourceClient', () => {
    let structuredDataClient;
    const testResourceId = 'session://test-session/data-resource/test-task';

    beforeEach(async () => {
      structuredDataClient = await resourceService.openStructuredDataResourceClient(testResourceId);
    });

    it('should preview structured data correctly', async () => {
      const mockResponse = {
        data: {
          total_rows: 2,
          data_schema: {
            fields: [
              { name: 'id', type: 'integer', nullable: false },
              { name: 'name', type: 'string', nullable: true }
            ]
          },
          data: [
            { id: 1, name: 'Alice' },
            { id: 2, name: 'Bob' }
          ]
        }
      };
      axios.post.mockResolvedValue(mockResponse);

      const result = await structuredDataClient.previewData(0, 10);

      expect(axios.post).toHaveBeenCalledWith('/resources/data-resource/structured/preview', {
        resource_id: testResourceId,
        limit: 10,
        offset: 0
      });
      expect(result.total).toBe(2);
      expect(result.schema.length).toBe(2);
      // expect(result.schema[0]).toBeInstanceOf(SchemaField); // This check is no longer needed/relevant here
      expect(result.data).toEqual([
        { id: 1, name: 'Alice' },
        { id: 2, name: 'Bob' }
      ]);
    });

    it('should handle error when previewing data', async () => {
      const errorMessage = 'Network error';
      axios.post.mockRejectedValue(new Error(errorMessage));

      await expect(structuredDataClient.previewData()).rejects.toThrow(`预览数据失败: ${errorMessage}`);
    });

    it('should get structured data schema correctly', async () => {
      const mockResponse = {
        data: {
          data_schema: {
            fields: [
              { name: 'col1', type: 'string', nullable: true },
              { name: 'col2', type: 'integer', nullable: false }
            ]
          }
        }
      };
      axios.post.mockResolvedValue(mockResponse);

      const schema = await structuredDataClient.getSchema();

      expect(axios.post).toHaveBeenCalledWith('/resources/data-resource/structured/schema', {
        resource_id: testResourceId
      });
      expect(schema.length).toBe(2);
      // expect(schema[0]).toBeInstanceOf(SchemaField); // This check is no longer needed/relevant here
      expect(schema[0].name).toBe('col1');
      expect(schema[1].type).toBe('integer');
    });

    it('should handle error when getting schema', async () => {
      const errorMessage = 'API error';
      axios.post.mockRejectedValue(new Error(errorMessage));

      await expect(structuredDataClient.getSchema()).rejects.toThrow(`获取数据结构失败: ${errorMessage}`);
    });

    it('should return the resource ID', () => {
      expect(structuredDataClient.getResourceId()).toBe(testResourceId);
    });

    it('should resolve when closing the client', async () => {
      await expect(structuredDataClient.close()).resolves.toBeUndefined();
    });
  });
}); 