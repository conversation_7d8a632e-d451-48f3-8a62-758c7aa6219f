/**
 * DataProcessService 单元测试
 * @description 测试数据处理服务的所有功能
 */

import {
  BackendInfo,
  Session,
  TaskInfo,
  OperatorInfo,
  PageParam,
  ExecutionGraph, // Added for new tests
  GraphNode,
  GraphEdge,
  ClientEditStatus,
  PendingChanges,
} from '@/pages/dpe-web/models';
import {
  ExecutionGraphClient,
  MergeMode,
  VerifyResult,
  VerifyFailureDetail,
  Subscription,
} from '@/pages/dpe-web/models/client-api';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }))
}));

const mockAxios = require('axios');

// Mock EventSource for SSE testing
global.EventSource = jest.fn(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  close: jest.fn(),
  readyState: 1
}));

// Mock ExecutionGraphRepositoryService for createSessionFromGraph test
jest.mock('@/pages/dpe-web/services/ExecutionGraphRepositoryService.js', () => ({
  __esModule: true, // This is important for default exports
  default: {
    getGraph: jest.fn(),
  },
}));

const mockExecutionGraphRepositoryService = require('@/pages/dpe-web/services/ExecutionGraphRepositoryService.js').default;

describe('DataProcessService', () => {
  let service;
  let mockClient;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create mock axios instance
    mockClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    };
    
    mockAxios.create.mockImplementation(() => mockClient);
    
    // Create service instance
    service = new DataProcessService('/api');
  });

  afterEach(() => {
    // Clean up SSE connections
    if (service && service.closeAllConnections) {
      service.closeAllConnections();
    }
  });

  describe('constructor', () => {
    test('should create instance with default baseURL', () => {
      const defaultService = new DataProcessService();
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: '/api',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });

    test('should create instance with custom baseURL', () => {
      // Re-initialize service to use a custom baseURL for this specific test
      service = new DataProcessService('http://custom-api.com');
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://custom-api.com',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });
  });

  describe('getBackendTypes', () => {
    test('should return backend types successfully', async () => {
      const mockResponse = {
        data: {
          backends: [
            {
              backend_type: 'duckdb',
              metadata: {
                display_name: 'DuckDB后端',
                description: '基于DuckDB的数据处理后端',
                labels: {},
                annotations: {}
              },
              session_configs: []
            }
          ]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getBackendTypes();

      expect(mockClient.get).toHaveBeenCalledWith('/backends');
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(BackendInfo);
      expect(result[0].type).toBe('duckdb');
      expect(result[0].metadata.displayName).toBe('DuckDB后端');
    });

    test('should throw error when request fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.getBackendTypes()).rejects.toThrow('获取后端类型列表失败: Network error');
    });
  });

  describe('queryBackendTypes', () => {
    test('should return paginated backend types successfully', async () => {
      const mockBackendResponse = {
          data: [
            {
              backend_type: 'duckdb',
              metadata: {
                display_name: 'DuckDB后端',
                description: '基于DuckDB的数据处理后端',
                labels: {},
                annotations: {}
              },
              session_configs: []
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
            pageTotal: 1,
            recordTotal: 1
        }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const pageParam = new PageParam({ pageIndex: 0, limit: 10 });
      const params = {
        pageParam: pageParam,
        filters: { backend_type_pattern: 'duck' }
      };

      const result = await service.queryBackendTypes(params);

      expect(mockClient.post).toHaveBeenCalledWith('/backends/query', {
        pageParam: pageParam.toBackendModel(),
        filters: { backend_type_pattern: 'duck' }
      });
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(BackendInfo);
      expect(result.data[0].type).toBe('duckdb');
      expect(result.data[0].metadata.displayName).toBe('DuckDB后端');
      expect(result.pageParam).toBeInstanceOf(PageParam);
      expect(result.pageParam.pageIndex).toBe(0);
    });

    test('should handle empty params', async () => {
      const mockBackendResponse = {
        data: [],
        pageParam: { pageIndex: 0, limit: 10, pageTotal: 0, recordTotal: 0 }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const result = await service.queryBackendTypes();

      expect(mockClient.post).toHaveBeenCalledWith('/backends/query', {
        pageParam: undefined,
        filters: undefined,
      });
      expect(result.data).toHaveLength(0);
    });

    test('should throw error when request fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      const params = {
        pageParam: new PageParam({ pageIndex: 0, limit: 10 }),
        filters: { backend_type_pattern: 'duck' }
      };

      await expect(service.queryBackendTypes(params)).rejects.toThrow('查询后端类型列表失败: Network error');
    });
  });

  describe('createSession', () => {
    test('should create session successfully', async () => {
      const mockResponse = {
        data: {
          session_id: 'session-123',
          backend_type: 'duckdb',
          state: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          metadata: {
            display_name: '测试会话',
            description: '这是一个测试会话',
            labels: { test: 'label' },
            annotations: { note: 'annotation' }
          }
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.createSession('duckdb', {
        sessionId: 'my-session',
        config: { max_memory: '4GB' }
      });

      expect(mockClient.post).toHaveBeenCalledWith('/sessions', {
        backend_name: 'duckdb',
        session_id: 'my-session',
        config: { max_memory: '4GB' }
      });
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('session-123');
      expect(result.backendType).toBe('duckdb');
      expect(result.state).toBe('RUNNING');
      expect(result.createdAt).toEqual(new Date('2023-01-01T00:00:00Z'));
      expect(result.metadata.displayName).toBe('测试会话');
    });

    test('should create session with minimal params', async () => {
      const mockResponse = {
        data: {
          session_id: 'session-456',
          backend_type: 'duckdb',
          state: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.createSession();

      expect(mockClient.post).toHaveBeenCalledWith('/sessions', {});
      expect(result.id).toBe('session-456');
    });

    test('should throw error when request fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      await expect(service.createSession('duckdb')).rejects.toThrow('创建会话失败: Network error');
    });
  });

  describe('createDagSession', () => {
    test('should create DAG session successfully with a graph', async () => {
      const mockResponse = {
        data: {
          session_id: 'dag-session-123',
          backend_type: 'duckdb',
          state: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const graph = new ExecutionGraph({
        id: 'graph-1',
        metadata: { displayName: '测试图' },
        nodes: { 'node-1': new GraphNode({ id: 'node-1', opType: 'test-op' }) },
        edges: [new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'output' })],
      });

      const result = await service.createDagSession('duckdb', {
        sessionId: 'my-dag-session',
        config: { max_memory: '4GB' },
        graph
      });

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/dag', {
        backend_name: 'duckdb',
        session_id: 'my-dag-session',
        config: { max_memory: '4GB' },
        graph: graph.toBackendModel(),
      });
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('dag-session-123');
    });

    test('should create DAG session successfully without a graph', async () => {
      const mockResponse = {
        data: {
          session_id: 'dag-session-456',
          backend_type: 'duckdb',
          state: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.createDagSession('duckdb', {
        sessionId: 'my-dag-session-no-graph',
        config: { max_memory: '4GB' },
      });

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/dag', {
        backend_name: 'duckdb',
        session_id: 'my-dag-session-no-graph',
        config: { max_memory: '4GB' },
      });
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('dag-session-456');
    });

    test('should throw error when create DAG session fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      await expect(service.createDagSession('duckdb')).rejects.toThrow('创建DAG会话失败: Network error');
    });
  });

  describe('listSessions', () => {
    test('should return list of sessions successfully', async () => {
      const mockResponse = {
        data: {
          sessions: [{
            session_id: 'session-1',
              backend_type: 'duckdb',
            state: 'RUNNING',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          }, {
            session_id: 'session-2',
              backend_type: 'spark',
            state: 'CLOSED',
            created_at: '2023-01-02T00:00:00Z',
            updated_at: '2023-01-02T00:00:00Z'
          }]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listSessions();

      expect(mockClient.get).toHaveBeenCalledWith('/sessions');
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(Session);
      expect(result[0].id).toBe('session-1');
      expect(result[1].id).toBe('session-2');
    });

    test('should throw error when listing sessions fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.listSessions()).rejects.toThrow('获取会话列表失败: Network error');
    });
  });

  describe('querySessions', () => {
    test('should return paginated sessions successfully', async () => {
      const mockBackendResponse = {
          data: [
            {
            session_id: 'session-1',
              backend_type: 'duckdb',
            state: 'RUNNING',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
          pageTotal: 1,
            recordTotal: 1
        }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const pageParam = new PageParam({ pageIndex: 0, limit: 10 });
      const params = {
        pageParam: pageParam,
        filters: { backendType: 'duckdb' }
      };

      const result = await service.querySessions(params);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/query', {
        pageParam: pageParam.toBackendModel(),
        filters: { backendType: 'duckdb' }
      });
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(Session);
      expect(result.data[0].id).toBe('session-1');
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });

    test('should handle empty query params', async () => {
      const mockBackendResponse = {
        data: [],
        pageParam: { pageIndex: 0, limit: 10, pageTotal: 0, recordTotal: 0 }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const result = await service.querySessions();

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/query', {
        pageParam: undefined,
        filters: undefined,
      });
      expect(result.data).toHaveLength(0);
    });

    test('should throw error when query sessions fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      const params = {
        pageParam: new PageParam({ pageIndex: 0, limit: 10 }),
        filters: { backendType: 'duckdb' }
      };

      await expect(service.querySessions(params)).rejects.toThrow('查询会话列表失败: Network error');
    });
  });

  describe('getSession', () => {
    test('should return session details successfully', async () => {
      const mockResponse = {
        data: {
          session_id: 'session-123',
          backend_type: 'duckdb',
          state: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          metadata: {
            display_name: '测试会话详情',
            description: '这是一个测试会话详情',
            labels: { detail: 'test' },
            annotations: { info: 'more' }
          }
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getSession('session-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123');
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('session-123');
      expect(result.metadata.displayName).toBe('测试会话详情');
    });

    test('should throw error when getting session details fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.getSession('session-123')).rejects.toThrow('获取会话详情失败: Network error');
    });
  });

  describe('closeSession', () => {
    test('should close session successfully', async () => {
      const mockResponse = {
        data: true
      };

      mockClient.delete.mockResolvedValue(mockResponse);

      const result = await service.closeSession('session-123');

      expect(mockClient.delete).toHaveBeenCalledWith('/sessions/session-123');
      expect(result).toBe(true);
    });

    test('should throw error when closing session fails', async () => {
      const error = new Error('Network error');
      mockClient.delete.mockRejectedValue(error);

      await expect(service.closeSession('session-123')).rejects.toThrow('关闭会话失败: Network error');
    });
  });

  describe('listOperatorTypes', () => {
    test('should return list of operator types successfully', async () => {
      const mockResponse = {
        data: {
          operators: [{
            type: 'op-1',
            metadata: { display_name: 'Operator One' },
            op_config_metadata: [],
            input_fields: []
          }, {
            type: 'op-2',
            metadata: { display_name: 'Operator Two' },
            op_config_metadata: [],
            input_fields: []
          }]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listOperatorTypes('duckdb');

      expect(mockClient.get).toHaveBeenCalledWith('/backends/duckdb/supported_operators');
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(OperatorInfo);
      expect(result[0].type).toBe('op-1');
      expect(result[0].metadata.displayName).toBe('Operator One');
    });

    test('should return empty array if no operators', async () => {
      const mockResponse = {
        data: {}
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listOperatorTypes('duckdb');

      expect(result).toHaveLength(0);
    });

    test('should throw error when listing operator types fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.listOperatorTypes('duckdb')).rejects.toThrow('获取算子类型列表失败: Network error');
    });
  });

  describe('queryOperatorTypes', () => {
    test('should return paginated operator types successfully', async () => {
      const mockBackendResponse = {
          data: [
            {
            type: 'op-1',
            metadata: { display_name: 'Operator One' },
            op_config_metadata: [],
            input_fields: []
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
          pageTotal: 1,
            recordTotal: 1
        }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const pageParam = new PageParam({ pageIndex: 0, limit: 10 });
      const params = {
        pageParam: pageParam,
        filters: { category: 'data-source' }
      };

      const result = await service.queryOperatorTypes('duckdb', params);

      expect(mockClient.post).toHaveBeenCalledWith('/backends/duckdb/operators/query', {
        pageParam: pageParam.toBackendModel(),
        filters: { category: 'data-source' }
      });
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(OperatorInfo);
      expect(result.data[0].type).toBe('op-1');
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });

    test('should handle empty query params', async () => {
      const mockBackendResponse = {
        data: [],
        pageParam: { pageIndex: 0, limit: 10, pageTotal: 0, recordTotal: 0 }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const result = await service.queryOperatorTypes('duckdb');

      expect(mockClient.post).toHaveBeenCalledWith('/backends/duckdb/operators/query', {
        pageParam: undefined,
        filters: undefined,
      });
      expect(result.data).toHaveLength(0);
    });

    test('should throw error when query operator types fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      const params = {
        pageParam: new PageParam({ pageIndex: 0, limit: 10 }),
        filters: { category: 'data-source' }
      };

      await expect(service.queryOperatorTypes('duckdb', params)).rejects.toThrow('查询算子类型列表失败: Network error');
    });
  });

  describe('getOperatorInfo', () => {
    test('should return operator info successfully', async () => {
      const mockResponse = {
        data: {
          type: 'my-operator',
          metadata: { display_name: 'My Operator' },
          op_config_metadata: [{
            key: 'param1',
            schema: { type: 'string' }
          }],
          input_fields: []
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getOperatorInfo('duckdb', 'my-operator');

      expect(mockClient.get).toHaveBeenCalledWith('/backends/duckdb/operators/my-operator');
      expect(result).toBeInstanceOf(OperatorInfo);
      expect(result.type).toBe('my-operator');
      expect(result.metadata.displayName).toBe('My Operator');
      expect(result.opConfigMetadata[0].key).toBe('param1');
    });

    test('should throw error when getting operator info fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.getOperatorInfo('duckdb', 'my-operator')).rejects.toThrow('获取算子配置失败: Network error');
    });
  });

  describe('runOperator', () => {
    test('should run operator successfully', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-123',
          session_id: 'session-123',
          node_id: 'node-1',
          status: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const operatorConfig = { type: 'mock-op', params: { a: 1 } };
      const result = await service.runOperator('session-123', operatorConfig);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/run?wait=false', operatorConfig);
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('task-123');
      expect(result.sessionId).toBe('session-123');
      expect(result.nodeId).toBe('node-1');
    });

    test('should run operator with wait=true', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-456',
          session_id: 'session-456',
          node_id: 'node-2',
          status: 'COMPLETED',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const operatorConfig = { type: 'mock-op-2', params: { b: 2 } };
      const result = await service.runOperator('session-456', operatorConfig, true);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-456/tasks/run?wait=true', operatorConfig);
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('task-456');
      expect(result.status).toBe('COMPLETED');
    });

    test('should throw error when running operator fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      const operatorConfig = { type: 'mock-op', params: { a: 1 } };
      await expect(service.runOperator('session-123', operatorConfig)).rejects.toThrow('提交运行算子任务失败: Network error');
    });
  });

  describe('listTasks', () => {
    test('should return list of tasks successfully', async () => {
      const mockResponse = {
        data: {
          tasks: [{
              task_id: 'task-1',
            session_id: 's1',
            node_id: 'n1',
              status: 'RUNNING',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          }, {
            task_id: 'task-2',
            session_id: 's1',
            node_id: 'n2',
            status: 'COMPLETED',
            created_at: '2023-01-02T00:00:00Z',
            updated_at: '2023-01-02T00:00:00Z'
          }]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listTasks('session-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/tasks');
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(TaskInfo);
      expect(result[0].id).toBe('task-1');
      expect(result[1].id).toBe('task-2');
    });

    test('should throw error when listing tasks fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.listTasks('session-123')).rejects.toThrow('获取任务列表失败: Network error');
    });
  });

  describe('queryTasks', () => {
    test('should return paginated tasks successfully', async () => {
      const mockBackendResponse = {
          data: [
            {
              task_id: 'task-1',
            session_id: 's1',
            node_id: 'n1',
            status: 'RUNNING',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
          pageTotal: 1,
            recordTotal: 1
        }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const pageParam = new PageParam({ pageIndex: 0, limit: 10 });
      const params = {
        pageParam: pageParam,
        filters: { status: 'RUNNING' }
      };

      const result = await service.queryTasks('session-123', params);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/query', {
        pageParam: pageParam.toBackendModel(),
        filters: { status: 'RUNNING' }
      });
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(TaskInfo);
      expect(result.data[0].id).toBe('task-1');
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });

    test('should handle empty query params', async () => {
      const mockBackendResponse = {
        data: [],
        pageParam: { pageIndex: 0, limit: 10, pageTotal: 0, recordTotal: 0 }
      };

      mockClient.post.mockResolvedValue({
        data: mockBackendResponse
      });

      const result = await service.queryTasks('session-123');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/query', {
        pageParam: undefined,
        filters: undefined,
      });
      expect(result.data).toHaveLength(0);
    });

    test('should throw error when query tasks fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      const params = {
        pageParam: new PageParam({ pageIndex: 0, limit: 10 }),
        filters: { status: 'RUNNING' }
      };

      await expect(service.queryTasks('session-123', params)).rejects.toThrow('查询任务列表失败: Network error');
    });
  });

  describe('getTaskInfo', () => {
    test('should return task info successfully', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-123',
          session_id: 'session-123',
          node_id: 'node-1',
          status: 'COMPLETED',
          message: 'Task completed',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          result_resource_id: 'res-1',
          result: { value: 123 },
          is_upstream_changed: false,
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getTaskInfo('session-123', 'task-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/tasks/task-123?wait=false&timeout=60');
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('task-123');
      expect(result.status).toBe('COMPLETED');
      expect(result.message).toBe('Task completed');
      expect(result.resultResourceId).toBe('res-1');
      expect(result.isUpstreamChanged).toBe(false);
    });

    test('should return task info with wait and timeout', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-456',
          session_id: 'session-456',
          node_id: 'node-2',
          status: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getTaskInfo('session-456', 'task-456', true, 10);

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-456/tasks/task-456?wait=true&timeout=10');
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('task-456');
      expect(result.status).toBe('RUNNING');
    });

    test('should throw error when getting task info fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.getTaskInfo('session-123', 'task-123')).rejects.toThrow('获取任务详情失败: Network error');
    });
  });

  describe('cancelTask', () => {
    test('should cancel task successfully', async () => {
      const mockResponse = {
        data: true
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.cancelTask('session-123', 'task-123');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/task-123/cancel');
      expect(result).toBe(true);
    });

    test('should throw error when canceling task fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      await expect(service.cancelTask('session-123', 'task-123')).rejects.toThrow('取消任务失败: Network error');
    });
  });

  describe('runDagTasks', () => {
    test('should run DAG tasks successfully', async () => {
      const mockResponse = {
        data: {
          executed_tasks: [{
            task_id: 'task-1',
            session_id: 's1',
            node_id: 'n1',
            status: 'COMPLETED',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          }]
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const taskIds = ['task-1'];
      const result = await service.runDagTasks('session-1', taskIds);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-1/dag/run_task', {
        task_ids: taskIds,
        rerun_mode: 'NEVER'
      });
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(TaskInfo);
      expect(result[0].id).toBe('task-1');
    });

    test('should run DAG tasks with rerun mode', async () => {
      const mockResponse = {
        data: { 
          executed_tasks: [{
            task_id: 'task-2',
            session_id: 's1',
            node_id: 'n2',
            status: 'COMPLETED',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          }]
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const taskIds = ['task-2'];
      const result = await service.runDagTasks('session-1', taskIds, 'ALL');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-1/dag/run_task', {
        task_ids: taskIds,
        rerun_mode: 'ALL'
      });
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('task-2');
    });

    test('should throw error when running DAG tasks fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      await expect(service.runDagTasks('session-1', ['task-1'])).rejects.toThrow('批量运行DAG任务失败: Network error');
    });
  });

  describe('runAllDagTasks', () => {
    test('should run all DAG tasks successfully', async () => {
      const mockResponse = {
        data: {
          executed_tasks: [{
            task_id: 'task-all-1',
            session_id: 's1',
            node_id: 'n1',
            status: 'COMPLETED',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          }]
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.runAllDagTasks('session-1');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-1/dag/run_all_tasks', {
        rerun_mode: 'NEVER'
      });
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(TaskInfo);
      expect(result[0].id).toBe('task-all-1');
    });

    test('should run all DAG tasks with rerun mode', async () => {
      const mockResponse = {
        data: {
          executed_tasks: [{
            task_id: 'task-all-2',
            session_id: 's1',
            node_id: 'n2',
            status: 'COMPLETED',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          }]
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.runAllDagTasks('session-1', 'ALL');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-1/dag/run_all_tasks', {
        rerun_mode: 'ALL'
      });
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('task-all-2');
    });

    test('should throw error when running all DAG tasks fails', async () => {
      const error = new Error('Network error');
      mockClient.post.mockRejectedValue(error);

      await expect(service.runAllDagTasks('session-1')).rejects.toThrow('运行所有DAG任务失败: Network error');
    });
  });

  describe('deleteDagTasks', () => {
    test('should delete DAG tasks successfully', async () => {
      const mockResponse = {
        data: { message: 'tasks deleted' }
      };

      mockClient.delete.mockResolvedValue(mockResponse);

      const taskIds = ['task-1'];
      const result = await service.deleteDagTasks('session-1', taskIds);

      expect(mockClient.delete).toHaveBeenCalledWith('/sessions/session-1/dag/tasks', {
        data: {
          task_ids: taskIds,
          force: false,
          cleanup_resources: true
        }
      });
      expect(result.message).toBe('tasks deleted');
    });

    test('should delete DAG tasks with force and no cleanup', async () => {
      const mockResponse = {
        data: { message: 'tasks deleted forcefully' }
      };

      mockClient.delete.mockResolvedValue(mockResponse);

      const taskIds = ['task-2'];
      const result = await service.deleteDagTasks('session-1', taskIds, true, false);

      expect(mockClient.delete).toHaveBeenCalledWith('/sessions/session-1/dag/tasks', {
        data: {
          task_ids: taskIds,
          force: true,
          cleanup_resources: false
        }
      });
      expect(result.message).toBe('tasks deleted forcefully');
    });

    test('should throw error when deleting DAG tasks fails', async () => {
      const error = new Error('Network error');
      mockClient.delete.mockRejectedValue(error);

      await expect(service.deleteDagTasks('session-1', ['task-1'])).rejects.toThrow('批量删除DAG任务失败: Network error');
    });
  });

  describe('exportExecutionGraph', () => {
    test('should export execution graph successfully', async () => {
      const mockGraphData = {
        graph_id: 'exported-graph-1',
        nodes: {},
        edges: [],
        create_time: '2023-01-01T00:00:00Z',
        update_time: '2023-01-01T00:00:00Z',
        metadata: {
          display_name: '导出的图',
        }
      };

      mockClient.get.mockResolvedValue({
        data: mockGraphData
      });

      const result = await service.exportExecutionGraph('session-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/dag/export_graph');
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('exported-graph-1');
      expect(result.metadata.displayName).toBe('导出的图');
    });

    test('should throw error when exporting execution graph fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.exportExecutionGraph('session-123')).rejects.toThrow('导出执行图失败: Network error');
    });
  });

  describe('listenSessionLifecycle', () => {
    let listener;
    let eventSourceInstance;

    beforeEach(() => {
      listener = {
        onSessionCreated: jest.fn(),
        onSessionStateChanged: jest.fn(),
        onSessionClosed: jest.fn(),
      };
      // Capture the instance created by new EventSource
      EventSource.mockImplementation((url) => {
        eventSourceInstance = {
          url: url,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1
      };
        return eventSourceInstance;
      });
    });

    test('should register session created event listener', () => {
      const subscription = service.listenSessionLifecycle(listener);

      expect(EventSource).toHaveBeenCalledWith('/api/sessions/lifecycle-subscribe');
      expect(eventSourceInstance.addEventListener).toHaveBeenCalledWith('session_lifecycle', expect.any(Function));

      // Simulate event
      const eventHandler = eventSourceInstance.addEventListener.mock.calls[0][1];
      const mockEventData = {
        event_type: 'session_created',
        session_id: 's1',
        backend_type: 'duckdb',
        state: 'CREATED',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };
      eventHandler({ data: JSON.stringify(mockEventData) });

      expect(listener.onSessionCreated).toHaveBeenCalledWith(expect.any(Session));
      expect(listener.onSessionCreated.mock.calls[0][0].id).toBe('s1');
      expect(subscription).toBeInstanceOf(Subscription);
    });

    test('should register session state changed event listener', () => {
      const subscription = service.listenSessionLifecycle(listener);

      const eventHandler = eventSourceInstance.addEventListener.mock.calls[0][1];
      const mockEventData = {
        event_type: 'session_state_changed',
        session_id: 's1',
        backend_type: 'duckdb',
        state: 'RUNNING',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        data: {
          old_state: 'CREATED',
          new_state: 'RUNNING',
        }
      };
      eventHandler({ data: JSON.stringify(mockEventData) });

      expect(listener.onSessionStateChanged).toHaveBeenCalledWith(expect.any(Session), 'CREATED', 'RUNNING');
      expect(subscription).toBeInstanceOf(Subscription);
    });

    test('should register session closed event listener', () => {
      const subscription = service.listenSessionLifecycle(listener);

      const eventHandler = eventSourceInstance.addEventListener.mock.calls[0][1];
      const mockEventData = {
        event_type: 'session_closed',
        session_id: 's1',
        backend_type: 'duckdb',
        state: 'CLOSED',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };
      eventHandler({ data: JSON.stringify(mockEventData) });

      expect(listener.onSessionClosed).toHaveBeenCalledWith(expect.any(Session));
      expect(subscription).toBeInstanceOf(Subscription);
    });

    test('should unsubscribe correctly', () => {
      const subscription = service.listenSessionLifecycle(listener);
      subscription.unsubscribe();
      expect(eventSourceInstance.close).toHaveBeenCalledTimes(1);
    });
  });

  describe('listenTaskLifecycle', () => {
    let listener;
    let eventSourceInstance;

    beforeEach(() => {
      listener = {
        onTaskStatusChanged: jest.fn(),
        onTaskCompleted: jest.fn(),
        onTaskError: jest.fn(),
      };
      EventSource.mockImplementation((url) => {
        eventSourceInstance = {
          url: url,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1
      };
        return eventSourceInstance;
      });
    });

    test('should register task status changed event listener', () => {
      const subscription = service.listenTaskLifecycle('session-1', listener);

      expect(EventSource).toHaveBeenCalledWith('/api/sessions/lifecycle-subscribe?session_id=session-1');
      expect(eventSourceInstance.addEventListener).toHaveBeenCalledWith('task_lifecycle', expect.any(Function));

      const eventHandler = eventSourceInstance.addEventListener.mock.calls[0][1];
      const mockEventData = {
        event_type: 'task_status_changed',
        task_id: 't1',
        session_id: 's1',
        node_id: 'n1',
        status: 'RUNNING',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        data: {
          old_status: 'PENDING',
          new_status: 'RUNNING',
        }
      };
      eventHandler({ data: JSON.stringify(mockEventData) });

      expect(listener.onTaskStatusChanged).toHaveBeenCalledWith(expect.any(TaskInfo), 'PENDING', 'RUNNING');
      expect(subscription).toBeInstanceOf(Subscription);
    });

    test('should register task completed event listener', () => {
      const subscription = service.listenTaskLifecycle('session-1', listener);

      const eventHandler = eventSourceInstance.addEventListener.mock.calls[0][1];
      const mockEventData = {
        event_type: 'task_completed',
        task_id: 't1',
        session_id: 's1',
        node_id: 'n1',
        status: 'COMPLETED',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };
      eventHandler({ data: JSON.stringify(mockEventData) });

      expect(listener.onTaskCompleted).toHaveBeenCalledWith(expect.any(TaskInfo));
      expect(subscription).toBeInstanceOf(Subscription);
    });

    test('should register task error event listener', () => {
      const subscription = service.listenTaskLifecycle('session-1', listener);

      const eventHandler = eventSourceInstance.addEventListener.mock.calls[0][1];
      const mockEventData = {
        event_type: 'task_error',
        task_id: 't1',
        session_id: 's1',
        node_id: 'n1',
        status: 'FAILED',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        data: {
          error_message: 'Something went wrong',
        }
      };
      eventHandler({ data: JSON.stringify(mockEventData) });

      expect(listener.onTaskError).toHaveBeenCalledWith(expect.any(TaskInfo), expect.any(Error));
      expect(listener.onTaskError.mock.calls[0][1].message).toBe('Something went wrong');
      expect(subscription).toBeInstanceOf(Subscription);
    });

    test('should unsubscribe correctly', () => {
      const subscription = service.listenTaskLifecycle('session-1', listener);
      subscription.unsubscribe();
      expect(eventSourceInstance.close).toHaveBeenCalledTimes(1);
    });
  });

  describe('closeAllConnections', () => {
    test('should close all active SSE connections', () => {
      const mockEventSource1 = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1
      };
      const mockEventSource2 = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1
      };

      EventSource.mockImplementationOnce(() => mockEventSource1);
      service.listenSessionLifecycle({}, 's1');
      EventSource.mockImplementationOnce(() => mockEventSource2);
      service.listenTaskLifecycle('s2', {});

      service.closeAllConnections();

      expect(mockEventSource1.close).toHaveBeenCalledTimes(1);
      expect(mockEventSource2.close).toHaveBeenCalledTimes(1);
      expect(service.sseConnections.size).toBe(0);
    });
  });

  describe('openGraphClient', () => {
    let mockWebSocket;
    beforeEach(() => {
      mockWebSocket = {
        onopen: null,
        onclose: null,
        onerror: null,
        onmessage: null,
        send: jest.fn(),
        close: jest.fn(),
      };
      global.WebSocket = jest.fn(() => mockWebSocket);
    });

    afterEach(() => {
      global.WebSocket = undefined;
    });

    test('should open graph client successfully', async () => {
      const clientPromise = service.openGraphClient('session-123');
      mockWebSocket.onopen(); // Simulate successful connection
      const client = await clientPromise;

      expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:8000/api/sessions/session-123/dag/graph-client');
      expect(client).toBeInstanceOf(ExecutionGraphClient);
      expect(client.sessionId).toBe('session-123');
      expect(client.isConnected).toBe(true);
    });

    test('should throw error if WebSocket connection fails', async () => {
      const clientPromise = service.openGraphClient('session-123');
      mockWebSocket.onerror({ message: 'Connection refused' }); // Simulate connection error

      await expect(clientPromise).rejects.toThrow('打开执行图客户端失败: WebSocket连接失败: Connection refused');
    });

    test('should throw error if WebSocket connection times out', async () => {
      jest.useFakeTimers();
      const clientPromise = service.openGraphClient('session-123');
      jest.advanceTimersByTime(5001); // Advance past timeout

      await expect(clientPromise).rejects.toThrow('打开执行图客户端失败: WebSocket连接超时');
      jest.useRealTimers();
    });

    test('add_nodes should send correct payload and return node IDs', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const nodes = [new GraphNode({ id: 'n1', opType: 'op-1' })];
      const mockResult = ['n1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.add_nodes(nodes);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'add_nodes',
        payload: {
          nodes: nodes.map(node => node.toBackendModel()),
        },
      }));
      expect(result).toEqual(mockResult);
    });

    test('update_nodes_config should send correct payload and return node IDs', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const updates = { 'n1': { key: 'value' } };
      const mockResult = ['n1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.update_nodes_config(updates);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'update_nodes_config',
        payload: { updates },
      }));
      expect(result).toEqual(mockResult);
    });

    test('delete_nodes should send correct payload and return node IDs', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const nodeIds = ['n1'];
      const mockResult = ['n1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.delete_nodes(nodeIds);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'delete_nodes',
        payload: { node_ids: nodeIds },
      }));
      expect(result).toEqual(mockResult);
    });

    test('add_edges should send correct payload and return edge IDs', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const edges = [new GraphEdge({ source: 'n1', target: 'n2', targetConfig: 'out' })];
      const mockResult = ['e1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.add_edges(edges);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'add_edges',
        payload: {
          edges: edges.map(edge => edge.toBackendModel()),
        },
      }));
      expect(result).toEqual(mockResult);
    });

    test('delete_edges should send correct payload and return edge IDs', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const edges = [new GraphEdge({ source: 'n1', target: 'n2', targetConfig: 'out' })];
      const mockResult = ['e1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.delete_edges(edges);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'delete_edges',
        payload: {
          edges: edges.map(edge => edge.toBackendModel()),
        },
      }));
      expect(result).toEqual(mockResult);
    });

    test('add_op should send correct payload and return operator info', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const operator = new OperatorInfo({ type: 'test-op', metadata: { displayName: 'Test Operator' } });
      const mockResult = { type: 'test-op', metadata: { display_name: 'Test Operator' } };
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.add_op(operator);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'add_op',
        payload: {
          operator: operator.toBackendModel(),
        },
      }));
      expect(result).toBeInstanceOf(OperatorInfo);
      expect(result.type).toBe('test-op');
      expect(result.metadata.displayName).toBe('Test Operator');
    });

    test('load_graph should send correct payload and return affected node IDs', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const graph = new ExecutionGraph({ id: 'g1', nodes: { 'n1': new GraphNode({ id: 'n1', opType: 'op-A' }) } });
      const mockResult = ['n1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.load_graph(graph, MergeMode.OVERWRITE);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'load_graph',
        payload: {
          graph: graph.toBackendModel(),
          merge_mode: MergeMode.OVERWRITE,
        },
      }));
      expect(result).toEqual(mockResult);
    });

    test('verify should return verification result', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const mockResult = { valid: false, failed_details: [{ rule_type: 'test-rule', message: 'Test message', extra_info: {} }] };
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.verify();
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'verify',
        payload: {},
      }));
      expect(result).toBeInstanceOf(VerifyResult);
      expect(result.valid).toBe(false);
      expect(result.failedDetails[0]).toBeInstanceOf(VerifyFailureDetail);
      expect(result.failedDetails[0].message).toBe('Test message');
    });

    test('save should return save result', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const mockResult = ['g1'];
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.save();
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'save',
        payload: {},
      }));
      expect(result).toEqual(mockResult);
    });

    test('export_graph should return exported graph', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const mockResult = { graph_id: 'exported-g1', nodes: {}, edges: [] };
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.export_graph();
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'export_graph',
        payload: {},
      }));
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('exported-g1');
    });

    test('resolve_task should send correct payload and return task info', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const operator = new OperatorInfo({ type: 'test-op', metadata: { displayName: 'Test Operator' } });
      const mockResult = { task_id: 't1', status: 'PENDING' };
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.resolve_task(operator);
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'resolve_task',
        payload: {
          operator: operator.toBackendModel(),
        },
      }));
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('t1');
    });

    test('get_edit_status should return client edit status', async () => {
      const client = await service.openGraphClient('s1');
      mockWebSocket.onopen();

      const mockResult = {
        has_pending_changes: true,
        current_effective_graph: { graph_id: 'effective-g1', nodes: {}, edges: [] },
        current_graph: { graph_id: 'current-g1', nodes: {}, edges: [] },
        pending_changes: {
          nodes_to_add: { 'node-1': { id: 'node-1', op_type: 'test-op' } },
          nodes_to_update: { 'node-2': { param1: 'value1' } },
          nodes_to_delete: ['node-3'],
          edges_to_add: [{ source: 'n1', target: 'n2', target_config: 'output' }],
          edges_to_delete: [{ source: 'n4', target: 'n5', target_config: 'input' }],
        },
      };
      mockWebSocket.onmessage({ data: JSON.stringify({ message_id: 'msg-1', success: true, result: mockResult }) });

      const resultPromise = client.get_edit_status();
      const result = await resultPromise;

      expect(mockWebSocket.send).toHaveBeenCalledWith(JSON.stringify({
        message_id: 'msg-1',
        action: 'current_edit_status',
        payload: {},
      }));
      expect(result).toBeInstanceOf(ClientEditStatus);
      expect(result.hasPendingChanges).toBe(true);
      expect(result.currentEffectiveGraph).toBeInstanceOf(ExecutionGraph);
      expect(result.currentEffectiveGraph.id).toBe('effective-g1');
      expect(result.currentGraph).toBeInstanceOf(ExecutionGraph);
      expect(result.currentGraph.id).toBe('current-g1');
      expect(result.pendingChanges).toBeInstanceOf(PendingChanges);
      expect(result.pendingChanges.nodesToAdd['node-1']).toBeInstanceOf(GraphNode);
      expect(result.pendingChanges.nodesToAdd['node-1'].opType).toBe('test-op');
    });
  });

  describe('createSessionFromGraph', () => {
    test('should create session from graph successfully', async () => {
      const mockGraph = new ExecutionGraph({
        id: 'mock-graph-id',
        metadata: { displayName: 'Mock Graph' },
        nodes: { 'nodeA': new GraphNode({ id: 'nodeA', opType: 'source' }) },
        edges: [],
      });

      const mockSessionResponse = {
        session_id: 'new-session-from-graph',
        backend_type: 'test-backend',
        state: 'RUNNING',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
      };

      mockExecutionGraphRepositoryService.getGraph.mockResolvedValue(mockGraph);
      mockClient.post.mockResolvedValue({
        data: mockSessionResponse
      });

      const result = await service.createSessionFromGraph('mock-graph-id', 'test-backend', { config: { test: 'config' } });

      expect(mockExecutionGraphRepositoryService.getGraph).toHaveBeenCalledWith('mock-graph-id');
      expect(mockClient.post).toHaveBeenCalledWith('/sessions/dag', {
        backend_name: 'test-backend',
        config: { test: 'config' },
        graph: mockGraph.toBackendModel(),
      });
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('new-session-from-graph');
    });

    test('should throw error when create session from graph fails', async () => {
      const error = new Error('Graph not found');
      mockExecutionGraphRepositoryService.getGraph.mockRejectedValue(error);

      await expect(service.createSessionFromGraph('non-existent-graph', 'test-backend')).rejects.toThrow('根据执行图创建会话失败: Graph not found');
    });
  });
});