/**
 * MockExecutionGraphClient 测试用例
 * 基于后端 Python 测试用例实现的完整 Jest 测试
 */

import MockExecutionGraphClient, {
  MockDagTask,
  MergeMode,
  VerifyResult,
  VerifyFailureDetail,
  ConflictError,
  OperatorNotFoundError,
  GraphValidationError
} from './MockExecutionGraphClient';

import {
  ExecutionGraph,
  GraphNode,
  GraphEdge,
  ObjectMetadata,
  OperatorInfo
} from '@/pages/dpe-web/models';

import { ExecutionGraphClient, UpdateNodeParam } from '@/pages/dpe-web/models/client-api';


/**
 * 模拟算子
 */
class SimpleReadOperatorForTest {
  constructor(data = {}) {
    this.taskId = data.taskId;
    this.sourcePath = data.sourcePath || data.source_path;
  }
}

describe('MockExecutionGraphClient', () => {
  /** @type {ExecutionGraphClient} */
  let client; 
  /** @type {OperatorInfo[]} */
  let operators;

  beforeEach(() => {
    operators = [
      new OperatorInfo({
        type: 'ReadStructuredDataResource',
        metadata: new ObjectMetadata({ displayName: '读取结构化数据' })
      }),
      new OperatorInfo({
        type: 'FilterStructuredData',
        metadata: new ObjectMetadata({ displayName: '过滤结构化数据' })
      }),
      new OperatorInfo({
        type: 'ReadOp',
        metadata: new ObjectMetadata({ displayName: '读取算子' })
      }),
      new OperatorInfo({
        type: 'FilterOp',
        metadata: new ObjectMetadata({ displayName: '过滤算子' })
      }),
      new OperatorInfo({
        type: 'TransformOp',
        metadata: new ObjectMetadata({ displayName: '转换算子' })
      })
    ];
    client = new MockExecutionGraphClient(operators, 'test-session');
  });

  afterEach(async () => {
    if (client && !client._closed) {
      await client.close();
    }
  });

  describe('客户端初始化', () => {
    it('应该正确初始化客户端', () => {
      const testClient = new MockExecutionGraphClient(operators);
      
      expect(testClient._sessionId).toBe('test-session');
      expect(testClient._closed).toBe(false);
      expect(testClient._supportedOperators.size).toBe(5);
      expect(testClient._supportedOperators.has('ReadOp')).toBe(true);
      expect(testClient._currentExecutionGraph).toBeDefined();
      
      // 检查pending_changes初始状态
      expect(Object.keys(testClient._pendingChanges.nodesToAdd)).toHaveLength(0);
      expect(Object.keys(testClient._pendingChanges.operatorsToAdd)).toHaveLength(0);
    });

    it('应该支持带初始图的初始化', () => {
      const initialGraph = new ExecutionGraph({
        id: 'initial-graph',
        metadata: new ObjectMetadata({ displayName: '初始图' }),
        nodes: {
          'init-node': new GraphNode({
            id: 'init-node',
            opType: 'ReadOp',
            opConfig: { source: 'initial' }
          })
        },
        edges: []
      });
      
      const testClient = new MockExecutionGraphClient(operators, 'test-session', initialGraph);
      
      expect(testClient._currentExecutionGraph.id).toBe('initial-graph');
      expect(testClient._currentExecutionGraph.nodes['init-node']).toBeDefined();
    });

    it('应该支持空算子列表初始化', () => {
      const testClient = new MockExecutionGraphClient([], 'test-session');
      
      expect(testClient._supportedOperators.size).toBe(0);
      expect(testClient._sessionId).toBe('test-session');
    });
  });

  describe('节点操作', () => {
    it('应该正确添加节点', async () => {
      const nodes = [
        new GraphNode({
          id: 'node-1',
          metadata: new ObjectMetadata({ displayName: '测试节点1' }),
          opType: 'ReadStructuredDataResource',
          opConfig: { source: 'data-resource://test', format: 'csv' }
        }),
        new GraphNode({
          id: 'node-2',
          metadata: new ObjectMetadata({ displayName: '测试节点2' }),
          opType: 'FilterStructuredData',
          opConfig: { resourceId: 'session://test/data-resource/node-1' }
        })
      ];

      const result = await client.add_nodes(nodes);

      expect(result).toHaveLength(2);
      expect(result).toContain('node-1');
      expect(result).toContain('node-2');

      // 验证节点被添加到pending_changes中
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(2);
      expect(client._pendingChanges.nodesToAdd['node-1']).toBeDefined();
      expect(client._pendingChanges.nodesToAdd['node-2']).toBeDefined();
    });

    it('应该拒绝不支持的算子类型', async () => {
      const unsupportedNode = new GraphNode({
        id: 'unsupported-node',
        metadata: new ObjectMetadata({ displayName: '不支持的节点' }),
        opType: 'UnsupportedOperator',
        opConfig: {}
      });

      await expect(client.add_nodes([unsupportedNode]))
        .rejects.toThrow(OperatorNotFoundError);
      await expect(client.add_nodes([unsupportedNode]))
        .rejects.toThrow('不支持的算子类型: UnsupportedOperator');
    });

    it('应该允许空算子列表时添加任何节点', async () => {
      const emptyOperatorsClient = new MockExecutionGraphClient([], 'test-session');
      
      try {
        const unsupportedNode = new GraphNode({
          id: 'any-node',
          opType: 'AnyOperator',
          opConfig: {}
        });

        const result = await emptyOperatorsClient.add_nodes([unsupportedNode]);
        expect(result).toEqual(['any-node']);
      } finally {
        await emptyOperatorsClient.close();
      }
    });

    it('应该正确更新节点配置', async () => {
      // 先添加一个节点
      const nodes = [new GraphNode({
        id: 'node-1',
        metadata: new ObjectMetadata({ displayName: '测试节点' }),
        opType: 'ReadStructuredDataResource',
        opConfig: { source: 'data-resource://old' }
      })];
      await client.add_nodes(nodes);

      // 更新节点配置
      const configUpdates = {
        'node-1': new UpdateNodeParam({
          metadata: new ObjectMetadata({ displayName: '更新测试节点' }),
          opConfig: { source: 'data-resource://new', format: 'parquet' }
        })
      };

      const result = await client.update_nodes(configUpdates);

      expect(result).toEqual(['node-1']);

      // 验证配置更新被添加到pending_changes中
      expect(client._pendingChanges.nodesToUpdate['node-1']).toBeDefined();
      expect(client._pendingChanges.nodesToUpdate['node-1'].metadata).toBeDefined();
      expect(client._pendingChanges.nodesToUpdate['node-1'].source).toBe('data-resource://new');
      expect(client._pendingChanges.nodesToUpdate['node-1'].format).toBe('parquet');
    });

    it('应该正确删除节点', async () => {
      // 先添加节点
      const nodes = [
        new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} }),
        new GraphNode({ id: 'node-2', opType: 'FilterOp', opConfig: {} })
      ];
      await client.add_nodes(nodes);

      // 删除节点
      const result = await client.delete_nodes(['node-1']);

      expect(result).toEqual(['node-1']);

      // 验证节点被从待添加列表中移除
      expect(client._pendingChanges.nodesToAdd['node-1']).toBeUndefined();
      expect(client._pendingChanges.nodesToAdd['node-2']).toBeDefined();

      // 验证节点被添加到待删除列表中
      expect(client._pendingChanges.nodesToDelete.has('node-1')).toBe(true);
    });
  });

  describe('边操作', () => {
    it('应该正确添加边', async () => {
      const edges = [
        new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'resourceId' }),
        new GraphEdge({ source: 'node-2', target: 'node-3', targetConfig: 'resourceId' })
      ];

      const result = await client.add_edges(edges);

      expect(result).toHaveLength(2);
      expect(result).toContain('node-1-node-2');
      expect(result).toContain('node-2-node-3');

      // 验证边被添加到pending_changes中
      expect(client._pendingChanges.edgesToAdd).toHaveLength(2);
    });

    it('应该正确删除边', async () => {
      const edges = [
        new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'resourceId' })
      ];

      const result = await client.delete_edges(edges);

      expect(result).toEqual(['node-1-node-2']);

      // 验证边被添加到待删除列表中
      expect(client._pendingChanges.edgesToDelete).toHaveLength(1);
    });
  });

  describe('算子操作', () => {
    it('应该正确添加算子', async () => {
      const op = new SimpleReadOperatorForTest({
        sourcePath: '/data/test.csv'
      }); // 没有task_id，会自动生成

      const result = await client.add_op(op);

      // 验证task_id被自动生成
      expect(result.taskId).toBeDefined();
      expect(result.taskId).not.toBeNull();

      // 验证算子被添加到pending_changes中
      expect(client._pendingChanges.operatorsToAdd[result.taskId]).toBeDefined();
      expect(client._pendingChanges.operatorsToAdd[result.taskId].taskId).toBe(result.taskId);
    });

    it('应该正确添加带有已有task_id的算子', async () => {
      const op = new SimpleReadOperatorForTest({
        taskId: 'custom-task-id',
        sourcePath: '/data/test.csv'
      });

      const result = await client.add_op(op);

      // 验证使用了已有的task_id
      expect(result.taskId).toBe('custom-task-id');

      // 验证算子被添加到pending_changes中
      expect(client._pendingChanges.operatorsToAdd['custom-task-id']).toBeDefined();
    });
  });

  describe('图加载功能', () => {
    it('应该在冲突时失败', async () => {
      // 先添加一个节点
      const nodes = [new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} })];
      await client.add_nodes(nodes);

      // 创建包含冲突节点的图
      const conflictingGraph = new ExecutionGraph({
        id: 'test-graph',
        metadata: new ObjectMetadata({ displayName: '测试图' }),
        nodes: {
          'node-1': new GraphNode({ id: 'node-1', opType: 'FilterOp', opConfig: {} })
        },
        edges: []
      });

      // 测试FAILED_ON_CONFLICT模式应该抛出异常
      await expect(client.load_graph(conflictingGraph, MergeMode.FAILED_ON_CONFLICT))
        .rejects.toThrow(ConflictError);
    });

    it('应该在覆盖模式下正确加载图', async () => {
      // 先添加一个节点
      const nodes = [new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} })];
      await client.add_nodes(nodes);

      // 创建包含冲突节点的图
      const newGraph = new ExecutionGraph({
        id: 'test-graph',
        metadata: new ObjectMetadata({ displayName: '测试图' }),
        nodes: {
          'node-1': new GraphNode({ id: 'node-1', opType: 'FilterOp', opConfig: {} }),
          'node-2': new GraphNode({ id: 'node-2', opType: 'TransformOp', opConfig: {} })
        },
        edges: [
          new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'resourceId' })
        ]
      });

      const result = await client.load_graph(newGraph, MergeMode.OVERWRITE_EXISTING);

      expect(result).toHaveLength(2);
      expect(result).toContain('node-1');
      expect(result).toContain('node-2');

      // 验证图被正确加载
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(2);
      expect(client._pendingChanges.edgesToAdd).toHaveLength(1);
    });
  });

  describe('验证功能', () => {
    it('应该验证有效图', async () => {
      // 添加有效的节点和边
      const nodes = [
        new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} }),
        new GraphNode({ id: 'node-2', opType: 'FilterOp', opConfig: {} })
      ];
      await client.add_nodes(nodes);

      const edges = [new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'resourceId' })];
      await client.add_edges(edges);

      const result = await client.verify();

      expect(result.valid).toBe(true);
      expect(result.failedDetails).toHaveLength(0);
    });

    it('应该验证无效图', async () => {
      // 设置客户端验证失败
      client.setVerificationFailure(true, '测试验证失败');

      const result = await client.verify();

      expect(result.valid).toBe(false);
      expect(result.failedDetails).toHaveLength(1);
      expect(result.failedDetails[0].message).toContain('测试验证失败');
    });
  });

  describe('保存功能', () => {
    it('应该正确保存变更', async () => {
      // 添加一些变更
      const nodes = [new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} })];
      await client.add_nodes(nodes);

      const edges = [new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'resourceId' })];
      await client.add_edges(edges);

      const result = await client.save();

      expect(result).toContain('node-1');

      // 验证pending_changes被清空
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(0);
      expect(client._pendingChanges.edgesToAdd).toHaveLength(0);
    });
  });

  describe('导出功能', () => {
    it('应该正确导出图', async () => {
      const result = await client.export_graph();

      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('empty-graph');
    });

    it('应该导出初始图', async () => {
      const initialGraph = new ExecutionGraph({
        id: 'initial-test-graph',
        metadata: new ObjectMetadata({ displayName: '初始测试图' }),
        nodes: {},
        edges: []
      });
      
      const testClient = new MockExecutionGraphClient(operators, 'test-session', initialGraph);
      
      try {
        const result = await testClient.export_graph();
        expect(result).toBeInstanceOf(ExecutionGraph);
        expect(result.id).toBe('initial-test-graph');
      } finally {
        await testClient.close();
      }
    });
  });

  describe('任务解析功能', () => {
    it('应该解析已存在的任务', async () => {
      const op = new SimpleReadOperatorForTest({
        taskId: 'existing-task',
        sourcePath: '/data/test.csv'
      });
      
      const mockTask = new MockDagTask('existing-task', 'test-session', op);
      client.addTask('existing-task', mockTask);

      const result = await client.resolve_task(op);

      expect(result).toBe(mockTask);
    });

    it('应该在任务不存在时抛出异常', async () => {
      const op = new SimpleReadOperatorForTest({
        taskId: 'nonexistent-task',
        sourcePath: '/data/test.csv'
      });

      await expect(client.resolve_task(op))
        .rejects.toThrow(OperatorNotFoundError);
    });

    it('应该在算子没有task_id时抛出异常', async () => {
      const op = new SimpleReadOperatorForTest({
        sourcePath: '/data/test.csv'
      }); // 没有task_id

      await expect(client.resolve_task(op))
        .rejects.toThrow('算子没有task_id');
    });
  });

  describe('客户端关闭', () => {
    it('应该正确关闭客户端', async () => {
      // 添加一些待提交的变更
      const nodes = [new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} })];
      await client.add_nodes(nodes);

      // 关闭客户端
      await client.close();

      // 验证pending_changes被清空
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(0);

      // 验证任务被清除
      expect(client._tasks.size).toBe(0);
      expect(client._closed).toBe(true);
    });

    it('应该在关闭后拒绝操作', async () => {
      await client.close();

      await expect(client.add_nodes([]))
        .rejects.toThrow('客户端已关闭');
    });
  });

  describe('客户端隔离', () => {
    it('应该确保客户端之间的隔离性', async () => {
      const client1 = new MockExecutionGraphClient(operators);
      const client2 = new MockExecutionGraphClient(operators);

      try {
        // 在client1中添加节点
        const nodes1 = [new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} })];
        await client1.add_nodes(nodes1);

        // 在client2中添加不同的节点
        const nodes2 = [new GraphNode({ id: 'node-2', opType: 'FilterOp', opConfig: {} })];
        await client2.add_nodes(nodes2);

        // 验证客户端之间的变更是隔离的
        expect(client1._pendingChanges.nodesToAdd['node-1']).toBeDefined();
        expect(client1._pendingChanges.nodesToAdd['node-2']).toBeUndefined();

        expect(client2._pendingChanges.nodesToAdd['node-2']).toBeDefined();
        expect(client2._pendingChanges.nodesToAdd['node-1']).toBeUndefined();
      } finally {
        await client1.close();
        await client2.close();
      }
    });
  });

  describe('批量操作', () => {
    it('应该正确处理批量操作', async () => {
      // 批量添加节点
      const nodes = Array.from({ length: 5 }, (_, i) => 
        new GraphNode({ id: `node-${i}`, opType: 'ReadOp', opConfig: {} })
      );
      const nodeResult = await client.add_nodes(nodes);
      expect(nodeResult).toHaveLength(5);

      // 批量添加边
      const edges = Array.from({ length: 4 }, (_, i) => 
        new GraphEdge({ source: `node-${i}`, target: `node-${i+1}`, targetConfig: 'resourceId' })
      );
      const edgeResult = await client.add_edges(edges);
      expect(edgeResult).toHaveLength(4);

      // 批量删除节点
      const deleteResult = await client.delete_nodes(['node-0', 'node-1']);
      expect(deleteResult).toHaveLength(2);

      // 验证最终状态
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(3); // 5 - 2 = 3
      expect(client._pendingChanges.nodesToDelete.size).toBe(2);
      expect(client._pendingChanges.edgesToAdd).toHaveLength(4);
    });
  });

  describe('状态管理', () => {
    it('应该正确管理pending_changes状态', async () => {
      // 初始状态应该为空
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(0);
      expect(Object.keys(client._pendingChanges.nodesToUpdate)).toHaveLength(0);
      expect(client._pendingChanges.nodesToDelete.size).toBe(0);
      expect(client._pendingChanges.edgesToAdd).toHaveLength(0);
      expect(client._pendingChanges.edgesToDelete).toHaveLength(0);
      expect(Object.keys(client._pendingChanges.operatorsToAdd)).toHaveLength(0);

      // 添加各种变更
      const nodes = [new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} })];
      await client.add_nodes(nodes);

      await client.update_nodes({ 
        'node-1': new UpdateNodeParam({
          opConfig: { param: 'value' }
        })
      });

      const edges = [new GraphEdge({ source: 'node-1', target: 'node-2', targetConfig: 'resourceId' })];
      await client.add_edges(edges);

      const op = new SimpleReadOperatorForTest({
        taskId: 'op-1',
        sourcePath: '/data/pending_test.csv'
      });
      await client.add_op(op);

      // 验证状态正确更新
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(1);
      expect(Object.keys(client._pendingChanges.nodesToUpdate)).toHaveLength(1);
      expect(client._pendingChanges.edgesToAdd).toHaveLength(1);
      expect(Object.keys(client._pendingChanges.operatorsToAdd)).toHaveLength(1);

      // 保存操作
      await client.save();

      // 验证保存后状态被清空
      expect(Object.keys(client._pendingChanges.nodesToAdd)).toHaveLength(0);
      expect(Object.keys(client._pendingChanges.nodesToUpdate)).toHaveLength(0);
      expect(client._pendingChanges.edgesToAdd).toHaveLength(0);
      expect(Object.keys(client._pendingChanges.operatorsToAdd)).toHaveLength(0);
    });
  });

  describe('编辑状态功能', () => {
    it('应该在无未提交变更时返回正确状态', async () => {
      const status = await client.get_edit_status();

      expect(status.hasPendingChanges).toBe(false);
      expect(status.currentEffectiveGraph).toBeInstanceOf(ExecutionGraph);
      expect(status.currentGraph.id).toBe('empty-graph-with-pending-changes');

      // 验证pending_changes为空
      expect(Object.keys(status.pendingChanges.nodesToAdd)).toHaveLength(0);
      expect(Object.keys(status.pendingChanges.nodesToUpdate)).toHaveLength(0);
      expect(status.pendingChanges.nodesToDelete).toHaveLength(0);
      expect(status.pendingChanges.edgesToAdd).toHaveLength(0);
      expect(status.pendingChanges.edgesToDelete).toHaveLength(0);

      // 验证PendingChanges不包含operatorsToAdd字段
      expect(status.pendingChanges.operatorsToAdd).toBeUndefined();
    });

    it('应该在有未提交节点变更时返回正确状态', async () => {
      // 使用带初始图的客户端
      const initialGraph = new ExecutionGraph({
        id: 'test-graph',
        metadata: new ObjectMetadata({ displayName: '测试图' }),
        nodes: {
          'existing-node': new GraphNode({ 
            id: 'existing-node', 
            opType: 'ReadOp', 
            opConfig: { param: 'old_value' } 
          })
        },
        edges: []
      });
      
      const testClient = new MockExecutionGraphClient(operators, 'test-session', initialGraph);

      try {
        // 添加一些未提交的节点变更
        const newNode = new GraphNode({ 
          id: 'new-node', 
          opType: 'FilterOp', 
          opConfig: { filter: 'condition' } 
        });
        await testClient.add_nodes([newNode]);
        await testClient.update_nodes({ 
          'existing-node': new UpdateNodeParam({
            opConfig: { param: 'new_value', extra: 'field' }
          })
        });
        await testClient.delete_nodes(['node-to-delete']);

        const status = await testClient.get_edit_status();

        expect(status.hasPendingChanges).toBe(true);

        // 验证current_graph包含了未提交的变更
        expect(status.currentGraph.nodes['new-node']).toBeDefined();
        expect(status.currentGraph.nodes['new-node'].opType).toBe('FilterOp');
        expect(status.currentGraph.nodes['existing-node'].opConfig.param).toBe('new_value');
        expect(status.currentGraph.nodes['existing-node'].opConfig.extra).toBe('field');

        // 验证pending_changes
        expect(status.pendingChanges.nodesToAdd['new-node']).toBeDefined();
        expect(status.pendingChanges.nodesToUpdate['existing-node']).toBeDefined();
        expect(status.pendingChanges.nodesToDelete).toContain('node-to-delete');
      } finally {
        await testClient.close();
      }
    });

    it('应该在有未提交边变更时返回正确状态', async () => {
      // 使用带边的初始图的客户端
      const existingEdge = new GraphEdge({ 
        source: 'node-1', 
        target: 'node-2', 
        targetConfig: 'input' 
      });
      const initialGraph = new ExecutionGraph({
        id: 'test-graph',
        metadata: new ObjectMetadata({ displayName: '测试图' }),
        nodes: {
          'node-1': new GraphNode({ id: 'node-1', opType: 'ReadOp', opConfig: {} }),
          'node-2': new GraphNode({ id: 'node-2', opType: 'FilterOp', opConfig: {} })
        },
        edges: [existingEdge]
      });
      
      const testClient = new MockExecutionGraphClient(operators, 'test-session', initialGraph);

      try {
        // 添加一些未提交的边变更
        const newEdge = new GraphEdge({ 
          source: 'node-2', 
          target: 'node-3', 
          targetConfig: 'output' 
        });
        const edgeToDelete = new GraphEdge({ 
          source: 'node-1', 
          target: 'node-2', 
          targetConfig: 'input' 
        });

        await testClient.add_edges([newEdge]);
        await testClient.delete_edges([edgeToDelete]);

        const status = await testClient.get_edit_status();

        expect(status.hasPendingChanges).toBe(true);

        // 验证current_graph的边变更
        const currentEdges = status.currentGraph.edges;
        // 应该包含新添加的边
        expect(currentEdges.some(e => 
          e.source === 'node-2' && e.target === 'node-3'
        )).toBe(true);
        // 应该不包含待删除的边
        expect(currentEdges.some(e => 
          e.source === 'node-1' && e.target === 'node-2'
        )).toBe(false);

        // 验证pending_changes
        expect(status.pendingChanges.edgesToAdd).toHaveLength(1);
        expect(status.pendingChanges.edgesToAdd[0].source).toBe('node-2');
        expect(status.pendingChanges.edgesToDelete).toHaveLength(1);
        expect(status.pendingChanges.edgesToDelete[0].source).toBe('node-1');
      } finally {
        await testClient.close();
      }
    });

    it('应该在PendingChanges中排除operators_to_add字段', async () => {
      // 添加算子（这会在内部GraphChanges中添加operators_to_add）
      const op = new SimpleReadOperatorForTest({
        taskId: 'test-op',
        sourcePath: '/data/test.csv'
      });
      await client.add_op(op);

      // 验证内部_pendingChanges包含operators_to_add
      expect(Object.keys(client._pendingChanges.operatorsToAdd)).toHaveLength(1);

      const status = await client.get_edit_status();

      expect(status.hasPendingChanges).toBe(true);

      // 验证PendingChanges不包含operators_to_add字段
      expect(status.pendingChanges.operatorsToAdd).toBeUndefined();
      // 但其他字段应该存在
      expect(status.pendingChanges.nodesToAdd).toBeDefined();
      expect(status.pendingChanges.nodesToUpdate).toBeDefined();
      expect(status.pendingChanges.nodesToDelete).toBeDefined();
      expect(status.pendingChanges.edgesToAdd).toBeDefined();
      expect(status.pendingChanges.edgesToDelete).toBeDefined();
    });

    it('应该在复杂场景下正确处理编辑状态', async () => {
      // 需要添加 OldOp 算子类型支持
      const extendedOperators = [
        ...operators,
        new OperatorInfo({
          type: 'OldOp',
          metadata: new ObjectMetadata({ displayName: '旧算子' })
        })
      ];

      // 设置复杂的初始执行图
      const complexInitialGraph = new ExecutionGraph({
        id: 'complex-graph',
        metadata: new ObjectMetadata({ displayName: '复杂测试图', description: '测试用复杂图' }),
        nodes: {
          'read-node': new GraphNode({ 
            id: 'read-node', 
            opType: 'ReadOp', 
            opConfig: { path: '/data/input.csv' } 
          }),
          'filter-node': new GraphNode({ 
            id: 'filter-node', 
            opType: 'FilterOp', 
            opConfig: { condition: 'value > 0' } 
          }),
          'old-node': new GraphNode({ 
            id: 'old-node', 
            opType: 'OldOp', 
            opConfig: { legacy: true } 
          })
        },
        edges: [
          new GraphEdge({ source: 'read-node', target: 'filter-node', targetConfig: 'input' }),
          new GraphEdge({ source: 'filter-node', target: 'old-node', targetConfig: 'data' }),
          new GraphEdge({ source: 'old-node', target: 'output-node', targetConfig: 'result' })
        ]
      });

      const complexClient = new MockExecutionGraphClient(extendedOperators, 'test-session', complexInitialGraph);

      try {
        // 执行复杂的变更操作
        // 1. 添加新节点
        const newNode = new GraphNode({ 
          id: 'transform-node', 
          opType: 'TransformOp', 
          opConfig: { transform: 'normalize' } 
        });
        await complexClient.add_nodes([newNode]);

        // 2. 更新现有节点
        await complexClient.update_nodes({
          'filter-node': new UpdateNodeParam({
            opConfig: { condition: 'value > 10', new_param: 'added' }
          }),
          'read-node': new UpdateNodeParam({
            opConfig: { batch_size: 1000 }
          })
        });

        // 3. 删除节点
        await complexClient.delete_nodes(['old-node']);

        // 4. 添加新边
        const newEdges = [
          new GraphEdge({ source: 'filter-node', target: 'transform-node', targetConfig: 'input' }),
          new GraphEdge({ source: 'transform-node', target: 'output-node', targetConfig: 'result' })
        ];
        await complexClient.add_edges(newEdges);

        // 5. 删除旧边
        const oldEdges = [
          new GraphEdge({ source: 'filter-node', target: 'old-node', targetConfig: 'data' }),
          new GraphEdge({ source: 'old-node', target: 'output-node', targetConfig: 'result' })
        ];
        await complexClient.delete_edges(oldEdges);

        // 6. 添加算子
        const op = new SimpleReadOperatorForTest({ 
          taskId: 'extra-op', 
          sourcePath: '/data/extra.csv' 
        });
        await complexClient.add_op(op);

        const status = await complexClient.get_edit_status();

        expect(status.hasPendingChanges).toBe(true);
        expect(status.currentEffectiveGraph.id).toBe('complex-graph');

        // 验证current_graph反映了所有变更
        const currentNodes = status.currentGraph.nodes;
        const currentEdges = status.currentGraph.edges;

        // 验证节点变更
        expect(currentNodes['transform-node']).toBeDefined(); // 添加的节点
        expect(currentNodes['old-node']).toBeUndefined(); // 删除的节点
        expect(currentNodes['filter-node'].opConfig.condition).toBe('value > 10'); // 更新的节点
        expect(currentNodes['filter-node'].opConfig.new_param).toBe('added');
        expect(currentNodes['read-node'].opConfig.batch_size).toBe(1000);

        // 验证边变更
        const edgeSet = new Set(currentEdges.map(e => `${e.source}-${e.target}-${e.targetConfig}`));
        expect(edgeSet.has('filter-node-transform-node-input')).toBe(true); // 添加的边
        expect(edgeSet.has('transform-node-output-node-result')).toBe(true);
        expect(edgeSet.has('filter-node-old-node-data')).toBe(false); // 删除的边
        expect(edgeSet.has('old-node-output-node-result')).toBe(false);

        // 验证pending_changes
        expect(Object.keys(status.pendingChanges.nodesToAdd)).toHaveLength(1);
        expect(Object.keys(status.pendingChanges.nodesToUpdate)).toHaveLength(2);
        expect(status.pendingChanges.nodesToDelete).toHaveLength(1);
        expect(status.pendingChanges.edgesToAdd).toHaveLength(2);
        expect(status.pendingChanges.edgesToDelete).toHaveLength(2);

        // 确保不包含operators_to_add
        expect(status.pendingChanges.operatorsToAdd).toBeUndefined();
      } finally {
        await complexClient.close();
      }
    });
  });
});
