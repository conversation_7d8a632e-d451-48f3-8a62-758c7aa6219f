/**
 * RichExecutionGraph 单元测试
 * 
 * @description
 * 测试 RichExecutionGraph 类的核心功能，特别是 mergeFromExecutionGraph 方法
 * 确保在数据合并时保持 displayAttr 不丢失
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */

import RichExecutionGraph, { RichGraphNode, RichGraphEdge } from '@/pages/dpe-web/models/RichGraph.js';
import ExecutionGraph, { GraphNode, GraphEdge } from '@/pages/dpe-web/models/ExecutionGraph.js';
import ObjectMetadata from '@/pages/dpe-web/models/ObjectMetadata.js';

describe('RichExecutionGraph', () => {
  
  /**
   * 测试 mergeFromExecutionGraph 方法
   */
  describe('mergeFromExecutionGraph', () => {
    
    /**
     * 测试基本的数据合并功能
     */
    test('应该正确合并ExecutionGraph数据', () => {
      // 创建原始的RichExecutionGraph，包含displayAttr
      const originalNode = new RichGraphNode({
        id: 'node1',
        metadata: new ObjectMetadata({
          displayName: '原始节点',
          description: '原始节点描述'
        }),
        opType: 'TestOperator',
        opConfig: { param1: 'oldValue' },
        displayAttr: {
          x: 100,
          y: 200,
          width: 120,
          height: 80,
          shape: 'custom-shape',
          attrs: { fill: '#ff0000' }
        }
      });
      
      const originalEdge = new RichGraphEdge({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        targetConfigs: ['input1'],
        displayAttr: {
          shape: 'custom-edge',
          attrs: { 
            line: { stroke: '#00ff00', strokeWidth: 3 }
          }
        }
      });
      
      const richGraph = new RichExecutionGraph({
        id: 'test-graph',
        metadata: new ObjectMetadata({
          displayName: '测试图',
          description: '测试用的执行图'
        }),
        nodes: { 'node1': originalNode },
        edges: [originalEdge]
      });
      
      // 创建更新的ExecutionGraph数据
      const updatedNode = new GraphNode({
        id: 'node1',
        metadata: new ObjectMetadata({
          displayName: '更新节点',
          description: '更新节点描述'
        }),
        opType: 'UpdatedOperator',
        opConfig: { param1: 'newValue', param2: 'addedValue' }
      });
      
      const updatedEdge = new GraphEdge({
        source: 'node1',
        target: 'node2',
        targetConfig: 'input2'
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'test-graph-updated',
        metadata: new ObjectMetadata({
          displayName: '更新测试图',
          description: '更新后的测试图'
        }),
        nodes: { 'node1': updatedNode },
        edges: [updatedEdge]
      });
      
      // 执行合并
      richGraph.mergeFromExecutionGraph(executionGraph);
      
      // 验证节点数据已更新
      const mergedNode = richGraph.getNode('node1');
      expect(mergedNode).toBeDefined();
      expect(mergedNode.metadata.displayName).toBe('更新节点');
      expect(mergedNode.metadata.description).toBe('更新节点描述');
      expect(mergedNode.opType).toBe('UpdatedOperator');
      expect(mergedNode.opConfig.param1).toBe('newValue');
      expect(mergedNode.opConfig.param2).toBe('addedValue');
      
      // 验证displayAttr被保持
      expect(mergedNode.displayAttr.x).toBe(100);
      expect(mergedNode.displayAttr.y).toBe(200);
      expect(mergedNode.displayAttr.width).toBe(120);
      expect(mergedNode.displayAttr.height).toBe(80);
      expect(mergedNode.displayAttr.shape).toBe('custom-shape');
      expect(mergedNode.displayAttr.attrs.fill).toBe('#ff0000');
      
      // 验证边数据已更新
      const mergedEdges = richGraph.getAllEdges();
      expect(mergedEdges).toHaveLength(1);
      const mergedEdge = mergedEdges[0];
      expect(mergedEdge.source).toBe('node1');
      expect(mergedEdge.target).toBe('node2');
      expect(mergedEdge.targetConfigs).toContain('input2');
      
      // 验证边的displayAttr被保持
      expect(mergedEdge.displayAttr.shape).toBe('custom-edge');
      expect(mergedEdge.displayAttr.attrs.line.stroke).toBe('#00ff00');
      expect(mergedEdge.displayAttr.attrs.line.strokeWidth).toBe(3);
    });
    
    /**
     * 测试新增节点的处理
     */
    test('应该正确处理新增的节点', () => {
      // 创建只有一个节点的RichExecutionGraph
      const originalNode = new RichGraphNode({
        id: 'node1',
        metadata: new ObjectMetadata({ displayName: '节点1' }),
        opType: 'Type1',
        displayAttr: { x: 50, y: 50 }
      });
      
      const richGraph = new RichExecutionGraph({
        id: 'test-graph',
        nodes: { 'node1': originalNode },
        edges: []
      });
      
      // 创建包含新节点的ExecutionGraph
      const node1 = new GraphNode({
        id: 'node1',
        metadata: new ObjectMetadata({ displayName: '更新节点1' }),
        opType: 'Type1Updated'
      });
      
      const node2 = new GraphNode({
        id: 'node2',
        metadata: new ObjectMetadata({ displayName: '新节点2' }),
        opType: 'Type2'
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'test-graph',
        nodes: { 'node1': node1, 'node2': node2 },
        edges: []
      });
      
      // 执行合并
      richGraph.mergeFromExecutionGraph(executionGraph);
      
      // 验证原节点保持displayAttr
      const mergedNode1 = richGraph.getNode('node1');
      expect(mergedNode1.metadata.displayName).toBe('更新节点1');
      expect(mergedNode1.opType).toBe('Type1Updated');
      expect(mergedNode1.displayAttr.x).toBe(50);
      expect(mergedNode1.displayAttr.y).toBe(50);
      
      // 验证新节点有默认displayAttr
      const mergedNode2 = richGraph.getNode('node2');
      expect(mergedNode2).toBeDefined();
      expect(mergedNode2.metadata.displayName).toBe('新节点2');
      expect(mergedNode2.opType).toBe('Type2');
      expect(mergedNode2.displayAttr).toBeDefined();
      expect(typeof mergedNode2.displayAttr.x).toBe('number');
      expect(typeof mergedNode2.displayAttr.y).toBe('number');
    });
    
    /**
     * 测试节点删除的处理
     */
    test('应该正确处理删除的节点', () => {
      // 创建有两个节点的RichExecutionGraph
      const node1 = new RichGraphNode({
        id: 'node1',
        metadata: new ObjectMetadata({ displayName: '节点1' }),
        displayAttr: { x: 100, y: 100 }
      });
      
      const node2 = new RichGraphNode({
        id: 'node2',
        metadata: new ObjectMetadata({ displayName: '节点2' }),
        displayAttr: { x: 200, y: 200 }
      });
      
      const richGraph = new RichExecutionGraph({
        id: 'test-graph',
        nodes: { 'node1': node1, 'node2': node2 },
        edges: []
      });
      
      // 创建只有一个节点的ExecutionGraph（删除了node2）
      const updatedNode1 = new GraphNode({
        id: 'node1',
        metadata: new ObjectMetadata({ displayName: '保留节点1' })
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'test-graph',
        nodes: { 'node1': updatedNode1 },
        edges: []
      });
      
      // 执行合并
      richGraph.mergeFromExecutionGraph(executionGraph);
      
      // 验证node1仍存在且保持displayAttr
      const remainingNode = richGraph.getNode('node1');
      expect(remainingNode).toBeDefined();
      expect(remainingNode.metadata.displayName).toBe('保留节点1');
      expect(remainingNode.displayAttr.x).toBe(100);
      expect(remainingNode.displayAttr.y).toBe(100);
      
      // 验证node2被删除
      const deletedNode = richGraph.getNode('node2');
      expect(deletedNode).toBeUndefined();
      
      // 验证总节点数正确
      const allNodes = richGraph.getAllNodes();
      expect(allNodes).toHaveLength(1);
    });
    
    /**
     * 测试边的合并处理
     */
    test('应该正确处理边的合并', () => {
      // 创建包含边的RichExecutionGraph
      const edge1 = new RichGraphEdge({
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        targetConfigs: ['input1'],
        displayAttr: {
          attrs: { line: { stroke: '#blue' } }
        }
      });
      
      const richGraph = new RichExecutionGraph({
        id: 'test-graph',
        nodes: {},
        edges: [edge1]
      });
      
      // 创建更新的ExecutionGraph，包含不同的边配置
      const updatedEdge = new GraphEdge({
        source: 'node1',
        target: 'node2',
        targetConfig: 'input2'
      });
      
      const newEdge = new GraphEdge({
        source: 'node2',
        target: 'node3',
        targetConfig: 'input1'
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'test-graph',
        nodes: {},
        edges: [updatedEdge, newEdge]
      });
      
      // 执行合并
      richGraph.mergeFromExecutionGraph(executionGraph);
      
      const allEdges = richGraph.getAllEdges();
      expect(allEdges).toHaveLength(2);
      
      // 验证原边保持displayAttr但更新targetConfig
      const mergedEdge1 = allEdges.find(e => e.source === 'node1' && e.target === 'node2');
      expect(mergedEdge1).toBeDefined();
      expect(mergedEdge1.targetConfigs).toContain('input2');
      expect(mergedEdge1.displayAttr.attrs.line.stroke).toBe('#blue');
      
      // 验证新边有默认displayAttr
      const newMergedEdge = allEdges.find(e => e.source === 'node2' && e.target === 'node3');
      expect(newMergedEdge).toBeDefined();
      expect(newMergedEdge.targetConfigs).toContain('input1');
      expect(newMergedEdge.displayAttr).toBeDefined();
    });
    
    /**
     * 测试空图的处理
     */
    test('应该正确处理空图的合并', () => {
      const richGraph = new RichExecutionGraph({
        id: 'empty-graph',
        nodes: {},
        edges: []
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'empty-graph',
        nodes: {},
        edges: []
      });
      
      // 执行合并
      richGraph.mergeFromExecutionGraph(executionGraph);
      
      // 验证结果仍为空
      expect(richGraph.getAllNodes()).toHaveLength(0);
      expect(richGraph.getAllEdges()).toHaveLength(0);
    });
    
    /**
     * 测试元数据的更新
     */
    test('应该正确更新图的元数据', () => {
      const richGraph = new RichExecutionGraph({
        id: 'test-graph',
        metadata: new ObjectMetadata({
          displayName: '原始图',
          description: '原始描述'
        }),
        nodes: {},
        edges: []
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'updated-graph',
        metadata: new ObjectMetadata({
          displayName: '更新图',
          description: '更新描述'
        }),
        nodes: {},
        edges: []
      });
      
      // 执行合并
      richGraph.mergeFromExecutionGraph(executionGraph);
      
      // 验证元数据被更新
      expect(richGraph.id).toBe('updated-graph');
      expect(richGraph.metadata.displayName).toBe('更新图');
      expect(richGraph.metadata.description).toBe('更新描述');
    });
  });
  
  /**
   * 测试其他相关方法确保兼容性
   */
  describe('基础功能兼容性测试', () => {
    
    test('fromExecutionGraph静态方法应该正常工作', () => {
      const graphNode = new GraphNode({
        id: 'test-node',
        metadata: new ObjectMetadata({ displayName: '测试节点' }),
        opType: 'TestOp'
      });
      
      const executionGraph = new ExecutionGraph({
        id: 'test-graph',
        nodes: { 'test-node': graphNode },
        edges: []
      });
      
      const richGraph = RichExecutionGraph.fromExecutionGraph(executionGraph);
      
      expect(richGraph).toBeInstanceOf(RichExecutionGraph);
      expect(richGraph.id).toBe('test-graph');
      expect(richGraph.getAllNodes()).toHaveLength(1);
      
      const node = richGraph.getNode('test-node');
      expect(node).toBeDefined();
      expect(node.metadata.displayName).toBe('测试节点');
      expect(node.displayAttr).toBeDefined();
    });
    
    test('toExecutionGraph方法应该正常工作', () => {
      const richNode = new RichGraphNode({
        id: 'rich-node',
        metadata: new ObjectMetadata({ displayName: '富节点' }),
        opType: 'RichOp',
        displayAttr: { x: 150, y: 250 }
      });
      
      const richGraph = new RichExecutionGraph({
        id: 'rich-graph',
        nodes: { 'rich-node': richNode },
        edges: []
      });
      
      const executionGraph = richGraph.toExecutionGraph();
      
      expect(executionGraph).toBeInstanceOf(ExecutionGraph);
      expect(executionGraph.id).toBe('rich-graph');
      expect(Object.keys(executionGraph.nodes)).toHaveLength(1);
      
      const node = executionGraph.nodes['rich-node'];
      expect(node).toBeDefined();
      expect(node.metadata.displayName).toBe('富节点');
      expect(node.opType).toBe('RichOp');
      // displayAttr不应该在基础ExecutionGraph中
      expect(node.displayAttr).toBeUndefined();
    });
  });
}); 