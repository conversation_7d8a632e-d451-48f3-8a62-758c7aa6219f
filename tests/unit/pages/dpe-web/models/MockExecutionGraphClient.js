/**
 * MockExecutionGraphClient 
 * 模拟ExecutionGraphClient的前端实现，用于测试
 */

import {
  ExecutionGraph,
  GraphNode,
  GraphEdge,
  ObjectMetadata,
  PendingChanges,
  ClientEditStatus,
  OperatorInfo
} from '@/pages/dpe-web/models';
import { 
  ExecutionGraphClient,
  MergeMode,
  VerifyResult,
  VerifyFailureDetail,
  ConflictError,
  OperatorNotFoundError,
  GraphValidationError,
  UpdateNodeParam
} from '@/pages/dpe-web/models/client-api';



/**
 * 模拟的DAG任务
 */
class MockDagTask {
  constructor(taskId, sessionId, operator) {
    this.taskId = taskId;
    this.sessionId = sessionId;
    this.operator = operator;
  }

  status() {
    return 'completed';
  }

  async result(timeout) {
    return {
      resourceId: `session://${this.sessionId}/data-resource/${this.taskId}`
    };
  }

  async exception(timeout) {
    return null;
  }

  getResourceAccessor() {
    return {
      async getResource(timeout) {
        return {
          resourceId: `session://${this.sessionId}/data-resource/${this.taskId}`,
          resourceType: 'structured_data'
        };
      }
    };
  }
}

/**
 * MockExecutionGraphClient 实现
 * 
 * 提供 ExecutionGraphClient 的纯前端模拟实现，用于测试场景。
 * 支持算子类型验证、初始图加载、完整的图编辑功能。
 * 
 * @class MockExecutionGraphClient
 * @extends {ExecutionGraphClient}
 */
class MockExecutionGraphClient extends ExecutionGraphClient {

  /**
   * 创建 MockExecutionGraphClient 实例
   * 
   * @param {OperatorInfo[]} [operators=[]] - 支持的算子列表。空数组表示支持所有算子类型。
   *   每个 OperatorInfo 对象包含：
   *   - type: string - 算子类型标识
   *   - metadata: ObjectMetadata - 算子元数据
   *   - opConfigMetadata: Array<ConfigField> - 配置元数据（可选）
   *   - inputFields: Array<string> - 输入字段（可选）
   * 
   * @param {string} [sessionId='test-session'] - 会话标识符，用于区分不同的客户端会话
   * 
   * @param {ExecutionGraph|null} [initialGraph=null] - 初始执行图。
   *   如果提供，客户端将使用此图作为起始状态；
   *   如果为 null，则创建空的执行图。
   *   ExecutionGraph 包含：
   *   - id: string - 图的唯一标识
   *   - metadata: ObjectMetadata - 图的元数据
   *   - nodes: Object<string, GraphNode> - 节点映射
   *   - edges: Array<GraphEdge> - 边列表
   * 
   * @example
   * // 创建支持特定算子的客户端
   * const operators = [
   *   new OperatorInfo({
   *     type: 'ReadOp',
   *     metadata: new ObjectMetadata({ displayName: '读取算子' })
   *   }),
   *   new OperatorInfo({
   *     type: 'FilterOp', 
   *     metadata: new ObjectMetadata({ displayName: '过滤算子' })
   *   })
   * ];
   * const client = new MockExecutionGraphClient(operators, 'my-session');
   * 
   * @example
   * // 创建带初始图的客户端
   * const initialGraph = new ExecutionGraph({
   *   id: 'start-graph',
   *   metadata: new ObjectMetadata({ displayName: '起始图' }),
   *   nodes: {
   *     'node1': new GraphNode({ id: 'node1', opType: 'ReadOp', opConfig: {} })
   *   },
   *   edges: []
   * });
   * const client = new MockExecutionGraphClient(operators, 'session-1', initialGraph);
   * 
   * @example
   * // 创建支持所有算子的客户端（空算子列表）
   * const client = new MockExecutionGraphClient([], 'test-session');
   * 
   * @throws {Error} 如果参数类型不正确
   */
  constructor(operators = [], sessionId = 'test-session', initialGraph = null) {
    super();
    this._supportedOperators = new Set(operators.map(op => op.type));
    this._sessionId = sessionId;
    this._currentExecutionGraph = initialGraph || new ExecutionGraph({
      id: 'empty-graph',
      metadata: new ObjectMetadata({ displayName: '空图' }),
      nodes: {},
      edges: []
    });
    this._tasks = new Map();
    this._pendingChanges = {
      nodesToAdd: {},
      nodesToUpdate: {},
      nodesToDelete: new Set(),
      edgesToAdd: [],
      edgesToDelete: [],
      operatorsToAdd: {}
    };
    this._closed = false;
    
    // 测试控制标志
    this._shouldFailVerification = false;
    this._verificationFailureReason = '';
  }

  async connect() {
    // 模拟WebSocket连接
    return Promise.resolve();
  }

  async get_edit_status() {
    this._checkClosed();
    
    // 获取当前生效的图
    const currentEffectiveGraph = this._currentExecutionGraph;

    // 计算包含变更的当前图
    const currentGraph = this._applyPendingChangesToGraph(currentEffectiveGraph);

    // 检查是否有待提交变更
    const hasPendingChanges = this._hasPendingChanges();

    // 创建PendingChanges对象（不包含operatorsToAdd）
    const pendingChanges = new PendingChanges({
      nodesToAdd: this._pendingChanges.nodesToAdd,
      nodesToUpdate: this._pendingChanges.nodesToUpdate,
      nodesToDelete: Array.from(this._pendingChanges.nodesToDelete),
      edgesToAdd: this._pendingChanges.edgesToAdd,
      edgesToDelete: this._pendingChanges.edgesToDelete
    });

    return new ClientEditStatus({
      hasPendingChanges,
      currentEffectiveGraph,
      currentGraph,
      pendingChanges
    });
  }

  async add_nodes(nodes) {
    this._checkClosed();
    
    const addedNodeIds = [];
    
    for (const node of nodes) {
      const nodeInstance = node instanceof GraphNode ? node : new GraphNode(node);
      
      // 验证算子是否被支持
      if (this._supportedOperators.size > 0 && !this._supportedOperators.has(nodeInstance.opType)) {
        throw new OperatorNotFoundError(`不支持的算子类型: ${nodeInstance.opType}`);
      }
      
      this._pendingChanges.nodesToAdd[nodeInstance.id] = nodeInstance;
      addedNodeIds.push(nodeInstance.id);
    }
    
    return addedNodeIds;
  }

  async update_nodes(updates) {
    this._checkClosed();
    
    const updatedNodeIds = [];
    
    for (const [nodeId, updateParam] of Object.entries(updates)) {
      if (!this._pendingChanges.nodesToUpdate[nodeId]) {
        this._pendingChanges.nodesToUpdate[nodeId] = {};
      }
      
      // 处理 UpdateNodeParam 结构
      if (updateParam.metadata) {
        this._pendingChanges.nodesToUpdate[nodeId].metadata = updateParam.metadata;
      }
      
      if (updateParam.opConfig) {
        Object.assign(this._pendingChanges.nodesToUpdate[nodeId], updateParam.opConfig);
      }
      
      updatedNodeIds.push(nodeId);
    }
    
    return updatedNodeIds;
  }

  async delete_nodes(nodeIds) {
    this._checkClosed();
    
    const deletedNodeIds = [];
    
    for (const nodeId of nodeIds) {
      // 如果节点在待添加列表中，直接从待添加列表中移除
      if (this._pendingChanges.nodesToAdd[nodeId]) {
        delete this._pendingChanges.nodesToAdd[nodeId];
      }
      
      // 添加到待删除列表
      this._pendingChanges.nodesToDelete.add(nodeId);
      deletedNodeIds.push(nodeId);
    }
    
    return deletedNodeIds;
  }

  async add_edges(edges) {
    this._checkClosed();
    
    const addedEdgeIds = [];
    
    for (const edge of edges) {
      const edgeInstance = edge instanceof GraphEdge ? edge : new GraphEdge(edge);
      this._pendingChanges.edgesToAdd.push(edgeInstance);
      addedEdgeIds.push(`${edgeInstance.source}-${edgeInstance.target}`);
    }
    
    return addedEdgeIds;
  }

  async delete_edges(edges) {
    this._checkClosed();
    
    const deletedEdgeIds = [];
    
    for (const edge of edges) {
      const edgeInstance = edge instanceof GraphEdge ? edge : new GraphEdge(edge);
      this._pendingChanges.edgesToDelete.push(edgeInstance);
      deletedEdgeIds.push(`${edgeInstance.source}-${edgeInstance.target}`);
    }
    
    return deletedEdgeIds;
  }

  async add_op(operator) {
    this._checkClosed();
    
    // 创建算子副本
    const opCopy = { ...operator };
    
    // 如果没有task_id，自动生成一个
    if (!opCopy.taskId) {
      opCopy.taskId = `auto-task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    // 将算子添加到待添加列表
    this._pendingChanges.operatorsToAdd[opCopy.taskId] = opCopy;
    
    return opCopy;
  }

  async load_graph(graph, mergeMode = MergeMode.FAILED_ON_CONFLICT) {
    this._checkClosed();
    
    const graphInstance = graph instanceof ExecutionGraph ? graph : new ExecutionGraph(graph);
    
    // 检查冲突
    if (mergeMode === MergeMode.FAILED_ON_CONFLICT) {
      for (const nodeId of Object.keys(graphInstance.nodes)) {
        if (this._pendingChanges.nodesToAdd[nodeId]) {
          throw new ConflictError('节点ID冲突', 'node_id_conflict');
        }
      }
    }
    
    const affectedNodeIds = [];
    
    // 根据合并模式处理节点
    for (const [nodeId, node] of Object.entries(graphInstance.nodes)) {
      if (mergeMode === MergeMode.OVERWRITE_EXISTING || !this._pendingChanges.nodesToAdd[nodeId]) {
        this._pendingChanges.nodesToAdd[nodeId] = new GraphNode(node);
        affectedNodeIds.push(nodeId);
      }
    }
    
    // 添加边
    for (const edge of graphInstance.edges) {
      this._pendingChanges.edgesToAdd.push(new GraphEdge(edge));
    }
    
    return affectedNodeIds;
  }

  async verify() {
    this._checkClosed();
    
    try {
      // 模拟验证逻辑
      if (this._shouldFailVerification) {
        throw new GraphValidationError(this._verificationFailureReason || '验证失败');
      }
      
      return new VerifyResult(true, []);
    } catch (error) {
      if (error instanceof GraphValidationError) {
        return new VerifyResult(false, [
          new VerifyFailureDetail('validation_error', error.reason, {})
        ]);
      }
      throw error;
    }
  }

  async save() {
    this._checkClosed();
    
    // 先验证
    const verifyResult = await this.verify();
    if (!verifyResult.valid) {
      throw new GraphValidationError('验证失败: ' + verifyResult.failedDetails.map(d => d.message).join(', '));
    }
    
    // 应用变更到当前执行图
    this._currentExecutionGraph = this._applyPendingChangesToGraph(this._currentExecutionGraph);
    
    // 返回所有变更的节点ID
    const result = [
      ...Object.keys(this._pendingChanges.nodesToAdd),
      ...Object.keys(this._pendingChanges.nodesToUpdate),
      ...Array.from(this._pendingChanges.nodesToDelete)
    ];
    
    // 清空待提交变更
    this._clearPendingChanges();
    
    return result;
  }

  async export_graph() {
    this._checkClosed();
    
    return this._currentExecutionGraph;
  }

  async resolve_task(operator) {
    this._checkClosed();
    
    if (!operator.taskId) {
      throw new Error('算子没有task_id');
    }
    
    // 查找任务
    const task = this._tasks.get(operator.taskId);
    if (task) {
      return task;
    }
    
    throw new OperatorNotFoundError(`找不到task_id为 ${operator.taskId} 的任务`);
  }

  async close() {
    if (this._closed) {
      return;
    }
    
    // 清空待提交变更
    this._clearPendingChanges();
    
    // 清除任务
    this._tasks.clear();
    
    this._closed = true;
  }

  // 测试辅助方法
  
  /**
   * 设置验证失败 (仅用于测试)
   * @param {boolean} shouldFail - 是否验证失败
   * @param {string} reason - 失败原因
   */
  setVerificationFailure(shouldFail, reason = '') {
    this._shouldFailVerification = shouldFail;
    this._verificationFailureReason = reason;
  }
  
  /**
   * 添加任务 (仅用于测试)
   * @param {string} taskId - 任务ID
   * @param {MockDagTask} task - 任务对象
   */
  addTask(taskId, task) {
    this._tasks.set(taskId, task);
  }

  // 私有方法

  _checkClosed() {
    if (this._closed) {
      throw new Error('客户端已关闭');
    }
  }

  _hasPendingChanges() {
    return (
      Object.keys(this._pendingChanges.nodesToAdd).length > 0 ||
      Object.keys(this._pendingChanges.nodesToUpdate).length > 0 ||
      this._pendingChanges.nodesToDelete.size > 0 ||
      this._pendingChanges.edgesToAdd.length > 0 ||
      this._pendingChanges.edgesToDelete.length > 0 ||
      Object.keys(this._pendingChanges.operatorsToAdd).length > 0
    );
  }

  _clearPendingChanges() {
    this._pendingChanges = {
      nodesToAdd: {},
      nodesToUpdate: {},
      nodesToDelete: new Set(),
      edgesToAdd: [],
      edgesToDelete: [],
      operatorsToAdd: {}
    };
  }

  _applyPendingChangesToGraph(baseGraph) {
    // 创建基础图的副本
    const resultGraph = new ExecutionGraph({
      id: baseGraph.id + '-with-pending-changes',
      metadata: baseGraph.metadata,
      nodes: { ...baseGraph.nodes },
      edges: [...baseGraph.edges]
    });

    // 应用节点添加
    for (const [nodeId, node] of Object.entries(this._pendingChanges.nodesToAdd)) {
      resultGraph.nodes[nodeId] = new GraphNode(node);
    }

    // 应用节点更新
    for (const [nodeId, updates] of Object.entries(this._pendingChanges.nodesToUpdate)) {
      if (resultGraph.nodes[nodeId]) {
        Object.assign(resultGraph.nodes[nodeId].opConfig, updates);
      }
    }

    // 应用节点删除
    for (const nodeId of this._pendingChanges.nodesToDelete) {
      delete resultGraph.nodes[nodeId];
    }

    // 应用边添加
    for (const edge of this._pendingChanges.edgesToAdd) {
      resultGraph.edges.push(new GraphEdge(edge));
    }

    // 应用边删除
    for (const edgeToDelete of this._pendingChanges.edgesToDelete) {
      resultGraph.edges = resultGraph.edges.filter(edge => 
        !(edge.source === edgeToDelete.source && 
          edge.target === edgeToDelete.target && 
          edge.targetConfig === edgeToDelete.targetConfig)
      );
    }

    return resultGraph;
  }
}

export default MockExecutionGraphClient;
export {
  MockExecutionGraphClient,
  MockDagTask,
  MergeMode,
  VerifyResult,
  VerifyFailureDetail,
  ConflictError,
  OperatorNotFoundError,
  GraphValidationError
};
