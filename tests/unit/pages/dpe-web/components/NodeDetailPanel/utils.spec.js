/**
 * NodeDetailPanel 工具函数单元测试
 * @description 测试 NodeDetailPanel 组件的工具函数库
 */

import {
  TASK_STATUS,
  TASK_STATUS_DISPLAY,
  TASK_STATUS_COLOR,
  validateNodeFormData,
  formatTaskStatus,
  getTaskStatusColor,
  formatDateTime,
  createErrorMessage,
  deepClone,
  isDeepEqual,
  safeGet
} from '@/pages/dpe-web/components/NodeDetailPanel/utils';

describe('NodeDetailPanel 工具函数', () => {

  describe('常量定义', () => {
    it('应该正确定义任务状态枚举', () => {
      expect(TASK_STATUS.CREATED).toBe('CREATED');
      expect(TASK_STATUS.PENDING).toBe('PENDING');
      expect(TASK_STATUS.RUNNING).toBe('RUNNING');
      expect(TASK_STATUS.SUCCESS).toBe('SUCCESS');
      expect(TASK_STATUS.ERROR).toBe('ERROR');
      expect(TASK_STATUS.CANCELLED).toBe('CANCELLED');
      expect(TASK_STATUS.UNKNOWN).toBe('UNKNOWN');
    });

    it('应该正确定义任务状态显示映射', () => {
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.CREATED]).toBe('已创建');
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.PENDING]).toBe('等待中');
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.RUNNING]).toBe('运行中');
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.SUCCESS]).toBe('成功');
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.ERROR]).toBe('失败');
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.CANCELLED]).toBe('已取消');
      expect(TASK_STATUS_DISPLAY[TASK_STATUS.UNKNOWN]).toBe('未知');
    });

    it('应该正确定义任务状态颜色映射', () => {
      expect(TASK_STATUS_COLOR[TASK_STATUS.CREATED]).toBe('default');
      expect(TASK_STATUS_COLOR[TASK_STATUS.PENDING]).toBe('processing');
      expect(TASK_STATUS_COLOR[TASK_STATUS.RUNNING]).toBe('processing');
      expect(TASK_STATUS_COLOR[TASK_STATUS.SUCCESS]).toBe('success');
      expect(TASK_STATUS_COLOR[TASK_STATUS.ERROR]).toBe('error');
      expect(TASK_STATUS_COLOR[TASK_STATUS.CANCELLED]).toBe('warning');
      expect(TASK_STATUS_COLOR[TASK_STATUS.UNKNOWN]).toBe('default');
    });
  });

  describe('validateNodeFormData', () => {
    let validFormData;
    let operatorInfo;

    beforeEach(() => {
      validFormData = {
        metadata: {
          displayName: '测试节点',
          description: '测试描述',
          labels: {},
          annotations: {}
        },
        opConfigs: {
          param1: 'value1',
          param2: 123
        },
        opInputs: {
          input1: ['upstream1'],
          input2: []
        }
      };

      operatorInfo = {
        opConfigMetadata: [
          { key: 'param1', schema: { type: 'string', required: true } },
          { key: 'param2', schema: { type: 'number', required: false } },
          { key: 'param3', schema: { type: 'boolean', required: false } }
        ],
        inputFields: ['input1', 'input2']
      };
    });

    it('应该验证有效的表单数据', () => {
      const result = validateNodeFormData(validFormData, operatorInfo);
      expect(result.valid).toBe(true);
      expect(result.message).toBe('业务验证通过');
    });

    it('应该拒绝空的显示名称', () => {
      validFormData.metadata.displayName = '';
      const result = validateNodeFormData(validFormData, operatorInfo);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('节点显示名称是必需的');

      validFormData.metadata.displayName = '   ';
      const result2 = validateNodeFormData(validFormData, operatorInfo);
      expect(result2.valid).toBe(false);
      expect(result2.message).toBe('节点显示名称是必需的');
    });

    it('应该验证必需的配置项', () => {
      delete validFormData.opConfigs.param1;
      const result = validateNodeFormData(validFormData, operatorInfo);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('配置项 "param1" 是必需的');
    });

    it('应该验证配置项的类型', () => {
      validFormData.opConfigs.param1 = 123; // 应该是字符串
      const result = validateNodeFormData(validFormData, operatorInfo);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('配置项 "param1" 的类型不正确，期望: string，实际: number');
    });

    it('应该验证输入字段配置', () => {
      delete validFormData.opInputs.input1;
      const result = validateNodeFormData(validFormData, operatorInfo);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('缺少输入字段配置: "input1"');
    });

    it('应该处理没有算子信息的情况', () => {
      const result = validateNodeFormData(validFormData, null);
      expect(result.valid).toBe(true);
      expect(result.message).toBe('业务验证通过');
    });

    it('应该处理没有配置元数据的算子信息', () => {
      const simpleOperatorInfo = { inputFields: ['input1'] };
      const result = validateNodeFormData(validFormData, simpleOperatorInfo);
      expect(result.valid).toBe(true); // 只需要 input1，它存在
      expect(result.message).toBe('业务验证通过');
    });

    it('应该验证不同数据类型的配置项', () => {
      const extendedFormData = {
        ...validFormData,
        opConfigs: {
          stringParam: 'text',
          numberParam: 42,
          booleanParam: true,
          arrayParam: [1, 2, 3],
          objectParam: { key: 'value' }
        }
      };

      const extendedOperatorInfo = {
        opConfigMetadata: [
          { key: 'stringParam', schema: { type: 'string' } },
          { key: 'numberParam', schema: { type: 'number' } },
          { key: 'booleanParam', schema: { type: 'boolean' } },
          { key: 'arrayParam', schema: { type: 'array' } },
          { key: 'objectParam', schema: { type: 'object' } }
        ],
        inputFields: ['input1', 'input2']
      };

      const result = validateNodeFormData(extendedFormData, extendedOperatorInfo);
      expect(result.valid).toBe(true);
    });

    it('应该处理null和undefined的配置值', () => {
      validFormData.opConfigs.param1 = null;
      const result = validateNodeFormData(validFormData, operatorInfo);
      expect(result.valid).toBe(false);
      expect(result.message).toBe('配置项 "param1" 是必需的');

      validFormData.opConfigs.param1 = undefined;
      const result2 = validateNodeFormData(validFormData, operatorInfo);
      expect(result2.valid).toBe(false);
      expect(result2.message).toBe('配置项 "param1" 是必需的');
    });
  });

  describe('formatTaskStatus', () => {
    it('应该正确格式化已知的任务状态', () => {
      expect(formatTaskStatus(TASK_STATUS.CREATED)).toBe('已创建');
      expect(formatTaskStatus(TASK_STATUS.PENDING)).toBe('等待中');
      expect(formatTaskStatus(TASK_STATUS.RUNNING)).toBe('运行中');
      expect(formatTaskStatus(TASK_STATUS.SUCCESS)).toBe('成功');
      expect(formatTaskStatus(TASK_STATUS.ERROR)).toBe('失败');
      expect(formatTaskStatus(TASK_STATUS.CANCELLED)).toBe('已取消');
      expect(formatTaskStatus(TASK_STATUS.UNKNOWN)).toBe('未知');
    });

    it('应该处理未知状态', () => {
      expect(formatTaskStatus('UNKNOWN_STATUS')).toBe('UNKNOWN_STATUS');
      expect(formatTaskStatus('')).toBe('未知');
      expect(formatTaskStatus(null)).toBe('未知');
      expect(formatTaskStatus(undefined)).toBe('未知');
    });
  });

  describe('getTaskStatusColor', () => {
    it('应该返回正确的状态颜色', () => {
      expect(getTaskStatusColor(TASK_STATUS.CREATED)).toBe('default');
      expect(getTaskStatusColor(TASK_STATUS.PENDING)).toBe('processing');
      expect(getTaskStatusColor(TASK_STATUS.RUNNING)).toBe('processing');
      expect(getTaskStatusColor(TASK_STATUS.SUCCESS)).toBe('success');
      expect(getTaskStatusColor(TASK_STATUS.ERROR)).toBe('error');
      expect(getTaskStatusColor(TASK_STATUS.CANCELLED)).toBe('warning');
      expect(getTaskStatusColor(TASK_STATUS.UNKNOWN)).toBe('default');
    });

    it('应该处理未知状态', () => {
      expect(getTaskStatusColor('UNKNOWN_STATUS')).toBe('default');
      expect(getTaskStatusColor(null)).toBe('default');
      expect(getTaskStatusColor(undefined)).toBe('default');
    });
  });

  describe('formatDateTime', () => {
    const testDate = new Date('2023-12-01T10:30:45Z');
    const isoString = '2023-12-01T10:30:45Z';

    it('应该格式化Date对象', () => {
      const result = formatDateTime(testDate);
      expect(result).toMatch(/2023-12-01 \d{2}:30:45/);
    });

    it('应该格式化ISO字符串', () => {
      const result = formatDateTime(isoString);
      expect(result).toMatch(/2023-12-01 \d{2}:30:45/);
    });

    it('应该支持不显示秒', () => {
      const result = formatDateTime(testDate, { showSeconds: false });
      expect(result).toMatch(/2023-12-01 \d{2}:30$/);
      expect(result).not.toContain(':45');
    });

    it('应该支持不显示日期', () => {
      const result = formatDateTime(testDate, { showDate: false });
      expect(result).toMatch(/\d{2}:30:45/);
      expect(result).not.toContain('2023-12-01');
    });

    it('应该支持只显示时间不显示秒', () => {
      const result = formatDateTime(testDate, { showDate: false, showSeconds: false });
      expect(result).toMatch(/\d{2}:30$/);
      expect(result).not.toContain('2023-12-01');
      expect(result).not.toContain(':45');
    });

    it('应该处理空值', () => {
      expect(formatDateTime(null)).toBe('');
      expect(formatDateTime(undefined)).toBe('');
      expect(formatDateTime('')).toBe('');
    });

    it('应该处理无效日期', () => {
      expect(formatDateTime('invalid-date')).toBe('invalid-date');
      expect(formatDateTime('not a date')).toBe('not a date');
    });

    it('应该处理无效的Date对象', () => {
      const invalidDate = new Date('invalid');
      expect(formatDateTime(invalidDate)).toBe('Invalid Date');
    });
  });

  describe('createErrorMessage', () => {
    it('应该创建基本错误消息', () => {
      const result = createErrorMessage('测试操作', '网络错误');
      expect(result).toBe('测试操作失败: 网络错误');
    });

    it('应该处理Error对象', () => {
      const error = new Error('连接超时');
      const result = createErrorMessage('保存数据', error);
      expect(result).toBe('保存数据失败: 连接超时');
    });

    it('应该处理无错误信息的情况', () => {
      const result = createErrorMessage('删除文件', null);
      expect(result).toBe('删除文件失败');
    });

    it('应该记录上下文信息', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const context = { nodeId: 'node-123', sessionId: 'session-456' };
      
      const result = createErrorMessage('运行任务', '执行失败', context);
      
      expect(result).toBe('运行任务失败: 执行失败');
      expect(consoleSpy).toHaveBeenCalledWith(
        '运行任务失败: 执行失败 [上下文: nodeId=node-123, sessionId=session-456]'
      );
      
      consoleSpy.mockRestore();
    });

    it('应该处理空上下文', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const result = createErrorMessage('测试操作', '错误', {});
      
      expect(result).toBe('测试操作失败: 错误');
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('deepClone', () => {
    it('应该克隆基本类型', () => {
      expect(deepClone(42)).toBe(42);
      expect(deepClone('string')).toBe('string');
      expect(deepClone(true)).toBe(true);
      expect(deepClone(null)).toBe(null);
      expect(deepClone(undefined)).toBe(undefined);
    });

    it('应该克隆Date对象', () => {
      const date = new Date('2023-12-01');
      const cloned = deepClone(date);
      
      expect(cloned).toBeInstanceOf(Date);
      expect(cloned.getTime()).toBe(date.getTime());
      expect(cloned).not.toBe(date); // 不是同一个对象
    });

    it('应该克隆数组', () => {
      const arr = [1, 2, { a: 3 }];
      const cloned = deepClone(arr);
      
      expect(cloned).toEqual(arr);
      expect(cloned).not.toBe(arr);
      expect(cloned[2]).not.toBe(arr[2]); // 嵌套对象也被克隆
    });

    it('应该克隆对象', () => {
      const obj = {
        a: 1,
        b: {
          c: 2,
          d: [3, 4]
        }
      };
      const cloned = deepClone(obj);
      
      expect(cloned).toEqual(obj);
      expect(cloned).not.toBe(obj);
      expect(cloned.b).not.toBe(obj.b);
      expect(cloned.b.d).not.toBe(obj.b.d);
    });

    it('应该处理复杂嵌套结构', () => {
      const complex = {
        str: 'text',
        num: 42,
        bool: true,
        date: new Date('2023-12-01'),
        arr: [1, { nested: 'value' }],
        obj: {
          level1: {
            level2: {
              data: 'deep'
            }
          }
        }
      };
      
      const cloned = deepClone(complex);
      
      expect(cloned).toEqual(complex);
      expect(cloned.date).toBeInstanceOf(Date);
      expect(cloned.arr[1]).not.toBe(complex.arr[1]);
      expect(cloned.obj.level1.level2).not.toBe(complex.obj.level1.level2);
    });
  });

  describe('isDeepEqual', () => {
    it('应该比较基本类型', () => {
      expect(isDeepEqual(1, 1)).toBe(true);
      expect(isDeepEqual('a', 'a')).toBe(true);
      expect(isDeepEqual(true, true)).toBe(true);
      expect(isDeepEqual(null, null)).toBe(true);
      expect(isDeepEqual(undefined, undefined)).toBe(true);
      
      expect(isDeepEqual(1, 2)).toBe(false);
      expect(isDeepEqual('a', 'b')).toBe(false);
      expect(isDeepEqual(true, false)).toBe(false);
      expect(isDeepEqual(null, undefined)).toBe(false);
    });

    it('应该比较数组', () => {
      expect(isDeepEqual([1, 2, 3], [1, 2, 3])).toBe(true);
      expect(isDeepEqual([], [])).toBe(true);
      expect(isDeepEqual([1, [2, 3]], [1, [2, 3]])).toBe(true);
      
      expect(isDeepEqual([1, 2, 3], [1, 2, 4])).toBe(false);
      expect(isDeepEqual([1, 2], [1, 2, 3])).toBe(false);
      expect(isDeepEqual([1, [2, 3]], [1, [2, 4]])).toBe(false);
    });

    it('应该比较对象', () => {
      expect(isDeepEqual({ a: 1 }, { a: 1 })).toBe(true);
      expect(isDeepEqual({}, {})).toBe(true);
      expect(isDeepEqual({ a: { b: 2 } }, { a: { b: 2 } })).toBe(true);
      
      expect(isDeepEqual({ a: 1 }, { a: 2 })).toBe(false);
      expect(isDeepEqual({ a: 1 }, { b: 1 })).toBe(false);
      expect(isDeepEqual({ a: 1 }, { a: 1, b: 2 })).toBe(false);
    });

    it('应该区分数组和对象', () => {
      expect(isDeepEqual([], {})).toBe(false);
      expect(isDeepEqual([1, 2], { 0: 1, 1: 2 })).toBe(false);
    });

    it('应该处理复杂嵌套结构', () => {
      const obj1 = {
        a: [1, { b: 2 }],
        c: { d: { e: 3 } }
      };
      const obj2 = {
        a: [1, { b: 2 }],
        c: { d: { e: 3 } }
      };
      const obj3 = {
        a: [1, { b: 2 }],
        c: { d: { e: 4 } }
      };
      
      expect(isDeepEqual(obj1, obj2)).toBe(true);
      expect(isDeepEqual(obj1, obj3)).toBe(false);
    });
  });

  describe('safeGet', () => {
    const testObj = {
      a: {
        b: {
          c: 42
        },
        d: [1, 2, { e: 'value' }]
      },
      f: null,
      g: undefined
    };

    it('应该获取存在的属性', () => {
      expect(safeGet(testObj, 'a.b.c')).toBe(42);
      expect(safeGet(testObj, 'a.d.2.e')).toBe('value');
    });

    it('应该返回默认值对于不存在的属性', () => {
      expect(safeGet(testObj, 'a.b.x', 'default')).toBe('default');
      expect(safeGet(testObj, 'x.y.z', 'not found')).toBe('not found');
    });

    it('应该处理null和undefined值', () => {
      expect(safeGet(testObj, 'f.x', 'default')).toBe('default');
      expect(safeGet(testObj, 'g.x', 'default')).toBe('default');
    });

    it('应该处理非对象输入', () => {
      expect(safeGet(null, 'a.b', 'default')).toBe('default');
      expect(safeGet(undefined, 'a.b', 'default')).toBe('default');
      expect(safeGet('string', 'a.b', 'default')).toBe('default');
      expect(safeGet(123, 'a.b', 'default')).toBe('default');
    });

    it('应该处理空路径', () => {
      expect(safeGet(testObj, '', 'default')).toBe('default');
    });

    it('应该处理单级路径', () => {
      expect(safeGet(testObj, 'a')).toBe(testObj.a);
      expect(safeGet(testObj, 'f')).toBe(null);
    });

    it('应该使用undefined作为默认的默认值', () => {
      expect(safeGet(testObj, 'x.y')).toBeUndefined();
    });
  });

  describe('边界情况和错误处理', () => {
    it('所有函数应该处理null和undefined输入', () => {
      expect(() => formatTaskStatus(null)).not.toThrow();
      expect(() => getTaskStatusColor(undefined)).not.toThrow();
      expect(() => formatDateTime(null)).not.toThrow();
      expect(() => createErrorMessage('op', null)).not.toThrow();
      expect(() => deepClone(null)).not.toThrow();
      expect(() => isDeepEqual(null, undefined)).not.toThrow();
      expect(() => safeGet(null, 'path')).not.toThrow();
    });

    it('应该处理大型对象', () => {
      const largeObj = {};
      for (let i = 0; i < 1000; i++) {
        largeObj[`key${i}`] = { value: i };
      }

      expect(() => deepClone(largeObj)).not.toThrow();
      expect(() => isDeepEqual(largeObj, deepClone(largeObj))).not.toThrow();
    });
  });
}); 