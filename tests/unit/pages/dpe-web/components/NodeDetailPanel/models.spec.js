/**
 * NodeDetailPanel 数据模型单元测试
 * @description 测试 NodeDetailPanel 组件的数据模型定义和验证函数
 */

import {
  NodeDetailPanelCallbacks,
  createDefaultNodeFormData,
  validateNodeFormDataStructure
} from '@/pages/dpe-web/components/NodeDetailPanel/models';
import { ObjectMetadata } from '@/pages/dpe-web/models/ObjectMetadata';

describe('NodeDetailPanel 数据模型', () => {
  
  describe('NodeDetailPanelCallbacks', () => {
    let callbacks;

    beforeEach(() => {
      callbacks = new NodeDetailPanelCallbacks();
    });

    it('应该是一个类', () => {
      expect(NodeDetailPanelCallbacks).toBeInstanceOf(Function);
      expect(callbacks).toBeInstanceOf(NodeDetailPanelCallbacks);
    });

    describe('validate 方法', () => {
      it('应该抛出未实现错误', async () => {
        const formData = createDefaultNodeFormData();
        
        await expect(callbacks.validate(formData))
          .rejects
          .toThrow('Method \'validate\' must be implemented.');
      });
    });

    describe('save 方法', () => {
      it('应该抛出未实现错误', async () => {
        const formData = createDefaultNodeFormData();
        
        await expect(callbacks.save(formData))
          .rejects
          .toThrow('Method \'save\' must be implemented.');
      });
    });

    describe('runTask 方法', () => {
      it('应该抛出未实现错误', async () => {
        const formData = createDefaultNodeFormData();
        
        await expect(callbacks.runTask(formData))
          .rejects
          .toThrow('Method \'runTask\' must be implemented.');
      });
    });

    describe('cancelTask 方法', () => {
      it('应该抛出未实现错误', async () => {
        const taskId = 'test-task-123';
        
        await expect(callbacks.cancelTask(taskId))
          .rejects
          .toThrow('Method \'cancelTask\' must be implemented.');
      });
    });
  });

  describe('createDefaultNodeFormData', () => {
    it('应该创建默认的 NodeFormData 对象', () => {
      const defaultData = createDefaultNodeFormData();

      expect(defaultData).toEqual({
        metadata: expect.objectContaining({
          displayName: '',
          description: '',
          labels: {},
          annotations: {}
        }),
        opConfigs: {},
        opInputs: {}
      });

      expect(defaultData.metadata.displayName).toBe('');
      expect(defaultData.metadata.description).toBe('');
      expect(defaultData.metadata.labels).toEqual({});
      expect(defaultData.metadata.annotations).toEqual({});
    });

    it('应该支持覆盖默认值', () => {
      const overrides = {
        metadata: {
          displayName: '测试节点',
          description: '测试描述'
        },
        opConfigs: {
          param1: 'value1'
        },
        opInputs: {
          input1: ['upstream1']
        }
      };

      const formData = createDefaultNodeFormData(overrides);

      expect(formData.metadata.displayName).toBe('测试节点');
      expect(formData.metadata.description).toBe('测试描述');
      expect(formData.opConfigs).toEqual({ param1: 'value1' });
      expect(formData.opInputs).toEqual({ input1: ['upstream1'] });
    });

    it('应该正确合并嵌套的 metadata 对象', () => {
      const overrides = {
        metadata: {
          displayName: '自定义节点'
        }
      };

      const formData = createDefaultNodeFormData(overrides);

      expect(formData.metadata.displayName).toBe('自定义节点');
      expect(formData.metadata.description).toBe(''); // 保持默认值
      expect(formData.metadata.labels).toEqual({}); // 保持默认值
    });

    it('应该处理空覆盖对象', () => {
      const formData = createDefaultNodeFormData({});
      const defaultData = createDefaultNodeFormData();

      expect(formData).toEqual(defaultData);
    });
  });

  describe('validateNodeFormDataStructure', () => {
    let validFormData;

    beforeEach(() => {
      validFormData = {
        metadata: {
          displayName: '测试节点',
          description: '测试描述',
          labels: {},
          annotations: {}
        },
        opConfigs: {
          param1: 'value1',
          param2: 123
        },
        opInputs: {
          input1: ['upstream1', 'upstream2'],
          input2: []
        }
      };
    });

    it('应该验证有效的 NodeFormData 结构', () => {
      const result = validateNodeFormDataStructure(validFormData);

      expect(result.valid).toBe(true);
      expect(result.message).toBe('数据结构验证通过');
    });

    it('应该拒绝 null 或 undefined 数据', () => {
      expect(validateNodeFormDataStructure(null))
        .toEqual({ valid: false, message: 'NodeFormData 必须是一个对象' });

      expect(validateNodeFormDataStructure(undefined))
        .toEqual({ valid: false, message: 'NodeFormData 必须是一个对象' });
    });

    it('应该拒绝非对象类型的数据', () => {
      expect(validateNodeFormDataStructure('not an object'))
        .toEqual({ valid: false, message: 'NodeFormData 必须是一个对象' });

      expect(validateNodeFormDataStructure(123))
        .toEqual({ valid: false, message: 'NodeFormData 必须是一个对象' });

      expect(validateNodeFormDataStructure([]))
        .toEqual({ valid: false, message: 'NodeFormData 必须是一个对象' });
    });

    it('应该验证 metadata 字段的存在和类型', () => {
      delete validFormData.metadata;
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'metadata 字段是必需的且必须是对象' });

      validFormData.metadata = null;
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'metadata 字段是必需的且必须是对象' });

      validFormData.metadata = 'not an object';
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'metadata 字段是必需的且必须是对象' });
    });

    it('应该验证 opConfigs 字段的存在和类型', () => {
      delete validFormData.opConfigs;
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'opConfigs 字段是必需的且必须是对象' });

      validFormData.opConfigs = null;
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'opConfigs 字段是必需的且必须是对象' });

      validFormData.opConfigs = [];
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'opConfigs 字段是必需的且必须是对象' });
    });

    it('应该验证 opInputs 字段的存在和类型', () => {
      delete validFormData.opInputs;
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'opInputs 字段是必需的且必须是对象' });

      validFormData.opInputs = null;
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'opInputs 字段是必需的且必须是对象' });

      validFormData.opInputs = 'not an object';
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ valid: false, message: 'opInputs 字段是必需的且必须是对象' });
    });

    it('应该验证 opInputs 值的数组类型', () => {
      // 测试 input1 为非数组类型
      validFormData.opInputs.input1 = 'not an array';
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ 
          valid: false, 
          message: 'opInputs.input1 必须是数组，当前类型: string' 
        });

      // 恢复 input1，测试 input2 为非数组类型
      validFormData.opInputs.input1 = ['upstream1'];
      validFormData.opInputs.input2 = { key: 'value' };
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ 
          valid: false, 
          message: 'opInputs.input2 必须是数组，当前类型: object' 
        });
    });

    it('应该验证 opInputs 数组元素的字符串类型', () => {
      validFormData.opInputs.input1 = ['valid-string', 123];
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ 
          valid: false, 
          message: 'opInputs.input1[1] 必须是字符串，当前类型: number' 
        });

      validFormData.opInputs.input1 = ['valid-string', null];
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ 
          valid: false, 
          message: 'opInputs.input1[1] 必须是字符串，当前类型: object' 
        });

      validFormData.opInputs.input1 = [true, 'valid-string'];
      expect(validateNodeFormDataStructure(validFormData))
        .toEqual({ 
          valid: false, 
          message: 'opInputs.input1[0] 必须是字符串，当前类型: boolean' 
        });
    });

    it('应该允许空的 opInputs 数组', () => {
      validFormData.opInputs = {
        input1: [],
        input2: []
      };

      const result = validateNodeFormDataStructure(validFormData);
      expect(result.valid).toBe(true);
    });

    it('应该允许空的 opConfigs 对象', () => {
      validFormData.opConfigs = {};

      const result = validateNodeFormDataStructure(validFormData);
      expect(result.valid).toBe(true);
    });

    it('应该验证复杂的嵌套结构', () => {
      const complexFormData = {
        metadata: {
          displayName: '复杂节点',
          description: '复杂描述',
          labels: { env: 'test', version: '1.0' },
          annotations: { author: 'system' }
        },
        opConfigs: {
          stringParam: 'text',
          numberParam: 42,
          booleanParam: true,
          objectParam: { nested: 'value' },
          arrayParam: [1, 2, 3]
        },
        opInputs: {
          primaryInput: ['node1', 'node2', 'node3'],
          secondaryInput: ['node4'],
          optionalInput: []
        }
      };

      const result = validateNodeFormDataStructure(complexFormData);
      expect(result.valid).toBe(true);
      expect(result.message).toBe('数据结构验证通过');
    });
  });

  describe('边界情况测试', () => {
    it('应该处理循环引用对象', () => {
      const circularObj = { a: 1 };
      circularObj.self = circularObj;

      // 虽然有循环引用，但基本结构验证应该仍然工作
      const formData = {
        metadata: {},
        opConfigs: circularObj,
        opInputs: {}
      };

      // 验证不应该因为循环引用而崩溃
      expect(() => validateNodeFormDataStructure(formData)).not.toThrow();
    });

    it('应该处理大型数据对象', () => {
      const largeOpInputs = {};
      for (let i = 0; i < 1000; i++) {
        largeOpInputs[`input${i}`] = [`upstream${i}`];
      }

      const largeFormData = {
        metadata: { displayName: 'Large Node' },
        opConfigs: {},
        opInputs: largeOpInputs
      };

      const result = validateNodeFormDataStructure(largeFormData);
      expect(result.valid).toBe(true);
    });

    it('应该处理特殊字符和Unicode字符', () => {
      const unicodeFormData = {
        metadata: {
          displayName: '测试节点 🚀',
          description: 'Тест описание',
          labels: { '特殊-键': 'value' },
          annotations: {}
        },
        opConfigs: {
          'unicode-param': '测试值 🌟',
          'emoji-param': '🔥💯'
        },
        opInputs: {
          'unicode-input': ['节点-1', 'ノード-2']
        }
      };

      const result = validateNodeFormDataStructure(unicodeFormData);
      expect(result.valid).toBe(true);
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成验证', () => {
      const start = Date.now();
      
      // 创建一个包含大量字段的对象
      const bigOpInputs = {};
      for (let i = 0; i < 100; i++) {
        bigOpInputs[`field${i}`] = [];
        for (let j = 0; j < 10; j++) {
          bigOpInputs[`field${i}`].push(`upstream${i}-${j}`);
        }
      }

      const bigFormData = {
        metadata: { displayName: 'Big Node' },
        opConfigs: {},
        opInputs: bigOpInputs
      };

      validateNodeFormDataStructure(bigFormData);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(100); // 应该在100ms内完成
    });
  });
}); 