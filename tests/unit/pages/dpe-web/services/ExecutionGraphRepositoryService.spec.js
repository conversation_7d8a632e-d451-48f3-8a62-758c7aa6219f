/**
 * ExecutionGraphRepositoryService 单元测试
 * @description 测试执行图仓库服务的所有功能
 */

import { ExecutionGraphRepositoryService } from '@/pages/dpe-web/services/ExecutionGraphRepositoryService';
import { ExecutionGraph, PageParam } from '@/pages/dpe-web/models';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }))
}));

const mockAxios = require('axios');

// Mock FileReader for file operations
global.FileReader = jest.fn(() => ({
  readAsText: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  onload: null,
  onerror: null
}));

// Mock File constructor for testing
global.File = class MockFile {
  constructor(bits, name, options = {}) {
    this.name = name || 'test.json';
    this.type = options.type || 'application/json';
    this.size = Array.isArray(bits) ? bits.join('').length : 0;
    this._content = Array.isArray(bits) ? bits.join('') : '';
  }

  text() {
    return Promise.resolve(this._content);
  }
};

// Mock file for testing
const createMockFile = (content, name = 'test.json', type = 'application/json') => {
  return new File([content], name, { type });
};

describe('ExecutionGraphRepositoryService', () => {
  let service;
  let mockClient;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create mock axios instance
    mockClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    };
    
    mockAxios.create.mockImplementation(() => mockClient);
    
    // Create service instance
    service = new ExecutionGraphRepositoryService('/api/graphs');
  });

  describe('constructor', () => {
    test('should create instance with default baseURL', () => {
      const defaultService = new ExecutionGraphRepositoryService();
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: '/api/graphs',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });

    test('should create instance with custom baseURL', () => {
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: '/api/graphs',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });

    test('should initialize local graphs', () => {
      expect(service.localGraphs.size).toBeGreaterThan(0);
      expect(service.localGraphs.has('sample-sales-analysis')).toBe(true);
      expect(service.localGraphs.has('sample-data-transformation')).toBe(true);
    });
  });

  describe('listGraphs', () => {
    test('should return graphs from API successfully', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 'graph-1',
              metadata: {
                displayName: 'Test Graph',
                description: 'A test graph',
                labels: {},
                annotations: {}
              },
              nodes: {},
              edges: []
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
            recordTotal: 1
          }
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const params = {
        pageParam: { pageIndex: 0, limit: 10 },
        filters: { displayNamePattern: 'test' }
      };

      const result = await service.listGraphs(params);

      expect(mockClient.post).toHaveBeenCalledWith('/query', params);
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(ExecutionGraph);
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });

    test('should fallback to local data when API fails', async () => {
      const apiError = new Error('API not available');
      mockClient.post.mockRejectedValue(apiError);

      // Mock console.warn to avoid test output pollution
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const result = await service.listGraphs();

      expect(consoleSpy).toHaveBeenCalledWith('执行图API不可用，使用本地数据:', apiError.message);
      expect(result.data.length).toBeGreaterThan(0);
      expect(result.data[0]).toBeInstanceOf(ExecutionGraph);
      expect(result.pageParam).toBeInstanceOf(PageParam);

      consoleSpy.mockRestore();
    });

    test('should handle empty params', async () => {
      const apiError = new Error('API not available');
      mockClient.post.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const result = await service.listGraphs({});

      expect(result.data.length).toBeGreaterThan(0);
      expect(result.pageParam).toBeDefined();

      consoleSpy.mockRestore();
    });

    test('should filter local graphs by displayNamePattern', async () => {
      const apiError = new Error('API not available');
      mockClient.post.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const params = {
        filters: { displayNamePattern: '销售' }
      };

      const result = await service.listGraphs(params);

      expect(result.data.length).toBe(1);
      expect(result.data[0].metadata.displayName).toContain('销售');

      consoleSpy.mockRestore();
    });

    test('should filter local graphs by labels', async () => {
      const apiError = new Error('API not available');
      mockClient.post.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const params = {
        filters: { labels: { team: 'data-analytics' } }
      };

      const result = await service.listGraphs(params);

      expect(result.data.length).toBe(1);
      expect(result.data[0].metadata.labels.team).toBe('data-analytics');

      consoleSpy.mockRestore();
    });

    test('should handle pagination', async () => {
      const apiError = new Error('API not available');
      mockClient.post.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const params = {
        pageParam: { pageIndex: 0, limit: 1 }
      };

      const result = await service.listGraphs(params);

      expect(result.data.length).toBe(1);
      expect(result.pageParam.limit).toBe(1);
      expect(result.pageParam.pageIndex).toBe(0);

      consoleSpy.mockRestore();
    });
  });

  describe('getGraph', () => {
    test('should return graph from API successfully', async () => {
      const mockResponse = {
        data: {
          id: 'graph-123',
          metadata: {
            displayName: 'Test Graph',
            description: 'A test graph'
          },
          nodes: {},
          edges: []
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getGraph('graph-123');

      expect(mockClient.get).toHaveBeenCalledWith('/graph-123');
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('graph-123');
    });

    test('should fallback to local data when API fails', async () => {
      const apiError = new Error('API not available');
      mockClient.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const result = await service.getGraph('sample-sales-analysis');

      expect(consoleSpy).toHaveBeenCalledWith('执行图API不可用，使用本地数据:', apiError.message);
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('sample-sales-analysis');

      consoleSpy.mockRestore();
    });

    test('should throw error for non-existent graph', async () => {
      const apiError = new Error('API not available');
      mockClient.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      await expect(service.getGraph('non-existent')).rejects.toThrow('获取执行图详情失败: 执行图 \'non-existent\' 未找到');

      consoleSpy.mockRestore();
    });
  });

  describe('saveGraph', () => {
    test('should return graph from API successfully', async () => {
      const mockResponse = {
        data: {
          id: 'graph-123',
          metadata: {
            displayName: 'Test Graph',
            description: 'A test graph'
          },
          nodes: {},
          edges: []
        }
      };

      mockClient.put.mockResolvedValue(mockResponse);

      const graph = new ExecutionGraph({
        id: 'graph-123',
        metadata: { displayName: 'Test Graph' },
        nodes: {},
        edges: []
      });

      const result = await service.saveGraph(graph);

      expect(mockClient.put).toHaveBeenCalledWith('/graph-123', graph);
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('graph-123');
    });

    test('should fallback to local storage when API fails', async () => {
      const apiError = new Error('API not available');
      mockClient.put.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const graph = new ExecutionGraph({
        id: 'test-graph',
        metadata: { displayName: 'Test Graph' },
        nodes: {},
        edges: []
      });

      const result = await service.saveGraph(graph);

      expect(consoleSpy).toHaveBeenCalledWith('执行图API不可用，保存到本地存储:', apiError.message);
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('test-graph');
      expect(service.localGraphs.has('test-graph')).toBe(true);

      consoleSpy.mockRestore();
    });

    test('should throw error for invalid graph', async () => {
      await expect(service.saveGraph(null)).rejects.toThrow('保存执行图失败: 执行图ID不能为空');
    });
  });

  describe('createGraph', () => {
    test('should create graph via API successfully', async () => {
      const mockResponse = {
        data: {
          id: 'new-graph-123',
          metadata: { displayName: 'New Graph' },
          nodes: {},
          edges: []
        }
      };

      mockClient.put.mockResolvedValue(mockResponse);

      const graphData = {
        metadata: { displayName: 'New Graph' },
        nodes: {},
        edges: []
      };

      const result = await service.createGraph(graphData);

      // 检查调用参数，使用动态生成的ID匹配
      expect(mockClient.put).toHaveBeenCalledWith(
        expect.stringMatching(/^\/graph-\d+-[a-z0-9]+$/), 
        expect.any(ExecutionGraph)
      );
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('new-graph-123');
    });

    test('should fallback to local creation when API fails', async () => {
      const apiError = new Error('API not available');
      mockClient.put.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const graphData = {
        metadata: { displayName: 'New Local Graph' },
        nodes: {},
        edges: []
      };

      const result = await service.createGraph(graphData);

      expect(consoleSpy).toHaveBeenCalledWith('执行图API不可用，保存到本地存储:', apiError.message);
      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toMatch(/^graph-/);
      expect(service.localGraphs.has(result.id)).toBe(true);

      consoleSpy.mockRestore();
    });

    test('should throw error for invalid graph data', async () => {
      await expect(service.createGraph(null)).rejects.toThrow('创建执行图失败: Cannot read properties of null (reading \'id\')');
    });
  });

  describe('deleteGraph', () => {
    test('should delete graph via API successfully', async () => {
      mockClient.delete.mockResolvedValue({ data: true }); // Service returns boolean for success

      const result = await service.deleteGraph('graph-123');

      expect(mockClient.delete).toHaveBeenCalledWith('/graph-123');
      expect(result).toBe(true);
    });

    test('should fallback to local deletion when API fails', async () => {
      const apiError = new Error('API not available');
      mockClient.delete.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Add a graph to delete
      service.localGraphs.set('test-delete', new ExecutionGraph({
        id: 'test-delete',
        metadata: { displayName: 'Test Delete' }
      }));

      const result = await service.deleteGraph('test-delete');

      expect(consoleSpy).toHaveBeenCalledWith('执行图API不可用，从本地存储删除:', apiError.message);
      expect(result).toBe(true);
      expect(service.localGraphs.has('test-delete')).toBe(false);

      consoleSpy.mockRestore();
    });

    test('should throw error for non-existent local graph', async () => {
      const apiError = new Error('API not available');
      mockClient.delete.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      await expect(service.deleteGraph('non-existent')).rejects.toThrow('删除执行图失败: 执行图 \'non-existent\' 未找到');

      consoleSpy.mockRestore();
    });
  });

  describe('importGraph', () => {
    test('should import graph from JSON string', async () => {
      const graphData = {
        metadata: { displayName: 'Imported Graph' },
        nodes: {},
        edges: []
      };

      const result = await service.importGraph(JSON.stringify(graphData));

      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toMatch(/^imported-/);
    });

    test('should import graph from file', async () => {
      const graphData = {
        metadata: { displayName: 'File Graph' },
        nodes: {},
        edges: []
      };

      const file = createMockFile(JSON.stringify(graphData), 'graph.json');
      
      const result = await service.importGraph(file);

      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toMatch(/^imported-/);
    });

    test('should throw error for invalid JSON', async () => {
      await expect(service.importGraph('invalid json')).rejects.toThrow('导入执行图失败: 无效的JSON格式: Unexpected token i in JSON at position 0');
    });

    test('should throw error for invalid file type', async () => {
      const file = createMockFile('content', 'graph.txt', 'text/plain');
      
      await expect(service.importGraph(file)).rejects.toThrow('导入执行图失败: 无效的JSON格式: Unexpected token c in JSON at position 0');
    });

    test('should throw error for invalid graph structure', async () => {
      const invalidData = { invalid: 'structure' };
      
      await expect(service.importGraph(JSON.stringify(invalidData))).rejects.toThrow('导入执行图失败: 无效的执行图结构');
    });

    test('should handle file reading with FileReader fallback', async () => {
      const graphData = {
        id: 'reader-graph',
        metadata: { displayName: 'Reader Graph' },
        nodes: {},
        edges: []
      };

      // 创建一个File对象，但是模拟没有text()方法的情况
      const file = new File([JSON.stringify(graphData)], 'graph.json', { type: 'application/json' });
      // 删除text方法来模拟旧浏览器
      delete file.text;

      const result = await service.importGraph(file);

      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('reader-graph');
    });
  });

  describe('exportGraph', () => {
    test('should export graph as JSON by default', async () => {
      const mockResponse = {
        data: {
          id: 'export-graph',
          metadata: { displayName: 'Export Graph' },
          nodes: {},
          edges: []
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.exportGraph('export-graph');

      expect(mockClient.get).toHaveBeenCalledWith('/export-graph');
      expect(typeof result).toBe('string'); // Expect string output
      expect(JSON.parse(result).id).toBe('export-graph');
    });

    test('should export graph with specified format', async () => {
      const mockResponse = {
        data: {
          id: 'export-graph',
          metadata: { displayName: 'Export Graph' },
          nodes: {},
          edges: []
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

                      await expect(service.exportGraph('export-graph', 'yaml')).rejects.toThrow('导出执行图失败: 不支持的导出格式: yaml');

        expect(mockClient.get).toHaveBeenCalledWith('/export-graph');
    });

    test('should fallback to local export when API fails', async () => {
      const apiError = new Error('API not available');
      mockClient.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const result = await service.exportGraph('sample-sales-analysis');

      expect(consoleSpy).toHaveBeenCalledWith('执行图API不可用，使用本地数据:', apiError.message);
      expect(typeof result).toBe('string'); // Expect string output
      expect(JSON.parse(result).id).toBe('sample-sales-analysis');

      consoleSpy.mockRestore();
    });

    test('should throw error for unsupported format', async () => {
      // 需要先模拟getGraph成功，然后格式检查失败
      const mockResponse = {
        data: {
          id: 'graph-id',
          metadata: { displayName: 'Test Graph' },
          nodes: {},
          edges: []
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      await expect(service.exportGraph('graph-id', 'unsupported')).rejects.toThrow('导出执行图失败: 不支持的导出格式: unsupported');
    });
  });

  describe('copyGraph', () => {
    test('should copy graph successfully', async () => {
      const apiError = new Error('API not available');
      mockClient.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const options = {
        newId: 'copied-graph',
        newName: 'Copied Graph'
      };

      const result = await service.copyGraph('sample-sales-analysis', options);

      expect(result).toBeInstanceOf(ExecutionGraph);
      expect(result.id).toBe('copied-graph');
      expect(result.metadata.displayName).toBe('Copied Graph');

      consoleSpy.mockRestore();
    });

    test('should generate default copy name', async () => {
      const apiError = new Error('API not available');
      mockClient.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      const result = await service.copyGraph('sample-sales-analysis');

      expect(result.id).toMatch(/^sample-sales-analysis-copy-/);
      expect(result.metadata.displayName).toContain(' (副本)');

      consoleSpy.mockRestore();
    });

    test('should throw error for non-existent graph', async () => {
      const apiError = new Error('API not available');
      mockClient.get.mockRejectedValue(apiError);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

      await expect(service.copyGraph('non-existent')).rejects.toThrow('复制执行图失败: 获取执行图详情失败: 执行图 \'non-existent\' 未找到');

      consoleSpy.mockRestore();
    });
  });

  describe('searchGraphs', () => {
    test('should search graphs by keyword', async () => {
      const result = await service.searchGraphs('销售');

      expect(result.length).toBe(1);
      expect(result[0].metadata.displayName).toContain('销售');
    });

    test('should search with options', async () => {
      const options = {
        searchFields: ['metadata.displayName'],
        caseSensitive: false,
        exactMatch: false
      };

      const result = await service.searchGraphs('数据', options);

      expect(result.length).toBeGreaterThan(0);
    });

    test('should return empty result for no matches', async () => {
      const result = await service.searchGraphs('non-existent-term');

      expect(result.length).toBe(0);
    });

    test('should handle exact match search', async () => {
      const options = { exactMatch: true };

      const result = await service.searchGraphs('销售数据分析工作流', options);

      expect(result.length).toBe(1);
    });

    test('should handle case sensitive search', async () => {
      const options = { caseSensitive: true };

      const result = await service.searchGraphs('销售', options);

      expect(result.length).toBe(1);
    });
  });

  describe('getGraphStatistics', () => {
    test('should return graph statistics', async () => {
      const stats = await service.getGraphStatistics();

      expect(stats).toHaveProperty('totalGraphs');
      expect(stats).toHaveProperty('byTeam');
      expect(stats).toHaveProperty('byVersion');
      expect(stats).toHaveProperty('recentlyModified');
      expect(stats.totalGraphs).toBeGreaterThan(0);
    });

    test('should calculate statistics correctly', async () => {
      const stats = await service.getGraphStatistics();

      expect(stats.byTeam['data-analytics']).toBe(1);
      expect(stats.byTeam['data-engineering']).toBe(1);
      expect(stats.recentlyModified.length).toBeGreaterThan(0);
    });
  });

  describe('_readFileAsText', () => {
    test('should read file as text using FileReader', async () => {
      const fileContent = 'test content';
      const file = createMockFile(fileContent);

      // Mock FileReader
      const mockFileReader = {
        result: fileContent,
        readAsText: jest.fn(function(file) {
          this.result = fileContent;
          // Immediately trigger the load event
          setTimeout(() => {
            if (this.onload) {
              this.onload({ target: { result: fileContent } });
            }
          }, 0);
        }),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        onload: null,
        onerror: null
      };

      global.FileReader = jest.fn(() => mockFileReader);

      const result = await service._readFileAsText(file);

      expect(result).toBe(fileContent);
      expect(mockFileReader.readAsText).toHaveBeenCalledWith(file);
    });

    test('should handle FileReader error', async () => {
      const file = createMockFile('content');

      // Mock FileReader with error
      const mockFileReader = {
        readAsText: jest.fn(function(file) {
          // Immediately trigger the error event
          setTimeout(() => {
            if (this.onerror) {
              this.onerror(new Error('Read error'));
            }
          }, 0);
        }),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        onload: null,
        onerror: null
      };

      global.FileReader = jest.fn(() => mockFileReader);

      await expect(service._readFileAsText(file)).rejects.toThrow();
    });
  });

  describe('_getNestedValue', () => {
    test('should get nested object value', () => {
      const obj = {
        level1: {
          level2: {
            value: 'test'
          }
        }
      };

      const result = service._getNestedValue(obj, 'level1.level2.value');
      expect(result).toBe('test');
    });

    test('should return undefined for non-existent path', () => {
      const obj = { a: { b: 'value' } };

      const result = service._getNestedValue(obj, 'a.b.c.d');
      expect(result).toBeUndefined();
    });

    test('should handle empty path', () => {
      const obj = { value: 'test' };

      const result = service._getNestedValue(obj, '');
      expect(result).toBe(obj);
    });
  });
});