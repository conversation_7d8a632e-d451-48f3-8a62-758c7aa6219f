/**
 * DataProcessService 单元测试
 * @description 测试数据处理服务的所有功能
 */

import { DataProcessService } from '@/pages/dpe-web/services/DataProcessService';
import {
  BackendInfo,
  Session,
  TaskInfo,
  OperatorInfo,
  PageParam
} from '@/pages/dpe-web/models';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }))
}));

const mockAxios = require('axios');

// Mock EventSource for SSE testing
global.EventSource = jest.fn(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  close: jest.fn(),
  readyState: 1
}));

describe('DataProcessService', () => {
  let service;
  let mockClient;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create mock axios instance
    mockClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    };
    
    mockAxios.create.mockImplementation(() => mockClient);
    
    // Create service instance
    service = new DataProcessService('/api');
  });

  afterEach(() => {
    // Clean up SSE connections
    if (service && service.closeAllConnections) {
      service.closeAllConnections();
    }
  });

  describe('constructor', () => {
    test('should create instance with default baseURL', () => {
      const defaultService = new DataProcessService();
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: '/api',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });

    test('should create instance with custom baseURL', () => {
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: '/api',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    });
  });

  describe('getBackendTypes', () => {
    test('should return backend types successfully', async () => {
      const mockResponse = {
        data: {
          backends: [
            {
              backend_type: 'duckdb',
              metadata: {
                display_name: 'DuckDB后端',
                description: '基于DuckDB的数据处理后端',
                labels: {},
                annotations: {}
              },
              session_configs: []
            }
          ]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getBackendTypes();

      expect(mockClient.get).toHaveBeenCalledWith('/backends');
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(BackendInfo);
      expect(result[0].type).toBe('duckdb');
    });

    test('should throw error when request fails', async () => {
      const error = new Error('Network error');
      mockClient.get.mockRejectedValue(error);

      await expect(service.getBackendTypes()).rejects.toThrow('获取后端类型列表失败: Network error');
    });
  });

  describe('queryBackendTypes', () => {
    test('should return paginated backend types successfully', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              backend_type: 'duckdb',
              metadata: {
                display_name: 'DuckDB后端',
                description: '基于DuckDB的数据处理后端',
                labels: {},
                annotations: {}
              },
              session_configs: []
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
            pageTotal: 1,
            recordTotal: 1
          }
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const params = {
        pageParam: { pageIndex: 0, limit: 10 },
        filters: { backend_type_pattern: 'duck' }
      };

      const result = await service.queryBackendTypes(params);

      expect(mockClient.post).toHaveBeenCalledWith('/backends/query', params);
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toBeInstanceOf(BackendInfo);
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });

    test('should handle empty params', async () => {
      const mockResponse = {
        data: { data: [], pageParam: { pageIndex: 0, limit: 10 } }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.queryBackendTypes();

      expect(mockClient.post).toHaveBeenCalledWith('/backends/query', {});
      expect(result.data).toHaveLength(0);
    });
  });

  describe('createSession', () => {
    test('should create session successfully', async () => {
      const mockResponse = {
        data: {
          id: 'session-123',
          backend_type: 'duckdb',
          state: 'RUNNING',
          created_at: '2023-01-01T00:00:00Z'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.createSession('duckdb', {
        sessionId: 'my-session',
        config: { max_memory: '4GB' }
      });

      expect(mockClient.post).toHaveBeenCalledWith('/sessions', {
        backend_name: 'duckdb',
        session_id: 'my-session',
        config: { max_memory: '4GB' }
      });
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('session-123');
    });

    test('should create session with minimal params', async () => {
      const mockResponse = {
        data: {
          id: 'session-456',
          backend_type: 'duckdb',
          state: 'RUNNING'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.createSession();

      expect(mockClient.post).toHaveBeenCalledWith('/sessions', {});
      expect(result.id).toBe('session-456');
    });
  });

  describe('createDagSession', () => {
    test('should create DAG session successfully', async () => {
      const mockResponse = {
        data: {
          id: 'dag-session-123',
          backend_type: 'duckdb',
          state: 'RUNNING'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const graph = { nodes: {}, edges: [] };
      const result = await service.createDagSession('duckdb', {
        sessionId: 'my-dag-session',
        config: { max_memory: '4GB' },
        graph
      });

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/dag', {
        backend_name: 'duckdb',
        session_id: 'my-dag-session',
        config: { max_memory: '4GB' },
        graph
      });
      expect(result).toBeInstanceOf(Session);
    });
  });

  describe('listSessions', () => {
    test('should return sessions list successfully', async () => {
      const mockResponse = {
        data: {
          sessions: [
            {
              id: 'session-1',
              backend_type: 'duckdb',
              state: 'RUNNING'
            },
            {
              id: 'session-2',
              backend_type: 'spark',
              state: 'STOPPED'
            }
          ]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listSessions();

      expect(mockClient.get).toHaveBeenCalledWith('/sessions');
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(Session);
      expect(result[1]).toBeInstanceOf(Session);
    });
  });

  describe('querySessions', () => {
    test('should return paginated sessions successfully', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 'session-1',
              backend_type: 'duckdb',
              state: 'RUNNING'
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
            recordTotal: 1
          }
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const params = {
        pageParam: { pageIndex: 0, limit: 10 },
        filters: { backend_type: 'duckdb' }
      };

      const result = await service.querySessions(params);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/query', params);
      expect(result.data).toHaveLength(1);
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });
  });

  describe('getSession', () => {
    test('should return session info successfully', async () => {
      const mockResponse = {
        data: {
          id: 'session-123',
          backend_type: 'duckdb',
          state: 'RUNNING'
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getSession('session-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123');
      expect(result).toBeInstanceOf(Session);
      expect(result.id).toBe('session-123');
    });

    test('should throw error for invalid session ID', async () => {
      const error = new Error('Session not found');
      mockClient.get.mockRejectedValue(error);

      await expect(service.getSession('invalid-session')).rejects.toThrow('获取会话详情失败: Session not found');
    });
  });

  describe('closeSession', () => {
    test('should close session successfully', async () => {
      const mockResponse = {
        data: { message: 'Session closed successfully' }
      };

      mockClient.delete.mockResolvedValue(mockResponse);

      const result = await service.closeSession('session-123');

      expect(mockClient.delete).toHaveBeenCalledWith('/sessions/session-123');
      expect(result.message).toBe('Session closed successfully');
    });
  });

  describe('listOperatorTypes', () => {
    test('should return operator types successfully', async () => {
      const mockResponse = {
        data: {
          operators: [
            {
              type: 'ReadStructuredDataResource',
              metadata: {
                display_name: '读取结构化数据',
                description: '从数据源读取结构化数据'
              }
            }
          ]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listOperatorTypes('duckdb');

      expect(mockClient.get).toHaveBeenCalledWith('/backends/duckdb/supported_operators');
      expect(result).toHaveLength(1);
      expect(result[0]).toBeInstanceOf(OperatorInfo);
    });
  });

  describe('queryOperatorTypes', () => {
    test('should return paginated operator types successfully', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              type: 'ReadStructuredDataResource',
              metadata: {
                display_name: '读取结构化数据',
                description: '从数据源读取结构化数据'
              }
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
            recordTotal: 1
          }
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const params = {
        pageParam: { pageIndex: 0, limit: 10 },
        filters: { category: 'input' }
      };

      const result = await service.queryOperatorTypes('duckdb', params);

      expect(mockClient.post).toHaveBeenCalledWith('/backends/duckdb/operators/query', params);
      expect(result.data).toHaveLength(1);
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });
  });

  describe('getOperatorInfo', () => {
    test('should return operator info successfully', async () => {
      const mockResponse = {
        data: {
          type: 'ReadStructuredDataResource',
          metadata: {
            display_name: '读取结构化数据',
            description: '从数据源读取结构化数据'
          },
          config_fields: []
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getOperatorInfo('duckdb', 'ReadStructuredDataResource');

      expect(mockClient.get).toHaveBeenCalledWith('/backends/duckdb/operators/ReadStructuredDataResource');
      expect(result).toBeInstanceOf(OperatorInfo);
    });
  });

  describe('runOperator', () => {
    test('should run operator successfully', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-123',
          status: 'RUNNING',
          operator_type: 'ReadStructuredDataResource'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const operatorConfig = {
        type: 'ReadStructuredDataResource',
        source: 'test.csv'
      };

      const result = await service.runOperator('session-123', operatorConfig, false);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/run?wait=false', operatorConfig);
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('task-123');
    });

    test('should run operator with wait=true', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-456',
          status: 'COMPLETED'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const operatorConfig = { type: 'TestOperator' };
      const result = await service.runOperator('session-123', operatorConfig, true);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/run?wait=true', operatorConfig);
      expect(result.id).toBe('task-456');
    });
  });

  describe('listTasks', () => {
    test('should return tasks list successfully', async () => {
      const mockResponse = {
        data: {
          tasks: [
            {
              task_id: 'task-1',
              status: 'COMPLETED',
              operator_type: 'ReadStructuredDataResource'
            },
            {
              task_id: 'task-2',
              status: 'RUNNING',
              operator_type: 'FilterStructuredData'
            }
          ]
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.listTasks('session-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/tasks');
      expect(result).toHaveLength(2);
      expect(result[0]).toBeInstanceOf(TaskInfo);
      expect(result[1]).toBeInstanceOf(TaskInfo);
    });
  });

  describe('queryTasks', () => {
    test('should return paginated tasks successfully', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              task_id: 'task-1',
              status: 'COMPLETED',
              operator_type: 'ReadStructuredDataResource'
            }
          ],
          pageParam: {
            pageIndex: 0,
            limit: 10,
            recordTotal: 1
          }
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const params = {
        pageParam: { pageIndex: 0, limit: 10 },
        filters: { status: 'COMPLETED' }
      };

      const result = await service.queryTasks('session-123', params);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/query', params);
      expect(result.data).toHaveLength(1);
      expect(result.pageParam).toBeInstanceOf(PageParam);
    });
  });

  describe('getTaskInfo', () => {
    test('should return task info successfully', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-123',
          status: 'COMPLETED',
          operator_type: 'ReadStructuredDataResource',
          result: {}
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getTaskInfo('session-123', 'task-123', false, 60);

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/tasks/task-123?wait=false&timeout=60');
      expect(result).toBeInstanceOf(TaskInfo);
      expect(result.id).toBe('task-123');
    });

    test('should use default parameters', async () => {
      const mockResponse = {
        data: {
          task_id: 'task-456',
          status: 'RUNNING'
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.getTaskInfo('session-123', 'task-456');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/tasks/task-456?wait=false&timeout=60');
      expect(result.id).toBe('task-456');
    });
  });

  describe('cancelTask', () => {
    test('should cancel task successfully', async () => {
      const mockResponse = {
        data: { message: 'Task cancelled successfully' }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.cancelTask('session-123', 'task-123');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/tasks/task-123/cancel');
      expect(result.message).toBe('Task cancelled successfully');
    });
  });

  describe('runDagTasks', () => {
    test('should run DAG tasks successfully', async () => {
      const mockResponse = {
        data: {
          executed_tasks: [
            { task_id: 'task-1', status: 'RUNNING' },
            { task_id: 'task-2', status: 'RUNNING' }
          ],
          message: 'Tasks submitted successfully'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const taskIds = ['task-1', 'task-2'];
      const result = await service.runDagTasks('session-123', taskIds, 'RERUN_FAILED');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/dag/run_task', {
        task_ids: taskIds,
        rerun_mode: 'RERUN_FAILED'
      });
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('task-1');
    });

    test('should use default rerun mode', async () => {
      const mockResponse = {
        data: { 
          executed_tasks: [{ task_id: 'task-1', status: 'RUNNING' }]
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.runDagTasks('session-123', ['task-1']);

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/dag/run_task', {
        task_ids: ['task-1'],
        rerun_mode: 'NEVER'
      });
      expect(result).toHaveLength(1);
    });
  });

  describe('runAllDagTasks', () => {
    test('should run all DAG tasks successfully', async () => {
      const mockResponse = {
        data: {
          executed_tasks: [
            { task_id: 'task-1', status: 'RUNNING' },
            { task_id: 'task-2', status: 'RUNNING' },
            { task_id: 'task-3', status: 'RUNNING' }
          ],
          message: 'All tasks submitted successfully'
        }
      };

      mockClient.post.mockResolvedValue(mockResponse);

      const result = await service.runAllDagTasks('session-123', 'RERUN_ALL');

      expect(mockClient.post).toHaveBeenCalledWith('/sessions/session-123/dag/run_all_tasks', {
        rerun_mode: 'RERUN_ALL'
      });
      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('task-1');
    });
  });

  describe('deleteDagTasks', () => {
    test('should delete DAG tasks successfully', async () => {
      const mockResponse = {
        data: {
          deleted_tasks: ['task-1', 'task-2'],
          message: 'Tasks deleted successfully'
        }
      };

      mockClient.delete.mockResolvedValue(mockResponse);

      const taskIds = ['task-1', 'task-2'];
      const result = await service.deleteDagTasks('session-123', taskIds, true, false);

      expect(mockClient.delete).toHaveBeenCalledWith('/sessions/session-123/dag/tasks', {
        data: {
          task_ids: taskIds,
          force: true,
          cleanup_resources: false
        }
      });
      expect(result.deleted_tasks).toEqual(taskIds);
    });

    test('should use default parameters', async () => {
      const mockResponse = {
        data: { deleted_tasks: ['task-1'] }
      };

      mockClient.delete.mockResolvedValue(mockResponse);

      const result = await service.deleteDagTasks('session-123', ['task-1']);

      expect(mockClient.delete).toHaveBeenCalledWith('/sessions/session-123/dag/tasks', {
        data: {
          task_ids: ['task-1'],
          force: false,
          cleanup_resources: true
        }
      });
    });
  });

  describe('exportExecutionGraph', () => {
    test('should export execution graph successfully', async () => {
      const mockResponse = {
        data: {
          graph: {
            nodes: { 'node-1': {} },
            edges: []
          }
        }
      };

      mockClient.get.mockResolvedValue(mockResponse);

      const result = await service.exportExecutionGraph('session-123');

      expect(mockClient.get).toHaveBeenCalledWith('/sessions/session-123/dag/export_graph');
      expect(result.graph).toBeDefined();
      expect(result.graph.nodes).toBeDefined();
    });
  });

  describe('listenSessionLifecycle', () => {
    let mockEventSource;

    beforeEach(() => {
      mockEventSource = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1
      };
      global.EventSource = jest.fn(() => mockEventSource);
    });

    test('should setup SSE listener for session lifecycle', () => {
      const listener = {
        onSessionCreated: jest.fn(),
        onSessionStateChanged: jest.fn(),
        onSessionClosed: jest.fn()
      };

      const subscription = service.listenSessionLifecycle(
        listener, 
        'session-123', 
        ['session_created', 'session_state_changed']
      );

      expect(global.EventSource).toHaveBeenCalledWith(
        '/api/sessions/lifecycle-subscribe?session_id=session-123&event_types=session_created%2Csession_state_changed'
      );
      expect(mockEventSource.addEventListener).toHaveBeenCalledTimes(2);
      expect(subscription.unsubscribe).toBeDefined();
    });

    test('should handle SSE events correctly', () => {
      const listener = {
        onSessionCreated: jest.fn(),
        onSessionStateChanged: jest.fn(),
        onSessionClosed: jest.fn()
      };

      service.listenSessionLifecycle(listener, 'session-123');

      // Simulate SSE events
      const eventHandlers = {};
      mockEventSource.addEventListener.mockImplementation((event, handler) => {
        eventHandlers[event] = handler;
      });

      // Re-setup to capture handlers
      service.listenSessionLifecycle(listener, 'session-123');

      // Simulate session created event  
      const sessionCreatedEvent = {
        data: JSON.stringify({
          event_type: 'session_created',
          id: 'session-123',
          session_id: 'session-123',
          state: 'RUNNING'
        })
      };

      if (eventHandlers.session_lifecycle) {
        eventHandlers.session_lifecycle(sessionCreatedEvent);
      }

      expect(listener.onSessionCreated).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'session-123' })
      );
    });

    test('should return subscription object', () => {
      const listener = { onSessionCreated: jest.fn() };
      const subscription = service.listenSessionLifecycle(listener, 'session-123');

      expect(subscription).toBeDefined();
      expect(typeof subscription.unsubscribe).toBe('function');

      // Test unsubscribe
      subscription.unsubscribe();
      expect(mockEventSource.close).toHaveBeenCalled();
    });
  });

  describe('listenTaskLifecycle', () => {
    let mockEventSource;

    beforeEach(() => {
      mockEventSource = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1
      };
      global.EventSource = jest.fn(() => mockEventSource);
    });

    test('should setup SSE listener for task lifecycle', () => {
      const listener = {
        onTaskStatusChanged: jest.fn(),
        onTaskCompleted: jest.fn(),
        onTaskError: jest.fn()
      };

      const subscription = service.listenTaskLifecycle(
        'session-123',
        listener,
        ['task_status_changed', 'task_completed']
      );

      expect(global.EventSource).toHaveBeenCalledWith(
        '/api/sessions/lifecycle-subscribe?session_id=session-123&event_types=task_status_changed%2Ctask_completed'
      );
      expect(subscription.unsubscribe).toBeDefined();
    });
  });

  describe('closeAllConnections', () => {
    test('should close all SSE connections', () => {
      const mockEventSource1 = { close: jest.fn() };
      const mockEventSource2 = { close: jest.fn() };

      // Manually add connections to the map
      service.sseConnections.set('conn1', mockEventSource1);
      service.sseConnections.set('conn2', mockEventSource2);

      service.closeAllConnections();

      expect(mockEventSource1.close).toHaveBeenCalled();
      expect(mockEventSource2.close).toHaveBeenCalled();
      expect(service.sseConnections.size).toBe(0);
    });
  });

  describe('Subscription class', () => {
    test('should create subscription and call unsubscribe function', () => {
      const unsubscribeFn = jest.fn();
      const subscription = new (require('@/pages/dpe-web/services/DataProcessService').Subscription)(unsubscribeFn);

      subscription.unsubscribe();

      expect(unsubscribeFn).toHaveBeenCalled();
    });

    test('should handle multiple unsubscribe calls safely', () => {
      const unsubscribeFn = jest.fn();
      const subscription = new (require('@/pages/dpe-web/services/DataProcessService').Subscription)(unsubscribeFn);

      subscription.unsubscribe();
      subscription.unsubscribe(); // Should not throw

      expect(unsubscribeFn).toHaveBeenCalledTimes(1);
    });
  });
});