/**
 * 测试相关路由配置，注意 `tests` 目录下的页面要采用相对导入，因为 `@` 只能加载 `src` 目录下的文件
 */
const testRoutes = [
  {
    path: '/test-example-button',
    name: 'TestExampleButton',
    component: () => import('../e2e/components/ExampleButtonTest.vue'),
  },
  // docs: 测试分页组件页面路由
  {
    path: '/test-pagination',
    name: 'TestPagination',
    component: () => import('../e2e/components/PaginationTest.vue'),
  },
  // docs: 测试动态表单页面路由
  {
    path: '/test/dynamic-form',
    name: 'DynamicFormTest',
    component: () => import('../e2e/pages/dpe-web/components/DynamicFormTest.vue')
  },
  // docs: 测试简化版动态表单页面路由
  {
    path: '/test/dynamic-form-simplify',
    name: 'DynamicFormSimplifyTest',
    component: () => import('../e2e/pages/dpe-web/components/DynamicFormSimplifyTest.vue')
  },
  // docs: 测试GraphEditor组件页面路由
  {
    path: '/test/graph-editor',
    name: 'GraphEditorTest',
    component: () => import('../e2e/pages/dpe-web/components/GraphEditorTest.vue')
  },
  // docs: 测试NodeStructuredDataContent组件页面路由
  {
    path: '/test-pages/NodeStructuredDataContentTest',
    name: 'NodeStructuredDataContentTest',
    component: () => import('../e2e/pages/dpe-web/components/NodeDetailPanel/components/NodeDataPanel/contents/NodeStructuredDataContentTest.vue')
  },
  // docs: 测试NodeConfigPanel组件页面路由
  {
    path: '/dpe-web/components/NodeDetailPanel/components/NodeConfigPanelTest',
    name: 'NodeConfigPanelTest',
    component: () => import('../e2e/pages/dpe-web/components/NodeDetailPanel/components/NodeConfigPanelTest.vue')
  },
  // docs: 测试NodeTaskInfoPanel组件页面路由
  {
    path: '/NodeTaskInfoPanelTest',
    name: 'NodeTaskInfoPanelTest',
    component: () => import('../e2e/pages/dpe-web/components/NodeDetailPanel/components/NodeTaskInfoPanelTest.vue')
  },
  // docs: 测试NodeDataPanel组件页面路由
  {
    path: '/test-routes/NodeDataPanelTest',
    name: 'NodeDataPanelTest',
    component: () => import('../e2e/pages/dpe-web/components/NodeDetailPanel/components/NodeDataPanelTest.vue')
  },
  // docs: 测试NodeDetailPanel主组件页面路由
  {
    path: '/test/node-detail-panel',
    name: 'NodeDetailPanelTest',
    component: () => import('../e2e/pages/dpe-web/components/NodeDetailPanel/NodeDetailPanelTest.vue')
  },
  // docs: 测试OperatorPanel组件页面路由
  {
    path: '/OperatorPanelTest.vue',
    name: 'OperatorPanelTest',
    component: () => import('../e2e/pages/dpe-web/components/OperatorPanelTest.vue')
  },
  // docs: 测试CreateSessionDialog组件页面路由
  {
    path: '/test/create-session-dialog',
    name: 'CreateSessionDialogTest',
    component: () => import('../e2e/pages/dpe-web/components/CreateSessionDialogTest.vue')
  },
  // docs: 测试SessionDetailContent组件页面路由
  {
    path: '/test/session-detail-content',
    name: 'SessionDetailContentTest',
    component: () => import('../e2e/pages/dpe-web/pages/SessionDetailPage/SessionDetailContentTest.vue')
  },
]

  // docs: 导出测试路由数组
  export default testRoutes;