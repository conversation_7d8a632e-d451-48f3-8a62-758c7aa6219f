const path = require('path');
const templates = require('./docgen.templates.js'); // 根据您的实际路径调整

module.exports = {
  // 您的组件和服务的根目录。这里通常与您的项目结构保持一致。
  // 例如，如果您的通用组件在 src/components 下，服务在 src/services 下。
  componentsRoot: 'src',
  // 要解析的文件 glob 模式，现在只包含 .vue 组件。
  // 这将匹配 src/components 及其子目录下的所有 .vue 文件。
  components: ['components/**/*.vue'],
  // 生成 Markdown 文件的输出目录。建议使用一个独立的目录，避免与 styleguidist 的输出冲突。
  outDir: 'docs/references',

  // 引入自定义模板，确保只生成 @public 方法，并包含其他所有元数据
  templates: {
    methods: templates.methods,
    props: templates.props,
    events: templates.events,
    slots: templates.slots,
    component: templates.component, // 使用自定义的组件整体模板
  },

  // vue-docgen-api 的选项。
  // 重要的是在这里配置 webpack 的 alias 或 resolve 路径，
  // 以确保 vue-docgen-api 能够正确解析组件内部的导入路径。
  // 例如，如果您的 jsconfig.json 中定义了路径别名 @/* 映射到 src/*，则需要在此处反映。
  apiOptions: {
    // 如果您的项目使用 webpack alias，可以在这里复制或手动配置
    alias: {
      '@': path.resolve(__dirname, 'src')
    },
    // 如果您的项目有自定义的 resolve 路径，可以在这里配置
    // resolve: [path.resolve(__dirname, 'src')],

    // **重要提示：**
    // `vue-docgen-api` 默认不直接解析 Vue 组件 Options API 中的 `provide` 和 `inject` 选项的详细结构。
    // 如果要实现从 Options API 中自动提取 `provide` 和 `inject` 的详细信息
    // (例如，其提供的具体属性、类型、默认值或注入的依赖来源等)，
    // 您需要编写自定义的 `addScriptHandlers` 来遍历组件 AST (抽象语法树) 并收集这些数据。
    // 这通常是一个更高级的用法，需要对 Babel AST (抽象语法树) 及其遍历有所了解。
    // 以下是一个概念性的示例结构，实际实现需要根据您的 provide/inject 模式来编写具体的 AST 解析逻辑：
    // addScriptHandlers: [
    //   function (documentation, componentDefinition, astPath, opt) {
    //     // 示例：解析 provide 选项
    //     // componentDefinition 是组件定义 (例如 Vue.extend 或 export default {}) 的 AST 节点。
    //     if (componentDefinition.has('properties')) {
    //       const provideProperty = componentDefinition.get('properties').find(p => p.node.key.name === 'provide');
    //       if (provideProperty && provideProperty.has('value')) {
    //         const provideValue = provideProperty.get('value').node;
    //         let parsedProvides = [];
    //         // 根据 provideValue 的类型 (例如，对象字面量或函数调用) 来遍历其 AST
    //         // 并提取所需信息，例如键名、类型、描述等。
    //         // 将提取的信息添加到 `documentation` 对象中，例如：
    //         // documentation.set('provides', parsedProvides);
    //       }
    //       // 同样，可以为 inject 编写类似的解析逻辑
    //       const injectProperty = componentDefinition.get('properties').find(p => p.node.key.name === 'inject');
    //       if (injectProperty && injectProperty.has('value')) {
    //         const injectValue = injectProperty.get('value').node;
    //         let parsedInjects = [];
    //         // ... 解析 inject 的逻辑 ...
    //         // documentation.set('injects', parsedInjects);
    //       }
    //     }
    //   }
    // ]
  }
};