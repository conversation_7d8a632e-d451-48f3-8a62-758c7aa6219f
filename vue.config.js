const { defineConfig } = require('@vue/cli-service')
const path = require('path')


module.exports = defineConfig({
  configureWebpack: {
    resolve: {
      alias: {
        'core-js/modules': 'core-js/modules'
      }
    },
    devtool: 'source-map',
  },
  transpileDependencies: ['webpack-dev-server', '@ant-design/icons-vue', 'ant-design-vue'],
  publicPath: '/demo/',
  devServer: {
    port: 8080,
    client: {
      // 禁用错误覆盖层，特别是在测试环境中
      overlay: process.env.NODE_ENV !== 'test' ? true : false,
      // 在测试环境中禁用其他可能的干扰
      progress: process.env.NODE_ENV !== 'test'
    },
    // 添加中间件支持
    // setupMiddlewares: (middlewares, devServer) => {
    //   if (!devServer) {
    //     throw new Error('webpack-dev-server is not defined');
    //   }

    //   // 添加API Mock中间件（仅在开发环境）
    //   if (process.env.NODE_ENV === 'development') {
    //     // 添加JSON body解析器
    //     devServer.app.use('/api', require('express').json());
    //     devServer.app.use(apiMockMiddleware);
    //     console.log('🎭 API Mock中间件已启用，支持结构化数据API模拟');
    //   }

    //   return middlewares;
    // },
    proxy: {
      '/demo/daasMeta': {
        target: 'http://10.16.196.68:8081',
        changeOrigin: true,
        pathRewrite: {
          '^/demo': ''
        }
      }
    }
  },
  // 添加或修改这一行来禁用开发服务器时的 ESLint 检查
  lintOnSave: false
})
