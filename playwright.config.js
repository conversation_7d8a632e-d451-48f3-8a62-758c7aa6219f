// @ts-check
import { defineConfig, devices } from '@playwright/test';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// import dotenv from 'dotenv';
// import path from 'path';
// dotenv.config({ path: path.resolve(__dirname, '.env') });


const devServerPort = 8200;

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 1,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [['list'], ['html', { open: 'never' }]],

  // docs: 配置全局快照路径模板，将所有截图保存到 test-results 目录
  snapshotPathTemplate: 'test-results/{testFileName}-{testName}-{projectName}/{arg}{ext}',

  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: `http://localhost:${devServerPort}/demo/`,

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    screenshot: "on",
    locale: 'zh-CN',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },

    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },

    /* Test against mobile viewports. */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },

    /* Test against branded browsers. */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    // 在 Windows 上检查端口是否被占用。如果占用，立即退出并报错；否则，设置 PORT 环境变量并运行开发服务器。
    // netstat -ano | findstr :${devServerPort} 检查端口是否有监听进程
    // && exit 1 如果 findstr 找到结果 (端口被占用)，则执行 exit 1
    // || set PORT=${devServerPort} && npm run serve 如果 findstr 没有找到结果 (端口未被占用)，则设置 PORT 环境变量并执行 npm run serve
    command: `chcp 65001 && npm run serve`,
    env: {
      PORT: devServerPort.toString(),
      // docs: 确保在运行Playwright测试时NODE_ENV设置为'test'，保证加载测试路由
      NODE_ENV: 'test',
    },
    url: `http://localhost:${devServerPort}/demo/`,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
});

